package com.paic.ncbs.claim.dao.mapper.duty;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.model.dto.duty.ClmsCargoTrans;

/**
 * <p>
 * 货运险运输路径表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
public interface ClmsCargoTransMapper extends BaseMapper<ClmsCargoTrans> {

    ClmsCargoTrans getClmsCargoTrans(String reportNo, String caseTimes);

    void saveClmsCargoTrans(ClmsCargoTrans clmsCargoTrans);

    void deleteByReportNoAndCaseTimes(String reportNo, String caseTimes);

}
