package com.paic.ncbs.claim.dao.mapper.checkloss;

import com.paic.ncbs.claim.model.dto.duty.OtherLossPptDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface OtherLossPptMapper {

    public void removeOtherLossPptList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("taskCode") String taskCode);

    public List<OtherLossPptDTO> getOtherLossPptListByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("status") String status, @Param("taskCode") String taskCode);

    public void addOtherLossPptList(@Param("otherLossPptList") List<OtherLossPptDTO> otherLossPptList, @Param("caseTimes") Integer caseTimes,
                                    @Param("userId") String userId, @Param("channelProcessId") String channelProcessId);

    public List<OtherLossPptDTO> getOtherLossPptList(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskCode") String taskCode);

    public List<String> getAccidentReasonList(@Param("reportNo") String reportNo);

}
