package com.paic.ncbs.claim.feign.fallback;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.feign.TpaGlobalFeign;
import com.paic.ncbs.claim.model.dto.investigate.TpaGlobalAgentDTO;
import com.paic.ncbs.claim.model.vo.investigate.TpaInvestigateVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TpaGlobalFallback implements FallbackFactory<TpaGlobalFeign> {
    @Override
    public TpaGlobalFeign create(Throwable cause) {
        return new TpaGlobalFeign() {
            @Override
            public String getExternalDepartmentList(TpaGlobalAgentDTO param) {
                log.error("TpaGlobalFeign.getExternalDepartmentList error, param={}", JSON.toJSONString(param), cause);
                return null;
            }

            @Override
            public String fainshTask(TpaInvestigateVO tpaInvestigateVO) {
                log.error("TpaGlobalFeign.fainshTask error, param={}", JSON.toJSONString(tpaInvestigateVO), cause);
                return null;
            }
        };
    }
}
