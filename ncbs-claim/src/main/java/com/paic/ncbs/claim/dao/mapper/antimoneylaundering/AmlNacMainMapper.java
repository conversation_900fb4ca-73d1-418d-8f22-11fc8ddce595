package com.paic.ncbs.claim.dao.mapper.antimoneylaundering;

import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

/**
 * 反洗钱黑名单主表mapper
 */
@MapperScan
public interface AmlNacMainMapper {

    /**
     * 获取洗钱黑名单数量
     *
     * @param clientName 客户名称
     * @param clientCertificateNo 客户证件号码
     * @return 行数
     */
    int getMoneyLaunderingBlackListCount(@Param("clientName") String clientName, @Param("clientCertificateNo") String clientCertificateNo);

    /**
     * 获取高风险国家地区黑名单数量
     *
     * @param nationCode 国籍
     * @return 行数
     */
    int getHighRiskCountryBlackListCount(@Param("nationCode") String nationCode);
}
