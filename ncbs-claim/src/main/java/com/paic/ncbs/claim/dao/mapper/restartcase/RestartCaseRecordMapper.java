package com.paic.ncbs.claim.dao.mapper.restartcase;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.restartcase.RestartCaseRecordEntity;
import com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2023-05-22 15:15
 */
public interface RestartCaseRecordMapper extends BaseDao<RestartCaseRecordEntity> {

    List<RestartCaseRecordEntity> getRestartCaseList(String reportNo, Integer caseTimes);

    List<RestartCaseRecordEntity> getThisRestartCaseList(String reportNo, Integer caseTimes);

    List<RestartCaseRecordEntity> getRestartCaseListByApproval(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    List<RestartCaseRecordEntity> getRestartApprovalReturnList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    List<RestartCaseRecordEntity> getRestartCaseListForDetail(String reportNo, Integer caseTimes);

    List<RestartCaseRecordEntity> getRestartList(String reportNo);

    void saveApprovalProcess(RestartCaseRecordEntity restartCaseRecordEntity);

    void invalidRestartCase(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    Integer countTask(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes );

    List<WholeCaseVO> getHistoryCaseByReportNo(@Param("reportNo") String reportNo, @Param("departmentCodes") List<String> departmentCodes);

    WholeCaseVO getCaseByReportNoAndCaseTimes(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    List<WholeCaseVO> getHistoryCaseByPolicyCaseNo(@Param("policyNo") String policyNo,@Param("caseNo") String caseNo,@Param("batchNo") String batchNo, @Param("departmentCodes") List<String> departmentCodes);

    List<WholeCaseVO> getHistoryCaseByBatchNo(@Param("batchNo") String batchNo, @Param("departmentCodes") List<String> departmentCodes);


    void updateIsNewest(RestartCaseRecordEntity restartCaseRecordEntity);

    RestartCaseRecordEntity  getRestartAuditTime(String reportNo,Integer caseTimes);

    void invalidRestartCaseRecord(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    Integer getRestartCaseRecordcount(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes );
}
