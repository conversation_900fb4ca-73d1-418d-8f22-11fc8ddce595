package com.paic.ncbs.claim.mq.producer.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.model.dto.mq.CompensateDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLCompensateDto;
import com.paic.ncbs.claim.mq.producer.MqProducerClaimPaymentInformationService;
import lombok.extern.slf4j.Slf4j;
//import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class MqProducerClaimPaymentInformationServiceImpl implements MqProducerClaimPaymentInformationService {
    @Value("${mq.rabbit.exchange.ncbs.claim.root:ncbsClaimExchange}")
    private String ncbsClaimExchange;
    //立案环节topic
    @Value("${mq.compensate.topic}")
    private String compensateTopic;
   /* @Autowired
    private RabbitTemplate rabbitTemplate;*/

    @Override
    @Async("asyncPool")
    public void syncClaimPaymentInformationLink(String compensateNo, Date payTime) {
        // 数据组装
        CompensateDto compensateDto =new CompensateDto();
        List<PrpLCompensateDto> prpLCompensateDtos = new ArrayList<>();
        PrpLCompensateDto prpLCompensateDto = new PrpLCompensateDto();
        prpLCompensateDto.setCompensateNo(compensateNo);
        prpLCompensateDto.setPayTime(payTime);
        prpLCompensateDtos.add(prpLCompensateDto);
        compensateDto.setPrpLCompensateDtoList(prpLCompensateDtos);
        //发送MQ消息
        JSONObject jsonObj = (JSONObject) JSON.toJSON(compensateDto);
        log.info("syncClaimLink===sendMessage: {}",jsonObj);
       // rabbitTemplate.convertAndSend(ncbsClaimExchange, compensateTopic, compensateDto);
    }


}

