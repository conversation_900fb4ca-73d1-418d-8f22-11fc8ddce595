package com.paic.ncbs.claim.dao.entity.ahcs;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.math.BigDecimal;
import java.util.Date;

public class AhcsDutyAttributeEntity extends EntityDTO {

    private static final long serialVersionUID = 498723211188848585L;


    private String idAhcsDutyAttribute;

    private String createdBy;

    private Date createdDate;

    private String updatedBy;

    private Date updatedDate;

    private String idAhcsPolicyDuty;

    private String attributeCode;

    private String attributeValue;

    private BigDecimal attrRateValue;

    private String attributeUnit;

    public String getIdAhcsDutyAttribute() {
        return idAhcsDutyAttribute;
    }

    public void setIdAhcsDutyAttribute(String idAhcsDutyAttribute) {
        this.idAhcsDutyAttribute = idAhcsDutyAttribute == null ? null : idAhcsDutyAttribute.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(Date updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getIdAhcsPolicyDuty() {
        return idAhcsPolicyDuty;
    }

    public void setIdAhcsPolicyDuty(String idAhcsPolicyDuty) {
        this.idAhcsPolicyDuty = idAhcsPolicyDuty == null ? null : idAhcsPolicyDuty.trim();
    }

    public String getAttributeCode() {
        return attributeCode;
    }

    public void setAttributeCode(String attributeCode) {
        this.attributeCode = attributeCode == null ? null : attributeCode.trim();
    }

    public String getAttributeValue() {
        return attributeValue;
    }

    public void setAttributeValue(String attributeValue) {
        this.attributeValue = attributeValue == null ? null : attributeValue.trim();
    }

    public BigDecimal getAttrRateValue() {
        return attrRateValue;
    }

    public void setAttrRateValue(BigDecimal attrRateValue) {
        this.attrRateValue = attrRateValue;
    }

    public String getAttributeUnit() {
        return attributeUnit;
    }

    public void setAttributeUnit(String attributeUnit) {
        this.attributeUnit = attributeUnit;
    }

}