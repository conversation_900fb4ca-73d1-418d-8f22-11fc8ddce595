package com.paic.ncbs.claim.dao.mapper.endcase;

import com.paic.ncbs.claim.dao.entity.ClaimRejectionApprovalRecordEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ClaimRejectionApprovalRecordEntityMapper {

    int deleteByPrimaryKey(String idAhcsCaseRegisterApply);

    int insert(ClaimRejectionApprovalRecordEntity record);

    int updateByPrimaryKey(ClaimRejectionApprovalRecordEntity record);

    /**
     * 查询待审批的记录
     * @param reportNo
     * @param caseTimes
     * @return
     */
    ClaimRejectionApprovalRecordEntity selectPendingRecord(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    /**
     * 查询审批同意的的记录
     * @param reportNo
     * @param caseTimes
     * @return
     */
    ClaimRejectionApprovalRecordEntity selectAgreedRecord(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    /**
     * 查询拒赔审批记录
     * @param reportNo
     * @param caseTimes
     * @return
     */
    List<ClaimRejectionApprovalRecordEntity> selectByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

}