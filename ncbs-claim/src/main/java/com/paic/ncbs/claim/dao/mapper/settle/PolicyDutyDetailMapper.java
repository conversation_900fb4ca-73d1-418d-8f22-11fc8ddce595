package com.paic.ncbs.claim.dao.mapper.settle;

import com.paic.ncbs.claim.model.dto.settle.PolicyDutyDetailDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;
import java.util.Map;

@MapperScan
public interface PolicyDutyDetailMapper {

	List<PolicyDutyDetailDTO> getByDutyID(@Param("idAhcsEstimateDuty")String idAhcsEstimateDuty);

	List<Map<String,String>> getDutyDetailByReportNo(@Param("reportNo") String reportNo, @Param("insuredApplyStatus") String insuredApplyStatus);

	Integer getElementCode1ByReportNoAndAccidentType(@Param("reportNo") String reportNo, @Param("insuredApplyStatus") String insuredApplyStatus, @Param("element2List") String[] element2List, @Param("element1List") String[] element1List);


}
