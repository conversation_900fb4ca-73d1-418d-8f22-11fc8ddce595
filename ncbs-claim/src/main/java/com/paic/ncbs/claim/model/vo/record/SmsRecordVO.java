package com.paic.ncbs.claim.model.vo.record;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class SmsRecordVO extends EntityDTO {
        private String idAhcsSmsInfo;
        private String smsContent;
        private String mobileNo;
        private String sendUser;
        private String reportNo;
        private Date sendDate;
        private String smsStatus;
        private String sendLink;
        private String baseDesc;
        private String sendSeriesId;
        private String sendWay;
        private BigDecimal baseId;
        private BigDecimal baseStatus;
        private String requestId;
        private String smsTemplateCode;
        private String recipient;


}