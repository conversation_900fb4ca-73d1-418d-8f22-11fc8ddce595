package com.paic.ncbs.claim.dao.mapper.endcase;

import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import com.paic.ncbs.claim.model.vo.endcase.CaseRegisterApplyVO;
import com.paic.ncbs.claim.model.dto.endcase.CaseRegisterApplyDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface CaseRegisterApplyMapper {

    void addCaseRegisterApplyDTO(CaseRegisterApplyDTO caseRegisterApplyDTO);

    CaseRegisterApplyDTO getLastestRegisterApplyDTO(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    List<CaseRegisterApplyVO> getLastestRegisterApplyVOList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    void saveRegisterAuditInfo(CaseRegisterApplyVO caseRegisterApplyVO);

    void modifyRegisterAuditStatus(CaseRegisterApplyVO caseRegisterApplyVO);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);

    /**
     * 查询报案未立案数据
     * @param configDays
     */
    List<String> getNoRegisterData(Integer configDays);
}
