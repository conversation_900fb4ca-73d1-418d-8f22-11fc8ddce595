package com.paic.ncbs.claim.dao.mapper.checkloss;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.checkloss.DisabilityAppraisalDTO;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface DisabilityAppraisalMapper extends BaseDao<DisabilityAppraisalDTO> {

    public void addDisabilityAppraisal(DisabilityAppraisalDTO disabilityAppraisalDTO);

    public void modifyDisabilityAppraisal(DisabilityAppraisalDTO disabilityAppraisalDTO);

    public DisabilityAppraisalDTO getDisabilityAppraisal(DisabilityAppraisalDTO disabilityAppraisalDTO);

    public List<DisabilityAppraisalDTO> getIsAppraisal(@Param("idAhcsChannelProcess") String idAhcsChannelProcess);

    public DisabilityAppraisalDTO getDisabilityAppraisalByIdAhcsChannelProcess(@Param("idAhcsChannelProcess") String idAhcsChannelProcess);

    public void removeDisabilityAppraisalByIdAhcsChannelProcess(@Param("idAhcsChannelProcess") String idAhcsChannelProcess);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);
}
