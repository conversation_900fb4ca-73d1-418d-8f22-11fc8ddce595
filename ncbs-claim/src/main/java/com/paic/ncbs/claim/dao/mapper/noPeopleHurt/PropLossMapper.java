package com.paic.ncbs.claim.dao.mapper.noPeopleHurt;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.dao.entity.duty.PropLossEntity;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;

public interface PropLossMapper extends BaseMapper<PropLossEntity> {

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);
}