package com.paic.ncbs.claim.model.vo.batch;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@ApiModel(description = "退运险领款人信息")
public class OnlinePaymentInfo {

    @ApiModelProperty(" 开户行帐号")
    //@NotBlank(message = " 开户行帐号不能为空")
    private String clientBankAccount;

    @ApiModelProperty("银行大类编码")
    //@NotBlank(message = "银行大类编码不能为空")
    private String clientBankCode;

    @ApiModelProperty("银行大类名称")
    //@NotBlank(message = "银行大类名称不能为空")
    private String clientBankName;

    @ApiModelProperty("帐号类型:公司 =0，个人帐号=1")
    //@NotBlank(message = "帐号类型不能为空")
    private String bankAccountAttribute;

    @ApiModelProperty(" 客户证件号码")
    private String clientCertificateNo;

    @ApiModelProperty(" 客户证件类型")
    private String clientCertificateType;

    @ApiModelProperty(" 公司法人的证件类型")
    private String companyCardType;

    @ApiModelProperty("  公司组织机构代码 ")
    private String organizeCode;

    @ApiModelProperty("客户联系电话")
    //@NotBlank(message = "客户联系电话不能为空")
    private String clientMobile;

    @ApiModelProperty("领款人姓名")
    @NotBlank(message = "领款人姓名不能为空")
    private String clientName;

    @ApiModelProperty("赔款金额")
    @NotNull(message = "赔款金额不能为空")
    private BigDecimal payAmount;

    @ApiModelProperty("  客户类型：01=被保险人,02=投保人,08=索赔人,13=法定受益人,14=受益人,09=其他")
    @NotBlank(message = "客户类型不能为空")
    private String clientType;

    @ApiModelProperty("支行名称")
    private String bankDetail ;

    @ApiModelProperty("支行编码")
    private String bankDetailCode;

    @ApiModelProperty("支行赔款表的唯一标识")
    private String paySerialNo;

    @ApiModelProperty("支付、结算方式：01-公司柜面;02-实时支付;03-批量转账")
    @NotBlank(message = "支付、结算方式不能为空")
    private String collectPayApproach;

    @ApiModelProperty("领款方式为微信零钱和美团支付的时候必传")
    private String openId;

    @ApiModelProperty("领款方式：1-微信零钱，2-银行转账, 3-美团点评支付")
    private String payType;

}
