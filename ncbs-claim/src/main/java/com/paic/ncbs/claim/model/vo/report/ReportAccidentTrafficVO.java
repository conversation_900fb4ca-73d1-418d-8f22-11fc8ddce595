package com.paic.ncbs.claim.model.vo.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "其他交通工具延误信息VO")
public class ReportAccidentTrafficVO {

    @ApiModelProperty(value = "报案号")
    private String reportNo;

    @ApiModelProperty(value = "出发地")
    private String departurePlace;

    @ApiModelProperty(value = "目的地")
    private String destination;

    @ApiModelProperty(value = "交通工具是否延误(是：Y 否：N)")
    private String isTrafficDelay;

    @ApiModelProperty(value = "变更的港口")
    private String changedPort;

    @ApiModelProperty(value = "轮船延误情况")
    private String[] steamerDelayCase;

    @ApiModelProperty(value = "原定出发时间")
    private Date originalDepartureDate;

    @ApiModelProperty(value = "实际出发时间")
    private Date actualDepartureDate;

    @ApiModelProperty(value = "原定到达时间")
    private Date originalArrivalDate;

    @ApiModelProperty(value = "实际到达时间")
    private Date actualArrivalDate;

    @ApiModelProperty(value = "费用预估")
    private BigDecimal costEstimate;

    @ApiModelProperty(value = "延误时长（分钟）")
    private int delayTime;

    public String getReportNo() {
        return reportNo;
    }

    private String transportation;

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String getDeparturePlace() {
        return departurePlace;
    }

    public void setDeparturePlace(String departurePlace) {
        this.departurePlace = departurePlace == null ? null : departurePlace.trim();
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination == null ? null : destination.trim();
    }

    public String getIsTrafficDelay() {
        return isTrafficDelay;
    }

    public void setIsTrafficDelay(String isTrafficDelay) {
        this.isTrafficDelay = isTrafficDelay == null ? null : isTrafficDelay.trim();
    }

    public String getChangedPort() {
        return changedPort;
    }

    public void setChangedPort(String changedPort) {
        this.changedPort = changedPort == null ? null : changedPort.trim();
    }

    public String[] getSteamerDelayCase() {
        return steamerDelayCase;
    }

    public void setSteamerDelayCase(String[] steamerDelayCase) {
        this.steamerDelayCase = steamerDelayCase;
    }

    public Date getOriginalDepartureDate() {
        return originalDepartureDate;
    }

    public void setOriginalDepartureDate(Date originalDepartureDate) {
        this.originalDepartureDate = originalDepartureDate;
    }

    public Date getActualDepartureDate() {
        return actualDepartureDate;
    }

    public void setActualDepartureDate(Date actualDepartureDate) {
        this.actualDepartureDate = actualDepartureDate;
    }

    public Date getOriginalArrivalDate() {
        return originalArrivalDate;
    }

    public void setOriginalArrivalDate(Date originalArrivalDate) {
        this.originalArrivalDate = originalArrivalDate;
    }

    public Date getActualArrivalDate() {
        return actualArrivalDate;
    }

    public void setActualArrivalDate(Date actualArrivalDate) {
        this.actualArrivalDate = actualArrivalDate;
    }

    public BigDecimal getCostEstimate() {
        return costEstimate;
    }

    public void setCostEstimate(BigDecimal costEstimate) {
        this.costEstimate = costEstimate;
    }

    public String getTransportation() {
        return transportation;
    }

    public void setTransportation(String transportation) {
        this.transportation = transportation;
    }

    public int getDelayTime() {
        return delayTime;
    }

    public void setDelayTime(int delayTime) {
        this.delayTime = delayTime;
    }
}
