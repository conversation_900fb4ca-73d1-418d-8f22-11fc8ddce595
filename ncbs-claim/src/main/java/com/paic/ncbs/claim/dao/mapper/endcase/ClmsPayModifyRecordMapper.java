package com.paic.ncbs.claim.dao.mapper.endcase;

import com.paic.ncbs.claim.model.dto.endcase.ClmsPayModifyRecordDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface ClmsPayModifyRecordMapper {

    void addClmsPayModifyRecordDTO (ClmsPayModifyRecordDTO clmsPayModifyRecordDTO);

    List<ClmsPayModifyRecordDTO> getClmsPayModifyRecordList(@Param("idClmPaymentItem") String idClmPaymentItem);

}
