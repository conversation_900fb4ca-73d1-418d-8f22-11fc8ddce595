package com.paic.ncbs.claim.dao.mapper.noPeopleHurt;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.dao.entity.duty.PropDetailLossEntity;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PropDetailLossMapper extends BaseMapper<PropDetailLossEntity> {

    /**
     * 批量新增数据
     *
     * @param detailLossEntityList
     * @return 影响行数
     */
    int insertBatch(@Param("detailLossEntityList") List<PropDetailLossEntity> detailLossEntityList);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);
}