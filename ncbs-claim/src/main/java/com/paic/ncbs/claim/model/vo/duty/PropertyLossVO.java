package com.paic.ncbs.claim.model.vo.duty;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.checkloss.PropertyLossDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel("财产损失表VO")
public class PropertyLossVO {

    @ApiModelProperty("")
    private String moduleCode;

    @ApiModelProperty("通道号")
    private String idAhcsChannelProcess;

    @ApiModelProperty("财产损失表主键")
    private String idAhcsPropertyLoss;

    @ApiModelProperty("境内外：0：境内，1：境外")
    private String accidentOverseas;

    @ApiModelProperty("出险省份")
    private String provinceCode;

    @ApiModelProperty("出险大洲")
    private String accidentContinentCode;

    @ApiModelProperty("出险城市CODE")
    private String accidentCityCode;

    @ApiModelProperty("客户号")
    private String accidentCountyCode;

    @ApiModelProperty("出险县")
    private String accidentPlace;

    @ApiModelProperty("状态。1：发送，0：暂存")
    private String status;

    @ApiModelProperty("环节号")
    private String taskCode;

    @ApiModelProperty("出险类型列表")
    private List<String> accidentTypeList;

    @ApiModelProperty("三者财产定损金额")
    private BigDecimal thirdPropertyLossAmount;

    @ApiModelProperty("")
    private String showThirdProperty;

    @ApiModelProperty("财产损失表DTO")
    private List<PropertyLossDTO> PropertyLossList;

    @ApiModelProperty("定损金额")
    private BigDecimal lossAmount;

    @ApiModelProperty("变更原因")
    private String alterReason;

    @ApiModelProperty("出险时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date accidentDate;

    @ApiModelProperty("事故原因编码")
    private String accidentCauseCode;

    @ApiModelProperty("其他事故原因")
    private String otherAccidentCause;

    public BigDecimal getThirdPropertyLossAmount() {
        return thirdPropertyLossAmount;
    }

    public void setThirdPropertyLossAmount(BigDecimal thirdPropertyLossAmount) {
        this.thirdPropertyLossAmount = thirdPropertyLossAmount;
    }

    public List<String> getAccidentTypeList() {
        return accidentTypeList;
    }

    public void setAccidentTypeList(List<String> accidentTypeList) {
        this.accidentTypeList = accidentTypeList;
    }

    public String getModuleCode() {
        return moduleCode;
    }

    public String getIdAhcsChannelProcess() {
        return idAhcsChannelProcess;
    }

    public void setIdAhcsChannelProcess(String idAhcsChannelProcess) {
        this.idAhcsChannelProcess = idAhcsChannelProcess;
    }

    public String getIdAhcsPropertyLoss() {
        return idAhcsPropertyLoss;
    }

    public void setIdAhcsPropertyLoss(String idAhcsPropertyLoss) {
        this.idAhcsPropertyLoss = idAhcsPropertyLoss;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public String getAccidentOverseas() {
        return accidentOverseas;
    }

    public void setAccidentOverseas(String accidentOverseas) {
        this.accidentOverseas = accidentOverseas;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getAccidentContinentCode() {
        return accidentContinentCode;
    }

    public void setAccidentContinentCode(String accidentContinentCode) {
        this.accidentContinentCode = accidentContinentCode;
    }

    public String getAccidentCityCode() {
        return accidentCityCode;
    }

    public void setAccidentCityCode(String accidentCityCode) {
        this.accidentCityCode = accidentCityCode;
    }

    public String getAccidentCountyCode() {
        return accidentCountyCode;
    }

    public void setAccidentCountyCode(String accidentCountyCode) {
        this.accidentCountyCode = accidentCountyCode;
    }

    public String getAccidentPlace() {
        return accidentPlace;
    }

    public void setAccidentPlace(String accidentPlace) {
        this.accidentPlace = accidentPlace;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<PropertyLossDTO> getPropertyLossList() {
        return PropertyLossList;
    }

    public void setPropertyLossList(List<PropertyLossDTO> propertyLossList) {
        PropertyLossList = propertyLossList;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getShowThirdProperty() {
        return showThirdProperty;
    }

    public void setShowThirdProperty(String showThirdProperty) {
        this.showThirdProperty = showThirdProperty;
    }

    public BigDecimal getLossAmount() {
        return lossAmount;
    }

    public void setLossAmount(BigDecimal lossAmount) {
        this.lossAmount = lossAmount;
    }

    public String getAlterReason() {
        return alterReason;
    }

    public void setAlterReason(String alterReason) {
        this.alterReason = alterReason;
    }

    public Date getAccidentDate() {
        return accidentDate;
    }

    public void setAccidentDate(Date accidentDate) {
        this.accidentDate = accidentDate;
    }

    public String getAccidentCauseCode() {
        return accidentCauseCode;
    }

    public void setAccidentCauseCode(String accidentCauseCode) {
        this.accidentCauseCode = accidentCauseCode;
    }

    public String getOtherAccidentCause() {
        return otherAccidentCause;
    }

    public void setOtherAccidentCause(String otherAccidentCause) {
        this.otherAccidentCause = otherAccidentCause;
    }
}
