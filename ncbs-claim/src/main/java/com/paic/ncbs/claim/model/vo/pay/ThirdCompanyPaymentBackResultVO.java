package com.paic.ncbs.claim.model.vo.pay;

public class ThirdCompanyPaymentBackResultVO {

	// 业务类型	类型：E批单、Y预赔、C实赔
	private String certitype;
	// 业务单号	批单号、计算书号
	private String certino;
	// 期次	期次号
	private String serialno;
	// 领款人名称	领款人名称
	private String unitname;
	// 支付状态	2成功，3失败，6退票
	private String payStatus;
	// 支付类型 1 赔款 2 费用
	private String paytype;
	// 支付信息	成功或失败原因
	private String payInfo;
	// 支付日期	资金支付日期
	private String paymentDate;
	//	金额	支付金额
	private String payValue;
	// 资金ID	资金支付唯一标识
	private String sourcenotecode;

	public String getCertitype() {
		return certitype;
	}

	public void setCertitype(String certitype) {
		this.certitype = certitype;
	}

	public String getCertino() {
		return certino;
	}

	public void setCertino(String certino) {
		this.certino = certino;
	}

	public String getSerialno() {
		return serialno;
	}

	public void setSerialno(String serialno) {
		this.serialno = serialno;
	}

	public String getUnitname() {
		return unitname;
	}

	public void setUnitname(String unitname) {
		this.unitname = unitname;
	}

	public String getPayStatus() {
		return payStatus;
	}

	public void setPayStatus(String payStatus) {
		this.payStatus = payStatus;
	}

	public String getPayInfo() {
		return payInfo;
	}

	public void setPayInfo(String payInfo) {
		this.payInfo = payInfo;
	}

	public String getPaymentDate() {
		return paymentDate;
	}

	public void setPaymentDate(String paymentDate) {
		this.paymentDate = paymentDate;
	}

	public String getPayValue() {
		return payValue;
	}

	public void setPayValue(String payValue) {
		this.payValue = payValue;
	}

	public String getSourcenotecode() {
		return sourcenotecode;
	}

	public void setSourcenotecode(String sourcenotecode) {
		this.sourcenotecode = sourcenotecode;
	}

	public String getPaytype() {
		return paytype;
	}

	public void setPaytype(String paytype) {
		this.paytype = paytype;
	}
}
