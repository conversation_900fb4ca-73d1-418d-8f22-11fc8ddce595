package com.paic.ncbs.claim.model.vo.duty;

import com.paic.ncbs.claim.model.dto.duty.*;
import com.paic.ncbs.claim.model.vo.dynamic.DynamicFieldResultVO;
import com.paic.ncbs.claim.model.vo.settle.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@ApiModel("核责信息VO")
@Getter
@Setter
public class DutyVerifyVO {

    @ApiModelProperty("初核结论（1-赔付 4-拒赔 15-合议）")
    private String verifyConclusion;

    @ApiModelProperty("初核说明")
    private String verifyCommont;

    @ApiModelProperty("减损金额")
    private BigDecimal reduceAmount;

    @ApiModelProperty("案件类别：1.人伤 2.非人伤")
    private List<String> caseClass;

    @ApiModelProperty("诊断信息")
    private List<PersonDiagnoseDTO> diagnoseInfo;

    @ApiModelProperty("意外")
    private SettleAccidentVO settleAccident;

    @ApiModelProperty("疾病")
    private SettleDiseaseVO settleDisease;

    @ApiModelProperty("重大疾病")
    private SettleBigDiseaseVO settleBigDisease;

    @ApiModelProperty("伤残")
    private SettleDisabilityVO settleDisability;

    @ApiModelProperty("身故")
    private SettleDeathVO settleDeath;

    @ApiModelProperty("理算津贴")
    private Map<String,List<PersonBenefitDTO>> settleBenefit;

    @ApiModelProperty("救援信息")
    private SettleRescueVO settleRescue;

    @ApiModelProperty("人伤门诊住院信息")
    private List<PersonHospitalDTO> personHospital;

    @ApiModelProperty("理算非人伤VO")
    private SettleNoPeopleVO settleNoPeopleVO;
    @ApiModelProperty("人身伤亡信息")
    private ClmsPersonalInjuryDeathInfoDTO clmsPersonalInjuryDeathInfoDTO;

    @ApiModelProperty("第三者财产损失信息")
    private List<ClmsPropertyLossInfoDTO> clmsPropertyLossInfoDTO;

    @ApiModelProperty("法律责任及其他信息")
    private List<ClmsLegalLiabilityClassInfoDTO> clmsLegalLiabilityClassInfoDTO;

    @ApiModelProperty("个人财产损失信息")
    //private ClmsSubstanceLossInfoDTO clmsSubstanceLossInfoDTO;
    private List<ClmsSubstanceLossInfoDTO> substanceLossInfoDTOList;

    @ApiModelProperty("企业财产损失信息")
    private PropLossDTO propLossDTO;

    @ApiModelProperty("现金损失信息")
    private List<ClmsCashLossInfoDTO> clmsCashLossInfoDTO;

    @ApiModelProperty("延误信息")
    private ClmsTravelDelayInfoDTO clmsTravelDelayInfoDTO;

    @ApiModelProperty("救援信息")
    private List<ClmsRescueInfoDTO> clmsRescueInfoDTO;

    @ApiModelProperty("给付津贴")
    private List<ClmsAllowanceInfoDTO> clmsAllowanceInfoDTO;

    private ClmsRiskInfoDTO clmsRiskInfoDTO;

    @ApiModelProperty("重复发票信息")
    private List<String> duplicateBillMsgs;

    @ApiModelProperty("票据风险")
    private List<String> billRsikMsgs;

    @ApiModelProperty(value = "动态字段")
    private List<DynamicFieldResultVO> dynamicFieldResult;
}
