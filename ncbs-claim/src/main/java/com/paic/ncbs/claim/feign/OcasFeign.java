package com.paic.ncbs.claim.feign;

import com.paic.ncbs.claim.config.AdminFallBackFactory;
import com.paic.ncbs.claim.config.FeignConfiguration;
import com.paic.ncbs.claim.model.dto.realname.OcasRealNameDTO;
import com.paic.ncbs.claim.model.dto.report.CopyPolicyQueryVO;
import com.paic.ncbs.claim.model.dto.report.RatingQueryVO;
import com.paic.ncbs.claim.model.dto.secondunderwriting.UwSendPosConclusionDTO;
import com.paic.ncbs.claim.model.vo.senconduw.SurrendApplyReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * 本地联调抄单时 ,@FeignClient 注解中增加 url = "http://10.119.134.97:8080" 调试本地
 */
@FeignClient(name = "${out.api.admin}", configuration = {FeignConfiguration.class}, fallbackFactory = AdminFallBackFactory.class)
@Component
public interface OcasFeign {

	@PostMapping(value="/ocas/do/app/getPolicyInfoByPolicyNo/contract")
	String getPolicyInfoByPolicyNoContract(@RequestBody CopyPolicyQueryVO vo);

	@PostMapping(value="/ocas/do/app/queryPolicyInfoAction/getPolicyInfoByPolicyNo")
	String getPolicyInfoByPolicyNo(Map<String,String> param);

	@PostMapping(value="/ocas/do/getDocumentByPolicyNo")
	String getDocumentByPolicyNo(Map<String,String> param);

	@PostMapping(value="/ocas/do/policyList/queryPolicyPremiumInfo")
	String getPremiumByPolicyNo(Map<String,String> param);

	@PostMapping(value="/ocas/do/app/edrApplyInfo/getEdrApplyList")
	String getEdrApplyList(Map<String,Object> param);

	/**
	 * sceneCodeToChinese.put("00017", "注销");
	 * sceneCodeToChinese.put("00006", "退保");
	 * SCENE20001("20001", "变更投保人"),
	 * SCENE20002("20002", "修改被保险人关键信息和受益人"),
	 * SCENE20003("20003", "变更被保险人"),
	 * SCENE20004("20004", "原方案加人"),
	 * SCENE20005("20005", "新增方案加人"),
	 * SCENE20006("20006", "减少被保险人"),
	 * SCENE20007("20007", "修改保险方案"),
	 * SCENE20008("20008", "修改保险止期"),
	 * SCENE20021("20021", "修改客户信息"),
	 * SCENE20022("20022", "续期抽档"),
	 * SCENE20023("20023", "保单中止"),
	 * SCENE20024("20024", "保单复效"),
	 * SCENE20025("20025", "标的变更"),
	 * SCENE20026("20026", "保单冻结"),
	 * SCENE20027("20027", "再保"),
	 * SCENE20028("20028", "定期结算"),
	 * SCENE20029("20029", "保单基础信息变更"),
	 * @param param
	 * @return
	 */
	@PostMapping(value="/ocas/do/app/commonquery/queryEdrApplyHistoryList")
	String queryEdrApplyHistoryList(Map<String,Object> param);

	@PostMapping(value="/ocas/do/channel/queryEPolicy")
	String queryEPolicy(Map<String,Object> param);

	/**
	 * 查询客户类型(CRM)
	 */
	@PostMapping("/ocas/do/clientManage/searchCustomerRating")
	String searchCustomerRating(@RequestBody RatingQueryVO queryVO);

	/**
	 * 判断是否宽限期
	 * @param vo
	 * @return String
	 */
	@PostMapping(value="/ocas/do/app/contract/isGarcePerid")
	String isGarcePerid(@RequestBody CopyPolicyQueryVO vo);

	/**
	 * 根据客户号查询明细健康险个单
	 * @return String
	 */
	@PostMapping(value="/ocas/do/app/policyInfo/queryByClientNo")
	String queryByClientNo(Map<String,String> param);

	/**
	 * 根据客户号查询明细健康险个单
	 * @return String
	 */
	@PostMapping(value="/ocas/do/app/edrApplySurrender/calculateAgree")
	String calculateAgree(Map<String,String> param);

	/**
	 * 解约接口
	 * @return String
	 */
	@PostMapping(value="/ocas/do/app/edrApplySurrender/applyEndorse")
	String applyEndorse(SurrendApplyReq surrendApplyReq);

	/**
	 * 理赔二核结论变更
	 * @param param
	 * @return
	 */
	@PostMapping(value="/ocas/do/app/updateUnderwritingConclusion")
	String updateUnderwritingConclusion(UwSendPosConclusionDTO param);

	/**
	 * 查询电子保单电子发票
	 * @param param
	 * @return
	 */
	@PostMapping(value="/ocas/do/channel/queryEPolicyClaim")
	String queryEPolicyClaim(Map<String, Object> param);

	/**
	 * 自动实名化接口
	 * @param ocasRealNameDTO
	 * @return
	 */
	@PostMapping(value="/ocas/do/app/edrApplyInput/autoRealName")
	String autoRealName(OcasRealNameDTO ocasRealNameDTO);

}
