package com.paic.ncbs.claim.model.vo.batch;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

@ApiModel("联系人")
@Data
public class OnlineBatchLinkMan extends EntityDTO {

    @ApiModelProperty("报案人姓名")
    @NotBlank(message = "报案人姓名不能为空")
    private String applicantPerson;

    @ApiModelProperty("报案人类型")
//    @NotBlank(message = "报案人类型不能为空")
    private String applicantType;

    @ApiModelProperty("报案人证件类型")
//    @NotBlank(message = "报案人证件类型不能为空")
    private String certificateType;

    @ApiModelProperty("报案人证件号")
//    @NotBlank(message = "报案人证件号不能为空")
    private String certificateNo;

    @ApiModelProperty("联系人姓名")
//    @NotBlank(message = "联系人姓名不能为空")
    private String linkManName;

    @ApiModelProperty("与被保险人关系")
//    @NotBlank(message = "与被保险人关系不能为空")
    private String linkManRelation;

    @ApiModelProperty("联系人电话")
//    @NotBlank(message = "联系人电话不能为空")
    private String linkManTelephone;

    @ApiModelProperty("短信发送 Y：发送 N：不发送")
//    @NotBlank(message = "短信发送不能为空")
    private String sendMessage;

}