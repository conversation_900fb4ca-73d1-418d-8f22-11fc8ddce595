package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

public class ClmpReportLossExcEntity extends EntityDTO {

    private static final long serialVersionUID = -6170094207031012413L;

    private String idClmpReportLossExc;

    private String idClmpReportExc;

    private String reportNo;

    private String policyNo;

    private String lossType;

    private String lossItemLevel1;

    private String lossItemLevel2;

    private String lossItemLevel3;

    private String migrateFrom = "np";

    private String objectType;

    public String getIdClmpReportLossExc() {
        return idClmpReportLossExc;
    }

    public void setIdClmpReportLossExc(String idClmpReportLossExc) {
        this.idClmpReportLossExc = idClmpReportLossExc == null ? null : idClmpReportLossExc.trim();
    }

    public String getIdClmpReportExc() {
        return idClmpReportExc;
    }

    public void setIdClmpReportExc(String idClmpReportExc) {
        this.idClmpReportExc = idClmpReportExc == null ? null : idClmpReportExc.trim();
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo == null ? null : policyNo.trim();
    }

    public String getLossType() {
        return lossType;
    }

    public void setLossType(String lossType) {
        this.lossType = lossType == null ? null : lossType.trim();
    }

    public String getLossItemLevel1() {
        return lossItemLevel1;
    }

    public void setLossItemLevel1(String lossItemLevel1) {
        this.lossItemLevel1 = lossItemLevel1 == null ? null : lossItemLevel1.trim();
    }

    public String getLossItemLevel2() {
        return lossItemLevel2;
    }

    public void setLossItemLevel2(String lossItemLevel2) {
        this.lossItemLevel2 = lossItemLevel2 == null ? null : lossItemLevel2.trim();
    }

    public String getLossItemLevel3() {
        return lossItemLevel3;
    }

    public void setLossItemLevel3(String lossItemLevel3) {
        this.lossItemLevel3 = lossItemLevel3 == null ? null : lossItemLevel3.trim();
    }

    public String getMigrateFrom() {
        return migrateFrom;
    }

    public void setMigrateFrom(String migrateFrom) {
        this.migrateFrom = migrateFrom == null ? null : migrateFrom.trim();
    }

    public String getObjectType() {
        return objectType;
    }

    public void setObjectType(String objectType) {
        this.objectType = objectType == null ? null : objectType.trim();
    }
}