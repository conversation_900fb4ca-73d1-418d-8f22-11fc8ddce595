package com.paic.ncbs.claim.feign;

import com.paic.ncbs.claim.feign.fallback.AmlFeignFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 *  global反洗钱
 * <AUTHOR>
 */
@FeignClient(name = "aml-main",url = "${ncbs.aml.url:http://109.101.105.81:31521}", fallbackFactory = AmlFeignFallback.class)
@Component
public interface AmlFeign {


	/**
	 * 上报反洗钱可疑数据
	 *
	 * @param paraXML
	 * @return
	 */
	@PostMapping(value = "/ChnWeb/gn/autoAntimoney.do?method=saveAutoAntimoneyKeyiTargetInfo", consumes = "application/xml;charset=UTF-8", produces = "application/xml;charset=UTF-8")
	String reportGlobalAmlTarget(@RequestParam("paraXML") String paraXML);

}
