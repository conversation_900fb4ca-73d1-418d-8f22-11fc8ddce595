package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.math.BigDecimal;
import java.util.Date;

public class ClmReportInfoEntity extends EntityDTO {

    private static final long serialVersionUID = -7336504346917780637L;

    private String idClmReportInfo;

    private String clientId;

    private String reportNo;

    private String reportMode;

    private Date reportDate;

    private String driverName;

    private String isCargoLoss;

    private String isCarLoss;

    private String isInjured;

    private String reportOnPort;

    private String isThirdAgentReport;

    private String isAgentCase;

    private String reporterName;

    private String reporterCallNo;

    private String reporterRegisterTel;

    private String reportRegisterUm;

    private String acceptDepartmentCode;

    private String reportOption;

    private Date cancelReportDate;

    private String remark;

    private String migrateFrom = "np";

    private String isWaitCall;

    private String damageClimate;

    private String disposeStyle;

    private String occurrenceArea;

    private String reason;

    private String flagSelfService;

    private String repaireFactoryId;

    private String repairFactoryName;

    private String bjAppCaseNumber;

    private BigDecimal reportedLossAmount;

    private String isAcceptDirectPay;

    private String isCommunitySurvey;

    private String driveSex;

    private String driveCardId;

    private String reportType;

    public String getIdClmReportInfo() {
        return idClmReportInfo;
    }

    public void setIdClmReportInfo(String idClmReportInfo) {
        this.idClmReportInfo = idClmReportInfo == null ? null : idClmReportInfo.trim();
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String getReportMode() {
        return reportMode;
    }

    public void setReportMode(String reportMode) {
        this.reportMode = reportMode == null ? null : reportMode.trim();
    }

    public Date getReportDate() {
        return reportDate;
    }

    public void setReportDate(Date reportDate) {
        this.reportDate = reportDate;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName == null ? null : driverName.trim();
    }

    public String getIsCargoLoss() {
        return isCargoLoss;
    }

    public void setIsCargoLoss(String isCargoLoss) {
        this.isCargoLoss = isCargoLoss == null ? null : isCargoLoss.trim();
    }

    public String getIsCarLoss() {
        return isCarLoss;
    }

    public void setIsCarLoss(String isCarLoss) {
        this.isCarLoss = isCarLoss == null ? null : isCarLoss.trim();
    }

    public String getIsInjured() {
        return isInjured;
    }

    public void setIsInjured(String isInjured) {
        this.isInjured = isInjured == null ? null : isInjured.trim();
    }

    public String getReportOnPort() {
        return reportOnPort;
    }

    public void setReportOnPort(String reportOnPort) {
        this.reportOnPort = reportOnPort == null ? null : reportOnPort.trim();
    }

    public String getIsThirdAgentReport() {
        return isThirdAgentReport;
    }

    public void setIsThirdAgentReport(String isThirdAgentReport) {
        this.isThirdAgentReport = isThirdAgentReport == null ? null : isThirdAgentReport.trim();
    }

    public String getIsAgentCase() {
        return isAgentCase;
    }

    public void setIsAgentCase(String isAgentCase) {
        this.isAgentCase = isAgentCase == null ? null : isAgentCase.trim();
    }

    public String getReporterName() {
        return reporterName;
    }

    public void setReporterName(String reporterName) {
        this.reporterName = reporterName == null ? null : reporterName.trim();
    }

    public String getReporterCallNo() {
        return reporterCallNo;
    }

    public void setReporterCallNo(String reporterCallNo) {
        this.reporterCallNo = reporterCallNo == null ? null : reporterCallNo.trim();
    }

    public String getReporterRegisterTel() {
        return reporterRegisterTel;
    }

    public void setReporterRegisterTel(String reporterRegisterTel) {
        this.reporterRegisterTel = reporterRegisterTel == null ? null : reporterRegisterTel.trim();
    }

    public String getReportRegisterUm() {
        return reportRegisterUm;
    }

    public void setReportRegisterUm(String reportRegisterUm) {
        this.reportRegisterUm = reportRegisterUm == null ? null : reportRegisterUm.trim();
    }

    public String getAcceptDepartmentCode() {
        return acceptDepartmentCode;
    }

    public void setAcceptDepartmentCode(String acceptDepartmentCode) {
        this.acceptDepartmentCode = acceptDepartmentCode == null ? null : acceptDepartmentCode.trim();
    }

    public String getReportOption() {
        return reportOption;
    }

    public void setReportOption(String reportOption) {
        this.reportOption = reportOption == null ? null : reportOption.trim();
    }

    public Date getCancelReportDate() {
        return cancelReportDate;
    }

    public void setCancelReportDate(Date cancelReportDate) {
        this.cancelReportDate = cancelReportDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getMigrateFrom() {
        return migrateFrom;
    }

    public void setMigrateFrom(String migrateFrom) {
        this.migrateFrom = migrateFrom == null ? null : migrateFrom.trim();
    }

    public String getIsWaitCall() {
        return isWaitCall;
    }

    public void setIsWaitCall(String isWaitCall) {
        this.isWaitCall = isWaitCall == null ? null : isWaitCall.trim();
    }

    public String getDamageClimate() {
        return damageClimate;
    }

    public void setDamageClimate(String damageClimate) {
        this.damageClimate = damageClimate == null ? null : damageClimate.trim();
    }

    public String getDisposeStyle() {
        return disposeStyle;
    }

    public void setDisposeStyle(String disposeStyle) {
        this.disposeStyle = disposeStyle == null ? null : disposeStyle.trim();
    }

    public String getOccurrenceArea() {
        return occurrenceArea;
    }

    public void setOccurrenceArea(String occurrenceArea) {
        this.occurrenceArea = occurrenceArea == null ? null : occurrenceArea.trim();
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason == null ? null : reason.trim();
    }

    public String getFlagSelfService() {
        return flagSelfService;
    }

    public void setFlagSelfService(String flagSelfService) {
        this.flagSelfService = flagSelfService == null ? null : flagSelfService.trim();
    }

    public String getRepaireFactoryId() {
        return repaireFactoryId;
    }

    public void setRepaireFactoryId(String repaireFactoryId) {
        this.repaireFactoryId = repaireFactoryId == null ? null : repaireFactoryId.trim();
    }

    public String getRepairFactoryName() {
        return repairFactoryName;
    }

    public void setRepairFactoryName(String repairFactoryName) {
        this.repairFactoryName = repairFactoryName == null ? null : repairFactoryName.trim();
    }

    public String getBjAppCaseNumber() {
        return bjAppCaseNumber;
    }

    public void setBjAppCaseNumber(String bjAppCaseNumber) {
        this.bjAppCaseNumber = bjAppCaseNumber == null ? null : bjAppCaseNumber.trim();
    }

    public BigDecimal getReportedLossAmount() {
        return reportedLossAmount;
    }

    public void setReportedLossAmount(BigDecimal reportedLossAmount) {
        this.reportedLossAmount = reportedLossAmount;
    }

    public String getIsAcceptDirectPay() {
        return isAcceptDirectPay;
    }

    public void setIsAcceptDirectPay(String isAcceptDirectPay) {
        this.isAcceptDirectPay = isAcceptDirectPay == null ? null : isAcceptDirectPay.trim();
    }

    public String getIsCommunitySurvey() {
        return isCommunitySurvey;
    }

    public void setIsCommunitySurvey(String isCommunitySurvey) {
        this.isCommunitySurvey = isCommunitySurvey == null ? null : isCommunitySurvey.trim();
    }

    public String getDriveSex() {
        return driveSex;
    }

    public void setDriveSex(String driveSex) {
        this.driveSex = driveSex == null ? null : driveSex.trim();
    }

    public String getDriveCardId() {
        return driveCardId;
    }

    public void setDriveCardId(String driveCardId) {
        this.driveCardId = driveCardId == null ? null : driveCardId.trim();
    }

    public String getReportType() {
        return reportType;
    }

    public void setReportType(String reportType) {
        this.reportType = reportType == null ? null : reportType.trim();
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }
}