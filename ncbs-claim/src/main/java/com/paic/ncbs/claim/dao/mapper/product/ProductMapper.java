package com.paic.ncbs.claim.dao.mapper.product;

import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@MapperScan
public interface ProductMapper {

	List<BigDecimal> selectTaxRateByProductClass(@Param("productClassCode") String productClassCode);

	Map<String, Object> getPlanTaxRateAndProductClass(@Param("planCode") String planCode);
}
