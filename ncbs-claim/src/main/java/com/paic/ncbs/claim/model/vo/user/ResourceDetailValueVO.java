package com.paic.ncbs.claim.model.vo.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("权限资源细值表VO")
public class ResourceDetailValueVO {

    @ApiModelProperty(value = "权限组id")
    private String userPrivGroupRelId;

    @ApiModelProperty(value = "权限组主键")
    private String privilegeGroupId;

    @ApiModelProperty(value = "权限资源细值表主键")
    private String resourceDetailValueId;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "权限组名称")
    private String privilegeGroupName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "排序")
    private int sortNo;

    @ApiModelProperty(value = "权限类别")
    private String privilegeType;

    @ApiModelProperty(value = "承保机构编码")
    private String departmentCode;

    @ApiModelProperty(value = "资源明细编码")
    private String resourceDetailCode;

    @ApiModelProperty(value = "资源明细类型(01:单值;02:取值范围)")
    private String resourceDetailType;

    @ApiModelProperty(value = "资源明细值")
    private String resourceDetailValue;

    @ApiModelProperty(value = "资源明细最小值(单位：万元)")
    private String resourceDetailMinValue;

    @ApiModelProperty(value = "资源明细最大值(单位：万元)")
    private String resourceDetailMaxValue;

}
