package com.paic.ncbs.claim.dao.mapper.dashboard;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.dao.entity.dashboard.JobDashBoard;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 作业看板指标查询
 */
public interface JobDashboardMapper extends BaseMapper<JobDashBoard> {

    /**
     * 接报案数
     *
     * @param userCode   用戶代碼
     * @param startTime  统计开始时间
     * @param endTime    统计截止时间
     * @return           数量
     */
    int reportInfo(@Param("userCode") String userCode, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 立案估损（件数+新估损金额）
     *
     * @param userCode   用戶代碼
     * @param startTime  统计开始时间
     * @param endTime    统计截止时间
     * @return           数量+金额
     */
    JobDashBoard claimInfo(@Param("userCode") String userCode, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);


    /**
     * 结案（结案案件数）
     *
     * @param userCode   用戶代碼
     * @param startTime  统计开始时间
     * @param endTime    统计截止时间
     * @return           数量
     */
    int endCaseNum(@Param("userCode") String userCode, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);


    /**
     * 结案（结案金额）
     *
     * @param userCode   用戶代碼
     * @param startTime  统计开始时间
     * @param endTime    统计截止时间
     * @return           金额
     */
    BigDecimal endCaseAmount(@Param("userCode") String userCode, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);


    /**
     * 估损调整（件数+调整金额）
     *
     * @param userCode   用戶代碼
     * @param startTime  统计开始时间
     * @param endTime    统计截止时间
     * @return           数量+金额
     */
    JobDashBoard estimateInfo(@Param("userCode") String userCode, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);


    /**
     * 零注拒件数
     *
     * @param userCode   用戶代碼
     * @param startTime  统计开始时间
     * @param endTime    统计截止时间
     * @return           数量
     */
    int zeroCancelRefuseNum(@Param("userCode") String userCode, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);


    /**
     * 差错件数
     *
     * @param userCode   用戶代碼
     * @param startTime  统计开始时间
     * @param endTime    统计截止时间
     * @return           数量
     */
    int mistakeNum(@Param("userCode") String userCode, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);


    /**
     * 重开（案件数+估损金额）
     *
     * @param userName   用戶名称
     * @param startTime  统计开始时间
     * @param endTime    统计截止时间
     * @return           数量+金额
     */
    JobDashBoard reopenCaseInfo(@Param("userName") String userName, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);


    /**
     * 核赔（案件数+核赔金额）
     *
     * @param userCode   用戶代碼
     * @param startTime  统计开始时间
     * @param endTime    统计截止时间
     * @return           数量+金额
     */
    JobDashBoard verifyInfo(@Param("userCode") String userCode, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);



}
