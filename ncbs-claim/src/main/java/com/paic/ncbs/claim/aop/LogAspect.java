package com.paic.ncbs.claim.aop;

import com.alibaba.fastjson.JSON;

import com.google.common.base.Stopwatch;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Aspect
@Component
public class LogAspect {

    @Pointcut("execution(* com.paic.ncbs.claim.controller..*.*(..)) && !execution(* com.paic.ncbs.claim.controller..*.initBinder(..))")
    public void log() {
        // aop
    }

    @Around(value = "log()")
    public Object logAround(ProceedingJoinPoint joinPoint) {
        String uri = "";
        if (Objects.nonNull(RequestContextHolder.getRequestAttributes())) {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            uri = request.getRequestURI();
        }

        // 获取执行方法的类的名称（包名加类名）
        String className = joinPoint.getTarget().getClass().getName();
        // 获取实例的方法
        String methodName = joinPoint.getSignature().getName();
        // 获取请求参数
        Object[] args = joinPoint.getArgs();
        List<Object> params = new ArrayList<>();
        for (Object arg : args) {
            if (arg instanceof ServletRequest || arg instanceof ServletResponse || arg instanceof MultipartFile) {
                continue;
            }
            params.add(arg);
        }
        try {
            log.info("{}.{}() start, uri={}, param ===》\t {}", className, methodName, uri, JSON.toJSONString(params));
        } catch (Exception e) {
            log.warn("LogAspect.logAround get param error, uri={}", uri);
        }

        Stopwatch stopwatch = Stopwatch.createStarted();
        Object proceed;
        try {
            proceed = joinPoint.proceed();
        } catch (GlobalBusinessException e) {
            proceed = ResponseResult.fail(e.getCode(), e.getMessage());
        } catch (Throwable e) {
            log.error("{}.{}() error, uri={}, msg={}", className, methodName, uri, e.getMessage(), e);
            proceed = ResponseResult.fail(GlobalResultStatus.FAIL);
        }

        try {
            log.info("{}.{}() end, uri={}, usetime={}, return ===》\t {}", className, methodName, uri, stopwatch.elapsed(TimeUnit.MILLISECONDS), JSON.toJSONString(proceed));
        } catch (Exception e) {
            log.warn("LogAspect.logAround get return error, uri={}", uri);
        }
        return proceed;
    }
}
