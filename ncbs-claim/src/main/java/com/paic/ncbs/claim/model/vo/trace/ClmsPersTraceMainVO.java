package com.paic.ncbs.claim.model.vo.trace;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceMainDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * 通过ins-framework-mybatis工具自动生成，请勿手工修改。表clms_pers_trace_main的PO对象<br/>
 * 对应表名：clms_pers_trace_main,备注：人伤跟踪主表
 *
 */
@Data
@ApiModel("人伤信息表")
public class ClmsPersTraceMainVO   implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：id,备注：主键 */
	@TableField(value="id")
	private Integer id;
	/** 对应字段：report_no,备注：报案号 */
	@TableField(value="report_no")
	private String reportNo;
	/** 对应字段：case_times,备注：赔付次数 */
	@TableField(value="case_times")
	private Integer caseTimes;
	/** 对应字段：task_id,备注：任务id */
	@TableField(value="task_id")
	private String taskId;
	/** 对应字段：policy_no,备注：保单号 */
	@TableField(value="policy_no")
	private String policyNo;
	/** 对应字段：class_code,备注：险类代码 */
	@TableField(value="class_code")
	private String classCode;
	/** 对应字段：risk_code,备注：险种代码 */
	@TableField(value="risk_code")
	private String riskCode;
	/** 对应字段：insured_name,备注：被保险人 */
	@TableField(value="insured_name")
	private String insuredName;
	/** 对应字段：trace_person_code,备注：跟踪人员代码 */
	@TableField(value="trace_person_code")
	private String tracePersonCode;
	/** 对应字段：trace_person,备注：跟踪人员 */
	@TableField(value="trace_person")
	private String tracePerson;
	/** 对应字段：nearly_trace_time,备注：最近跟踪时间 */
	@TableField(value="nearly_trace_time")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date nearlyTraceTime;
	/** 对应字段：underwrite_date,备注：人伤核损时间(跟踪提交成功时间) */
	@TableField(value="underwrite_date")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date underwriteDate;
	/** 对应字段：underwrite_flag,备注：核损标志：未核损-0 待核损-3 已核损-1 核损退回-2 注销-9 高级审核退回-4 */
	@TableField(value="underwrite_flag")
	private String underwriteFlag;
	/** 对应字段：underwrite_code,备注：核损人代码 */
	@TableField(value="underwrite_code")
	private String underwriteCode;
	/** 对应字段：underwrite_name,备注：核损人名称 */
	@TableField(value="underwrite_name")
	private String underwriteName;
	/** 对应字段：underwrite_end_date,备注：核损通过日期 */
	@TableField(value="underwrite_end_date")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date underwriteEndDate;
	/** 对应字段：valid_flag,备注：有效标志 */
	@TableField(value="valid_flag")
	private String validFlag;
	/** 对应字段：is_survey,备注：是否发起调查 */
	@TableField(value="is_survey")
	private String isSurvey;
	/** 对应字段：survey_info,备注：调查信息 */
	@TableField(value="survey_info")
	private String surveyInfo;
	/** 对应字段：launch_survey,备注：发起调查 */
	@TableField(value="launch_survey")
	private String launchSurvey;
	/** 对应字段：first_submit_time,备注：初次提交时间 */
	@TableField(value="first_submit_time")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date firstSubmitTime;
	/** 对应字段：last_trace_date,备注：最近一次跟踪日期 */
	@TableField(value="last_trace_date")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date lastTraceDate;
	/** 对应字段：trace_status,备注：跟踪状态：0-初始状态、1-跟踪完成、2-注销跟踪、3-正在跟踪、4-待下次跟踪 */
	@TableField(value="trace_status")
	private String traceStatus;
	/** 对应字段：isno_endtrace,备注：是否需要继续跟踪 */
	@TableField(value="isno_endtrace")
	private String isnoEndtrace;
	/** 对应字段：trace_advise,备注：建议 */
	@TableField(value="trace_advise")
	private String traceAdvise;
	/** 对应字段：interval_day,备注：间隔天数 */
	@TableField(value="interval_day")
	private String intervalDay;
	/** 对应字段：remark,备注：备注 */
	@TableField(value="remark")
	private String remark;
	/** 对应字段：created_by,备注：创建人 */
	@TableField(value="created_by")
	private String createdBy;
	/** 对应字段：sys_ctime,备注：创建时间/任务开始时间 */
	@TableField(value="sys_ctime")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date sysCtime;
	/** 对应字段：sys_endctime,备注：任务结束时间 */
	@TableField(value="sys_endctime")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date sysEndctime;
	/** 对应字段：updated_by,备注：最新修改人员 */
	@TableField(value="updated_by")
	private String updatedBy;
	/** 对应字段：sys_utime,备注：最新修改时间 */
	@TableField(value="sys_utime")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date sysUtime;
	//处理人
	private String assigneeName;
	//方案名称
	private String riskGroupName;
	//保单id
	private String policyId;
}
