package com.paic.ncbs.claim.dao.mapper.checkloss;

import com.paic.ncbs.claim.model.dto.checkloss.PropertyLossDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface PropertyLossMapper {

    public void addPropertyLoss(@Param("propertyLossList") List<PropertyLossDTO> propertyLossList);

    public void removePropertyLoss(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("taskCode") String taskCode, @Param("channelProcessId") String channelProcessId);

    void updateEffective(PropertyLossDTO propertyLossDTO);

    public List<PropertyLossDTO> getPropertyLoss(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("status") String status, @Param("taskCode") String taskCode, @Param("channelProcessId") String channelProcessId);

    public List<PropertyLossDTO> getPropertyLossList(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskCode") String taskCode);

    public void addPropertyLossList(@Param("propertyLossList") List<PropertyLossDTO> propertyLossList,
                                    @Param("caseTimes") Integer caseTimes, @Param("userId") String userId, @Param("channelProcessId") String channelProcessId);

    public void updatePropertyLoss(PropertyLossDTO PropertyLossDTO);

    public void insertPropertyLoss(PropertyLossDTO PropertyLossDTO);

    public List<PropertyLossDTO> getPropertyLossByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("status") String status, @Param("taskCode") String taskCode);
}
