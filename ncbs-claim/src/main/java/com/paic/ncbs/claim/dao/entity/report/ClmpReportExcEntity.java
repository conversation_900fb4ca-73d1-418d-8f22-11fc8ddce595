package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.math.BigDecimal;
import java.util.Date;

public class ClmpReportExcEntity extends EntityDTO {

    private static final long serialVersionUID = 1213916535437398578L;

    private String idClmpReportExc;

    private String reportNo;

    private Date reportDate;

    private Date accidentDate;

    private String reporterName;

    private String reporterCallNo;

    private String receiverName;

    private String receiverTel;

    private String reportMode;

    private String isOverseasAccident;

    private String accidentPlace;

    private String accidentCauseLevel1;

    private String accidentCauseLevel2;

    private String isAlarm;

    private String isFlood;

    private String accidentDetail;

    private String lossDesc;

    private BigDecimal lossAmount;

    private String lossCurrencyCode;

    private String remark;

    private String accidentCauseLevel2Explain;

    private Date reportAcceptDate;

    private String reporterRegisterTel;

    public String getIdClmpReportExc() {
        return idClmpReportExc;
    }

    public void setIdClmpReportExc(String idClmpReportExc) {
        this.idClmpReportExc = idClmpReportExc == null ? null : idClmpReportExc.trim();
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public Date getReportDate() {
        return reportDate;
    }

    public void setReportDate(Date reportDate) {
        this.reportDate = reportDate;
    }

    public Date getAccidentDate() {
        return accidentDate;
    }

    public void setAccidentDate(Date accidentDate) {
        this.accidentDate = accidentDate;
    }

    public String getReporterName() {
        return reporterName;
    }

    public void setReporterName(String reporterName) {
        this.reporterName = reporterName == null ? null : reporterName.trim();
    }

    public String getReporterCallNo() {
        return reporterCallNo;
    }

    public void setReporterCallNo(String reporterCallNo) {
        this.reporterCallNo = reporterCallNo == null ? null : reporterCallNo.trim();
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName == null ? null : receiverName.trim();
    }

    public String getReceiverTel() {
        return receiverTel;
    }

    public void setReceiverTel(String receiverTel) {
        this.receiverTel = receiverTel == null ? null : receiverTel.trim();
    }

    public String getReportMode() {
        return reportMode;
    }

    public void setReportMode(String reportMode) {
        this.reportMode = reportMode == null ? null : reportMode.trim();
    }

    public String getIsOverseasAccident() {
        return isOverseasAccident;
    }

    public void setIsOverseasAccident(String isOverseasAccident) {
        this.isOverseasAccident = isOverseasAccident == null ? null : isOverseasAccident.trim();
    }

    public String getAccidentPlace() {
        return accidentPlace;
    }

    public void setAccidentPlace(String accidentPlace) {
        this.accidentPlace = accidentPlace == null ? null : accidentPlace.trim();
    }

    public String getAccidentCauseLevel1() {
        return accidentCauseLevel1;
    }

    public void setAccidentCauseLevel1(String accidentCauseLevel1) {
        this.accidentCauseLevel1 = accidentCauseLevel1 == null ? null : accidentCauseLevel1.trim();
    }

    public String getAccidentCauseLevel2() {
        return accidentCauseLevel2;
    }

    public void setAccidentCauseLevel2(String accidentCauseLevel2) {
        this.accidentCauseLevel2 = accidentCauseLevel2 == null ? null : accidentCauseLevel2.trim();
    }

    public String getIsAlarm() {
        return isAlarm;
    }

    public void setIsAlarm(String isAlarm) {
        this.isAlarm = isAlarm == null ? null : isAlarm.trim();
    }

    public String getIsFlood() {
        return isFlood;
    }

    public void setIsFlood(String isFlood) {
        this.isFlood = isFlood == null ? null : isFlood.trim();
    }

    public String getAccidentDetail() {
        return accidentDetail;
    }

    public void setAccidentDetail(String accidentDetail) {
        this.accidentDetail = accidentDetail == null ? null : accidentDetail.trim();
    }

    public String getLossDesc() {
        return lossDesc;
    }

    public void setLossDesc(String lossDesc) {
        this.lossDesc = lossDesc == null ? null : lossDesc.trim();
    }

    public BigDecimal getLossAmount() {
        return lossAmount;
    }

    public void setLossAmount(BigDecimal lossAmount) {
        this.lossAmount = lossAmount;
    }

    public String getLossCurrencyCode() {
        return lossCurrencyCode;
    }

    public void setLossCurrencyCode(String lossCurrencyCode) {
        this.lossCurrencyCode = lossCurrencyCode == null ? null : lossCurrencyCode.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getAccidentCauseLevel2Explain() {
        return accidentCauseLevel2Explain;
    }

    public void setAccidentCauseLevel2Explain(String accidentCauseLevel2Explain) {
        this.accidentCauseLevel2Explain = accidentCauseLevel2Explain == null ? null : accidentCauseLevel2Explain.trim();
    }

    public Date getReportAcceptDate() {
        return reportAcceptDate;
    }

    public void setReportAcceptDate(Date reportAcceptDate) {
        this.reportAcceptDate = reportAcceptDate;
    }

    public String getReporterRegisterTel() {
        return reporterRegisterTel;
    }

    public void setReporterRegisterTel(String reporterRegisterTel) {
        this.reporterRegisterTel = reporterRegisterTel == null ? null : reporterRegisterTel.trim();
    }
}