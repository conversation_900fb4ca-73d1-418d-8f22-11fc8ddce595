package com.paic.ncbs.claim.dao.mapper.config;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.config.PrivilegeGroupDTO;
import com.paic.ncbs.claim.model.dto.user.UserPrivGroupRelDTO;
import com.paic.ncbs.claim.model.vo.user.CasePrivilegeGroupVO;
import com.paic.ncbs.claim.model.vo.user.ResourceDetailValueVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface PrivilegeGroupMapper extends BaseDao<PrivilegeGroupDTO> {
//
//
//	public int addPrivilegeGroup(PrivilegeGroupDTO privilegeGroup);
//
//
//	public int addPrivilegeGroupList(@Param("paramList") List<PrivilegeGroupDTO> privilegeGroupList);
//
//
//	public int modifyPrivilegeGroup(PrivilegeGroupDTO privilegeGroup);
//
//
//	public int removePrivilegeGroupById(String idAhcsPrivilegeGroup);
//
//
//	public PrivilegeGroupDTO getPrivilegeGroupById(String idAhcsPrivilegeGroup);
//
//
//	public List<String> getPrivilegeGroupsList(@Param("list") List<String> strList, @Param("resourceCode") String resourceCode);
//
//
//	public List<PrivilegeGroupDTO> getPrivilegeGroupDTOList(@Param("userId") String userId);
//
//
//	public List<PrivilegeGroupDTO> getPrivilegeGroupByUid(@Param("userPrivRel") UserPrivGroupRelDTO userPrivRel, @Param("deptCode") String deptCode, @Param("resourceDetailCode") String resourceDetailCode);
//
//
//	public int removePrivilegeGroupByUserId(@Param("userId") String userId, @Param("caseTypeList")List<String> caseTypeList, @Param("resourceCode") String resourceCode);
//
//
//	public int addUserPrivilegeGroupList(@Param("paramList") List<UserPrivGroupRelDTO> userPrivilegeGroupList);
//
//
//	List<PrivilegeGroupDTO> getByResCodeAndDepCode(@Param("departmentCode") String departmentCode, @Param("resourceCode") String resourceCode);
//
//	public void deleteUserPrivilegeRelationList(@Param("deleteRDValueIdList") List<String> deleteRDValueIdList, @Param("deleteUserIdList") List<String> deleteUserIdList);
//
//
//	public Integer getIsPrivilege(@Param("caseType") String caseType, @Param("userId") String userId);

    public List<ResourceDetailValueVO> getResourceDetailValueVOListByDepartCode(CasePrivilegeGroupVO casePrivilegeGroupVO);

//	public List<String> isAssigneeOfPrivilegeParam(CasePrivilegeGroupVO casePrivilegeGroupVO);
//
//
//	public List<Map<String, String>> permissionDetailsList(@Param("userId") String userId);
//
//
//	public List<PrivilegeGroupDTO> getMaxPrivilegeGroupByUid(@Param("userPrivRel") UserPrivGroupRelDTO userPrivRel, @Param("deptCode") String deptCode, @Param("resourceDetailCode") String resourceDetailCode);
//
//
//	public List<CasePrivilegeGroupVO> getAssigneeDiffForCase(CasePrivilegeGroupVO casePrivilegeGroupVO);
//
//	public List<ResourceDetailValueVO>  getMaxPrivilegeGroupByUidCase(CasePrivilegeGroupVO casePrivilegeGroupVO);
//
//
//	public List<String> getUserPrivilegeGroupForClear();
//
//
//	public void removeBatchPrivilegeGroup(@Param("userIdList") List<String> userIdList);
//
//
//	public List<Map<String,String>> getAllPrivilegeGroupByUid(@Param("userId") String userId,@Param("caseTypeList") List<String> caseTypeList,@Param("resourceCode") String resourceCode);
//
//
//	public Integer getMaxPrivilegeLevel(@Param("userId") String userId,@Param("resourceType") String resourceType);
//
//
//	public Integer getMinPrivilegeLevel(@Param("userId") String userId,@Param("resourceType") String resourceType);
//
//
//	public List<Integer> getPrivilegeLevelByUserId(@Param("userId") String userId,@Param("resourceType") String resourceType
//	,@Param("resourceCode") String resourceCode);
//
//
//	public Integer checkDeptHasCasePrivilege(CasePrivilegeGroupVO casePrivilegeGroupVO);
//
//
//	public Integer checkDeptHasPrivilege(CasePrivilegeGroupVO casePrivilegeGroupVO);



    public List<PrivilegeGroupDTO> getPrivilegeGroupByUid(@Param("userPrivRel") UserPrivGroupRelDTO userPrivRel, @Param("deptCode") String deptCode, @Param("resourceDetailCode") String resourceDetailCode);


}