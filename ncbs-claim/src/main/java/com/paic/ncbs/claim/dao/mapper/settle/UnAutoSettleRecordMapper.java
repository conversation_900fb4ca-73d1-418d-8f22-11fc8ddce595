package com.paic.ncbs.claim.dao.mapper.settle;

import com.paic.ncbs.claim.model.dto.settle.UnAutoSettleRecordDTO;
import com.paic.ncbs.claim.model.vo.settle.SettleSpreadSheetResultVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface UnAutoSettleRecordMapper {

    void saveUnAutoSettleRecord(UnAutoSettleRecordDTO unAutoSettleRecordDTO);

    void batchSaveUnAutoSettleRecord(@Param("unAutoSettleRecordList") List<UnAutoSettleRecordDTO> unAutoSettleRecordList);

    void deleteUnautoSettleRecord(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    List<SettleSpreadSheetResultVO> getUnautoSettleRecord(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

}
