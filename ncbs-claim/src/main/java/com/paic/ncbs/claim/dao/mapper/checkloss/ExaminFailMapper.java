package com.paic.ncbs.claim.dao.mapper.checkloss;

import com.paic.ncbs.claim.model.vo.settle.SettleExaminFailVO;
import com.paic.ncbs.claim.model.dto.checkloss.ExaminFailConditonDTO;
import com.paic.ncbs.claim.model.dto.checkloss.ExaminFailDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface ExaminFailMapper {

    public void addExaminFail(ExaminFailDTO examinFailDTO);

    public void removeExaminFail(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("taskCode") String taskCode, @Param("channelProcessId") String channelProcessId);

    public ExaminFailDTO getExaminFail(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("status") String status, @Param("taskCode") String taskCode, @Param("channelProcessId") String channelProcessId);

    public SettleExaminFailVO getSettleExaminFail(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("status") String status, @Param("taskCode") String taskCode, @Param("idAhcsChannelProcess") String idAhcsChannelProcess);

    public List<ExaminFailDTO> getExaminFailList(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskCode") String taskCode);

    public void addExaminFailList(@Param("examinFailList") List<ExaminFailDTO> examinFailList,
                                  @Param("caseTimes") Integer caseTimes, @Param("userId") String userId, @Param("channelProcessId") String channelProcessId);

    public void updateExaminFail(ExaminFailDTO examinFailDTO);

    public Integer countByExaminFailConditon(ExaminFailConditonDTO examinFailConditonDTO);

    public Integer countByExaminFailSubjectConditon(ExaminFailConditonDTO examinFailConditonDTO);

}
