package com.paic.ncbs.claim.model.vo.duty;


import com.paic.ncbs.claim.model.dto.duty.SurveyDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("查勘案件类型VO")
public class SurveyCaseClassVO {

    @ApiModelProperty("任务实例ID")
    private String taskId;

    @ApiModelProperty("案件类型列表")
    private List<String> caseClassList;

    @ApiModelProperty("意键险查勘dto")
    private SurveyDTO surveyDTO;

	@ApiModelProperty(value = "报案类型")
    private String reportType;


    public String getReportType() {
        return reportType;
    }

    public void setReportType(String reportType) {
        this.reportType = reportType;
    }

    public SurveyCaseClassVO() {
        super();
    }

    public SurveyCaseClassVO(String taskId, List<String> caseClassList, SurveyDTO surveyDTO) {
        super();
        this.taskId = taskId;
        this.caseClassList = caseClassList;
        this.surveyDTO = surveyDTO;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public List<String> getCaseClassList() {
        return caseClassList;
    }

    public void setCaseClassList(List<String> caseClassList) {
        this.caseClassList = caseClassList;
    }

    public SurveyDTO getSurveyDTO() {
        return surveyDTO;
    }

    public void setSurveyDTO(SurveyDTO surveyDTO) {
        this.surveyDTO = surveyDTO;
    }


}
