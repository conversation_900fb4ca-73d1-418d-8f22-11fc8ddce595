package com.paic.ncbs.claim.dao.mapper.duty;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.model.dto.duty.ClmsItemLoss;

import java.util.List;

/**
 * <p>
 * 损失标的表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
public interface ClmsItemLossMapper extends BaseMapper<ClmsItemLoss> {

    /**
     * 获取损失标的信息
     * @param reportNo
     * @param caseTimes
     * @return
     */
    List<ClmsItemLoss> getClmsItemLossService(String reportNo,Integer caseTimes);

    /**
     * 保存损失标信息
     * @param clmsItemLoss
     */
    void saveClmsItemLoss(ClmsItemLoss clmsItemLoss);

    /**
     * 修改损失标信息
     * @param clmsItemLoss
     */
    void updateClmsItemLoss(ClmsItemLoss clmsItemLoss);

    /**
     * 删除损失标信息
     * @param id
     */
    void deleteById(String id);

    /**
     * 根据条件删除损失标信息
     * @param reportNo
     * @param caseTimes
     */
    void deleteByCondition(String reportNo,Integer caseTimes);

}
