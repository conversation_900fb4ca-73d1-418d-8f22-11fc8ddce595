package com.paic.ncbs.claim.model.vo.duty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@ApiModel("旅行变更发票信息表VO")
public class TravelAlertInvoiceVO {

    @ApiModelProperty("旅行变更发票信息表主键")
    private String idAhcsTravelAlertInvoice;

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("发票类型(0:机票,1:酒店)")
    private String invoiceType;

    @ApiModelProperty("发票号")
    private String invoiceNumber;

    @ApiModelProperty("发票金额")
    private BigDecimal invoiceAmount;
    public String getIdAhcsTravelAlertInvoice() {
        return idAhcsTravelAlertInvoice;
    }

    public void setIdAhcsTravelAlertInvoice(String idAhcsTravelAlertInvoice) {
        this.idAhcsTravelAlertInvoice = idAhcsTravelAlertInvoice;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public Integer getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Integer caseTimes) {
        this.caseTimes = caseTimes;
    }

    public String getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType;
    }

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    public BigDecimal getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(BigDecimal invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

}
