package com.paic.ncbs.claim.dao.mapper.prepay;

import com.paic.ncbs.claim.model.dto.prepayinfo.ClmsPolicyPrepayDutyDetailDTO;
import com.paic.ncbs.claim.model.vo.ahcs.PreDutyVO;
import com.paic.ncbs.claim.model.vo.ahcs.PrePolicyVO;
import com.paic.ncbs.claim.model.dto.prepayinfo.DutyPrepayInfoDTO;
import com.paic.ncbs.claim.model.dto.prepayinfo.PrePayInfoDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface PrePayMapper {

	/**
	 * @Description: 查询案件下未完成的预赔申请
	 * @param reportNo
	 * @param caseTimes
	 * @return
	 * 返回类型  int
	 */
	public Integer getNoFinishPrePay(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

	/**
	 *
	 * @Description: 保存预赔信息
	 * @param payInfoDTO
	 * 返回类型  void
	 */
	public void savePrePayInfo(PrePayInfoDTO payInfoDTO);

	/**
	 *
	 * @Description: 查询预赔明细
	 * @param prepayInfoDTO
	 * @return
	 * 返回类型  List<DutyPrepayInfoDTO>
	 */
	public List<DutyPrepayInfoDTO> getDutyPrepayInfoList(DutyPrepayInfoDTO prepayInfoDTO);

	/**
	 *
	 * @Description: 保存预赔详细信息
	 * @param dutyPrepayInfoDTOs
	 * 返回类型  void
	 */
	public void saveDutyPrepayInfo(@Param("dutyPrepayList")List<DutyPrepayInfoDTO> dutyPrepayInfoDTOs);

	/**
	 * 查询预赔次数。逻辑：已审批完成的预赔次数 + 1
	 * @param reportNo
	 * @param caseTimes
	 * @return
	 */
	public Integer getApprovedSubTimes(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes);

	public Integer getCurrentApprovedSubTimes(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes);


	List<PreDutyVO> getPrePayDutyList(@Param("reportNo") String reportNo);

	List<ClmsPolicyPrepayDutyDetailDTO> getPrePayDutyDetailList(@Param("reportNo") String reportNo);

	List<PrePayInfoDTO> getHistoryPrePayApprove(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

	PrePayInfoDTO getPrePayWaitApprove(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

	void updatePrePayApprove(PrePayInfoDTO prePayInfoDTO);

	Integer getPrePayHistory(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

	Integer getPrePayCount(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

	List<PreDutyVO> getPrePayApplyDutyList(@Param("reportNo") String reportNo,@Param("caseTimes") Integer caseTimes,@Param("subTimes") Integer subTimes);

	void deleteDutyPrepayInfoByReportNo(@Param("reportNo") String reportNo,@Param("caseTimes") Integer caseTimes,@Param("subTimes") Integer subTimes);

	String getPreBigType(@Param("prePayInfoId") String prePayInfoId);

	Integer getPrePayApplyCount(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

	List<PreDutyVO> getDutyPrepaySum(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

	List<PrePolicyVO> getFeePrepaySum(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

	/**
	 * 查询报案好流程状态
	 * @param reportNo
	 * @return
	 */
	String getProcessStatus(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

	/**
	 * 预赔审批节点查询预赔责任明细信息
	 * @param reportNo
	 * @return
	 */
	List<ClmsPolicyPrepayDutyDetailDTO> getApprovalPrePayDutyDetailList(@Param("reportNo")String reportNo,@Param("caseTimes") Integer caseTimes,@Param("subTimes") Integer subTimes);

	/**
	 * 预赔飘红记录
	 * @param reportNo
	 * @param caseTimes
	 * @return
	 */
	Integer getPrePayTotals(String reportNo, Integer caseTimes);

	/**
	 * 查询保单险种的责任已预赔的金额
	 * @param reportNo
	 * @return
	 */

	List<PreDutyVO> getPreEndDutyList(String reportNo);

	/**
	 * 理算节点查询预赔责任明细信息 当前报案号下的所有预赔申请金额信息
	 * @param reportNo
	 * @return
	 */
	List<ClmsPolicyPrepayDutyDetailDTO> getAllApprovalPrePayDutyDetailList(@Param("reportNo")String reportNo,@Param("caseTimes") Integer caseTimes);

	/**
	 * 责任是否共享保额
	 * @param reportNo
	 * @return
	 */
	List<PreDutyVO>  getDutyIsShareAmount(String reportNo);
}
