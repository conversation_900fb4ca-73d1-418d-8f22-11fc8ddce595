package com.paic.ncbs.claim.dao.mapper.investigate;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateScoreDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;


@MapperScan
public interface InvestigateScoreMapper extends BaseDao<InvestigateScoreDTO> {


	List<InvestigateScoreDTO> listInvestigateScore(@Param("idAhcsInvestigate") String idAhcsInvestigate);


	void saveInvestigateScore(InvestigateScoreDTO investigateScoreDTO);

	List<InvestigateScoreDTO> getAllScoreWithNoDepartment();
	void updateScoreDepartmentCode(@Param("idAhcsInvestigateScore") String idAhcsInvestigateScore, @Param("code") String code);

	InvestigateScoreDTO listScore(String idAhcsInvestigate);
}