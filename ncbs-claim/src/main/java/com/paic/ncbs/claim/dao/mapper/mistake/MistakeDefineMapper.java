package com.paic.ncbs.claim.dao.mapper.mistake;


import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.mistake.MistakeDefineDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface MistakeDefineMapper extends BaseDao<MistakeDefineDTO> {


	List<MistakeDefineDTO> getMistakeDefineList(@Param("mistakeType")String mistakeType);

    String getMistakeReasons(@Param("mistakeCodeList")List<String> mistakeCodeList);

    String getMistakeName(@Param("mistakeCode")String mistakeCode);

    List<MistakeDefineDTO> getDefinesByCodeList(@Param("mistakeCodeList")List<String> mistakeCodeList);
}
