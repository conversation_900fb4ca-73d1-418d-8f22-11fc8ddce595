package com.paic.ncbs.claim.dao.mapper.other;

import com.paic.ncbs.claim.model.dto.policy.ClaimRecordDTO;
import com.paic.ncbs.claim.model.dto.report.CustomerDTO;
import com.paic.ncbs.claim.model.dto.report.RecordDutyInfo;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface ClaimRecordMapper {

    List<CustomerDTO> getCustomerByPolicy(ClaimRecordDTO claimRecordDTO);

    List<CustomerDTO> getUnResolvedReportNo(ClaimRecordDTO claimRecordDTO);

    /**
      *
      * @Description 根据保单查询对应的责任是否有赔偿金额
      * <AUTHOR>
      * @Date 2023/8/25 13:58
      **/
    List<RecordDutyInfo> getRecordDutyByPolicy(ClaimRecordDTO claimRecordDTO);

    /**
     * 查询已赔标的
     * @param claimRecordDTO
     * @return
     */
    List<CustomerDTO> getRiskPropertyByPolicy(ClaimRecordDTO claimRecordDTO);
    
        
	/**
	 * 查询未决标的
	 * 
	 * @param claimRecordDTO
	 * @return
	 */
	List<CustomerDTO> getUnResolvedRiskPropertyByPolicy(ClaimRecordDTO claimRecordDTO);
}
