package com.paic.ncbs.claim.dao.mapper.estimate;

import com.paic.ncbs.claim.model.dto.estimate.EstimateChangeDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.List;

@MapperScan
public interface EstimateChangeMapper {

    List<EstimateChangeDTO> getAllEstimateChangeList(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes);

    List<EstimateChangeDTO> getLastEstimateChangeList(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes);

    void addEstimateChangeList(@Param("estimateChangeList") List<EstimateChangeDTO> estimateChangeList);

    void deleteEstimateChange(EstimateChangeDTO estimateChangeDTO);

    List<EstimateChangeDTO> getPolicyRegisterAmount(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes);

    BigDecimal getEstimateChangeAmount(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes);

    /**
     * 获取未决修正金额
     * @param reportNo
     * @param caseTimes
     * @param policyNo
     * @return
     */
    BigDecimal getChangeAmount(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes, @Param("policyNo")String policyNo);

    /**
     * 获取未决修正次数
     * @param reportNo
     * @param caseTimes
     * @param policyNo
     * @return
     */
    Integer selectCount(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes, @Param("policyNo")String policyNo);

    void updateEstimateDateByReportNo(@Param("reportNo") String reportNo);

    List<EstimateChangeDTO> getEstimateChangeListByIdFlagHistoryChange(@Param("reportNo")String reportNo, @Param("idFlagHistoryChange") String idFlagHistoryChange);

    /**
     * 获取未决修正申请次数
     */
    Integer getChangeApplyTimes(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes);

}