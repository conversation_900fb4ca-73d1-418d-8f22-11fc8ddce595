package com.paic.ncbs.claim.model.vo.duty;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel("死亡信息VO")
public class PersonDeathVO {

	@ApiModelProperty("死亡信息主键")
	private String personDeathId;

	@ApiModelProperty("报案号")
	private String reportNo;

	@ApiModelProperty("赔付次数")
	private int caseTimes;

	@ApiModelProperty("通道任务表主键")
	private String idAhcsChannelProcess;

	@ApiModelProperty("死亡证明出具单位(clm_common_parameter  COLLECTION_CODE=AHCS_DEATH_PROVER)")
	private String deathProver;

	@ApiModelProperty("死亡证明出具单位说明")
	private String deathProverDesc;

	@ApiModelProperty("死亡日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date deathDate;

	@ApiModelProperty("死亡原因")
	private String deathReason;

	@ApiModelProperty("死亡原因暂不明")
	private String deathReasonUnknown;

	@ApiModelProperty("死亡状态（取值clm_common_parameter  COLLECTION_CODE=AHCS_CLIENT_STATUS)")
	private String deathStatus;

	@ApiModelProperty("是否尸检")
	private String isAutopsy;

	@ApiModelProperty("")
	private String moduleCode;

	@ApiModelProperty("环节号")
	private String taskId;

	@ApiModelProperty("死亡证明 01：原件，02：复印件")
	private String deathPaperType;

	@ApiModelProperty("三方赔付")
	private String isThirdPay;

	@ApiModelProperty("金额RMB")
	private BigDecimal amount;

	@ApiModelProperty("是否鉴定")
	private String isAppraisal;

	@ApiModelProperty("标准省")
	private String provice;

	@ApiModelProperty("标准市")
	private String city;

	@ApiModelProperty("赔付比例")
	private BigDecimal payRate;

	@ApiModelProperty("是否异地鉴定")
	private String isnoPlaceAppraisal;

	@ApiModelProperty("多处伤残")
	private String disAbilities;

	@ApiModelProperty("鉴定机构证件类型")
	private String agencyIdType;

	@ApiModelProperty("鉴定机构证件号码")
	private String agencyIdNo;

	@ApiModelProperty("鉴定师资格证号码")
	private String agencyCertifyNo;

	private String idAhcsPersonDeath;
	@ApiModelProperty("死亡原因代码")
	private String deathReasonCode;

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}
	public String getPersonDeathId() {
		return personDeathId;
	}
	
	public void setPersonDeathId(String personDeathId) {
		this.personDeathId = personDeathId;
	}
	
	public String getReportNo() {
		return reportNo;
	}
	
	public void setReportNo(String reportNo) {
		this.reportNo = reportNo;
	}
	
	public int getCaseTimes() {
		return caseTimes;
	}
	
	public void setCaseTimes(int caseTimes) {
		this.caseTimes = caseTimes;
	}
	
	public String getIdAhcsChannelProcess() {
		return idAhcsChannelProcess;
	}

	public void setIdAhcsChannelProcess(String idAhcsChannelProcess) {
		this.idAhcsChannelProcess = idAhcsChannelProcess;
	}

	public String getDeathProver() {
		return deathProver;
	}
	
	public void setDeathProver(String deathProver) {
		this.deathProver = deathProver;
	}
	
	public String getDeathProverDesc() {
		return deathProverDesc;
	}
	
	public void setDeathProverDesc(String deathProverDesc) {
		this.deathProverDesc = deathProverDesc;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	public Date getDeathDate() {
		return deathDate;
	}
	
	public void setDeathDate(Date deathDate) {
		this.deathDate = deathDate;
	}
	
	public String getDeathReason() {
		return deathReason;
	}
	
	public void setDeathReason(String deathReason) {
		this.deathReason = deathReason;
	}
	
	public String getDeathReasonUnknown() {
		return deathReasonUnknown;
	}
	
	public void setDeathReasonUnknown(String deathReasonUnknown) {
		this.deathReasonUnknown = deathReasonUnknown;
	}
	
	public String getDeathStatus() {
		return deathStatus;
	}
	
	public void setDeathStatus(String deathStatus) {
		this.deathStatus = deathStatus;
	}
	
	public String getIsAutopsy() {
		return isAutopsy;
	}
	
	public void setIsAutopsy(String isAutopsy) {
		this.isAutopsy = isAutopsy;
	}

	public String getModuleCode() {
		return moduleCode;
	}

	public void setModuleCode(String moduleCode) {
		this.moduleCode = moduleCode;
	}

	public String getDeathPaperType() {
		return deathPaperType;
	}

	public void setDeathPaperType(String deathPaperType) {
		this.deathPaperType = deathPaperType;
	}

	public String getIsThirdPay() {
		return isThirdPay;
	}

	public void setIsThirdPay(String isThirdPay) {
		this.isThirdPay = isThirdPay;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public String getIsAppraisal() {
		return isAppraisal;
	}

	public void setIsAppraisal(String isAppraisal) {
		this.isAppraisal = isAppraisal;
	}

	public String getProvice() {
		return provice;
	}

	public void setProvice(String provice) {
		this.provice = provice;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public BigDecimal getPayRate() {
		return payRate;
	}

	public void setPayRate(BigDecimal payRate) {
		this.payRate = payRate;
	}

	public String getIsnoPlaceAppraisal() {
		return isnoPlaceAppraisal;
	}

	public void setIsnoPlaceAppraisal(String isnoPlaceAppraisal) {
		this.isnoPlaceAppraisal = isnoPlaceAppraisal;
	}

	public String getDisAbilities() {
		return disAbilities;
	}

	public void setDisAbilities(String disAbilities) {
		this.disAbilities = disAbilities;
	}

	public String getAgencyIdType() {
		return agencyIdType;
	}

	public void setAgencyIdType(String agencyIdType) {
		this.agencyIdType = agencyIdType;
	}

	public String getAgencyIdNo() {
		return agencyIdNo;
	}

	public void setAgencyIdNo(String agencyIdNo) {
		this.agencyIdNo = agencyIdNo;
	}

	public String getAgencyCertifyNo() {
		return agencyCertifyNo;
	}

	public void setAgencyCertifyNo(String agencyCertifyNo) {
		this.agencyCertifyNo = agencyCertifyNo;
	}

	public String getIdAhcsPersonDeath() {
		return idAhcsPersonDeath;
	}

	public void setIdAhcsPersonDeath(String idAhcsPersonDeath) {
		this.idAhcsPersonDeath = idAhcsPersonDeath;
	}

	public String getDeathReasonCode() {
		return deathReasonCode;
	}

	public void setDeathReasonCode(String deathReasonCode) {
		this.deathReasonCode = deathReasonCode;
	}
}
