package com.paic.ncbs.claim.dao.entity.clms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* clms_mng_dashboard_his实体类
* <AUTHOR>
* @since 2025-04-21
*/
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("clms_mng_dashboard_his")
    public class ClmsMngDashboardHis implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 唯一标识
    */
    @TableField(value = "id")
    private Long id;

    /**
    * 创建时间
    */
    @TableField(value = "sys_ctime")
    private LocalDateTime sysCtime;

    /**
    * 最新修改时间
    */
    @TableField(value = "sys_utime")
    private LocalDateTime sysUtime;

    /**
    * 考核指标日（取前一天的时间格式yyyyMMdd）
    */
    @TableField(value = "metrics_day")
    private String metricsDay;

    /**
    * 机构代码(业绩机构-各分公司分别一套数据)
    */
    @TableField(value = "company_code")
    private String companyCode;

    /**
    * 项目代码
    */
    @TableField(value = "project_code")
    private String projectCode;

    /**
    * 项目名称
    */
    @TableField(value = "project_name")
    private String projectName;

    /**
    * 险类代码
    */
    @TableField(value = "class_code")
    private String classCode;

    /**
    * 险类名称
    */
    @TableField(value = "class_name")
    private String className;

    /**
    * 统计口径(m-本月 y-本年)
    */
    @TableField(value = "statistic_caliber")
    private String statisticCaliber;

    /**
    * 出险报案周期-分子(单位：天)
    */
    @TableField(value = "report_peroid_numerator")
    private BigDecimal reportPeroidNumerator;

    /**
    * 出险报案周期-分母(单位：件)
    */
    @TableField(value = "report_peroid_denominator")
    private Integer reportPeroidDenominator;

    /**
    * 报案估损周期-分子(单位：天)
    */
    @TableField(value = "estimate_peroid_numerator")
    private BigDecimal estimatePeroidNumerator;

    /**
    * 报案估损周期-分母(单位：件)
    */
    @TableField(value = "estimate_peroid_denominator")
    private Integer estimatePeroidDenominator;

    /**
    * 报案结案周期-分子(单位：天)
    */
    @TableField(value = "endCase_peroid_numerator")
    private BigDecimal endcasePeroidNumerator;

    /**
    * 报案结案周期-分母(单位：件)
    */
    @TableField(value = "endCase_peroid_denominator")
    private Integer endcasePeroidDenominator;

    /**
    * 重开次数(单位：次)
    */
    @TableField(value = "reopen_times")
    private Integer reopenTimes;

    /**
    * 报案结案率-分子值(单位：件)
    */
    @TableField(value = "end_case_numerator")
    private Integer endCaseNumerator;

    /**
    * 报案结案率-分母值(单位：件)
    */
    @TableField(value = "end_case_denominator")
    private Integer endCaseDenominator;

    /**
    * 代数估损偏差金额(单位：元)
    */
    @TableField(value = "estimate_deviation_amount")
    private BigDecimal estimateDeviationAmount;

    /**
    * 代数估损偏差率-分子值(单位：元)
    */
    @TableField(value = "estimate_deviation_numerator")
    private BigDecimal estimateDeviationNumerator;

    /**
    * 代数估损偏差率-分母值(单位：元)
    */
    @TableField(value = "estimate_deviation_denominator")
    private BigDecimal estimateDeviationDenominator;

    /**
    * 新增损失(单位：元)
    */
    @TableField(value = "new_estimate")
    private BigDecimal newEstimate;

}