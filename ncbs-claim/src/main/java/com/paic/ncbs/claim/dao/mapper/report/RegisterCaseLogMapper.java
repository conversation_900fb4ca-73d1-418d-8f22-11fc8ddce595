package com.paic.ncbs.claim.dao.mapper.report;

import com.paic.ncbs.claim.model.dto.report.RegisterCaseLogDTO;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface RegisterCaseLogMapper {

    void addRegisterCaseLog(RegisterCaseLogDTO registerCaseLogDTO);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);
}
