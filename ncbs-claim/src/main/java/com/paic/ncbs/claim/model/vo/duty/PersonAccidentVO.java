package com.paic.ncbs.claim.model.vo.duty;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.paic.ncbs.claim.model.dto.taskdeal.ThirdCarDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel("意外信息VO")
@Accessors(chain = true)
public class PersonAccidentVO {

	@ApiModelProperty("意外信息主键")
	private String personAccidentId;

	@ApiModelProperty("报案号")
	private String reportNo;

	@ApiModelProperty("赔付次数")
	private int caseTimes;

	@ApiModelProperty("通道号(AHCS_CHANNEL_PROCESS表主键)")
	private String idAhcsChannelProcess;

	@ApiModelProperty("0-国内;1-国外")
	private String overseasOccur;

	@ApiModelProperty("出险省份")
	private String provinceCode;

	@ApiModelProperty("出险城市CODE")
	private String accidentCityCode;

	@ApiModelProperty("出险县")
	private String accidentCountyCode;

	@ApiModelProperty("出险地点")
	private String accidentPlace;

	//保持和报案一致
	@ApiModelProperty("0-国内;1-国外")
	private String whetherOutSideAccident;

	@ApiModelProperty("出险区域")
	private String accidentArea;

	@ApiModelProperty("出险省份")
	private String accidentProvince;

	@ApiModelProperty("出险城市CODE")
	private String accidentCity;

	@ApiModelProperty("出险县")
	private String accidentCounty;

	@ApiModelProperty("出险国家")
	private String accidentNation;

	//用于反显地址
	@ApiModelProperty("城市名称")
	private String accidentCityName;
	@ApiModelProperty("县名称")
	private String accidentCountyName;
	@ApiModelProperty("省名称")
	private String accidentProvinceName;
	@ApiModelProperty("区域")
	private String accidentAreaName;
	@ApiModelProperty("国家名称")
	private String accidentNationName;

	@ApiModelProperty("职业类别小类")
	private String subProfessionCode;

	@ApiModelProperty("费率比 0-1")
	private BigDecimal amountRate;

	@ApiModelProperty("")
	private String moduleCode;

	@ApiModelProperty("事故时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date accidentTime;

	@ApiModelProperty("事故经过")
	private String accidentDetail;

	@ApiModelProperty("环节号")
	private String taskId;

	@ApiModelProperty("重灾类型(取自数据常量：AHCS_HUGE_TYPE)")
	private String hugeAccidentType;

	@ApiModelProperty("是否大灾（Y-是 N-否）")
	private String isHugeAccident;

	@ApiModelProperty("重灾编码")
	private String hugeAccidentCode;

	@ApiModelProperty("重灾名称")
	private String hugeAccidentName;

	@ApiModelProperty("重灾信息表主键")
	private String idHugeAccidentInfo;

	@ApiModelProperty("高坠类型：0：无安全带，1：有安全带")
	private String highFailType;

	@ApiModelProperty("01:工伤，02：因公致伤")
	private List<String> workInjuryTypeList;

	@ApiModelProperty("")
	private List<String> showWorkInjuryTypeList;

	@ApiModelProperty("")
	private String showConstruction;

	@ApiModelProperty(" 建工险 Y：是，N：否（建工险用）")
	private String isConstruction;

	@ApiModelProperty("足额投保 Y：是，N：否（建工险用）")
	private String isFullAmount;

	@ApiModelProperty("赔付比例（建工险用）0-1")
	private BigDecimal constructionPayRate;

	@ApiModelProperty("承保区域内出险 Y：是，N：否（建工险用）")
	private String isPolicyAreaAccident;

	@ApiModelProperty("特种作业 Y：是，N：否（建工险用）")
	private String isSpecialWork;

	@ApiModelProperty("0:特种作业证不可免 1:特种作业证可免")
	private String specialWorkPaperNeed;

	@ApiModelProperty("三者车辆信息表DTO")
	private List<ThirdCarDTO> thirdCarList;


	@ApiModelProperty("致伤机制（取值clm_common_parameter  COLLECTION_CODE=AHCS_INJURY_MEC）")
	private String injuryMechanism;

	@ApiModelProperty("致伤机制说明")
	private String injuryMechanismDesc;

	@ApiModelProperty(" 出险交通工具（取值clm_common_parameter  COLLECTION_CODE=AHCS_TRAFFIC)")
	private String trafficTool;

	@ApiModelProperty("车辆类型(取值clm_common_parameter  COLLECTION_CODE=AHCS_CAR_TYPE)")
	private String carType;

	@ApiModelProperty("交通事故类型(取值clm_common_parameter  COLLECTION_CODE=AHCS_TRAFFIC_ACC)")
	private String trafficAccidentType;

	@ApiModelProperty("事故中被保险人身份(取值clm_common_parameter  COLLECTION_CODE=INSURED_IDENTITY1行人，2机动车驾驶员，3车上人员，4电动车驾驶员)")
	private String insuredIdentity;

	@ApiModelProperty("事故车辆性质(1营业,2非营业)")
	private String trafficAccidentNature;

	@ApiModelProperty("客户号")
	private String detailPlace;

	@ApiModelProperty("详细事故地点(取值clm_common_parameter  COLLECTION_CODE=AHCS_DETAIL_PLACE)")
	private String detailPlaceDesc;

	@ApiModelProperty("职业类别中类")
	private String professionCode;

	@ApiModelProperty("职业类别大类")
	private String professionBigdCode;


	public String getAccidentCityName() {
		return accidentCityName;
	}

	public void setAccidentCityName(String accidentCityName) {
		this.accidentCityName = accidentCityName;
	}

	public String getAccidentCountyName() {
		return accidentCountyName;
	}

	public void setAccidentCountyName(String accidentCountyName) {
		this.accidentCountyName = accidentCountyName;
	}

	public String getAccidentProvinceName() {
		return accidentProvinceName;
	}

	public void setAccidentProvinceName(String accidentProvinceName) {
		this.accidentProvinceName = accidentProvinceName;
	}

	public String getAccidentAreaName() {
		return accidentAreaName;
	}

	public void setAccidentAreaName(String accidentAreaName) {
		this.accidentAreaName = accidentAreaName;
	}

	public String getAccidentNationName() {
		return accidentNationName;
	}

	public void setAccidentNationName(String accidentNationName) {
		this.accidentNationName = accidentNationName;
	}

	public String getWhetherOutSideAccident() {
		return whetherOutSideAccident;
	}

	public void setWhetherOutSideAccident(String whetherOutSideAccident) {
		this.whetherOutSideAccident = whetherOutSideAccident;
	}

	public String getAccidentProvince() {
		return accidentProvince;
	}

	public void setAccidentProvince(String accidentProvince) {
		this.accidentProvince = accidentProvince;
	}

	public String getAccidentCity() {
		return accidentCity;
	}

	public void setAccidentCity(String accidentCity) {
		this.accidentCity = accidentCity;
	}

	public String getAccidentCounty() {
		return accidentCounty;
	}

	public void setAccidentCounty(String accidentCounty) {
		this.accidentCounty = accidentCounty;
	}

	public String getAccidentNation() {
		return accidentNation;
	}

	public void setAccidentNation(String accidentNation) {
		this.accidentNation = accidentNation;
	}

	public String getProfessionBigdCode() {
		return professionBigdCode;
	}

	public void setProfessionBigdCode(String professionBigdCode) {
		this.professionBigdCode = professionBigdCode;
	}

	public String getHugeAccidentType() {
		return hugeAccidentType;
	}

	public void setHugeAccidentType(String hugeAccidentType) {
		this.hugeAccidentType = hugeAccidentType;
	}

	public String getIsHugeAccident() {
		return isHugeAccident;
	}

	public void setIsHugeAccident(String isHugeAccident) {
		this.isHugeAccident = isHugeAccident;
	}

	public String getHugeAccidentCode() {
		return hugeAccidentCode;
	}

	public void setHugeAccidentCode(String hugeAccidentCode) {
		this.hugeAccidentCode = hugeAccidentCode;
	}

	public String getIdHugeAccidentInfo() {
		return idHugeAccidentInfo;
	}

	public String getHugeAccidentName() {
		return hugeAccidentName;
	}

	public void setHugeAccidentName(String hugeAccidentName) {
		this.hugeAccidentName = hugeAccidentName;
	}

	public void setIdHugeAccidentInfo(String idHugeAccidentInfo) {
		this.idHugeAccidentInfo = idHugeAccidentInfo;
	}

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}
	public String getPersonAccidentId() {
		return personAccidentId;
	}
	
	public void setPersonAccidentId(String personAccidentId) {
		this.personAccidentId = personAccidentId;
	}
	
	public String getReportNo() {
		return reportNo;
	}
	
	public void setReportNo(String reportNo) {
		this.reportNo = reportNo;
	}
	
	public int getCaseTimes() {
		return caseTimes;
	}
	
	public void setCaseTimes(int caseTimes) {
		this.caseTimes = caseTimes;
	}
	
	public String getIdAhcsChannelProcess() {
		return idAhcsChannelProcess;
	}

	public void setIdAhcsChannelProcess(String idAhcsChannelProcess) {
		this.idAhcsChannelProcess = idAhcsChannelProcess;
	}

	public String getOverseasOccur() {
		return overseasOccur;
	}
	
	public void setOverseasOccur(String overseasOccur) {
		this.overseasOccur = overseasOccur;
	}
	
	public String getAccidentArea() {
		return accidentArea;
	}
	
	public void setAccidentArea(String accidentArea) {
		this.accidentArea = accidentArea;
	}
	
	public String getProvinceCode() {
		return provinceCode;
	}
	
	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}
	
	public String getAccidentCityCode() {
		return accidentCityCode;
	}
	
	public void setAccidentCityCode(String accidentCityCode) {
		this.accidentCityCode = accidentCityCode;
	}
	
	public String getAccidentCountyCode() {
		return accidentCountyCode;
	}
	
	public void setAccidentCountyCode(String accidentCountyCode) {
		this.accidentCountyCode = accidentCountyCode;
	}
	
	public String getAccidentPlace() {
		return accidentPlace;
	}
	
	public void setAccidentPlace(String accidentPlace) {
		this.accidentPlace = accidentPlace;
	}
	
	public String getInjuryMechanism() {
		return injuryMechanism;
	}
	
	public void setInjuryMechanism(String injuryMechanism) {
		this.injuryMechanism = injuryMechanism;
	}
	
	public String getInjuryMechanismDesc() {
		return injuryMechanismDesc;
	}
	
	public void setInjuryMechanismDesc(String injuryMechanismDesc) {
		this.injuryMechanismDesc = injuryMechanismDesc;
	}
	
	public String getTrafficTool() {
		return trafficTool;
	}
	
	public void setTrafficTool(String trafficTool) {
		this.trafficTool = trafficTool;
	}
	
	public String getCarType() {
		return carType;
	}
	
	public void setCarType(String carType) {
		this.carType = carType;
	}
	
	public String getTrafficAccidentType() {
		return trafficAccidentType;
	}
	
	public void setTrafficAccidentType(String trafficAccidentType) {
		this.trafficAccidentType = trafficAccidentType;
	}
	
	public String getInsuredIdentity() {
		return insuredIdentity;
	}
	
	public void setInsuredIdentity(String insuredIdentity) {
		this.insuredIdentity = insuredIdentity;
	}
	
	public String getTrafficAccidentNature() {
		return trafficAccidentNature;
	}
	
	public void setTrafficAccidentNature(String trafficAccidentNature) {
		this.trafficAccidentNature = trafficAccidentNature;
	}
	
	public String getDetailPlace() {
		return detailPlace;
	}
	
	public void setDetailPlace(String detailPlace) {
		this.detailPlace = detailPlace;
	}
	
	public String getDetailPlaceDesc() {
		return detailPlaceDesc;
	}
	
	public void setDetailPlaceDesc(String detailPlaceDesc) {
		this.detailPlaceDesc = detailPlaceDesc;
	}
	
	public String getProfessionCode() {
		return professionCode;
	}
	
	public void setProfessionCode(String professionCode) {
		this.professionCode = professionCode;
	}
	
	public String getSubProfessionCode() {
		return subProfessionCode;
	}
	
	public void setSubProfessionCode(String subProfessionCode) {
		this.subProfessionCode = subProfessionCode;
	}

	public BigDecimal getAmountRate() {
		return amountRate;
	}

	public void setAmountRate(BigDecimal amountRate) {
		this.amountRate = amountRate;
	}

	public String getModuleCode() {
		return moduleCode;
	}

	public void setModuleCode(String moduleCode) {
		this.moduleCode = moduleCode;
	}

	public String getAccidentDetail() {
		return accidentDetail;
	}

	public void setAccidentDetail(String accidentDetail) {
		this.accidentDetail = accidentDetail;
	}

	public String getHighFailType() {
		return highFailType;
	}

	public void setHighFailType(String highFailType) {
		this.highFailType = highFailType;
	}

	public String getIsConstruction() {
		return isConstruction;
	}

	public void setIsConstruction(String isConstruction) {
		this.isConstruction = isConstruction;
	}

	public String getIsFullAmount() {
		return isFullAmount;
	}

	public void setIsFullAmount(String isFullAmount) {
		this.isFullAmount = isFullAmount;
	}

	public BigDecimal getConstructionPayRate() {
		return constructionPayRate;
	}

	public void setConstructionPayRate(BigDecimal constructionPayRate) {
		this.constructionPayRate = constructionPayRate;
	}

	public String getIsPolicyAreaAccident() {
		return isPolicyAreaAccident;
	}

	public void setIsPolicyAreaAccident(String isPolicyAreaAccident) {
		this.isPolicyAreaAccident = isPolicyAreaAccident;
	}

	public String getIsSpecialWork() {
		return isSpecialWork;
	}

	public void setIsSpecialWork(String isSpecialWork) {
		this.isSpecialWork = isSpecialWork;
	}

	public String getSpecialWorkPaperNeed() {
		return specialWorkPaperNeed;
	}

	public void setSpecialWorkPaperNeed(String specialWorkPaperNeed) {
		this.specialWorkPaperNeed = specialWorkPaperNeed;
	}

	public List<String> getWorkInjuryTypeList() {
		return workInjuryTypeList;
	}

	public void setWorkInjuryTypeList(List<String> workInjuryTypeList) {
		this.workInjuryTypeList = workInjuryTypeList;
	}

	public List<String> getShowWorkInjuryTypeList() {
		return showWorkInjuryTypeList;
	}

	public void setShowWorkInjuryTypeList(List<String> showWorkInjuryTypeList) {
		this.showWorkInjuryTypeList = showWorkInjuryTypeList;
	}

	public String getShowConstruction() {
		return showConstruction;
	}

	public void setShowConstruction(String showConstruction) {
		this.showConstruction = showConstruction;
	}

	public List<ThirdCarDTO> getThirdCarList() {
		return thirdCarList;
	}

	public void setThirdCarList(List<ThirdCarDTO> thirdCarList) {
		this.thirdCarList = thirdCarList;
	}

	public Date getAccidentTime() {
		return accidentTime;
	}

	public void setAccidentTime(Date accidentTime) {
		this.accidentTime = accidentTime;
	}

}
