package com.paic.ncbs.claim.dao.mapper.other;

import com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO;
import com.paic.ncbs.claim.model.dto.message.MailInfoDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface MailInfoMapper {

	void addMailInfo(MailInfoDTO mailInfoDTO);

	void updateMailInfo(MailInfoDTO mailInfoDTO);

	List<PolicyInfoDTO> getMailPolicy(@Param("reportNo") String reportNo);
}
