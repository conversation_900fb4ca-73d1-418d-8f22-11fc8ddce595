package com.paic.ncbs.claim.model.vo.trace;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 * 通过ins-framework-mybatis工具自动生成，请勿手工修改。表clms_pers_trace_exp的PO对象<br/>
 * 对应表名：clms_pers_trace_exp,备注：人伤跟踪损失表
 *
 */
@ApiModel("人伤跟踪损失信息")
@Data
public class ClmsPersTraceExpVO  implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：id,备注：主键 */
	@TableField(value="id")
	private Integer id;
	/** 对应字段：report_no,备注：报案号 */
	@TableField(value="report_no")
	private String reportNo;
	/** 对应字段：case_times,备注：赔付次数 */
	@TableField(value="case_times")
	private Integer caseTimes;
	/** 对应字段：injured_id,备注：伤亡人员表id */
	@TableField(value="injured_id")
	private Integer injuredId;
	/** 对应字段：kind_code,备注：险别代码 */
	@TableField(value="kind_code")
	private String kindCode;
	/** 对应字段：kind_name,备注：险别名称 */
	@TableField(value="kind_name")
	private String kindName;
	/** 对应字段：loss_item_no,备注：损失类别代码 */
	@TableField(value="loss_item_no")
	private String lossItemNo;
	/** 对应字段：loss_item_name,备注：损失类别名称 */
	@TableField(value="loss_item_name")
	private String lossItemName;
	/** 对应字段：family_no,备注：方案（序号） */
	@TableField(value="family_no")
	private String familyNo;
	/** 对应字段：item_no,备注：标的代码 */
	@TableField(value="item_no")
	private String itemNo;
	/** 对应字段：item_name,备注：标的名称 */
	@TableField(value="item_name")
	private String itemName;
	/** 对应字段：amount,备注：保额/责任限额 */
	@TableField(value="amount")
	private BigDecimal amount;
	/** 对应字段：currency,备注：理赔币别 默认cny */
	@TableField(value="currency")
	private String currency;
	/** 对应字段：exch_rate,备注：人民币兑换率 默认1 */
	@TableField(value="exch_rate")
	private BigDecimal exchRate;
	/** 对应字段：sum_def_loss,备注：定损金额 */
	@TableField(value="sum_def_loss")
	private BigDecimal sumDefLoss;
	/** 对应字段：claim_amount,备注：赔款立案金额 */
	@TableField(value="claim_amount")
	private BigDecimal claimAmount;
	/** 对应字段：currency1,备注：承保币 */
	@TableField(value="currency1")
	private String currency1;
	/** 对应字段：exch_rate1,备注：理赔币别兑换承保币别兑换率 */
	@TableField(value="exch_rate1")
	private BigDecimal exchRate1;
	/** 对应字段：currency2,备注：本位币， exchrate为本位币汇率 */
	@TableField(value="currency2")
	private String currency2;
	/** 对应字段：sum_def_loss_cny,备注：定损金额转人民币 */
	@TableField(value="sum_def_loss_cny")
	private BigDecimal sumDefLossCny;
	/** 对应字段：unit_amount,备注：单价 */
	@TableField(value="unit_amount")
	private BigDecimal unitAmount;
	/** 对应字段：quantity,备注：数量 */
	@TableField(value="quantity")
	private Integer quantity;
	/** 对应字段：disabled_grade,备注：不可能等级 */
	@TableField(value="disabled_grade")
	private String disabledGrade;
	/** 对应字段：disabled_pay_rate,备注：不可用赔付率 */
	@TableField(value="disabled_pay_rate")
	private BigDecimal disabledPayRate;
	/** 对应字段：estimate_loss,备注：估损金额 */
	@TableField(value="estimate_loss")
	private BigDecimal estimateLoss;
	/** 对应字段：valid_flag,备注：有效标志0-无效 1-有效 */
	@TableField(value="valid_flag")
	private String validFlag;
	/** 对应字段：remark,备注：备注 */
	@TableField(value="remark")
	private String remark;
	/** 对应字段：care_fee,备注：护理费 */
	@TableField(value="care_fee")
	private BigDecimal careFee;
	/** 对应字段：pay_pers_day,备注：支付天数 */
	@TableField(value="pay_pers_day")
	private BigDecimal payPersDay;
	/** 对应字段：in_hosiptal_day,备注：住院天数 */
	@TableField(value="in_hosiptal_day")
	private BigDecimal inHosiptalDay;
	/** 对应字段：outpay_day,备注：超额支付天数 */
	@TableField(value="outpay_day")
	private BigDecimal outpayDay;
	/** 对应字段：deductible,备注：免赔额 */
	@TableField(value="deductible")
	private BigDecimal deductible;
	/** 对应字段：deductible_rate,备注：免赔率 */
	@TableField(value="deductible_rate")
	private BigDecimal deductibleRate;
	/** 对应字段：insured_rate,备注：投保比例 */
	@TableField(value="insured_rate")
	private BigDecimal insuredRate;
	/** 对应字段：term_code,备注：小条款编码 */
	@TableField(value="term_code")
	private String termCode;
	/** 对应字段：term_name,备注：小条款名称 */
	@TableField(value="term_name")
	private String termName;
	/** 对应字段：tax_fee,备注：税额 */
	@TableField(value="tax_fee")
	private BigDecimal taxFee;
	@TableField(value="related_flag")
	private BigDecimal relatedFlag;
	/** 对应字段：created_by,备注：创建人 */
	@TableField(value="created_by")
	private String createdBy;
	/** 对应字段：sys_ctime,备注：创建时间 */
	@TableField(value="sys_ctime")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date sysCtime;
	/** 对应字段：updated_by,备注：最新修改人员 */
	@TableField(value="updated_by")
	private String updatedBy;
	/** 对应字段：sys_utime,备注：最新修改时间 */
	@TableField(value="sys_utime")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date sysUtime;
	//人伤跟踪损失信息详情
	private List<ClmsPersTraceFeeVO> clmsPersTraceFeeVOList;
}
