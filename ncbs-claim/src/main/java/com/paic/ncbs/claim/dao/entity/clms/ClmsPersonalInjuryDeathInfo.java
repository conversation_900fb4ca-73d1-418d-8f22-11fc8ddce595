package com.paic.ncbs.claim.dao.entity.clms;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.paic.ncbs.claim.model.dto.report.ProfessionDTO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 人身伤亡信息表(ClmsPersonalInjuryDeathInfo)实体类
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ClmsPersonalInjuryDeathInfo implements Serializable {
    private static final long serialVersionUID = -54526793798489870L;
    /**
     * 主键
     */
    private String id;
    /**
     * 报案号
     */
    private String reportNo;
    /**
     * 赔付次数
     */
    private Integer caseTimes;
    /**
     * 伤者信息-姓名
     */
    private String injuredName;
    /**
     * 伤者信息-性别：F:女性，M:男性
     */
    private String injuredGender;
    /**
     * 伤者信息-证件类型
     */
    private String injuredCertificateType;
    /**
     * 伤者信息-证件号
     */
    private String injuredCertificateNo;
    /**
     * 伤者信息-出生年月
     */
    private Date injuredBirthday;
    /**
     * 伤者信息-年龄
     */
    private Integer injuredAge;
    /**
     * 伤者信息-是否身故 Y、N
     */
    private String injuredWhetherDeath;
    /**
     * 伤者信息-身故日期
     */
    private Date injuredDiedDate;
    /**
     * 伤者信息-身故原因: 1 意外、2疾病、3自杀、99其他
     */
    private String injuredDiedCause;
    /**
     * 伤者信息-是否伤残 Y、N
     */
    private String injuredWhetherDisabled;
    /**
     * 伤者信息-伤残日期
     */
    private Date injuredDisabledDate;
    /**
     * 伤者信息-伤残等级-一级、二级、三级、四级、五级、六级、七级、八级、九级、十级
     */
    private String injuredDisabledGrade;
    /**
     * 人伤事故原因 枚举值：人身意外伤害、食物中毒、宠物伤人、疾病
     */
    private String personalInjuryCause;
    /**
     * 损失项目-医疗费
     */
    private BigDecimal lossMedicalAmount;
    /**
     * 损失项目-误工费
     */
    private BigDecimal lossWorkingTimeAmount;
    /**
     * 损失项目-身故、伤残赔偿金
     */
    private BigDecimal lossInjuryDeathAmount;
    /**
     * 损失项目-住院补贴
     */
    private BigDecimal lossHospitalizationSubsidyAmount;
    /**
     * 损失项目-其他费
     */
    private BigDecimal lossOtherAmount;
    /**
     * 损失金额-总赔偿金额 自动加总“医疗费”、“误工费”、“身故、伤残赔偿金”、“住院补贴”、“其他”等录入的金额合计，不支持修改
     */
    private BigDecimal sumPayAmount;
    /**
     * 创建人员
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDate;
    /**
     * 修改人员
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDate;

    /**
     * 客户号
     */
    private String clientNo;

    // 责任险新增以下字段
    private String injuredWhetherInHospital;

    private String injuredHospitalCode;

    private String injuredHospitalName;

    private ProfessionDTO injuredProfession;

	private String riskGroupNo;

    private String taskId;

    private String policyNo;

    private String riskGroupName;

    private String remark;

}

