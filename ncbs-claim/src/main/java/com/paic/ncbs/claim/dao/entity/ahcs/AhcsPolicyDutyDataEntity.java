package com.paic.ncbs.claim.dao.entity.ahcs;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 保单责任信息表
 */
public class AhcsPolicyDutyDataEntity {

    private String createdBy;

    private Date createdDate;

    private String updatedBy;

    private Date updatedDate;

    private String idAhcsPolicyDutyData;

    private String idAhcsPolicyDuty;

    private String idAhcsPolicyPlan;

    private String dutyName;

    private String dutyCode;

    private BigDecimal dutyAmount;

    private String dutyDesc;

    private String orgDutyCode;

    private String orgDutyName;

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(Date updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getIdAhcsPolicyDuty() {
        return idAhcsPolicyDuty;
    }

    public void setIdAhcsPolicyDuty(String idAhcsPolicyDuty) {
        this.idAhcsPolicyDuty = idAhcsPolicyDuty == null ? null : idAhcsPolicyDuty.trim();
    }

    public String getIdAhcsPolicyPlan() {
        return idAhcsPolicyPlan;
    }

    public void setIdAhcsPolicyPlan(String idAhcsPolicyPlan) {
        this.idAhcsPolicyPlan = idAhcsPolicyPlan == null ? null : idAhcsPolicyPlan.trim();
    }

    public String getDutyName() {
        return dutyName;
    }

    public void setDutyName(String dutyName) {
        this.dutyName = dutyName == null ? null : dutyName.trim();
    }

    public String getDutyCode() {
        return dutyCode;
    }

    public void setDutyCode(String dutyCode) {
        this.dutyCode = dutyCode == null ? null : dutyCode.trim();
    }

    public BigDecimal getDutyAmount() {
        return dutyAmount;
    }

    public void setDutyAmount(BigDecimal dutyAmount) {
        this.dutyAmount = dutyAmount;
    }

    public String getDutyDesc() {
        return dutyDesc;
    }

    public void setDutyDesc(String dutyDesc) {
        this.dutyDesc = dutyDesc == null ? null : dutyDesc.trim();
    }

    public String getOrgDutyCode() {
        return orgDutyCode;
    }

    public void setOrgDutyCode(String orgDutyCode) {
        this.orgDutyCode = orgDutyCode == null ? null : orgDutyCode.trim();
    }

    public String getOrgDutyName() {
        return orgDutyName;
    }

    public void setOrgDutyName(String orgDutyName) {
        this.orgDutyName = orgDutyName == null ? null : orgDutyName.trim();
    }

    public String getIdAhcsPolicyDutyData() {
        return idAhcsPolicyDutyData;
    }

    public void setIdAhcsPolicyDutyData(String idAhcsPolicyDutyData) {
        this.idAhcsPolicyDutyData = idAhcsPolicyDutyData;
    }
}