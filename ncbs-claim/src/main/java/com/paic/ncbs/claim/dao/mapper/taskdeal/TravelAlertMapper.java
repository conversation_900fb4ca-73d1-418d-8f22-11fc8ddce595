package com.paic.ncbs.claim.dao.mapper.taskdeal;

import com.paic.ncbs.claim.model.dto.taskdeal.TravelAlertDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface TravelAlertMapper {

    public void addTravelAlert(@Param("travelAlertList") List<TravelAlertDTO> travelAlertList);

    public void removeTravelAlert(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("taskCode") String taskCode, @Param("channelProcessId") String channelProcessId);

    public List<TravelAlertDTO> getTravelAlert(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("status") String status, @Param("taskCode") String taskCode, @Param("channelProcessId") String channelProcessId);

    public List<TravelAlertDTO> getTravelAlertList(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskCode") String taskCode);

    public void addTravelAlertList(@Param("travelAlertList") List<TravelAlertDTO> travelAlertList,
                                   @Param("caseTimes") Integer caseTimes, @Param("userId") String userId, @Param("channelProcessId") String channelProcessId);

    public void updateTravelAlert(TravelAlertDTO travelAlert);
}
