package com.paic.ncbs.claim.dao.entity.clms;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 理赔保单历史保结信息(ClmsPolicyHistoryUwInfoEntity)实体类
 *
 * <AUTHOR>
 * @since 2023-11-28 15:28:07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("ClmsPolicyHistoryUwInfoEntity实体类")
public class ClmsPolicyHistoryUwInfoEntity extends EntityDTO implements Serializable {
    private static final long serialVersionUID = 377180593707246164L;
    /**
     * 主键
     */
    private String id;
    /**
     * 报案号
     */
    private String reportNo;
    /**
     * 保单号
     */

    private String policyNo;
    /**
     * 产品名称
     */

    private String productName;
    /**
     * 方案名称
     */

    private String schemeName;
    /**
     * 险种名称
     */

    private String planName;
    /**
     * 保单起期
     */

    private Date insuranceBeginDate;
    /**
     * 保单止期
     */

    private Date insuranceEndDate;
    /**
     * 险种状态：有效、中止、终止
     */

    private String planStatus;
    /**
     * 核保决定=核保结论 除外、标体，拒保
     */

    private String uwConclusion;
    /**
     * 除外事项:核保说明 核保结论为“除外”或者“拒保”时才有核保说明
     */
    private String uwExceptions;
    /**
     * 核保完成时间
     */
    private Date uwCompleteDate;
    /**
     * 核保数据信息来源：0-理赔核保；1-抄单
     */
    private String uwDataSource;
    /**
     * 个团属性：P-个人，G-团体
     */
    private String businessType;
    /**
     * 被保险人姓名
     */
    private String insuredName;
    /**
     * 被保险人客户号
     */
    private String clientNo;

}

