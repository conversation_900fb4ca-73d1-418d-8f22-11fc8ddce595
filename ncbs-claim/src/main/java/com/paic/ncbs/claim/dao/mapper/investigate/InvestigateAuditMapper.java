package com.paic.ncbs.claim.dao.mapper.investigate;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateAuditVO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateVO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateAuditDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;


@MapperScan
public interface InvestigateAuditMapper extends BaseDao<InvestigateAuditDTO> {


    int addInvestigateAudit(InvestigateAuditDTO investigateAudit);


    int modifyInvestigateAudit(InvestigateAuditDTO investigateAudit);


    InvestigateAuditVO getInvestigateAuditByTaskId(
            @Param("idAhcsInvestigateTask") String idAhcsInvestigateTask);
    

	List<InvestigateAuditVO> getInvestigateAuditByInvestigateId(@Param("idAhcsInvestigate") String idAhcsInvestigate);


	InvestigateAuditVO getInvestigateMajorAuditByInvestigateId(@Param("idAhcsInvestigate") String idAhcsInvestigate);


	InvestigateAuditVO getInvestigateAuditById(@Param("idAhcsInvestigateAudit") String idAhcsInvestigateAudit);


	InvestigateVO getInvestigateAuditForBack(@Param("idAhcsInvestigate") String idAhcsInvestigate);

	
	
}