package com.paic.ncbs.claim.dao.mapper.checkloss;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.duty.PersonDisabilityDTO;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import com.paic.ncbs.claim.model.vo.checkloss.DisabilityVO;
import com.paic.ncbs.claim.model.vo.trace.ClmsPersonDisabilityVO;
import com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface PersonDisabilityMapper extends BaseDao<PersonDisabilityDTO> {

    public void savePersonDisability(PersonDisabilityDTO personDisabilityDTO);

    public void removePersonDisability(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId);

    void updateEffective(PersonDisabilityDTO personDisabilityDTO);

    public List<PersonDisabilityDTO> getPersonDisabilityList(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId, @Param("status") String status);

    List<PersonDisabilityDTO> getList(@Param("personDisability") PersonDisabilityDTO personDisability);

    List<String> getDisabilityClassList(@Param("personDisability") PersonDisabilityDTO personDisability);

    public List<DisabilityVO> getDisabilityList(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId, @Param("status") String status);

    public void addPersonDisabilityList(@Param("personDisabilityList") List<PersonDisabilityDTO> personDisabilityList,
                                        @Param("caseTimes") int caseTimes, @Param("userId") String userId, @Param("channelProcessId") String channelProcessId);

    public List<PersonDisabilityDTO> getPersonDisabilityDTOList(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId);

    public String getDisabilityDateByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    public List<PersonDisabilityDTO> getDisabilityListByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,
                                                                 @Param("status") String status, @Param("taskId") String taskId);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);

    /**
     * 查询伤残信息数据--根据报案号，任务号，赔付次数
     * @param personTranceRequestVo
     * @return
     */
    public List<PersonDisabilityDTO> selectPersonDisabilityDTO(PersonTranceRequestVo personTranceRequestVo);

    /**
     * 修改
     * @param personDisabilityDTO
     * @return
     */
    int updateByPrimaryKeySelective(PersonDisabilityDTO personDisabilityDTO);

    /**
     * 删除
     * @param personTranceRequestVo
     */
    void deletePersonDisability(PersonTranceRequestVo personTranceRequestVo);
}
