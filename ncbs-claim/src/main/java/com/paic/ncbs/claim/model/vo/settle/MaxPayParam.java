package com.paic.ncbs.claim.model.vo.settle;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

public class MaxPayParam extends EntityDTO {

    private static final long serialVersionUID = -783122286047477674L;
    @ApiModelProperty("报案号")
    private String reportNo;

    private String partyNo;
    @ApiModelProperty("保单号")
    private String policyNo;

    private String policyCerNo;
    @ApiModelProperty("赔案号")
    private String caseNo;

    private BigDecimal dutyHistoryIndemnityAmount;

    private BigDecimal dutyDetailHistoryIndemnityAmount;

    private String subpolicyNo;

    private String insuredCode;
    @ApiModelProperty("险种代码")
    private String planCode;

    private String dutyCode;

    private String dutyDetailCode;

    private BigDecimal dutyBaseAmount;

    private BigDecimal detailBaseAmount;

    private String isQueryOldReport;

    private List<String> dutyCodeList;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    /**
     * 查询场景（暂时只用于区分查询历史案件，查询历史案件时 计算剩余理赔金额的已赔付金额要包含当前报案号）
     */
    private String selectScene;

    public String getIsQueryOldReport() {
        return isQueryOldReport;
    }

    public void setIsQueryOldReport(String isQueryOldReport) {
        this.isQueryOldReport = isQueryOldReport;
    }

    public BigDecimal getDutyBaseAmount() {
        return dutyBaseAmount;
    }

    public void setDutyBaseAmount(BigDecimal dutyBaseAmount) {
        this.dutyBaseAmount = dutyBaseAmount;
    }

    public BigDecimal getDetailBaseAmount() {
        return detailBaseAmount;
    }

    public void setDetailBaseAmount(BigDecimal detailBaseAmount) {
        this.detailBaseAmount = detailBaseAmount;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getPartyNo() {
        return partyNo;
    }

    public void setPartyNo(String partyNo) {
        this.partyNo = partyNo;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getPlanCode() {
        return planCode;
    }

    public void setPlanCode(String planCode) {
        this.planCode = planCode;
    }

    public String getDutyCode() {
        return dutyCode;
    }

    public void setDutyCode(String dutyCode) {
        this.dutyCode = dutyCode;
    }

    public String getDutyDetailCode() {
        return dutyDetailCode;
    }

    public void setDutyDetailCode(String dutyDetailCode) {
        this.dutyDetailCode = dutyDetailCode;
    }

    public BigDecimal getDutyHistoryIndemnityAmount() {
        return dutyHistoryIndemnityAmount;
    }

    public void setDutyHistoryIndemnityAmount(BigDecimal dutyHistoryIndemnityAmount) {
        this.dutyHistoryIndemnityAmount = dutyHistoryIndemnityAmount;
    }

    public BigDecimal getDutyDetailHistoryIndemnityAmount() {
        return dutyDetailHistoryIndemnityAmount;
    }

    public void setDutyDetailHistoryIndemnityAmount(BigDecimal dutyDetailHistoryIndemnityAmount) {
        this.dutyDetailHistoryIndemnityAmount = dutyDetailHistoryIndemnityAmount;
    }

    public String getSubpolicyNo() {
        return subpolicyNo;
    }

    public void setSubpolicyNo(String subpolicyNo) {
        this.subpolicyNo = subpolicyNo;
    }

    public String getInsuredCode() {
        return insuredCode;
    }

    public void setInsuredCode(String insuredCode) {
        this.insuredCode = insuredCode;
    }

    public String getCaseNo() {
        return caseNo;
    }

    public void setCaseNo(String caseNo) {
        this.caseNo = caseNo;
    }

    public String getPolicyCerNo() {
        return policyCerNo;
    }

    public void setPolicyCerNo(String policyCerNo) {
        this.policyCerNo = policyCerNo;
    }

    public List<String> getDutyCodeList() {
        return dutyCodeList;
    }

    public void setDutyCodeList(List<String> dutyCodeList) {
        this.dutyCodeList = dutyCodeList;
    }

    public String getSelectScene() {
        return selectScene;
    }

    public void setSelectScene(String selectScene) {
        this.selectScene = selectScene;
    }

    public Integer getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Integer caseTimes) {
        this.caseTimes = caseTimes;
    }
}
