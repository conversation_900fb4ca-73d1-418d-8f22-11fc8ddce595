package com.paic.ncbs.claim.model.vo.pay;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PaymentInfoVO {
    private String idClmPaymentInfo;
    private String reportNo;
    private String applyNo;
    private Integer caseTimes;
    private Integer subTimes;
    private String idClmChannelProcess;
    private String paymentInfoType;
    private String collectPayApproach;
    private String bankAccountAttribute;
    private String cityName;
    private String clientBankAccount;
    private String clientBankCode;
    private String clientBankName;
    private String clientCertificateNo;
    private String clientCertificateType;
    private String clientMobile;
    private String clientName;
    private String clientType;
    private String provinceName;
    private String regionCode;
    private String remark;
    private String isMergePay;
    private String counterPaymentReason;
    private String otherReason;
    private String organizeCode;
    private String isTaxpayer;
    private String paymentUsage;
    private String mergeStrategy;
    private String bankDetail ;
    private String cityCode ;
    private String provinceCode ;

    private String proName;

    private String ciName;

    private String countryName;

    private String clientRelation;

    private String bankDetailCode;

    private String paymentInfoStatus;

    private String migrateFrom;
    @ApiModelProperty("机构类型")
    private String agencyType;

    @ApiModelProperty("客户号")
    private String customerNo;

    @ApiModelProperty("公司证件类型/企业证件类型：CertificateTypeEnum枚举类，统一社会信用代码-610099，组织机构代码-610001，税务登记证-610007，营业执照-610005，其他证件-619999")
    private String companyCardType;

    @ApiModelProperty(value = "微保支付类型:1 微信零钱 2 银行转账")
    private String payType;

    @ApiModelProperty(value = "国籍：洲编码（如亚洲-02）")
    private String areaCode;

    @ApiModelProperty(value = "国籍：数字编码（如中国-156）")
    private String nationCode;

    @ApiModelProperty(value = "职业类别大类：数字编码，如001")
    private String professionMaximumCode;

    @ApiModelProperty(value = "职业类别中类：数字编码，如00101")
    private String professionMediumCode;

    @ApiModelProperty(value = "职业类别小类：数字编码，如00101001")
    private String professionMinimumCode;

    @ApiModelProperty(value = "证件生效时间（格式：YYYY-MM-DD）")
    private String certificateEffectiveDate;

    @ApiModelProperty(value = "证件失效时间，长期标识为9999-12-31")
    private String certificateExpireDate;

    @ApiModelProperty(value = "性别：F-女性，M-男性")
    private String gender;

    @ApiModelProperty(value = "收入类型：1-5万以下，2-5到15万，3-15万以上，4-其他（200字符限制）")
    private String incomeCode;

    @ApiModelProperty(value = "收入文本：与收入类型对应（200字符限制）")
    private String incomeText;

    @ApiModelProperty(value = "工作单位")
    private String company;

    @ApiModelProperty(value = "地址")
    private String address;

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getNationCode() {
        return nationCode;
    }

    public void setNationCode(String nationCode) {
        this.nationCode = nationCode;
    }

    public String getProfessionMaximumCode() {
        return professionMaximumCode;
    }

    public void setProfessionMaximumCode(String professionMaximumCode) {
        this.professionMaximumCode = professionMaximumCode;
    }

    public String getProfessionMediumCode() {
        return professionMediumCode;
    }

    public void setProfessionMediumCode(String professionMediumCode) {
        this.professionMediumCode = professionMediumCode;
    }

    public String getProfessionMinimumCode() {
        return professionMinimumCode;
    }

    public void setProfessionMinimumCode(String professionMinimumCode) {
        this.professionMinimumCode = professionMinimumCode;
    }

    public String getCertificateEffectiveDate() {
        return certificateEffectiveDate;
    }

    public void setCertificateEffectiveDate(String certificateEffectiveDate) {
        this.certificateEffectiveDate = certificateEffectiveDate;
    }

    public String getCertificateExpireDate() {
        return certificateExpireDate;
    }

    public void setCertificateExpireDate(String certificateExpireDate) {
        this.certificateExpireDate = certificateExpireDate;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getIncomeCode() {
        return incomeCode;
    }

    public void setIncomeCode(String incomeCode) {
        this.incomeCode = incomeCode;
    }

    public String getIncomeText() {
        return incomeText;
    }

    public void setIncomeText(String incomeText) {
        this.incomeText = incomeText;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }


    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    @ApiModelProperty(value = "微信openId")
    private String openId;

    public String getBankDetailCode() {
        return bankDetailCode;
    }

    public void setBankDetailCode(String bankDetailCode) {
        this.bankDetailCode = bankDetailCode;
    }

    public String getClientRelation() {
        return clientRelation;
    }

    public void setClientRelation(String clientRelation) {
        this.clientRelation = clientRelation;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getProName() {
        return proName;
    }

    public void setProName(String proName) {
        this.proName = proName;
    }

    public String getCiName() {
        return ciName;
    }

    public void setCiName(String ciName) {
        this.ciName = ciName;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public String getBankDetail() {
        return bankDetail;
    }

    public void setBankDetail(String bankDetail) {
        this.bankDetail = bankDetail;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getIdClmPaymentInfo() {
        return idClmPaymentInfo;
    }

    public void setIdClmPaymentInfo(String idClmPaymentInfo) {
        this.idClmPaymentInfo = idClmPaymentInfo;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public Integer getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Integer caseTimes) {
        this.caseTimes = caseTimes;
    }

    public Integer getSubTimes() {
        return subTimes;
    }

    public void setSubTimes(Integer subTimes) {
        this.subTimes = subTimes;
    }

    public String getIdClmChannelProcess() {
        return idClmChannelProcess;
    }

    public void setIdClmChannelProcess(String idClmChannelProcess) {
        this.idClmChannelProcess = idClmChannelProcess;
    }

    public String getPaymentInfoType() {
        return paymentInfoType;
    }

    public void setPaymentInfoType(String paymentInfoType) {
        this.paymentInfoType = paymentInfoType;
    }

    public String getCollectPayApproach() {
        return collectPayApproach;
    }

    public void setCollectPayApproach(String collectPayApproach) {
        this.collectPayApproach = collectPayApproach;
    }

    public String getBankAccountAttribute() {
        return bankAccountAttribute;
    }

    public void setBankAccountAttribute(String bankAccountAttribute) {
        this.bankAccountAttribute = bankAccountAttribute;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getClientBankAccount() {
        return clientBankAccount;
    }

    public void setClientBankAccount(String clientBankAccount) {
        this.clientBankAccount = clientBankAccount;
    }

    public String getClientBankCode() {
        return clientBankCode;
    }

    public void setClientBankCode(String clientBankCode) {
        this.clientBankCode = clientBankCode;
    }

    public String getClientBankName() {
        return clientBankName;
    }

    public void setClientBankName(String clientBankName) {
        this.clientBankName = clientBankName;
    }

    public String getClientCertificateNo() {
        return clientCertificateNo;
    }

    public void setClientCertificateNo(String clientCertificateNo) {
        this.clientCertificateNo = clientCertificateNo;
    }

    public String getClientCertificateType() {
        return clientCertificateType;
    }

    public void setClientCertificateType(String clientCertificateType) {
        this.clientCertificateType = clientCertificateType;
    }

    public String getClientMobile() {
        return clientMobile;
    }

    public void setClientMobile(String clientMobile) {
        this.clientMobile = clientMobile;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getClientType() {
        return clientType;
    }

    public void setClientType(String clientType) {
        this.clientType = clientType;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getIsMergePay() {
        return isMergePay;
    }

    public void setIsMergePay(String isMergePay) {
        this.isMergePay = isMergePay;
    }

    public String getCounterPaymentReason() {
        return counterPaymentReason;
    }

    public void setCounterPaymentReason(String counterPaymentReason) {
        this.counterPaymentReason = counterPaymentReason;
    }

    public String getOtherReason() {
        return otherReason;
    }

    public void setOtherReason(String otherReason) {
        this.otherReason = otherReason;
    }

    public String getOrganizeCode() {
        return organizeCode;
    }

    public void setOrganizeCode(String organizeCode) {
        this.organizeCode = organizeCode;
    }

    public String getIsTaxpayer() {
        return isTaxpayer;
    }

    public void setIsTaxpayer(String isTaxpayer) {
        this.isTaxpayer = isTaxpayer;
    }


    public String getPaymentUsage() {
        return paymentUsage;
    }

    public void setPaymentUsage(String paymentUsage) {
        this.paymentUsage = paymentUsage;
    }


    public String getMergeStrategy() {
        return mergeStrategy;
    }

    public void setMergeStrategy(String mergeStrategy) {
        this.mergeStrategy = mergeStrategy;
    }

    public String getAgencyType() {
        return agencyType;
    }

    public void setAgencyType(String agencyType) {
        this.agencyType = agencyType;
    }

    public String getCompanyCardType() {
        return companyCardType;
    }

    public void setCompanyCardType(String companyCardType) {
        this.companyCardType = companyCardType;
    }

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getPaymentInfoStatus() {
        return paymentInfoStatus;
    }

    public void setPaymentInfoStatus(String paymentInfoStatus) {
        this.paymentInfoStatus = paymentInfoStatus;
    }

    public String getMigrateFrom() {
        return migrateFrom;
    }

    public void setMigrateFrom(String migrateFrom) {
        this.migrateFrom = migrateFrom;
    }
}
