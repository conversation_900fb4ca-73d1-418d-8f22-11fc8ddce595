package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * 意键险报案信息扩展表
 */
public class ReportAccidentExEntity extends EntityDTO {

    private static final long serialVersionUID = 2276111922783756952L;

    private String idAhcsReportAccidentEx;

    private String reportNo;

    private String insuredApplyStatus;

    private String insuredApplyType;

    private String medicalStatus;

    private String therapyType;

    private String doctorDiagnosis;

    private String accidentType;

    private String trafficAccidentType;

    private String insuredIdentity;

    private String trafficAccidentNature;

    private Date diedDate;

    private String diedStatus;

    private String diedCause;

    private String thisCarlicense;

    private String hospitalName;

    private String accidentStatusDetails;

    private String bigDiseaseCode;

    private String bigDiseaseName;

    private String accidentExtendInfo;

    private Map<String, Object> drivingTestExtendDto;

    private BigDecimal hospitalDays;

    private String clobAccidentExtend;

    public BigDecimal getHospitalDays() {
        return hospitalDays;
    }

    public void setHospitalDays(BigDecimal hospitalDays) {
        this.hospitalDays = hospitalDays;
    }

    public String getBigDiseaseCode() {
        return bigDiseaseCode;
    }

    public void setBigDiseaseCode(String bigDiseaseCode) {
        this.bigDiseaseCode = bigDiseaseCode;
    }

    public String getBigDiseaseName() {
        return bigDiseaseName;
    }

    public void setBigDiseaseName(String bigDiseaseName) {
        this.bigDiseaseName = bigDiseaseName;
    }

    public String getIdAhcsReportAccidentEx() {
        return idAhcsReportAccidentEx;
    }

    public void setIdAhcsReportAccidentEx(String idAhcsReportAccidentEx) {
        this.idAhcsReportAccidentEx = idAhcsReportAccidentEx;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getInsuredApplyStatus() {
        return insuredApplyStatus;
    }

    public void setInsuredApplyStatus(String insuredApplyStatus) {
        this.insuredApplyStatus = insuredApplyStatus;
    }

    public String getMedicalStatus() {
        return medicalStatus;
    }

    public void setMedicalStatus(String medicalStatus) {
        this.medicalStatus = medicalStatus;
    }

    public String getTherapyType() {
        return therapyType;
    }

    public void setTherapyType(String therapyType) {
        this.therapyType = therapyType;
    }

    public String getDoctorDiagnosis() {
        return doctorDiagnosis;
    }

    public void setDoctorDiagnosis(String doctorDiagnosis) {
        this.doctorDiagnosis = doctorDiagnosis;
    }

    public String getAccidentType() {
        return accidentType;
    }

    public void setAccidentType(String accidentType) {
        this.accidentType = accidentType;
    }

    public String getTrafficAccidentType() {
        return trafficAccidentType;
    }

    public void setTrafficAccidentType(String trafficAccidentType) {
        this.trafficAccidentType = trafficAccidentType;
    }

    public String getInsuredIdentity() {
        return insuredIdentity;
    }

    public void setInsuredIdentity(String insuredIdentity) {
        this.insuredIdentity = insuredIdentity;
    }

    public String getTrafficAccidentNature() {
        return trafficAccidentNature;
    }

    public void setTrafficAccidentNature(String trafficAccidentNature) {
        this.trafficAccidentNature = trafficAccidentNature;
    }

    public Date getDiedDate() {
        return diedDate;
    }

    public void setDiedDate(Date diedDate) {
        this.diedDate = diedDate;
    }

    public String getDiedStatus() {
        return diedStatus;
    }

    public void setDiedStatus(String diedStatus) {
        this.diedStatus = diedStatus;
    }

    public String getDiedCause() {
        return diedCause;
    }

    public void setDiedCause(String diedCause) {
        this.diedCause = diedCause;
    }

    public String getThisCarlicense() {
        return thisCarlicense;
    }

    public void setThisCarlicense(String thisCarlicense) {
        this.thisCarlicense = thisCarlicense;
    }

    public String getHospitalName() {
        return hospitalName;
    }

    public void setHospitalName(String hospitalName) {
        this.hospitalName = hospitalName;
    }

    public String getAccidentStatusDetails() {
        return accidentStatusDetails;
    }

    public void setAccidentStatusDetails(String accidentStatusDetails) {
        this.accidentStatusDetails = accidentStatusDetails;
    }

    public String getAccidentExtendInfo() {
        return accidentExtendInfo;
    }

    public void setAccidentExtendInfo(String accidentExtendInfo) {
        this.accidentExtendInfo = accidentExtendInfo;
    }

    public Map<String, Object> getDrivingTestExtendDto() {
        return drivingTestExtendDto;
    }

    public void setDrivingTestExtendDto(Map<String, Object> drivingTestExtendDto) {
        this.drivingTestExtendDto = drivingTestExtendDto;
    }

    public String getInsuredApplyType() {
        return insuredApplyType;
    }

    public void setInsuredApplyType(String insuredApplyType) {
        this.insuredApplyType = insuredApplyType;
    }

    public String getClobAccidentExtend() {
        return clobAccidentExtend;
    }

    public void setClobAccidentExtend(String clobAccidentExtend) {
        this.clobAccidentExtend = clobAccidentExtend;
    }

}