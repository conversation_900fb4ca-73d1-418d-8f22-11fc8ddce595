package com.paic.ncbs.claim.dao.entity.clms;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 理赔保单解约信息表(ClmsPolicySurrenderInfoEntity)实体类
 *
 * <AUTHOR>
 * @since 2023-11-03 14:49:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("ClmsPolicySurrenderInfo实体类")
public class ClmsPolicySurrenderInfoEntity extends EntityDTO implements Serializable {
    private static final long serialVersionUID = 263333257271304230L;
    /**
     * 主键
     */
    private String id;
    /**
     * 报案号
     */
    private String reportNo;
    /**
     * 赔付次数
     */
    private Integer caseTimes;
    /**
     * 保单号
     */
    private String policyNo;
    /**
     * 二核保单核保结论主键
     */
    private String idSeconduwPolicyConclusion;
    /**
     * 解约方式0 -不退费，1-退费
     */
    private String surrenderType;
    /**
     * 解约原因
     */
    private String surrenderReason;
    /**
     * 解约生效日
     */
    private Date effectiveDate;
    /**
     * 应收保费
     */
    private BigDecimal totalAgreePremium;
    /**
     * 实收保费
     */
    private BigDecimal totalActualPremium;
    /**
     * 未满期净保费
     */
    private BigDecimal unearnedPrem;
    /**
     * 退费金额类型 0-退未满期净保费，1-约定退费金额
     */
    private String refundType;
    /**
     * 同步批改状态 0-未同步，1-已同步
     */
    private String syncPosStatus;
    /**
     * 退费金额
     */
    private BigDecimal refundAmount;

    /**
     * 文件id
     */
    private String fileId;
    /**
     * 是否合议标志 0-未合议，1-合议
     */
    private String collegialFlag;

    /**
     * 合议附件文件id
     */
    private String collegialFileId;

    /**
     * 解约批文
     */
    private String endorseComment;

}

