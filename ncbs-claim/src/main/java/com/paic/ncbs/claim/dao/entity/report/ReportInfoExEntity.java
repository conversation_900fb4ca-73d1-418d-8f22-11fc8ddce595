package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.math.BigDecimal;

/**
 * 报案信息扩展表
 */
public class ReportInfoExEntity extends EntityDTO {

    private static final long serialVersionUID = -2685081204548727588L;

    private String idAhcsReportInfoEx;

    private String reportNo;

    private String caseClass;

    private String relationWithReporter;

    private String linkManName;

    private String linkManRelation;

    private String sendMessage;

    private BigDecimal costEstimate;

    private String reportRemark;

    private String succorService;

    private String succorCompany;

    private String succorNo;

    private String succorCompanyName;

    private String succorServiceLevel;

    private String succorServiceName;

    private String succorServiceCode;

    private String partnerCode;

    private String whetherAuthorized;

    private String documentGroupId;

    private String isSpecialReport;

    private String caseSign;

    private String isRepeatReport;

    private String reportExtend;
    private String residenceAddress;
    private String residenceProvince;
    private String residenceCity;
    private String residenceDistrict;
    private String wesureAutoClaim;
    private Integer riskLevelScore;
    private String riskLevelDesc;
    private String claimDealWay;

    public String getAcceptanceNumber() {
        return acceptanceNumber;
    }

    public void setAcceptanceNumber(String acceptanceNumber) {
        this.acceptanceNumber = acceptanceNumber;
    }

    private String acceptanceNumber;
    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    private String companyId;

    public String getIsQuickPay() {
        return isQuickPay;
    }

    public void setIsQuickPay(String isQuickPay) {
        this.isQuickPay = isQuickPay;
    }

    private String isQuickPay;

    public String getResidenceCity() {
        return residenceCity;
    }

    public void setResidenceCity(String residenceCity) {
        this.residenceCity = residenceCity;
    }

    public String getResidenceAddress() {
        return residenceAddress;
    }

    public void setResidenceAddress(String residenceAddress) {
        this.residenceAddress = residenceAddress;
    }

    public String getResidenceProvince() {
        return residenceProvince;
    }

    public void setResidenceProvince(String residenceProvince) {
        this.residenceProvince = residenceProvince;
    }

    public String getResidenceDistrict() {
        return residenceDistrict;
    }

    public void setResidenceDistrict(String residenceDistrict) {
        this.residenceDistrict = residenceDistrict;
    }

    public BigDecimal getCostEstimate() {
        return costEstimate;
    }

    private String caseType;

    public void setCostEstimate(BigDecimal costEstimate) {
        this.costEstimate = costEstimate;
    }

    public String getIdAhcsReportInfoEx() {
        return idAhcsReportInfoEx;
    }

    public void setIdAhcsReportInfoEx(String idAhcsReportInfoEx) {
        this.idAhcsReportInfoEx = idAhcsReportInfoEx == null ? null : idAhcsReportInfoEx.trim();
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String getCaseClass() {
        return caseClass;
    }

    public void setCaseClass(String caseClass) {
        this.caseClass = caseClass == null ? null : caseClass.trim();
    }

    public String getRelationWithReporter() {
        return relationWithReporter;
    }

    public void setRelationWithReporter(String relationWithReporter) {
        this.relationWithReporter = relationWithReporter == null ? null : relationWithReporter.trim();
    }

    public String getLinkManName() {
        return linkManName;
    }

    public void setLinkManName(String linkManName) {
        this.linkManName = linkManName == null ? null : linkManName.trim();
    }

    public String getLinkManRelation() {
        return linkManRelation;
    }

    public void setLinkManRelation(String linkManRelation) {
        this.linkManRelation = linkManRelation == null ? null : linkManRelation.trim();
    }

    public String getSendMessage() {
        return sendMessage;
    }

    public void setSendMessage(String sendMessage) {
        this.sendMessage = sendMessage == null ? null : sendMessage.trim();
    }

    public String getReportRemark() {
        return reportRemark;
    }

    public void setReportRemark(String reportRemark) {
        this.reportRemark = reportRemark == null ? null : reportRemark.trim();
    }

    public String getSuccorService() {
        return succorService;
    }

    public void setSuccorService(String succorService) {
        this.succorService = succorService;
    }

    public String getSuccorServiceCode() {
        return succorServiceCode;
    }

    public void setSuccorServiceCode(String succorServiceCode) {
        this.succorServiceCode = succorServiceCode;
    }

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }

    public String getWhetherAuthorized() {
        return whetherAuthorized;
    }

    public void setWhetherAuthorized(String whetherAuthorized) {
        this.whetherAuthorized = whetherAuthorized;
    }

    public String getDocumentGroupId() {
        return documentGroupId;
    }

    public void setDocumentGroupId(String documentGroupId) {
        this.documentGroupId = documentGroupId;
    }

    public String getIsSpecialReport() {
        return isSpecialReport;
    }

    public void setIsSpecialReport(String isSpecialReport) {
        this.isSpecialReport = isSpecialReport;
    }

    public String getCaseSign() {
        return caseSign;
    }

    public void setCaseSign(String caseSign) {
        this.caseSign = caseSign;
    }

    public String getSuccorCompany() {
        return succorCompany;
    }

    public void setSuccorCompany(String succorCompany) {
        this.succorCompany = succorCompany;
    }

    public String getSuccorCompanyName() {
        return succorCompanyName;
    }

    public void setSuccorCompanyName(String succorCompanyName) {
        this.succorCompanyName = succorCompanyName;
    }

    public String getSuccorServiceLevel() {
        return succorServiceLevel;
    }

    public void setSuccorServiceLevel(String succorServiceLevel) {
        this.succorServiceLevel = succorServiceLevel;
    }

    public String getSuccorServiceName() {
        return succorServiceName;
    }

    public void setSuccorServiceName(String succorServiceName) {
        this.succorServiceName = succorServiceName;
    }

    public String getSuccorNo() {
        return succorNo;
    }

    public void setSuccorNo(String succorNo) {
        this.succorNo = succorNo;
    }

    public String getIsRepeatReport() {
        return isRepeatReport;
    }

    public void setIsRepeatReport(String isRepeatReport) {
        this.isRepeatReport = isRepeatReport;
    }

    public String getReportExtend() {
        return reportExtend;
    }

    public void setReportExtend(String reportExtend) {
        this.reportExtend = reportExtend;
    }

    public String getCaseType() {
        return caseType;
    }

    public void setCaseType(String caseType) {
        this.caseType = caseType;
    }

    public String getWesureAutoClaim() {
        return wesureAutoClaim;
    }

    public void setWesureAutoClaim(String wesureAutoClaim) {
        this.wesureAutoClaim = wesureAutoClaim;
    }

    public Integer getRiskLevelScore() {
        return riskLevelScore;
    }

    public void setRiskLevelScore(Integer riskLevelScore) {
        this.riskLevelScore = riskLevelScore;
    }

    public String getRiskLevelDesc() {
        return riskLevelDesc;
    }

    public void setRiskLevelDesc(String riskLevelDesc) {
        this.riskLevelDesc = riskLevelDesc;
    }

    public String getClaimDealWay() {
        return claimDealWay;
    }

    public void setClaimDealWay(String claimDealWay) {
        this.claimDealWay = claimDealWay;
    }
}