package com.paic.ncbs.claim.dao.mapper.ahcs;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsCoinsureEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AhcsCoinsureMapper extends BaseDao<AhcsCoinsureEntity> {

    int deleteByPrimaryKey(String idAhcsCoinsure);

    int insert(AhcsCoinsureEntity record);

    int insertSelective(AhcsCoinsureEntity record);

    AhcsCoinsureEntity selectByPrimaryKey(String idAhcsCoinsure);

    int updateByPrimaryKeySelective(AhcsCoinsureEntity record);

    int updateByPrimaryKey(AhcsCoinsureEntity record);

    List<AhcsCoinsureEntity> selectByIdAhcsPolicyInfo(String idAhcsPolicyInfo);

    int deleteByIdAhcsPolicyInfo(String idAhcsPolicyInfo);

    int insertList(@Param("coinsureEntities") List<AhcsCoinsureEntity> coinsureEntities);

    void deleteByReportNoAndPolicyNo(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo);

    void insertBatch(@Param("list") List<AhcsCoinsureEntity> list);
}