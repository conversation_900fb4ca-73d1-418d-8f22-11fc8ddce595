package com.paic.ncbs.claim.model.vo.policy;
 

import java.math.BigDecimal;
import java.util.List;

public class PolicyDutyVO {

	 
	private BigDecimal dutyAmount;
	
	 
	private String dutyCode;
	
	 
	private BigDecimal dutyMaxPay;
	
	 
	private String dutyName;
	
	 
	private String dutyDesc;
	
	 
	private BigDecimal estimateAmount;
	
	 
	private String planCode;

	 
	private String planName;

     
    private String groupCode ;

     
	private String policyNo;
	
	 
	private String policyCerNo;
	
	 
	List<PolicyDutyDetailVO> ahcsPolicyDutyDetail;
	
	 
	private String isMergePolicyDuty;
	
	public List<PolicyDutyDetailVO> getAhcsPolicyDutyDetail() {
		return ahcsPolicyDutyDetail;
	}

	public void setAhcsPolicyDutyDetail(List<PolicyDutyDetailVO> ahcsPolicyDutyDetail) {
		this.ahcsPolicyDutyDetail = ahcsPolicyDutyDetail;
	}

	public BigDecimal getDutyAmount() {
		return dutyAmount;
	}

	public void setDutyAmount(BigDecimal dutyAmount) {
		this.dutyAmount = dutyAmount;
	}

	public String getDutyCode() {
		return dutyCode;
	}

	public void setDutyCode(String dutyCode) {
		this.dutyCode = dutyCode;
	}

	public BigDecimal getDutyMaxPay() {
		return dutyMaxPay;
	}

	public void setDutyMaxPay(BigDecimal dutyMaxPay) {
		this.dutyMaxPay = dutyMaxPay;
	}

	public String getDutyName() {
		return dutyName;
	}

	public void setDutyName(String dutyName) {
		this.dutyName = dutyName;
	}

	public BigDecimal getEstimateAmount() {
		return estimateAmount;
	}

	public void setEstimateAmount(BigDecimal estimateAmount) {
		this.estimateAmount = estimateAmount;
	}

	public String getPlanCode() {
		return planCode;
	}

	public void setPlanCode(String planCode) {
		this.planCode = planCode;
	}

	public String getPlanName() {
		return planName;
	}

	public void setPlanName(String planName) {
		this.planName = planName;
	}

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getPolicyNo() {
		return policyNo;
	}

	public void setPolicyNo(String policyNo) {
		this.policyNo = policyNo;
	}

	public String getDutyDesc() {
		return dutyDesc;
	}

	public void setDutyDesc(String dutyDesc) {
		this.dutyDesc = dutyDesc;
	}

	public String getPolicyCerNo() {
		return policyCerNo;
	}

	public void setPolicyCerNo(String policyCerNo) {
		this.policyCerNo = policyCerNo;
	}

	public String getIsMergePolicyDuty() {
		return isMergePolicyDuty;
	}

	public void setIsMergePolicyDuty(String isMergePolicyDuty) {
		this.isMergePolicyDuty = isMergePolicyDuty;
	}
}
