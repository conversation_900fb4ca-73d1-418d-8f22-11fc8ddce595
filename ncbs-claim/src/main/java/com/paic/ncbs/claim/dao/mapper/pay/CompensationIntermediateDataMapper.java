package com.paic.ncbs.claim.dao.mapper.pay;

import com.paic.ncbs.claim.model.dto.pay.CompensationIntermediateData;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-09-12
 * @description 监管使用赔付中间表
 */
public interface CompensationIntermediateDataMapper {

    /**
     * 插入赔付中间表
     * @param data
     * @return
     */
    int insert(CompensationIntermediateData data);

    List<CompensationIntermediateData> queryListByCondition(CompensationIntermediateData param);

    void batchInsert(List<CompensationIntermediateData> compensationIntermediateDataList);
}
