package com.paic.ncbs.claim.model.vo.user;

import com.paic.ncbs.claim.model.dto.user.ReceiverConfigDTO;

import java.util.List;

public class ReceiverConfigVO{
    private String idClmsReceiverConfig;
    private String deptCode;
    private String deptName;
    private String node;
    private String nodeName;
    private String amountScope;
    private String amountScopeName;
    private List<ReceiverVO> receiverList;
    private List<ReceiverVO> copyList;

    public ReceiverConfigVO() {

    }

    public ReceiverConfigVO(ReceiverConfigDTO dto) {
        this.idClmsReceiverConfig = dto.getIdClmsReceiverConfig();
        this.deptCode = dto.getDeptCode();
        this.deptName = dto.getDeptName();
        this.node = dto.getNode();
        this.amountScope = dto.getAmountScope();

    }
    public String getIdClmsReceiverConfig() {
        return idClmsReceiverConfig;
    }

    public void setIdClmsReceiverConfig(String idClmsReceiverConfig) {
        this.idClmsReceiverConfig = idClmsReceiverConfig;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getNode() {
        return node;
    }

    public void setNode(String node) {
        this.node = node;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getAmountScope() {
        return amountScope;
    }

    public void setAmountScope(String amountScope) {
        this.amountScope = amountScope;
    }

    public String getAmountScopeName() {
        return amountScopeName;
    }

    public void setAmountScopeName(String amountScopeName) {
        this.amountScopeName = amountScopeName;
    }

    public List<ReceiverVO> getReceiverList() {
        return receiverList;
    }

    public void setReceiverList(List<ReceiverVO> receiverList) {
        this.receiverList = receiverList;
    }

    public List<ReceiverVO> getCopyList() {
        return copyList;
    }

    public void setCopyList(List<ReceiverVO> copyList) {
        this.copyList = copyList;
    }
}
