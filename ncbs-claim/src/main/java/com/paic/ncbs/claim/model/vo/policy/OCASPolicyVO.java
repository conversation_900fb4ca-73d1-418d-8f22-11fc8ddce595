package com.paic.ncbs.claim.model.vo.policy;


import lombok.Data;

/**
 * <AUTHOR>
 *
 * 对接保全 返回接口数据
 */
@Data
public class OCASPolicyVO {

    private String policyNo;
    
    private String basePolicyNo;
    
    private String departmentCode;
    
    private String productClass;
    
    private String productCode;
    
    private String applicantName;
    
    private String dateInsuranceBegin;

    private String dateInsuranceEnd;

    private String riskPersonName;

    private String productKind;

    private String isElecSubPolicyNo;

    private String productName;

    private Boolean switchPolicyFormatEndorse;

}
