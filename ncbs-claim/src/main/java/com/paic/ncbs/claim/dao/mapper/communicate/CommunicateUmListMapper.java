package com.paic.ncbs.claim.dao.mapper.communicate;

import com.paic.ncbs.claim.model.dto.communicate.CommunicateUmListDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface CommunicateUmListMapper {

	List<CommunicateUmListDTO> getCommunicateUmList(@Param("departmentCodeAndName") String departmentCodeAndName);

	List<String> getCommunicateUmListByDeptCode(@Param("departmentCode") String departmentCode);

	List<String> getCommunicateUmListByParam(CommunicateUmListDTO communicateUmListDTO);

}
