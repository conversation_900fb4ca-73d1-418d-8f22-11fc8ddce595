package com.paic.ncbs.claim.dao.mapper.endcase;

import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import com.paic.ncbs.claim.model.vo.other.ResultAmountVO;
import com.paic.ncbs.claim.model.dto.endcase.CaseClassDTO;
import com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface CaseClassMapper {

     void saveCaseClass(CaseClassDTO caseClassDTO);

     void removeCaseClass(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes,
                                @Param("taskId") String taskId,@Param("caseSubClass") String caseSubClass);

    void updateEffective(CaseClassDTO caseClassDTO);

    default List<String> getCaseClassList(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes,
                                          @Param("taskId") String taskId) {
        return getCaseClassList(reportNo, caseTimes, taskId, null);
    }

     List<String> getCaseClassList(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes,
                                         @Param("taskId") String taskId, @Param("idAhcsAdditionalSurvey") String idAhcsAdditionalSurvey);

     List<String> getCaseClassListByAddSurveyId(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes,
                                                      @Param("taskId") String taskId, @Param("idAhcsAdditionalSurvey") String idAhcsAdditionalSurvey);

     List<String> getCaseClassNameList(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes,
                                             @Param("taskId") String taskId, @Param("status") String status);


    default List<CaseClassDTO> getBigCaseClassList(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes,
                                                   @Param("status") String status, @Param("taskId") String taskId) {
        return getBigCaseClassList(reportNo, caseTimes, status, taskId, null);
    }

     List<CaseClassDTO> getBigCaseClassList(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes,
                                                  @Param("status") String status, @Param("taskId") String taskId, @Param("idAhcsAdditionalSurvey") String idAhcsAdditionalSurvey);

     List<CaseClassDTO> getCaseClassDTOList(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes,
                                                  @Param("taskId") String taskId);

     List<CaseClassDTO> getCaseClassListByTasks(@Param("reportNo") String reportNo,
                                                      @Param("caseTimes") int caseTimes, @Param("tacheCodeList") List<String> tacheCodeList);

    default void addCaseClassList(@Param("caseClassList") List<CaseClassDTO> caseClassList,
                                  @Param("caseTimes") int caseTimes, @Param("userId") String userId) {
        addCaseClassList(caseClassList, caseTimes, userId, null);
    }

     void addCaseClassList(@Param("caseClassList") List<CaseClassDTO> caseClassList,
                                 @Param("caseTimes") int caseTimes, @Param("userId") String userId,
                                 @Param("idAhcsAdditionalSurvey") String idAhcsAdditionalSurvey);

     void addCaseClassLists(@Param("caseClassList") List<CaseClassDTO> caseClassList,
                                  @Param("caseTimes") int caseTimes);

    String getInsuredApplyStatus(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes,
                                 @Param("taskId") String taskId);

     List<CaseClassDTO> getAllCaseClassList(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes,
                                                  @Param("taskId") String taskId);

     List<CaseClassDTO> getNewCaseClassList(@Param("reportNo") String reportNo,
                                                  @Param("caseTimes") int caseTimes, @Param("lossObjectNo") String lossObjectNo);

     String getNonCasualtyCaseType(@Param("reportNo") String reportNo,
                                         @Param("caseTimes") Integer caseTimes);

     String getTravelChangeType(@Param("reportNo") String reportNo);

     List<CaseClassDTO> getCaseClassListByRct(@Param("reportNo") String reportNo,
                                                    @Param("caseTimes") int caseTimes, @Param("taskId") String taskId);

     List<String> getCaseClassCodeList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,
                                             @Param("taskId") String taskId);

     List<String> getCaseClassCodeListByTasks(@Param("reportNo") String reportNo,
                                                    @Param("caseTimes") int caseTimes, @Param("tacheCodeList") List<String> tacheCodeList);

     String getCurrentTaskCodeFromCaseClass(@Param("reportNo") String reportNo,
                                                  @Param("caseTimes") int caseTimes, @Param("status") String status);

     List<CaseClassDTO> getCaseClassListByTaches(@Param("reportNo") String reportNo,
                                                       @Param("caseTimes") int caseTimes, @Param("tacheCodeList") List<String> tacheCodeList);

     String getCaseClassTache(@Param("reportNo") String reportNo,
                                    @Param("caseTimes") int caseTimes, @Param("tacheCodeList") List<String> tacheCodeList);

     List<String> getCaseTacheClassCodeList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("status") String status);

     List<CaseClassDTO> getlossResultList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("status") String status);

     List<String> getBigCaseClassName(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes,
                                            @Param("status") String status, @Param("taskId") String taskId);

     List<String> getCaseClassCodeByReportNo(@Param("reportNo") String reportNo,
                                                   @Param("caseTimes") Integer caseTimes, @Param("tacheCodeList") List<String> tacheCodeList);

    ResultAmountVO getTotalAmount(@Param("reportNo") String reportNo);


    List<CaseClassDTO> getByAdditionalSurveyId(@Param("reportNo") String reportNo,
                                               @Param("caseTimes") Integer caseTimes,
                                               @Param("taskId") String taskId,
                                               @Param("idAhcsAdditionalSurvey") String idAhcsAdditionalSurvey);

    String getReportTrackTaskCode(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes, @Param("status") String status, @Param("taskId") String taskId);

    List<String> getCaseSubClassList(String reportNo, int caseTimes, String taskId);

    List<String>  getCaseClassNameListCode(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes,
                             @Param("taskId") String taskId, @Param("status") String status);
    void saveCaseClassList(@Param("caseClassList") List<CaseClassDTO> caseClassList,
                          @Param("caseTimes") int caseTimes, @Param("userId") String userId,
                          @Param("idAhcsAdditionalSurvey") String idAhcsAdditionalSurvey);

    /**
     * 获取案件类别大类
     * @param reportNo
     * @param caseTimes
     * @param taskId
     * @param status
     * @return
     */
    List<String>  getCaseClassParentAll(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes,
                                        @Param("taskId") String taskId, @Param("status") String status);

    void updatePersonEffective(PersonTranceRequestVo personTranceRequestVo);
}
