package com.paic.ncbs.claim.model.vo.duty;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel("航班延误VO")
public class FlightDelayVO {

    @ApiModelProperty("")
    private String moduleCode;

    @ApiModelProperty("航班延误主键")
    private String idAhcsFlightDelay;

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("通道号")
    private String idAhcsChannelProcess;

    @ApiModelProperty("原航班号")
    private String originalFlightNo;

    @ApiModelProperty("计划起飞时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date originalDepartTime;

    @ApiModelProperty("计划到达时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date originalArrivalTime;

    @ApiModelProperty("系统查询航班状态")
    private String queryFlightStatus;

    @ApiModelProperty("出发地")
    private String departPlace;

    @ApiModelProperty("出发地名称")
    private String departPlaceName;

    @ApiModelProperty("目的地")
    private String arrivalPlace;

    @ApiModelProperty("客户号")
    private String arrivalPlaceName;

    @ApiModelProperty("客票号")
    private String ticketNo;

    @ApiModelProperty("系统查询客票状态")
    private String queryTicketStatus;

    @ApiModelProperty("航班状态")
    private String flightStatus;

    @ApiModelProperty("")
    private String[] flightStatusArr;

    @ApiModelProperty("实际航班号")
    private String realFlightNo;

    @ApiModelProperty("实际起飞时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date realDepartTime;

    @ApiModelProperty("实际到达时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date realArrivalTime;

    /**
     * 起飞时间 DELAY_CALC_01 到达时间 DELAY_CALC_02 皆可 DELAY_CALC_03
     */
    @ApiModelProperty("延误计算方式")
    private String calculateMode;

    @ApiModelProperty("延误时长")
    private BigDecimal delayDuration;

    @ApiModelProperty("理赔申请人证件号")
    private String certificateNo;

    @ApiModelProperty("理赔申请人证件类型")
    private String certificateType;

    @ApiModelProperty("客户姓名")
    private String clientName;

    @ApiModelProperty("延误时长单位")
    private String delayDurationUnit;

    /**
     *累计赔 PAYPATTERN_01  择高赔 PAYPATTERN_02  部分累计部分择高 PAYPATTERN_03
     */
    @ApiModelProperty("赔付方式")
    private String payPattern;

    @ApiModelProperty("定损金额")
    private BigDecimal lossAmount;

    @ApiModelProperty("")
    private String showLossAmount;

    @ApiModelProperty("环节号")
    private String taskCode;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("理赔类型(1:全自动理赔,2:客户报案后自动理赔)")
    private String claimsType;

    @ApiModelProperty("机票是否使用(YES: 使用,NO 未使用)")
    private String isTicketUsed;

    @ApiModelProperty("是否购票(YES:已购票,NO:未购票)")
    private String isBuyTicket;

    @ApiModelProperty("机票总价")
    private BigDecimal totalTicketPrice;

    @ApiModelProperty("机票票价")
    private BigDecimal ticketPrice;

    @ApiModelProperty("机建费")
    private BigDecimal machineBuildingFee;

    @ApiModelProperty("航班状态(航旅纵横)")
    private String flightTravelStatus;

    @ApiModelProperty("是否有替代航班(Y-是;N-否)")
    private String isReplaceFlight;

    @ApiModelProperty("计划起飞时间(实际航班)")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date deptFlightTime;

    @ApiModelProperty("原航班行程验证结果(YES-存在;NO-不存在;UNKNOWN-未查询到结果)")
    private String originalTravelVerify;

    @ApiModelProperty("实际航班行程验证结果(YES-存在;NO-不存在;UNKNOWN-未查询到结果)")
    private String realTravelVerify;

    public String getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(String certificateType) {
        this.certificateType = certificateType;
    }

    public String getClaimsType() {
        return claimsType;
    }

    public void setClaimsType(String claimsType) {
        this.claimsType = claimsType;
    }

    public String getIsTicketUsed() {
        return isTicketUsed;
    }

    public void setIsTicketUsed(String isTicketUsed) {
        this.isTicketUsed = isTicketUsed;
    }

    public String getIsBuyTicket() {
        return isBuyTicket;
    }

    public void setIsBuyTicket(String isBuyTicket) {
        this.isBuyTicket = isBuyTicket;
    }

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public String getIdAhcsFlightDelay() {
        return idAhcsFlightDelay;
    }

    public void setIdAhcsFlightDelay(String idAhcsFlightDelay) {
        this.idAhcsFlightDelay = idAhcsFlightDelay;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public Integer getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Integer caseTimes) {
        this.caseTimes = caseTimes;
    }

    public String getIdAhcsChannelProcess() {
        return idAhcsChannelProcess;
    }

    public void setIdAhcsChannelProcess(String idAhcsChannelProcess) {
        this.idAhcsChannelProcess = idAhcsChannelProcess;
    }

    public String getOriginalFlightNo() {
        return originalFlightNo;
    }

    public void setOriginalFlightNo(String originalFlightNo) {
        this.originalFlightNo = originalFlightNo;
    }

    public Date getOriginalDepartTime() {
        return originalDepartTime;
    }

    public void setOriginalDepartTime(Date originalDepartTime) {
        this.originalDepartTime = originalDepartTime;
    }

    public Date getOriginalArrivalTime() {
        return originalArrivalTime;
    }

    public void setOriginalArrivalTime(Date originalArrivalTime) {
        this.originalArrivalTime = originalArrivalTime;
    }

    public String getQueryFlightStatus() {
        return queryFlightStatus;
    }

    public void setQueryFlightStatus(String queryFlightStatus) {
        this.queryFlightStatus = queryFlightStatus;
    }

    public String getDepartPlace() {
        return departPlace;
    }

    public void setDepartPlace(String departPlace) {
        this.departPlace = departPlace;
    }

    public String getArrivalPlace() {
        return arrivalPlace;
    }

    public void setArrivalPlace(String arrivalPlace) {
        this.arrivalPlace = arrivalPlace;
    }

    public String getTicketNo() {
        return ticketNo;
    }

    public void setTicketNo(String ticketNo) {
        this.ticketNo = ticketNo;
    }

    public String getQueryTicketStatus() {
        return queryTicketStatus;
    }

    public void setQueryTicketStatus(String queryTicketStatus) {
        this.queryTicketStatus = queryTicketStatus;
    }

    public String getFlightStatus() {
        return flightStatus;
    }

    public void setFlightStatus(String flightStatus) {
        this.flightStatus = flightStatus;
    }

    public String getRealFlightNo() {
        return realFlightNo;
    }

    public void setRealFlightNo(String realFlightNo) {
        this.realFlightNo = realFlightNo;
    }

    public Date getRealDepartTime() {
        return realDepartTime;
    }

    public void setRealDepartTime(Date realDepartTime) {
        this.realDepartTime = realDepartTime;
    }

    public Date getRealArrivalTime() {
        return realArrivalTime;
    }

    public void setRealArrivalTime(Date realArrivalTime) {
        this.realArrivalTime = realArrivalTime;
    }

    public String getCalculateMode() {
        return calculateMode;
    }

    public void setCalculateMode(String calculateMode) {
        this.calculateMode = calculateMode;
    }

    public BigDecimal getDelayDuration() {
        return delayDuration;
    }

    public void setDelayDuration(BigDecimal delayDuration) {
        this.delayDuration = delayDuration;
    }

    public String getDelayDurationUnit() {
        return delayDurationUnit;
    }

    public void setDelayDurationUnit(String delayDurationUnit) {
        this.delayDurationUnit = delayDurationUnit;
    }

    public String getPayPattern() {
        return payPattern;
    }

    public void setPayPattern(String payPattern) {
        this.payPattern = payPattern;
    }

    public BigDecimal getLossAmount() {
        return lossAmount;
    }

    public void setLossAmount(BigDecimal lossAmount) {
        this.lossAmount = lossAmount;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDepartPlaceName() {
        return departPlaceName;
    }

    public void setDepartPlaceName(String departPlaceName) {
        this.departPlaceName = departPlaceName;
    }

    public String getArrivalPlaceName() {
        return arrivalPlaceName;
    }

    public void setArrivalPlaceName(String arrivalPlaceName) {
        this.arrivalPlaceName = arrivalPlaceName;
    }

    public String[] getFlightStatusArr() {
        return flightStatusArr;
    }

    public void setFlightStatusArr(String[] flightStatusArr) {
        this.flightStatusArr = flightStatusArr;
    }

    public String getShowLossAmount() {
        return showLossAmount;
    }

    public void setShowLossAmount(String showLossAmount) {
        this.showLossAmount = showLossAmount;
    }

    public String getCertificateNo() {
        return certificateNo;
    }

    public void setCertificateNo(String certificateNo) {
        this.certificateNo = certificateNo;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public BigDecimal getTotalTicketPrice() {
        return totalTicketPrice;
    }

    public void setTotalTicketPrice(BigDecimal totalTicketPrice) {
        this.totalTicketPrice = totalTicketPrice;
    }

    public BigDecimal getTicketPrice() {
        return ticketPrice;
    }

    public void setTicketPrice(BigDecimal ticketPrice) {
        this.ticketPrice = ticketPrice;
    }

    public BigDecimal getMachineBuildingFee() {
        return machineBuildingFee;
    }

    public void setMachineBuildingFee(BigDecimal machineBuildingFee) {
        this.machineBuildingFee = machineBuildingFee;
    }

    public String getFlightTravelStatus() {
        return flightTravelStatus;
    }

    public void setFlightTravelStatus(String flightTravelStatus) {
        this.flightTravelStatus = flightTravelStatus;
    }

    public String getIsReplaceFlight() {
        return isReplaceFlight;
    }

    public void setIsReplaceFlight(String isReplaceFlight) {
        this.isReplaceFlight = isReplaceFlight;
    }

    public Date getDeptFlightTime() {
        return deptFlightTime;
    }

    public void setDeptFlightTime(Date deptFlightTime) {
        this.deptFlightTime = deptFlightTime;
    }

    public String getOriginalTravelVerify() {
        return originalTravelVerify;
    }

    public void setOriginalTravelVerify(String originalTravelVerify) {
        this.originalTravelVerify = originalTravelVerify;
    }

    public String getRealTravelVerify() {
        return realTravelVerify;
    }

    public void setRealTravelVerify(String realTravelVerify) {
        this.realTravelVerify = realTravelVerify;
    }


}
