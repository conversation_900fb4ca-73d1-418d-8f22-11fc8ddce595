package com.paic.ncbs.claim.dao.mapper.supplements;

import com.paic.ncbs.claim.dao.entity.supplements.SupplementsMaterialEntity;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * 补材任务记录Mpaper接口
 */
@MapperScan
public interface SupplementsMaterialMapper {

    /**
     * 根据ID查询补材任务信息
     * @param id
     * @return
     */
    SupplementsMaterialEntity selectByPrimaryKey(String id);

    /**
     * 保存
     *
     * @param entity
     */
    void insert(SupplementsMaterialEntity entity);

    /**
     * 更新
     *
     * @param entity
     */
    void update(SupplementsMaterialEntity entity);

    /**
     * 根据reportno，赔付次数，节点，查询下发补材任务次数
     *
     * @param entity
     * @return
     */
    SupplementsMaterialEntity getOneNewEntity(SupplementsMaterialEntity entity);

    List<SupplementsMaterialEntity> getSupplementsMaterial(SupplementsMaterialEntity entity);

    /**
     * 或到当前日期为止15天内未处理的任务数据
     * @param days 天数
     * @return
     */
    List<SupplementsMaterialEntity> getAllDataList(Integer days);


    /**
     * 客户补材查询
     * @param reportNo
     * @param caseTimes
     * @return
     */
    List<SupplementsMaterialEntity> getCustomerSupplements(@Param("reportNo") String reportNo, @Param("caseTimes")Integer caseTimes);

    /**
     *  获取补材任务短信通知列表
     * @param smsType
     * @param days
     * @return
     */
    List<SupplementsMaterialEntity> getSmsNotificationTask(@Param("smsType") String smsType,
                                                           @Param("days") Integer days);

    /***
     * 查询客户补材料完成时间
     * @param reportNo
     * @param caseTimes
     */
    SupplementsMaterialEntity getcompleteDate(String reportNo, int caseTimes);
}
