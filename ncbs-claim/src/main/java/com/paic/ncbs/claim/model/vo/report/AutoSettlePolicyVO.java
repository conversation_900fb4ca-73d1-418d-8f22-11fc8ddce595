package com.paic.ncbs.claim.model.vo.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

public class AutoSettlePolicyVO {

    private String reportNo;

    private Integer caseTimes;

    private String policyNo;

    private String caseNo;

    private String departmentChineseName;

    private String departmentCode;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date insuranceBeginTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date insuranceEndTime;

    private List<AutoSettleDetailVO> autoSettleDetails;


    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getDepartmentChineseName() {
        return departmentChineseName;
    }

    public void setDepartmentChineseName(String departmentChineseName) {
        this.departmentChineseName = departmentChineseName;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public List<AutoSettleDetailVO> getAutoSettleDetails() {
        return autoSettleDetails;
    }

    public void setAutoSettleDetails(List<AutoSettleDetailVO> autoSettleDetails) {
        this.autoSettleDetails = autoSettleDetails;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public Integer getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Integer caseTimes) {
        this.caseTimes = caseTimes;
    }

    public String getCaseNo() {
        return caseNo;
    }

    public void setCaseNo(String caseNo) {
        this.caseNo = caseNo;
    }

    public Date getInsuranceBeginTime() {
        return insuranceBeginTime;
    }

    public void setInsuranceBeginTime(Date insuranceBeginTime) {
        this.insuranceBeginTime = insuranceBeginTime;
    }

    public Date getInsuranceEndTime() {
        return insuranceEndTime;
    }

    public void setInsuranceEndTime(Date insuranceEndTime) {
        this.insuranceEndTime = insuranceEndTime;
    }
}
