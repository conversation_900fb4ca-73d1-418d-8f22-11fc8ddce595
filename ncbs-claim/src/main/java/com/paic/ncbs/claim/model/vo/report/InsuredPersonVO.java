package com.paic.ncbs.claim.model.vo.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("被保人VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InsuredPersonVO {

    @ApiModelProperty("成员姓名")
    private String memberName;

    @ApiModelProperty("性别,F-女，M-男")
    private String gender;

    @ApiModelProperty("年龄")
    private Integer age;
}
