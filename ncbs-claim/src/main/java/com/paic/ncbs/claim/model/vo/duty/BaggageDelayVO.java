package com.paic.ncbs.claim.model.vo.duty;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel("行李延误VO")
public class BaggageDelayVO {

    @ApiModelProperty("")
    private String moduleCode;

    @ApiModelProperty("行李延误主键")
    private String idAhcsBaggageDelay;

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("通道号")
    private String idAhcsChannelProcess;

    @ApiModelProperty("航班号")
    private String flightNo;

    @ApiModelProperty("航班到达时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date arrivalTime;

    @ApiModelProperty("行李签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date signInTime;

    @ApiModelProperty("出发地")
    private String departPlace;

    @ApiModelProperty("目的地")
    private String arrivalPlace;

    @ApiModelProperty("出发地名称")
    private String departPlaceName;

    @ApiModelProperty("目的地名称")
    private String arrivalPlaceName;

    @ApiModelProperty("行李延误时长")
    private BigDecimal delayDuration;

    @ApiModelProperty("行李延误时长单位")
    private String delayDurationUnit;

    @ApiModelProperty("环节号")
    private String taskCode;

    @ApiModelProperty("状态")
    private String status;

    public String getIdAhcsBaggageDelay() {
        return idAhcsBaggageDelay;
    }

    public void setIdAhcsBaggageDelay(String idAhcsBaggageDelay) {
        this.idAhcsBaggageDelay = idAhcsBaggageDelay;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public Integer getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Integer caseTimes) {
        this.caseTimes = caseTimes;
    }

    public String getIdAhcsChannelProcess() {
        return idAhcsChannelProcess;
    }

    public void setIdAhcsChannelProcess(String idAhcsChannelProcess) {
        this.idAhcsChannelProcess = idAhcsChannelProcess;
    }

    public String getFlightNo() {
        return flightNo;
    }

    public void setFlightNo(String flightNo) {
        this.flightNo = flightNo;
    }


    public Date getArrivalTime() {
        return arrivalTime;
    }

    public void setArrivalTime(Date arrivalTime) {
        this.arrivalTime = arrivalTime;
    }

    public Date getSignInTime() {
        return signInTime;
    }

    public void setSignInTime(Date signInTime) {
        this.signInTime = signInTime;
    }

    public String getDepartPlace() {
        return departPlace;
    }

    public void setDepartPlace(String departPlace) {
        this.departPlace = departPlace;
    }

    public String getArrivalPlace() {
        return arrivalPlace;
    }

    public void setArrivalPlace(String arrivalPlace) {
        this.arrivalPlace = arrivalPlace;
    }

    public BigDecimal getDelayDuration() {
        return delayDuration;
    }

    public void setDelayDuration(BigDecimal delayDuration) {
        this.delayDuration = delayDuration;
    }

    public String getDelayDurationUnit() {
        return delayDurationUnit;
    }

    public void setDelayDurationUnit(String delayDurationUnit) {
        this.delayDurationUnit = delayDurationUnit;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public String getDepartPlaceName() {
        return departPlaceName;
    }

    public void setDepartPlaceName(String departPlaceName) {
        this.departPlaceName = departPlaceName;
    }

    public String getArrivalPlaceName() {
        return arrivalPlaceName;
    }

    public void setArrivalPlaceName(String arrivalPlaceName) {
        this.arrivalPlaceName = arrivalPlaceName;
    }

}
