package com.paic.ncbs.claim.dao.entity.user;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.util.Date;

/**
 * 机构部门
 */
public class DepartmentDefineEntity extends EntityDTO {

    private static final long serialVersionUID = 731134248731016326L;

    private String departmentCode;

    private String departmentChineseName;

    private String departmentAbbrName;

    private Short departmentLevel;

    private Date foundDate;

    private String chineseAddress;

    private String postcode;

    private String departmentEnglishName;

    private String englishAddress;

    private String telephone;

    private String fax;

    private String upperDepartmentCode;

    private String linkManCode;

    private String reportBeforeRegister;

    private Date invalidateDate;

    private String departmentMark;

    private String alarmMark;

    private String vehicleReportTelephone;

    private String defaultLiscenseCode;

    private String defaultLiscense;

    private String email;

    private String vehicleLiscenceCodePrefix;

    private String telephoneAreaCode;

    private String rowId;

    private String departmentType;

    private String isVirtual;

    private Date validateDate;

    private String isVip;

    private String districtCode;

    private String isDepartmentNode;

    private String isNewDepartmentNode;

    private String departmentNodeType;

    private String departmentNodeStatus;

    private String uploadGroupsid;

    private Date upgradeDate;

    private String departmentNodeTypeClass;

    private Date departmentNodeStatusDate;

    private String departmentNodeName;

    private String taxRegisterNo;

    private String channelAttribute;

    private String isTownDepartment;

    private String townDepartmentType;

    private String volumeLastyear;

    private String firstYearCommission;

    private String firstPlan;

    private String secondPlan;

    private String thirdPlan;

    private String insuredPlace;

    private String taxRegisterName;

    private String centerOrPeriphery;

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode == null ? null : departmentCode.trim();
    }

    public String getDepartmentChineseName() {
        return departmentChineseName;
    }

    public void setDepartmentChineseName(String departmentChineseName) {
        this.departmentChineseName = departmentChineseName == null ? null : departmentChineseName.trim();
    }

    public String getDepartmentAbbrName() {
        return departmentAbbrName;
    }

    public void setDepartmentAbbrName(String departmentAbbrName) {
        this.departmentAbbrName = departmentAbbrName == null ? null : departmentAbbrName.trim();
    }

    public Short getDepartmentLevel() {
        return departmentLevel;
    }

    public void setDepartmentLevel(Short departmentLevel) {
        this.departmentLevel = departmentLevel;
    }

    public Date getFoundDate() {
        return foundDate;
    }

    public void setFoundDate(Date foundDate) {
        this.foundDate = foundDate;
    }

    public String getChineseAddress() {
        return chineseAddress;
    }

    public void setChineseAddress(String chineseAddress) {
        this.chineseAddress = chineseAddress == null ? null : chineseAddress.trim();
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode == null ? null : postcode.trim();
    }

    public String getDepartmentEnglishName() {
        return departmentEnglishName;
    }

    public void setDepartmentEnglishName(String departmentEnglishName) {
        this.departmentEnglishName = departmentEnglishName == null ? null : departmentEnglishName.trim();
    }

    public String getEnglishAddress() {
        return englishAddress;
    }

    public void setEnglishAddress(String englishAddress) {
        this.englishAddress = englishAddress == null ? null : englishAddress.trim();
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone == null ? null : telephone.trim();
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax == null ? null : fax.trim();
    }

    public String getUpperDepartmentCode() {
        return upperDepartmentCode;
    }

    public void setUpperDepartmentCode(String upperDepartmentCode) {
        this.upperDepartmentCode = upperDepartmentCode == null ? null : upperDepartmentCode.trim();
    }

    public String getLinkManCode() {
        return linkManCode;
    }

    public void setLinkManCode(String linkManCode) {
        this.linkManCode = linkManCode == null ? null : linkManCode.trim();
    }

    public String getReportBeforeRegister() {
        return reportBeforeRegister;
    }

    public void setReportBeforeRegister(String reportBeforeRegister) {
        this.reportBeforeRegister = reportBeforeRegister == null ? null : reportBeforeRegister.trim();
    }

    public Date getInvalidateDate() {
        return invalidateDate;
    }

    public void setInvalidateDate(Date invalidateDate) {
        this.invalidateDate = invalidateDate;
    }

    public String getDepartmentMark() {
        return departmentMark;
    }

    public void setDepartmentMark(String departmentMark) {
        this.departmentMark = departmentMark == null ? null : departmentMark.trim();
    }

    public String getAlarmMark() {
        return alarmMark;
    }

    public void setAlarmMark(String alarmMark) {
        this.alarmMark = alarmMark == null ? null : alarmMark.trim();
    }

    public String getVehicleReportTelephone() {
        return vehicleReportTelephone;
    }

    public void setVehicleReportTelephone(String vehicleReportTelephone) {
        this.vehicleReportTelephone = vehicleReportTelephone == null ? null : vehicleReportTelephone.trim();
    }

    public String getDefaultLiscenseCode() {
        return defaultLiscenseCode;
    }

    public void setDefaultLiscenseCode(String defaultLiscenseCode) {
        this.defaultLiscenseCode = defaultLiscenseCode == null ? null : defaultLiscenseCode.trim();
    }

    public String getDefaultLiscense() {
        return defaultLiscense;
    }

    public void setDefaultLiscense(String defaultLiscense) {
        this.defaultLiscense = defaultLiscense == null ? null : defaultLiscense.trim();
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    public String getVehicleLiscenceCodePrefix() {
        return vehicleLiscenceCodePrefix;
    }

    public void setVehicleLiscenceCodePrefix(String vehicleLiscenceCodePrefix) {
        this.vehicleLiscenceCodePrefix = vehicleLiscenceCodePrefix == null ? null : vehicleLiscenceCodePrefix.trim();
    }

    public String getTelephoneAreaCode() {
        return telephoneAreaCode;
    }

    public void setTelephoneAreaCode(String telephoneAreaCode) {
        this.telephoneAreaCode = telephoneAreaCode == null ? null : telephoneAreaCode.trim();
    }

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }

    public String getDepartmentType() {
        return departmentType;
    }

    public void setDepartmentType(String departmentType) {
        this.departmentType = departmentType == null ? null : departmentType.trim();
    }

    public String getIsVirtual() {
        return isVirtual;
    }

    public void setIsVirtual(String isVirtual) {
        this.isVirtual = isVirtual == null ? null : isVirtual.trim();
    }

    public Date getValidateDate() {
        return validateDate;
    }

    public void setValidateDate(Date validateDate) {
        this.validateDate = validateDate;
    }

    public String getIsVip() {
        return isVip;
    }

    public void setIsVip(String isVip) {
        this.isVip = isVip == null ? null : isVip.trim();
    }

    public String getDistrictCode() {
        return districtCode;
    }

    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode == null ? null : districtCode.trim();
    }

    public String getIsDepartmentNode() {
        return isDepartmentNode;
    }

    public void setIsDepartmentNode(String isDepartmentNode) {
        this.isDepartmentNode = isDepartmentNode == null ? null : isDepartmentNode.trim();
    }

    public String getIsNewDepartmentNode() {
        return isNewDepartmentNode;
    }

    public void setIsNewDepartmentNode(String isNewDepartmentNode) {
        this.isNewDepartmentNode = isNewDepartmentNode == null ? null : isNewDepartmentNode.trim();
    }

    public String getDepartmentNodeType() {
        return departmentNodeType;
    }

    public void setDepartmentNodeType(String departmentNodeType) {
        this.departmentNodeType = departmentNodeType == null ? null : departmentNodeType.trim();
    }

    public String getDepartmentNodeStatus() {
        return departmentNodeStatus;
    }

    public void setDepartmentNodeStatus(String departmentNodeStatus) {
        this.departmentNodeStatus = departmentNodeStatus == null ? null : departmentNodeStatus.trim();
    }

    public String getUploadGroupsid() {
        return uploadGroupsid;
    }

    public void setUploadGroupsid(String uploadGroupsid) {
        this.uploadGroupsid = uploadGroupsid == null ? null : uploadGroupsid.trim();
    }

    public Date getUpgradeDate() {
        return upgradeDate;
    }

    public void setUpgradeDate(Date upgradeDate) {
        this.upgradeDate = upgradeDate;
    }

    public String getDepartmentNodeTypeClass() {
        return departmentNodeTypeClass;
    }

    public void setDepartmentNodeTypeClass(String departmentNodeTypeClass) {
        this.departmentNodeTypeClass = departmentNodeTypeClass == null ? null : departmentNodeTypeClass.trim();
    }

    public Date getDepartmentNodeStatusDate() {
        return departmentNodeStatusDate;
    }

    public void setDepartmentNodeStatusDate(Date departmentNodeStatusDate) {
        this.departmentNodeStatusDate = departmentNodeStatusDate;
    }

    public String getDepartmentNodeName() {
        return departmentNodeName;
    }

    public void setDepartmentNodeName(String departmentNodeName) {
        this.departmentNodeName = departmentNodeName == null ? null : departmentNodeName.trim();
    }

    public String getTaxRegisterNo() {
        return taxRegisterNo;
    }

    public void setTaxRegisterNo(String taxRegisterNo) {
        this.taxRegisterNo = taxRegisterNo == null ? null : taxRegisterNo.trim();
    }

    public String getChannelAttribute() {
        return channelAttribute;
    }

    public void setChannelAttribute(String channelAttribute) {
        this.channelAttribute = channelAttribute == null ? null : channelAttribute.trim();
    }

    public String getIsTownDepartment() {
        return isTownDepartment;
    }

    public void setIsTownDepartment(String isTownDepartment) {
        this.isTownDepartment = isTownDepartment == null ? null : isTownDepartment.trim();
    }

    public String getTownDepartmentType() {
        return townDepartmentType;
    }

    public void setTownDepartmentType(String townDepartmentType) {
        this.townDepartmentType = townDepartmentType == null ? null : townDepartmentType.trim();
    }

    public String getVolumeLastyear() {
        return volumeLastyear;
    }

    public void setVolumeLastyear(String volumeLastyear) {
        this.volumeLastyear = volumeLastyear == null ? null : volumeLastyear.trim();
    }

    public String getFirstYearCommission() {
        return firstYearCommission;
    }

    public void setFirstYearCommission(String firstYearCommission) {
        this.firstYearCommission = firstYearCommission == null ? null : firstYearCommission.trim();
    }

    public String getFirstPlan() {
        return firstPlan;
    }

    public void setFirstPlan(String firstPlan) {
        this.firstPlan = firstPlan == null ? null : firstPlan.trim();
    }

    public String getSecondPlan() {
        return secondPlan;
    }

    public void setSecondPlan(String secondPlan) {
        this.secondPlan = secondPlan == null ? null : secondPlan.trim();
    }

    public String getThirdPlan() {
        return thirdPlan;
    }

    public void setThirdPlan(String thirdPlan) {
        this.thirdPlan = thirdPlan == null ? null : thirdPlan.trim();
    }

    public String getInsuredPlace() {
        return insuredPlace;
    }

    public void setInsuredPlace(String insuredPlace) {
        this.insuredPlace = insuredPlace == null ? null : insuredPlace.trim();
    }

    public String getTaxRegisterName() {
        return taxRegisterName;
    }

    public void setTaxRegisterName(String taxRegisterName) {
        this.taxRegisterName = taxRegisterName == null ? null : taxRegisterName.trim();
    }

    public String getCenterOrPeriphery() {
        return centerOrPeriphery;
    }

    public void setCenterOrPeriphery(String centerOrPeriphery) {
        this.centerOrPeriphery = centerOrPeriphery == null ? null : centerOrPeriphery.trim();
    }

}