package com.paic.ncbs.claim.model.vo.settle;

import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


public class PolicyInfoVO {

	private String policyStatus;

	private String policyNo;


	private String departmentCode;


	private String productCode;


	private String productName;


	private String replyCode;


	private String replyName;


	private String businessType;


	private String socialFlag;


	private BigDecimal totalModalPremium;


	private Date insuranceBeginTime;


	private Date insuranceEndTime;


	private String name;


	private String certificateNo;


	private int dangerousTimes;


	private String professionCode;


	private String channelSourceName;


	private String channelSourceCode;


	private String channelSourceDetailCode;


	private Date birthday;


	private Date applyDate;


	private Date edrEffectiveDate;


	private String mobileTelephone;


	private Date underwriteDate;


	private String agreementDefine;

	private List<PolicyPayDTO> policyPayList;


	private String remark;

	public String getMobileTelephone() {
		return mobileTelephone;
	}

	public void setMobileTelephone(String mobileTelephone) {
		this.mobileTelephone = mobileTelephone;
	}

	public String getChannelSourceDetailCode() {
		return channelSourceDetailCode;
	}

	public void setChannelSourceDetailCode(String channelSourceDetailCode) {
		this.channelSourceDetailCode = channelSourceDetailCode;
	}

	public Date getEdrEffectiveDate() {
		return edrEffectiveDate;
	}

	public void setEdrEffectiveDate(Date edrEffectiveDate) {
		this.edrEffectiveDate = edrEffectiveDate;
	}

	public String getChannelSourceName() {
		return channelSourceName;
	}

	public void setChannelSourceName(String channelSourceName) {
		this.channelSourceName = channelSourceName;
	}

	public String getChannelSourceCode() {
		return channelSourceCode;
	}

	public void setChannelSourceCode(String channelSourceCode) {
		this.channelSourceCode = channelSourceCode;
	}

	public String getProfessionCode() {
		return professionCode;
	}

	public void setProfessionCode(String professionCode) {
		this.professionCode = professionCode;
	}

	public int getDangerousTimes() {
		return dangerousTimes;
	}

	public void setDangerousTimes(int dangerousTimes) {
		this.dangerousTimes = dangerousTimes;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public Date getInsuranceBeginTime() {
		return insuranceBeginTime;
	}

	public void setInsuranceBeginTime(Date insuranceBeginTime) {
		this.insuranceBeginTime = insuranceBeginTime;
	}

	public Date getInsuranceEndTime() {
		return insuranceEndTime;
	}

	public void setInsuranceEndTime(Date insuranceEndTime) {
		this.insuranceEndTime = insuranceEndTime;
	}

	public BigDecimal getTotalModalPremium() {
		return totalModalPremium;
	}

	public void setTotalModalPremium(BigDecimal totalModalPremium) {
		this.totalModalPremium = totalModalPremium;
	}

	public String getSocialFlag() {
		return socialFlag;
	}

	public void setSocialFlag(String socialFlag) {
		this.socialFlag = socialFlag;
	}

	public String getPolicyNo() {
		return policyNo;
	}

	public void setPolicyNo(String policyNo) {
		this.policyNo = policyNo;
	}

	public String getPolicyStatus() {
		return policyStatus;
	}

	public void setPolicyStatus(String policyStatus) {
		this.policyStatus = policyStatus;
	}

	public String getDepartmentCode() {
		return departmentCode;
	}

	public void setDepartmentCode(String departmentCode) {
		this.departmentCode = departmentCode;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getReplyCode() {
		return replyCode;
	}

	public void setReplyCode(String replyCode) {
		this.replyCode = replyCode;
	}

	public String getReplyName() {
		return replyName;
	}

	public void setReplyName(String replyName) {
		this.replyName = replyName;
	}

	public String getBusinessType() {
		return businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

	public Date getBirthday() {
		return birthday;
	}

	public void setBirthday(Date birthday) {
		this.birthday = birthday;
	}

	public Date getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}

	public Date getUnderwriteDate() {
		return underwriteDate;
	}

	public void setUnderwriteDate(Date underwriteDate) {
		this.underwriteDate = underwriteDate;
	}

	public String getAgreementDefine() {
		return agreementDefine;
	}

	public void setAgreementDefine(String agreementDefine) {
		this.agreementDefine = agreementDefine;
	}

	public List<PolicyPayDTO> getPolicyPayList() {
		return policyPayList;
	}

	public void setPolicyPayList(List<PolicyPayDTO> policyPayList) {
		this.policyPayList = policyPayList;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
}
