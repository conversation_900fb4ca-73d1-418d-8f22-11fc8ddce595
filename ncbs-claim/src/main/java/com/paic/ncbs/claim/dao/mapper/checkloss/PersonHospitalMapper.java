package com.paic.ncbs.claim.dao.mapper.checkloss;


import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.duty.PersonHospitalDTO;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface PersonHospitalMapper extends BaseDao<PersonHospitalDTO> {
	public void addPersonHospitalList(List<PersonHospitalDTO> personHospitalList);

	public void removePersonHospital(PersonHospitalDTO personHospitalDTO);

	public void modifyPersonHospitalList(List<PersonHospitalDTO> personHospitalList);

	public List<PersonHospitalDTO> getPersonHospitalList(PersonHospitalDTO personHospitalDTO);

	 
	PersonHospitalDTO getPersonHospital(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("taskId") String taskId);

	 
	public List<PersonHospitalDTO> getPersonHospitalByIdAhcsChannelProcess(@Param("idAhcsChannelProcess") String idAhcsChannelProcess,@Param("taskId") String taskId,@Param("status") String status);
	
	 
	public void removePersonHospitalByIdAhcsChannelProcess(@Param("idAhcsChannelProcess") String idAhcsChannelProcess,@Param("taskId") String taskId);

	public int getPersonHospitalNumber(@Param("reportNo") String reportNo,@Param("hospitalizationNumber") String hospitalizationNumber);

	 
	public String getHospitalCode(@Param("reportNo")String reportNo, @Param("caseTimes") Integer caseTimes);
	
	 
	public String getHospitalName(@Param("reportNo")String reportNo, @Param("caseTimes") Integer caseTimes);
	
	 
	public List<PersonHospitalDTO> getPersonHospitals(@Param("idAhcsChannelProcess") String idAhcsChannelProcess,
			@Param("taskId") String taskId, @Param("status") String status);

	 
	public PersonHospitalDTO getHospitalInfoByReportNo(@Param("reportNo")String reportNo, @Param("caseTimes") Integer caseTimes);

	/**
	 * 案件重开，数据拷贝
	 * @param dto
	 */
	void copyForCaseReopen(CaseReopenCopyDTO dto);
}
