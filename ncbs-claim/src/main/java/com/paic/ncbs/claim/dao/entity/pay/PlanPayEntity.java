package com.paic.ncbs.claim.dao.entity.pay;


import com.paic.ncbs.claim.common.constant.ModelConsts;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 险种赔付
 */
@Getter
@Setter
public class PlanPayEntity extends EntityDTO {
    private static final long serialVersionUID = 1262397583511962490L;
    /** 主键(关联险种主键) */
    private String idClmPlanPay;
    /** 赔案号 */
    private String caseNo;
    /** 赔付次数 */
    private Integer caseTimes;
    /** 赔付类型(理算支付类型)（1-赔付 2-预陪 3-垫付 4-追偿 5-垫付转回 6-代位追偿 7-预付 8-营改增） */
    private String claimType = ModelConsts.CLAIM_TYPE_PAY;
    /** 预陪/垫付/追偿 次数 */
    private Integer subTimes;
    /** 赔付批次号 */
    private String idClmBatch;
    /** 险种代码 */
    private String planCode;
    /** 险种赔付/预陪/垫付/追偿金额 */
    private BigDecimal planPayAmount;
    /** 拒赔/注销金额 -- 新加应该未使用 */
    private BigDecimal refuseAmount;
    /** 冲减金额 -- 新加应该未使用 */
    private BigDecimal writeoffAmount;
    /** 仲裁费 */
    private BigDecimal arbitrateFee;
    /** 诉讼费 */
    private BigDecimal lawsuitFee;
    /** 律师费 */
    private BigDecimal lawyerFee;
    /** 检验费 -- 新加应该未使用 */
    private BigDecimal checkFee;
    /** 执行费 -- 新加应该未使用 */
    private BigDecimal executeFee;
    /** 公估费 -- 新加应该未使用 */
    private BigDecimal evaluationFee;
    /** 标的id -- 新加应该未使用 */
    private String idPlyRiskProperty;
    /** 减损费用/奖励费（意健险理赔） -- 新加应该未使用 */
    private BigDecimal decreaseFee;
    /** 备注 -- 新加应该未使用 */
    private String remark;
    /** 本次赔付垫支付转回金额 -- 新加应该未使用  */
    private BigDecimal advanceReturnAmount;
    /** 超限金额 -- 新加应该未使用  */
    private BigDecimal consultAmount;
    /** 数据来源，区分理赔各子领域以及其新老数据，默认n代表新车险理赔数据，c代码原车险理赔数据，np代表新财产险理赔数据，op代表原财产险理赔数据，a代表新农险数据 */
    private String migrateFrom;
    /** 追偿费用支出 -- 新加应该未使用  */
    private BigDecimal chaseFeeOut;
    /** 查勘费 -- 新加应该未使用  */
    private BigDecimal surveyFee;
    /** 专家鉴定费 -- 新加应该未使用  */
    private BigDecimal mavinAppraisalFee;
    /** 检验费 -- 新加应该未使用  */
    private BigDecimal inspectFee;
    /** 调查取证费用 -- 新加应该未使用  */
    private BigDecimal inquiryEvidenceFee;
    /** 其他 */
    private BigDecimal otherFee;
    /** 咨询费 -- 新加应该未使用 */
    private BigDecimal consultFee;
    /** 租车费 -- 新加应该未使用 */
    private BigDecimal carRentalFee;
    /** 检测费 -- 新加应该未使用 */
    private BigDecimal detectFee;
    /** 差旅费 -- 新加应该未使用 */
    private BigDecimal travelFee;
    /** 代位追偿金额 -- 新加应该未使用 */
    private BigDecimal subrogationAmount;
    /** 币种(01-人民币 02-港币 03-美元) -- 新加应该未使用  */
    private String currencyCode;
    /** 罚息收入 -- 新加应该未使用 */
    private BigDecimal amercement;
    /** 追偿费用转回 -- 新加应该未使用 */
    private BigDecimal chaseFeeBack;
    /** 查勘补助费 -- 新加应该未使用 */
    private BigDecimal surveySubsidyFee;
    /** 公估费(外包) -- 新加应该未使用 */
    private BigDecimal evaluationOutFee;
    /** 咨询费(对公) -- 新加应该未使用 */
    private BigDecimal consultPubFee;
    /** 国外代理费 -- 新加应该未使用 */
    private BigDecimal agencyFee;
    /** 公证取证费 -- 新加应该未使用 */
    private BigDecimal notarialFee;
    /** 前置调查费 -- 新加应该未使用 */
    private BigDecimal preInvestigateFee;
    /** 归档时间 -- 新加应该未使用 */
    private Date archiveDate;
    /** 删除时间 -- 新加应该未使用 */
    private Date deletedTime;
}
