package com.paic.ncbs.claim.model.vo.pet;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

public class PetInjureExVO {
    private String treatmentScheme;
    private String treatmentOperation;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date treatmentStartDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date treatmentEndDate;
    private BigDecimal treatmentDays;
    private String petHosProvinceCode;
    private String petHosCityCode;
    private String petHosCountryCode;
    private String petHosCode;
    private String petHosName;
    private BigDecimal treatmentAmount;

    public String getTreatmentScheme() {
        return treatmentScheme;
    }

    public void setTreatmentScheme(String treatmentScheme) {
        this.treatmentScheme = treatmentScheme;
    }

    public String getTreatmentOperation() {
        return treatmentOperation;
    }

    public void setTreatmentOperation(String treatmentOperation) {
        this.treatmentOperation = treatmentOperation;
    }

    public Date getTreatmentStartDate() {
        return treatmentStartDate;
    }

    public void setTreatmentStartDate(Date treatmentStartDate) {
        this.treatmentStartDate = treatmentStartDate;
    }

    public Date getTreatmentEndDate() {
        return treatmentEndDate;
    }

    public void setTreatmentEndDate(Date treatmentEndDate) {
        this.treatmentEndDate = treatmentEndDate;
    }

    public BigDecimal getTreatmentDays() {
        return treatmentDays;
    }

    public void setTreatmentDays(BigDecimal treatmentDays) {
        this.treatmentDays = treatmentDays;
    }

    public String getPetHosProvinceCode() {
        return petHosProvinceCode;
    }

    public void setPetHosProvinceCode(String petHosProvinceCode) {
        this.petHosProvinceCode = petHosProvinceCode;
    }

    public String getPetHosCityCode() {
        return petHosCityCode;
    }

    public void setPetHosCityCode(String petHosCityCode) {
        this.petHosCityCode = petHosCityCode;
    }

    public String getPetHosCountryCode() {
        return petHosCountryCode;
    }

    public void setPetHosCountryCode(String petHosCountryCode) {
        this.petHosCountryCode = petHosCountryCode;
    }

    public String getPetHosCode() {
        return petHosCode;
    }

    public void setPetHosCode(String petHosCode) {
        this.petHosCode = petHosCode;
    }

    public String getPetHosName() {
        return petHosName;
    }

    public void setPetHosName(String petHosName) {
        this.petHosName = petHosName;
    }

    public BigDecimal getTreatmentAmount() {
        return treatmentAmount;
    }

    public void setTreatmentAmount(BigDecimal treatmentAmount) {
        this.treatmentAmount = treatmentAmount;
    }
}
