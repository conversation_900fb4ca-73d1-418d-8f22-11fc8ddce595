package com.paic.ncbs.claim.dao.entity.clms;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import lombok.Data;

import java.util.Date;

/**
 * 函件信息业务对象
 */
@Data
public class ClmsSecondUwLetterDTO extends EntityDTO {
    /**
     * 主键
     */
    private String id;
    /**
     * 报案号
     */
    private String reportNo;
    /**
     * 赔付次数
     */
    private Integer caseTimes;
    /**
     * 函件url
     */
    private String fileId;

    /**
     * 函件类型 ：核保函、其他函件
     */
    private String fileType;

    /**
     * 函件id
     */
    private String idLetterInfo;

    /**
     * 理赔二核申请表主键
     */
    private String idClmsSecondUnderwriting;
    /**
     * 回销结论：01-同意，02-不同意
     */
    private String letterConclusion;

    /**
     * 回销说明
     */
    private String letterExplain;

    /**
     * 回销签署文件id，同意时必填
     */
    private String uploadFileId;
    /**
     * 函件回销人
     */
    private String letterCancelOperator;
    /**
     * 函件下发日期
     */
    private Date letterSendDate;
    /**
     * 函件回销日期
     */
    private Date letterCancelDate;


}
