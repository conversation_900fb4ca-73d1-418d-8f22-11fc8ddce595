package com.paic.ncbs.claim.model.vo.duty;

import com.paic.ncbs.claim.model.dto.settle.MedicalRuleConfigDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("DutyAttrConfigVO")
public class DutyAttrConfigVO {

	@ApiModelProperty("")
	private String queryDepartmentCode;

	@ApiModelProperty("")
	private String dimension;

	@ApiModelProperty("保单号")
	private String policyNo;

	@ApiModelProperty("")
	private String queryCondition;

	@ApiModelProperty("产品代码")
	private String productCode;

	@ApiModelProperty("产品大类:01车,02个财,03个意")
	private String productClass;

	@ApiModelProperty("层级号")
	private String subjectId;

	@ApiModelProperty("")
	private String version;

	@ApiModelProperty("套餐编码")
	private String packageCode;

	@ApiModelProperty("险种编码")
	private String planCode;

	@ApiModelProperty("责任编码")
	private String dutyCode;

	@ApiModelProperty("产品大类列表")
	private List<String> productClassList;

	@ApiModelProperty("套餐编码列表")
	private List<String> packageCodeList;

	@ApiModelProperty("")
	private String settingDutyAttrFlag;

	@ApiModelProperty("当前页")
	private int currentPage;

	@ApiModelProperty("每页数据显示数量")
	private int perPageSize;

	@ApiModelProperty("产品名称")
	private String productName;

	@ApiModelProperty("")
	private String idMarketproductPlanDutyRel;

	@ApiModelProperty("")
	private String idPackagePlanDutyRel;

	@ApiModelProperty("")
	private String idPackageInfo;

	@ApiModelProperty("")
	private List<DutyAttrVO> dutyAttrVOList;

	@ApiModelProperty("")
	private List<String> subjectIdList;

	@ApiModelProperty("")
	private String queryType;

	@ApiModelProperty(value = "备注")
	private String remarks;

	@ApiModelProperty("")
	private List<String> selectedAttrTypeList;

	@ApiModelProperty("")
	private MedicalRuleConfigDTO medicalRuleConfig;

	@ApiModelProperty(value = "承保机构编码")
  	private String departmentCode;

	@ApiModelProperty("")
  	private String medicalRuleContent;

	@ApiModelProperty("")
  	private String isDepartSetting;

  	public String getQueryDepartmentCode() {
		return queryDepartmentCode;
	}
	public void setQueryDepartmentCode(String queryDepartmentCode) {
		this.queryDepartmentCode = queryDepartmentCode;
	}

	public String getDimension() {
		return dimension;
	}

	public void setDimension(String dimension) {
		this.dimension = dimension;
	}

	public String getPolicyNo() {
		return policyNo;
	}

	public void setPolicyNo(String policyNo) {
		this.policyNo = policyNo;
	}
	
	public String getQueryCondition() {
		return queryCondition;
	}

	public void setQueryCondition(String queryCondition) {
		this.queryCondition = queryCondition;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public String getProductClass() {
		return productClass;
	}

	public void setProductClass(String productClass) {
		this.productClass = productClass;
	}

	public String getSubjectId() {
		return subjectId;
	}

	public void setSubjectId(String subjectId) {
		this.subjectId = subjectId;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getPackageCode() {
		return packageCode;
	}

	public void setPackageCode(String packageCode) {
		this.packageCode = packageCode;
	}

	public String getPlanCode() {
		return planCode;
	}

	public void setPlanCode(String planCode) {
		this.planCode = planCode;
	}

	public String getDutyCode() {
		return dutyCode;
	}

	public void setDutyCode(String dutyCode) {
		this.dutyCode = dutyCode;
	}

	public List<String> getProductClassList() {
		return productClassList;
	}

	public void setProductClassList(List<String> productClassList) {
		this.productClassList = productClassList;
	}

	public String getSettingDutyAttrFlag() {
		return settingDutyAttrFlag;
	}

	public void setSettingDutyAttrFlag(String settingDutyAttrFlag) {
		this.settingDutyAttrFlag = settingDutyAttrFlag;
	}

	public int getCurrentPage() {
		return currentPage;
	}

	public void setCurrentPage(int currentPage) {
		this.currentPage = currentPage;
	}

	public int getPerPageSize() {
		return perPageSize;
	}

	public void setPerPageSize(int perPageSize) {
		this.perPageSize = perPageSize;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getIdMarketproductPlanDutyRel() {
		return idMarketproductPlanDutyRel;
	}

	public void setIdMarketproductPlanDutyRel(String idMarketproductPlanDutyRel) {
		this.idMarketproductPlanDutyRel = idMarketproductPlanDutyRel;
	}

	public String getIdPackagePlanDutyRel() {
		return idPackagePlanDutyRel;
	}

	public void setIdPackagePlanDutyRel(String idPackagePlanDutyRel) {
		this.idPackagePlanDutyRel = idPackagePlanDutyRel;
	}

	public String getIdPackageInfo() {
		return idPackageInfo;
	}

	public void setIdPackageInfo(String idPackageInfo) {
		this.idPackageInfo = idPackageInfo;
	}

	public List<DutyAttrVO> getDutyAttrVOList() {
		return dutyAttrVOList;
	}

	public void setDutyAttrVOList(List<DutyAttrVO> dutyAttrVOList) {
		this.dutyAttrVOList = dutyAttrVOList;
	}

	public List<String> getPackageCodeList() {
		return packageCodeList;
	}

	public void setPackageCodeList(List<String> packageCodeList) {
		this.packageCodeList = packageCodeList;
	}

	public List<String> getSubjectIdList() {
		return subjectIdList;
	}

	public void setSubjectIdList(List<String> subjectIdList) {
		this.subjectIdList = subjectIdList;
	}

	public String getQueryType() {
		return queryType;
	}

	public void setQueryType(String queryType) {
		this.queryType = queryType;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public List<String> getSelectedAttrTypeList() {
		return selectedAttrTypeList;
	}

	public void setSelectedAttrTypeList(List<String> selectedAttrTypeList) {
		this.selectedAttrTypeList = selectedAttrTypeList;
	}

	public MedicalRuleConfigDTO getMedicalRuleConfig() {
		return medicalRuleConfig;
	}

	public void setMedicalRuleConfig(MedicalRuleConfigDTO medicalRuleConfig) {
		this.medicalRuleConfig = medicalRuleConfig;
	}

	public String getDepartmentCode() {
		return departmentCode;
	}

	public void setDepartmentCode(String departmentCode) {
		this.departmentCode = departmentCode;
	}
	public String getMedicalRuleContent() {
		return medicalRuleContent;
	}
	public void setMedicalRuleContent(String medicalRuleContent) {
		this.medicalRuleContent = medicalRuleContent;
	}
	public String getIsDepartSetting() {
		return isDepartSetting;
	}
	public void setIsDepartSetting(String isDepartSetting) {
		this.isDepartSetting = isDepartSetting;
	}
}
