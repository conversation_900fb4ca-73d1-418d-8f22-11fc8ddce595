package com.paic.ncbs.claim.dao.entity.antimoneylaundering;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 理赔送反洗钱记录表实体类
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class ClmsSendAmlRecordEntity extends EntityDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 报案号
     */
    private String reportNo;

    /**
     * 赔付次数
     */
    private Integer caseTimes;

    /**
     * 流水号
     */
    private String serialNo;

    /**
     * 反洗钱监测标准代码
     */
    private String amlCode;

    /**
     * 客户号
     */
    private String clientNo;

    /**
     * 客户姓名
     */
    private String clientName;

    /**
     * 客户证件号码
     */
    private String clientCertificateNo;

    /**
     * 证件类型
     */
    private String clientCertificateType;

    /**
     * 国籍
     */
    private String nationCode;

    /**
     * 审批结果标识，Y-是 N-否
     */
    private String auditFlag;

    /**
     * 送审说明
     */
    private String submissionInstructions;

    /**
     * 审核人代码
     */
    private String auditorCode;

    /**
     * 审核人名称
     */
    private String auditorName;

    /**
     * 审批时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /**
     * 审批说明
     */
    private String auditDescription;
}
