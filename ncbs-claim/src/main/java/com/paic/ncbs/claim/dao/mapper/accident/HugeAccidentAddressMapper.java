package com.paic.ncbs.claim.dao.mapper.accident;


import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.vo.accident.HugeAccidentAddressVO;
import com.paic.ncbs.claim.model.dto.accident.HugeAccidentAddressDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;


@MapperScan
public interface HugeAccidentAddressMapper extends BaseDao<HugeAccidentAddressDTO> {

	
	
	List<HugeAccidentAddressVO> getHugeAccidentAddressVOsByHugeId(@Param("idAhcsHugeAccidentInfo") String idAhcsHugeAccidentInfo);

}