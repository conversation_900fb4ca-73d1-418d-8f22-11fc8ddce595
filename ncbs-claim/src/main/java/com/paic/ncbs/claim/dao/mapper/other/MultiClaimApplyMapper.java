package com.paic.ncbs.claim.dao.mapper.other;

import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;

@MapperScan
public interface MultiClaimApplyMapper {

    String getMultiClaimPriorityReason(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes);

    int getMultiClaimIngByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    int getApplyTimesByRnCt(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes);

    String getCurrentMultiClaimApplyUm(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes, @Param("applyTimes") int applyTimes);


}
