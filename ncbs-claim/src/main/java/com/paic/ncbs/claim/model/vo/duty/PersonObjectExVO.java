package com.paic.ncbs.claim.model.vo.duty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@ApiModel("人员扩展信息表VO")
public class PersonObjectExVO {
    @ApiModelProperty("人员扩展信息表主键")
    private String idAhcsPersonObjectEx;

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("通道号")
    private String idAhcsChannelProcess;

    @ApiModelProperty("居住省份CODE")
    private String resideProvinceCode;

    @ApiModelProperty("居住城市CODE")
    private String resideCityCode;

    @ApiModelProperty("居住县/区CODE")
    private String resideCountyCode;

    @ApiModelProperty("居住地址")
    private String resideAddress;

    @ApiModelProperty("居住年限")
    private BigDecimal resideYears;

    @ApiModelProperty("工作省份CODE")
    private String workProvinceCode;

    @ApiModelProperty("工作城市CODE")
    private String workCityCode;

    @ApiModelProperty("工作县/区CODE")
    private String workCountyCode;

    @ApiModelProperty("工作地址")
    private String workAddress;

    @ApiModelProperty("单位名称")
    private String companyName;

    @ApiModelProperty("工作年限")
    private BigDecimal workYears;

    @ApiModelProperty("后续治疗方案及费用或其他补充")
    private String followUpTreatmentDesc;

    @ApiModelProperty("状态。1：发送，0：暂存")
    private String status;


    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getIdAhcsPersonObjectEx() {
        return idAhcsPersonObjectEx;
    }

    public void setIdAhcsPersonObjectEx(String idAhcsPersonObjectEx) {
        this.idAhcsPersonObjectEx = idAhcsPersonObjectEx;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public Integer getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Integer caseTimes) {
        this.caseTimes = caseTimes;
    }

    public String getIdAhcsChannelProcess() {
        return idAhcsChannelProcess;
    }

    public void setIdAhcsChannelProcess(String idAhcsChannelProcess) {
        this.idAhcsChannelProcess = idAhcsChannelProcess;
    }

    public String getResideProvinceCode() {
        return resideProvinceCode;
    }

    public void setResideProvinceCode(String resideProvinceCode) {
        this.resideProvinceCode = resideProvinceCode;
    }

    public String getResideCityCode() {
        return resideCityCode;
    }

    public void setResideCityCode(String resideCityCode) {
        this.resideCityCode = resideCityCode;
    }

    public String getResideCountyCode() {
        return resideCountyCode;
    }

    public void setResideCountyCode(String resideCountyCode) {
        this.resideCountyCode = resideCountyCode;
    }

    public String getResideAddress() {
        return resideAddress;
    }

    public void setResideAddress(String resideAddress) {
        this.resideAddress = resideAddress;
    }

    public BigDecimal getResideYears() {
        return resideYears;
    }

    public void setResideYears(BigDecimal resideYears) {
        this.resideYears = resideYears;
    }

    public String getWorkProvinceCode() {
        return workProvinceCode;
    }

    public void setWorkProvinceCode(String workProvinceCode) {
        this.workProvinceCode = workProvinceCode;
    }

    public String getWorkCityCode() {
        return workCityCode;
    }

    public void setWorkCityCode(String workCityCode) {
        this.workCityCode = workCityCode;
    }

    public String getWorkCountyCode() {
        return workCountyCode;
    }

    public void setWorkCountyCode(String workCountyCode) {
        this.workCountyCode = workCountyCode;
    }

    public String getWorkAddress() {
        return workAddress;
    }

    public void setWorkAddress(String workAddress) {
        this.workAddress = workAddress;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public BigDecimal getWorkYears() {
        return workYears;
    }

    public void setWorkYears(BigDecimal workYears) {
        this.workYears = workYears;
    }

    public String getFollowUpTreatmentDesc() {
        return followUpTreatmentDesc;
    }

    public void setFollowUpTreatmentDesc(String followUpTreatmentDesc) {
        this.followUpTreatmentDesc = followUpTreatmentDesc;
    }


}
