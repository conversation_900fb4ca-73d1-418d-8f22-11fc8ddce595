package com.paic.ncbs.claim.dao.mapper.taskdeal;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.model.dto.taskdeal.ClmsTaskConflictDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 流程任务控制规则表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-15
 */
public interface TaskConflictMapper extends BaseMapper<ClmsTaskConflictDTO> {

    public List<ClmsTaskConflictDTO> findByBpmKeyAndOpr(@Param("bpmKey")String bpmKey, @Param("planOperation")String planOperation);

}
