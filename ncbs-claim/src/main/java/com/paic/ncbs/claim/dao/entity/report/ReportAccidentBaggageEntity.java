package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 意键险行李延误信息表
 */
public class ReportAccidentBaggageEntity extends EntityDTO {

    private static final long serialVersionUID = 6113740849350078189L;

    private String idAhcsReportAccidentBag;

    private String reportNo;

    private String flightNo;

    private String overseasOccur;

    private String departurePlace;

    private String destination;

    private String departureProvince;

    private String departureCity;

    private String departureAirport;

    private String departureArea;

    private String departureNation;

    private String destinationProvince;

    private String destinationCity;

    private String destinationAirport;

    private String destinationArea;

    private String destinationNation;

    private Date planArrivalDate;

    private Date signDate;

    private String baggageDelayType;

    private BigDecimal costEstimate;

    private int delayTime;

    public String getIdAhcsReportAccidentBag() {
        return idAhcsReportAccidentBag;
    }

    public void setIdAhcsReportAccidentBag(String idAhcsReportAccidentBag) {
        this.idAhcsReportAccidentBag = idAhcsReportAccidentBag == null ? null : idAhcsReportAccidentBag.trim();
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String getFlightNo() {
        return flightNo;
    }

    public void setFlightNo(String flightNo) {
        this.flightNo = flightNo == null ? null : flightNo.trim();
    }

    public String getOverseasOccur() {
        return overseasOccur;
    }

    public void setOverseasOccur(String overseasOccur) {
        this.overseasOccur = overseasOccur == null ? null : overseasOccur.trim();
    }

    public String getDeparturePlace() {
        return departurePlace;
    }

    public void setDeparturePlace(String departurePlace) {
        this.departurePlace = departurePlace == null ? null : departurePlace.trim();
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination == null ? null : destination.trim();
    }

    public String getDepartureProvince() {
        return departureProvince;
    }

    public void setDepartureProvince(String departureProvince) {
        this.departureProvince = departureProvince == null ? null : departureProvince.trim();
    }

    public String getDepartureCity() {
        return departureCity;
    }

    public void setDepartureCity(String departureCity) {
        this.departureCity = departureCity == null ? null : departureCity.trim();
    }

    public String getDepartureAirport() {
        return departureAirport;
    }

    public void setDepartureAirport(String departureAirport) {
        this.departureAirport = departureAirport == null ? null : departureAirport.trim();
    }

    public String getDepartureArea() {
        return departureArea;
    }

    public void setDepartureArea(String departureArea) {
        this.departureArea = departureArea == null ? null : departureArea.trim();
    }

    public String getDepartureNation() {
        return departureNation;
    }

    public void setDepartureNation(String departureNation) {
        this.departureNation = departureNation == null ? null : departureNation.trim();
    }

    public String getDestinationProvince() {
        return destinationProvince;
    }

    public void setDestinationProvince(String destinationProvince) {
        this.destinationProvince = destinationProvince == null ? null : destinationProvince.trim();
    }

    public String getDestinationCity() {
        return destinationCity;
    }

    public void setDestinationCity(String destinationCity) {
        this.destinationCity = destinationCity == null ? null : destinationCity.trim();
    }

    public String getDestinationAirport() {
        return destinationAirport;
    }

    public void setDestinationAirport(String destinationAirport) {
        this.destinationAirport = destinationAirport == null ? null : destinationAirport.trim();
    }

    public String getDestinationArea() {
        return destinationArea;
    }

    public void setDestinationArea(String destinationArea) {
        this.destinationArea = destinationArea == null ? null : destinationArea.trim();
    }

    public String getDestinationNation() {
        return destinationNation;
    }

    public void setDestinationNation(String destinationNation) {
        this.destinationNation = destinationNation == null ? null : destinationNation.trim();
    }

    public Date getPlanArrivalDate() {
        return planArrivalDate;
    }

    public void setPlanArrivalDate(Date planArrivalDate) {
        this.planArrivalDate = planArrivalDate;
    }

    public Date getSignDate() {
        return signDate;
    }

    public void setSignDate(Date signDate) {
        this.signDate = signDate;
    }

    public String getBaggageDelayType() {
        return baggageDelayType;
    }

    public void setBaggageDelayType(String baggageDelayType) {
        this.baggageDelayType = baggageDelayType == null ? null : baggageDelayType.trim();
    }

    public BigDecimal getCostEstimate() {
        return costEstimate;
    }

    public void setCostEstimate(BigDecimal costEstimate) {
        this.costEstimate = costEstimate;
    }

    public int getDelayTime() {
        return delayTime;
    }

    public void setDelayTime(int delayTime) {
        this.delayTime = delayTime;
    }
}