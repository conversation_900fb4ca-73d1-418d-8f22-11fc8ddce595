package com.paic.ncbs.claim.dao.mapper.checkloss;

import com.paic.ncbs.claim.model.dto.duty.PersonBenefitDTO;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface PersonBenefitMapper {

     void addPersonBenefit(@Param("personBenefitList") List<PersonBenefitDTO> personBenefitList);

     void removePersonBenefit(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("taskId") String taskId, @Param("channelId") String channelId);

     List<PersonBenefitDTO> getPersonBenefit(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("taskId") String taskId, @Param("channelProcessId") String channelProcessId);

     List<PersonBenefitDTO> getPersonBenefitByType(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("benefitType") String benefitType, @Param("taskId") String taskId);

     List<PersonBenefitDTO> getPersonBenefitByTypes(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("benefitTypes") List<String> benefitTypes, @Param("taskId") String taskId, @Param("idAhcsChannelProcess") String idAhcsChannelProcess);

     List<PersonBenefitDTO> getPersonBenefitList(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId);

     void addPersonBenefitList(@Param("personBenefitList") List<PersonBenefitDTO> personBenefitList,
                                     @Param("caseTimes") int caseTimes, @Param("userId") String userId, @Param("channelProcessId") String channelProcessId);

     /**
      * 案件重开，数据拷贝
      * @param dto
      */
     void copyForCaseReopen(CaseReopenCopyDTO dto);
}
