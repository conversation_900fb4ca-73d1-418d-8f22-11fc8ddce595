package com.paic.ncbs.claim.feign.mesh;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.base.util.MeshSendUtils;
import com.paic.ncbs.claim.feign.OmpFeign;
import com.paic.ncbs.claim.model.dto.estimate.RepeatReceiptDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-02
 * @description 公共
 */
@Slf4j
@RefreshScope
@Component
public class OmpRequest {

    private static final String HTTP_REQUEST_PREFIX = "http://ncbs-omp-http";

    /**
     * 调用mesh和feign开关, true:调用mesh
     */
    @Value("${switch.mesh}")
    private Boolean switchMesh;

    @Autowired
    private OmpFeign ompFeign;

    public String checkRepeatReceipt(List<RepeatReceiptDTO> vos){
        String url = HTTP_REQUEST_PREFIX + "/omp/claim/riskcontrolplatform/checkRepeatReceipt";
        if (switchMesh){
            return MeshSendUtils.post(url, JSON.toJSONString(vos));
        }else {
            return ompFeign.checkRepeatReceipt(vos);
        }
    }

}
