package com.paic.ncbs.claim.model.vo.risk;



import com.paic.ncbs.claim.model.dto.other.RmGroupResultDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("规则组执行结果")
public class RmGroupResultVO {
	@ApiModelProperty("规则组执行结果列表")
	private List<RmGroupResultDTO> rmGroupResultDTOList;

	@ApiModelProperty("参数码中文名称")
	private String valueChineseName;
	
	public String getValueChineseName() {
		return valueChineseName;
	}

	public void setValueChineseName(String valueChineseName) {
		this.valueChineseName = valueChineseName;
	}

	public List<RmGroupResultDTO> getRmGroupResultDTOList() {
		return rmGroupResultDTOList;
	}

	public void setRmGroupResultDTOList(List<RmGroupResultDTO> rmGroupResultDTOList) {
		this.rmGroupResultDTOList = rmGroupResultDTOList;
	}

}
