package com.paic.ncbs.claim.dao.mapper.instalment;


import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.dto.instalment.InstalmentFailDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface InstalmentFailMapper {

    List<InstalmentFailDTO> findByReportNoAndCaseTimes(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    List<PaymentItemComData> findAllPaymentItem(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);


}
