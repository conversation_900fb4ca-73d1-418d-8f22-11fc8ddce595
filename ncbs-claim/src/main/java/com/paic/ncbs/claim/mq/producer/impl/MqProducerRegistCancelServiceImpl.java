package com.paic.ncbs.claim.mq.producer.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.report.*;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.NcbsConstant;
import com.paic.ncbs.claim.common.enums.AccidentTypeEnum;
import com.paic.ncbs.claim.common.enums.InsuredApplyTypeEnum;
import com.paic.ncbs.claim.common.enums.RelationType;
import com.paic.ncbs.claim.common.enums.ReportModeEnum;
import com.paic.ncbs.claim.common.util.CodeUtil;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.dao.entity.report.*;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseClassMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.ClmsEstimateRecordMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimatePolicyMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoExMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.mq.RegistCancelDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLRegistDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLRegistRPolicyDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLRegistTextDto;
import com.paic.ncbs.claim.model.dto.mq.swf.SwfFlowMainDto;
import com.paic.ncbs.claim.model.dto.mq.swf.SwfLogStoreDto;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.mq.producer.MqProducerRegistCancelService;
import com.paic.ncbs.claim.service.endcase.CaseBaseService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.report.ReportAccidentExService;
import com.paic.ncbs.claim.service.report.ReportAccidentService;
import com.paic.ncbs.claim.service.report.ReportCustomerInfoService;
import com.paic.ncbs.claim.service.report.ReportInfoService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
//import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class MqProducerRegistCancelServiceImpl implements MqProducerRegistCancelService {
    @Value("${mq.rabbit.exchange.ncbs.claim.root:ncbsClaimExchange}")
    private String ncbsClaimExchange;
    //报案注销环节topic
    @Value("${mq.registCancel.topic}")
    private String registCancelTopic;
    @Autowired
    private ReportCustomerInfoService reportCustomerInfoService;
    @Autowired
    private ReportAccidentService reportAccidentService;
    @Autowired
    private CaseClassMapper caseClassDao;
    @Autowired
    private EstimatePolicyMapper estimatePolicyMapper;
    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;
    @Autowired
    private ReportInfoService reportInfoService;
    @Autowired
    private ReportInfoExMapper reportInfoExMapper;
    @Autowired
    private PolicyInfoMapper policyInfoMapper;
    @Autowired
    private OcasMapper ocasMapper;
    @Autowired
    private ClmsEstimateRecordMapper clmsEstimateRecordMapper;
   /* @Autowired
    private RabbitTemplate rabbitTemplate;*/
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private DepartmentDefineMapper departmentDefineMapper;
    @Autowired
    private CaseBaseService caseBaseService;
    @Autowired
    private ReportAccidentExService reportAccidentExService;
    @Autowired
    private UserInfoService userInfoService;
    @Override
    @Async("asyncPool")
    public void syncProducerRegistCancelLink(String reportNo, Integer caseTimes) {
        // 数据组装
        RegistCancelDto registCancelDto = initProducerRegistCancelDto(reportNo,caseTimes);
        //发送MQ消息
        JSONObject jsonObj = (JSONObject) JSON.toJSON(registCancelDto);
        log.info("syncProducerRegistCancelLink===sendMessage: {}",jsonObj);
       // rabbitTemplate.convertAndSend(ncbsClaimExchange, registCancelTopic, registCancelDto);
    }


    private RegistCancelDto initProducerRegistCancelDto(String reportNo, Integer caseTimes) {
        ReportInfoEntity reportInfo = reportInfoService.getReportInfo(reportNo);
        //报案方式
        reportInfo.setReportMode(ReportModeEnum.getName(reportInfo.getReportMode() == null ? "2" : reportInfo.getReportMode()));
        //查询 报案客户 被保险人 信息表
        ReportCustomerInfoEntity customerInfo = reportCustomerInfoService.getReportCustomerInfoByReportNo(reportNo);
        //查询 事故信息表
        ReportAccidentEntity reportAccident = reportAccidentService.getReportAccident(reportNo);
//        List<EstimatePolicyDTO> policyList = estimatePolicyMapper.getByReportNoAndCaseTimes(reportNo, caseTimes);
        WholeCaseBaseDTO wholeCaseBaseEntity = wholeCaseBaseService.getWholeCaseBase(reportNo,caseTimes);
        List<ReportInfoExEntity> reportInfoExEntityList = reportInfoExMapper.getReportInfoEx(reportNo);
        //查询 意键险报案信息扩展表
        ReportAccidentExEntity reportAccidentEx = reportAccidentExService.getReportAccidentEx(reportNo);
        //查询保单信息表
        List<PolicyInfoDTO> policyInfoDTOList = policyInfoMapper.getPolicyInfoListByReportNo(reportNo);
//        EstimatePolicyDTO estimatePolicyDTO = policyList.get(0);
        PolicyInfoDTO policyInfoDTO = policyInfoDTOList.get(0);
        ReportInfoExEntity reportInfoExEntity = reportInfoExEntityList.get(0);

        RegistCancelDto registCancelDto=new RegistCancelDto();
        // 报案基本信息表
        PrpLRegistDto prpLRegistDto = initPrpLRegistDto(reportNo, reportInfo, customerInfo, reportInfoExEntity,
                reportAccident, policyInfoDTO,wholeCaseBaseEntity,reportAccidentEx,"cancel");
        registCancelDto.setPrpLRegistDto(prpLRegistDto);
        // 报案文字信息表
        PrpLRegistTextDto prpLRegistTextDto = initPrpLRegistTextDto(reportNo);

        registCancelDto.setPrpLRegistTextDto(prpLRegistTextDto);
        // 理赔工作流转储表
        List<CaseBaseEntity> caseBaseInfoList = caseBaseService.getCaseBaseInfoByReportNoAndCasetimes(reportNo, String.valueOf(caseTimes));
        List<SwfLogStoreDto> swfLogStoreDtoList = getSwfLogStoreDto(reportNo, caseTimes, customerInfo, policyInfoDTO,caseBaseInfoList, reportInfo);

        registCancelDto.setSwfLogStoreDtoList(swfLogStoreDtoList);

        List<EstimatePolicyDTO> policyList = estimatePolicyMapper.getByReportNoAndCaseTimes(reportNo, caseTimes);
        List<PrpLRegistRPolicyDto> prpLRegistRPolicyDtoList = getPrpLRegistRPolicyDtoList(reportNo, reportInfo, policyList);
        registCancelDto.setPrpLRegistRPolicyDtoList(prpLRegistRPolicyDtoList);
        SwfFlowMainDto swfFlowMainDto = getSwfFlowMainDto(reportNo, reportInfo, policyInfoDTO);
        registCancelDto.setSwfFlowMainDto(swfFlowMainDto);
        return registCancelDto;
    }

    private List<PrpLRegistRPolicyDto> getPrpLRegistRPolicyDtoList(String reportNo, ReportInfoEntity reportInfo, List<EstimatePolicyDTO> policyList) {
        List<PrpLRegistRPolicyDto> prpLRegistRPolicyDtoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(policyList)){
            for (int i = 0; i < policyList.size(); i++) {
                PrpLRegistRPolicyDto prpLRegistRPolicyDto = new PrpLRegistRPolicyDto();
                prpLRegistRPolicyDto.setRegistNo(reportNo);
                prpLRegistRPolicyDto.setPolicyNo(policyList.get(i).getPolicyNo());
                prpLRegistRPolicyDto.setClaimNo(null);
                Map<String, String> plyBaseInfo = ocasMapper.getPlyBaseInfo(policyList.get(i).getPolicyNo());
                String productCode = MapUtils.getString(plyBaseInfo,"productCode");
                String businessType = MapUtils.getString(plyBaseInfo,"businessType");
                prpLRegistRPolicyDto.setPolicyType(businessType);
                prpLRegistRPolicyDto.setFlowID("");
                prpLRegistRPolicyDto.setRemark(reportInfo.getRemark());
                prpLRegistRPolicyDto.setValidStatus("0");
                prpLRegistRPolicyDto.setRiskCode(CodeUtil.subStringRiskCode(productCode));
                prpLRegistRPolicyDto.setRegistFlag("1");
                prpLRegistRPolicyDto.setRegistCancelDate(new Date());
                prpLRegistRPolicyDto.setCompensateNo(null);
                prpLRegistRPolicyDto.setSerialNo(i+1);
                prpLRegistRPolicyDto.setUpdateTime(new Date());
                prpLRegistRPolicyDtoList.add(prpLRegistRPolicyDto);
            }
        }
        return prpLRegistRPolicyDtoList;
    }

    @SneakyThrows
    private List<SwfLogStoreDto> getSwfLogStoreDto(String reportNo, Integer caseTimes, ReportCustomerInfoEntity customerInfo, PolicyInfoDTO policyInfoDTO,List<CaseBaseEntity> caseBaseInfoList,ReportInfoEntity reportInfo) {
        List<SwfLogStoreDto> swfLogStoreDtoList=new ArrayList<>();
        SwfLogStoreDto swfLogStoreDto=new SwfLogStoreDto();
        TaskInfoDTO taskInfoDTO = taskInfoMapper.findLatestByReportNoAndBpmKey(reportNo, caseTimes, BpmConstants.OC_ZERO_CANCEL_DEPT_AUDIT);
        swfLogStoreDto.setLogNo(1);
        swfLogStoreDto.setModelNo(12);
        swfLogStoreDto.setNodeName("报案");
        swfLogStoreDto.setBusinessNo(reportNo);
        swfLogStoreDto.setHandleDept(taskInfoDTO.getDepartmentCode());
        swfLogStoreDto.setHandlerCode(taskInfoDTO.getAssigner());
        swfLogStoreDto.setHandlerName(taskInfoDTO.getAssigneeName());
        swfLogStoreDto.setFlowInTime(DateUtils.parseToFormatString(new Date(),DateUtils.FULL_DATE_STR));
        swfLogStoreDto.setTimeLimit(0);
        swfLogStoreDto.setHandleTime(DateUtils.parseToFormatString(new Date(),DateUtils.FULL_DATE_STR));
        swfLogStoreDto.setSubmitTime(DateUtils.parseToFormatString(new Date(),DateUtils.FULL_DATE_STR));
        swfLogStoreDto.setPackageID("0");
        swfLogStoreDto.setTaskNo(0);
        swfLogStoreDto.setNodeType("regis");
        swfLogStoreDto.setTitleStr("");
        swfLogStoreDto.setBusinessType("1");
        Map<String, String> productMap = ocasMapper.getPlyBaseInfo(policyInfoDTO.getPolicyNo());
        String productCode = MapUtils.getString(productMap,"productCode");
        swfLogStoreDto.setRiskCode(CodeUtil.subStringRiskCode(productCode));
        swfLogStoreDto.setKeyIn(reportNo);
        swfLogStoreDto.setKeyOut(reportNo);
        swfLogStoreDto.setDeptName(departmentDefineMapper.queryDepartmentNameByDeptCode(reportInfo.getAcceptDepartmentCode()));
        swfLogStoreDto.setMainFlowID("0");
        swfLogStoreDto.setSubFlowID("0");
        swfLogStoreDto.setPosX(0);
        swfLogStoreDto.setPosY(0);
        swfLogStoreDto.setEndFlag("1");
        swfLogStoreDto.setBeforeHandlerCode("");
        swfLogStoreDto.setBeforeHandlerName("");
        swfLogStoreDto.setPolicyNo(policyInfoDTO.getPolicyNo());
        swfLogStoreDto.setComCode(reportInfo.getAcceptDepartmentCode());
        swfLogStoreDto.setRegistNo(reportNo);
        swfLogStoreDto.setInsuredName(customerInfo.getName());
        swfLogStoreDto.setEntrustFlag("1");
        swfLogStoreDto.setEntrustNodeStatus("1");
        swfLogStoreDto.setDamageEndDate(new Date());
        swfLogStoreDto.setDamageStartDate(new Date());
        swfLogStoreDto.setUpdateTime(new Date());
        swfLogStoreDto.setFlowID("0");
        swfLogStoreDto.setNodeStatus("6");
        swfLogStoreDto.setNodeNo(1);
        swfLogStoreDto.setFlowStatus("0");
        swfLogStoreDtoList.add(swfLogStoreDto);

        return swfLogStoreDtoList;
    }

    private SwfFlowMainDto getSwfFlowMainDto(String reportNo, ReportInfoEntity reportInfo, PolicyInfoDTO policyInfoDTO) {
        SwfFlowMainDto swfFlowMainDto = new SwfFlowMainDto();
        swfFlowMainDto.setFlowID("");
        swfFlowMainDto.setFlowName(reportNo);
        swfFlowMainDto.setFlowStatus("0");
        swfFlowMainDto.setPolicyNo(policyInfoDTO.getPolicyNo());
        swfFlowMainDto.setCreatDate(reportInfo.getReportDate());
        swfFlowMainDto.setCloseDate(new Date());
        swfFlowMainDto.setModelNo(12);
        swfFlowMainDto.setStoreFlag("2");
        swfFlowMainDto.setUpdateTime(new Date());
        return swfFlowMainDto;
    }

    private PrpLRegistTextDto initPrpLRegistTextDto(String reportNo) {
        PrpLRegistTextDto prpLRegistTextDto=new PrpLRegistTextDto();
//        出险登记号	REGISTNO	Y
        prpLRegistTextDto.setRegistNo(reportNo);
//        文本类型	TEXTTYPE	Y
        prpLRegistTextDto.setTextType("1");
//        行号	LINENO	Y	内容超过长度自动换行
        prpLRegistTextDto.setLineNo(1);
//        内容	CONTEXT	Y
        prpLRegistTextDto.setContext("报案注销");
//        标志位	FLAG	CY	报案注销这个字段传值
        prpLRegistTextDto.setFlag(null);
//        更新时间	UPDATETIME	CY	只要对表操作就修改updatetime
        prpLRegistTextDto.setUpdateTime(new Date());
        return prpLRegistTextDto;
    }
    @SneakyThrows
    @Override
    public PrpLRegistDto initPrpLRegistDto(String reportNo, ReportInfoEntity reportInfo, ReportCustomerInfoEntity customerInfo, ReportInfoExEntity reportInfoExEntity,
                                           ReportAccidentEntity reportAccident, PolicyInfoDTO policyInfoDTO, WholeCaseBaseDTO wholeCaseBaseEntity,
                                           ReportAccidentExEntity reportAccidentEx,String flag) {
        PrpLRegistDto prpLRegistDto=new PrpLRegistDto();
//        报案号	REGISTNO	Y
        prpLRegistDto.setRegistNo(reportNo);
//        理赔类型	LFLAG	Y
        prpLRegistDto.setlFlag("L");
        String policyNo = policyInfoDTO.getPolicyNo();
        Map<String, String> productMap = ocasMapper.getPlyBaseInfo(policyNo);
        String productCode = MapUtils.getString(productMap,"productCode");

        String riskCode = CodeUtil.subStringRiskCode(productCode);

        BigDecimal recordAmount = clmsEstimateRecordMapper.getLastEstimateRecordAmount(wholeCaseBaseEntity.getReportNo(), wholeCaseBaseEntity.getCaseTimes());
//        险类代码	CLASSCODE	Y
        prpLRegistDto.setClassCode(riskCode.substring(0,2));
//        险种代码	RISKCODE	Y
        prpLRegistDto.setRiskCode(riskCode);
//        保单号码	POLICYNO	Y
        prpLRegistDto.setPolicyNo(policyNo);
//        语种	LANGUAGE	Y C:中文 E:英文
        prpLRegistDto.setLanguage("C");
//        被保险人代码	INSUREDCODE	Y		无明确字段(承保投保时，录入的被保险人人编码)
        String personCode = ocasMapper.getRiskPersonCode(policyNo, customerInfo.getCertificateNo(), customerInfo.getName());
        prpLRegistDto.setInsuredCode(personCode);
//        被保险人名称	INSUREDNAME	Y
        prpLRegistDto.setInsuredName(customerInfo.getName());
//        被保险人地址	INSUREDADDRESS	CY
        prpLRegistDto.setInsuredAddress(null);
//        与出险者关系	CLAUSETYPE	Y
        prpLRegistDto.setClauseType(RelationType.getCode(reportInfoExEntity.getLinkManRelation()));
//        报案日期	REPORTDATE	Y
        SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy/MM/dd");
        Date reportDate = reportInfo.getReportDate();
        prpLRegistDto.setReportDate(formatter1.parse(formatter1.format(reportDate)));
//        报案小时	REPORTHOUR	Y
        SimpleDateFormat formatter = new SimpleDateFormat("HH:mm:ss");
        prpLRegistDto.setReportHour(formatter.format(reportDate));
//        报案人	REPORTORNAME	Y
        prpLRegistDto.setReportOrName(reportInfo.getReporterName());
//        报案形式	REPORTTYPE	Y		枚举？(字典中存在枚举值).
        prpLRegistDto.setReportType("99");
//                联系电话	PHONENUMBER	Y		如果多个联系人能否只传一个(报案时，提出的联系人，存在多个人时，录入负责人电话)
        prpLRegistDto.setPhoneNumber(reportInfo.getReporterCallNo());
//        联系人	LINKERNAME	Y
        prpLRegistDto.setLinkerName(reportInfo.getReporterName());
        Date accidentDate = reportAccident.getAccidentDate();
//        出险日期起	DAMAGESTARTDATE	Y		只有一个时间，是否起止传同一个（是）
        prpLRegistDto.setDamageStartDate(formatter1.parse(formatter1.format(accidentDate)));
//        出险开始小时	DAMAGESTARTHOUR	Y
        prpLRegistDto.setDamageStartHour(formatter.format(accidentDate));
//        出险日期止	DAMAGEENDDATE	Y
        prpLRegistDto.setDamageEndDate(formatter1.parse(formatter1.format(accidentDate)));
//        出险终止小时	DAMAGEENDHOUR	Y
        prpLRegistDto.setDamageEndHour(formatter.format(accidentDate));
//        出险原因代码	DAMAGECODE	Y	见出险原因表	无明确字段(默认其他)
        prpLRegistDto.setDamageCode("1805");
//        出险原因说明	DAMAGENAME	Y
        prpLRegistDto.setDamageName("其他");
        List<String> caseSubClassList = caseClassDao.getCaseClassList(reportNo, wholeCaseBaseEntity.getCaseTimes(), "report1");
        if (CollectionUtils.isNotEmpty(caseSubClassList)) {
            //        出险类型	DAMAGETYPECODE	Y
            prpLRegistDto.setDamageTypeCode(InsuredApplyTypeEnum.getCode(caseSubClassList.get(0)));
            //        出险类型名称	DAMAGETYPENAME	Y
            prpLRegistDto.setDamageTypeName(InsuredApplyTypeEnum.getName(caseSubClassList.get(0)));
        } else {
            //        事故类型代码	DAMAGETYPECODE	Y
            prpLRegistDto.setDamageTypeCode(reportAccident.getAccidentType());
            //        事故类型说明	DAMAGETYPENAME	Y
            prpLRegistDto.setDamageTypeName(reportAccident.getAccidentName());
        }
        // prpLRegist表 addresscode出现地省代码，damageareacode出险地市代码，damageaddresstype出险地区代码
//        出险区域代码	DAMAGEAREACODE	Y不必填		出险地址省市区如何对应？
        prpLRegistDto.setDamageAreaCode(reportAccident.getAccidentCityCode());
//        出险地点分类	DAMAGEADDRESSTYPE	Y不必填
        prpLRegistDto.setDamageAddressType(reportAccident.getAccidentCountyCode());
//        地址编码	ADDRESSCODE	Y不必填
        prpLRegistDto.setAddressCode(reportAccident.getProvinceCode());
//        出险地点	DAMAGEADDRESS	Y	出现地点正常录入汉字
        prpLRegistDto.setDamageAddress(reportAccident.getAccidentPlace());
//        估损币别	ESTICURRENCY	Y	默认CNY	依赖报即立规则
        prpLRegistDto.setEstiCurrency("CNY");
//        估损金额	ESTIMATELOSS	Y
        prpLRegistDto.setEstimateLoss(recordAmount==null?new BigDecimal("1000"):recordAmount);
//        接案员姓名	RECEIVERNAME	Y		无明确字段（报案环节处理人）
        String reportRegisterUm = reportInfo.getReportRegisterUm();
        if ("SYSTEM".equalsIgnoreCase(reportRegisterUm) || "ADMIN".equalsIgnoreCase(reportRegisterUm)){
            reportRegisterUm = NcbsConstant.ZK_ADMIN_UM;
            prpLRegistDto.setReceiverName(NcbsConstant.ZK_ADMIN_NAME);
        } else {
            prpLRegistDto.setReceiverName(userInfoService.getUserNameById(reportRegisterUm));
        }
//        经办人代码	HANDLERCODE	Y	可选择必填	无明确字段(报案环节处理人代码)

        prpLRegistDto.setHandlerCode(reportRegisterUm);
//        归属业务员代码	HANDLER1CODE	Y	可选择必填	无明确字段(报案环节处理人代码)
        prpLRegistDto.setHandler1Code(reportRegisterUm);
//        业务归属机构代码	COMCODE	Y
        prpLRegistDto.setComCode(reportInfo.getAcceptDepartmentCode());
//        计算机输单日期	INPUTDATE	Y	系统自动获取当前日期
        prpLRegistDto.setInputDate(new Date());
//        受理标志	ACCEPTFLAG	Y	默认Y
        prpLRegistDto.setAcceptFlag("Y");
//        是否向别的保险公司投保	REPEATINSUREFLAG	Y	主共保	无明确字段(Y/N/U U表示未知)
        prpLRegistDto.setRepeatInsureFlag("U");
//        赔案类别	CLAIMTYPE	Y		能否默认传一般(可选择性必填)
        prpLRegistDto.setClaimType("0");

        if ("cancel".equals(flag)){
            TaskInfoDTO taskInfoDTO = taskInfoMapper.findLatestByReportNoAndBpmKey(reportNo, wholeCaseBaseEntity.getCaseTimes(), BpmConstants.OC_ZERO_CANCEL_DEPT_AUDIT);
            //  注销/拒赔日期	CANCELDATE	CY	报案注销回写报案表
            prpLRegistDto.setCancelDate(new Date());
            //  注销/拒赔人代码	DEALERCODE	CY		代码是指工号？是的，注销审核人工号
            prpLRegistDto.setDealerCode(taskInfoDTO.getAssigner());
        }

//        备注	REMARK	CY
        prpLRegistDto.setRemark(null);
//        操作员代码	OPERATORCODE	Y		申请人还是审批人？（报案操作人工号）
        prpLRegistDto.setOperatorCode(reportRegisterUm);
//        理赔登记机构	MAKECOM	Y		无明确字段(报案操作人归属机构)
        prpLRegistDto.setMakeCom(reportInfo.getAcceptDepartmentCode());
//                报案人电话	REPORTORPHONENUMBER	Y
        prpLRegistDto.setReportOrPhoneNumber(reportInfo.getReporterCallNo());
//        联系人通讯地址	LINKERADDRESS	CY	意健险报案环节可以选填
        prpLRegistDto.setLinkerAddress(null);
//        未决赔款准备金	ESTIMATEFEE	Y	默认传0.00
        prpLRegistDto.setEstimateFee(recordAmount==null?new BigDecimal("0.00"):recordAmount);
//        报案标志	REPORTFLAG	Y	默认0
        prpLRegistDto.setReportFlag("0");
//        异地赔案标记	ECDEMICFLAG	Y	默认01
        prpLRegistDto.setEcdemicFlag("01");
//        委托赔案标记	ENTRUSTFLAG	Y	默认01
        prpLRegistDto.setEntrustFlag("01");
//        赔付数量	LOSSESNUMBER	Y	默认0.00
        prpLRegistDto.setLossesNumber(new BigDecimal("0.00"));
//        医疗查勘人手机号	CHECKPERSONMOBILE	CY	意健险报案环节选填
        prpLRegistDto.setCheckPersonMobile(null);
//        医疗查勘处理单位	CHECKCOMCODE	CY
        prpLRegistDto.setCheckComCode(null);
//        医疗查勘处理人	CHECKPERSONCODE	CY
        prpLRegistDto.setCheckPersonCode(null);
//        发送短信标志	SENDMESSAGEFLAG	CY
        prpLRegistDto.setSendMessageFlag(null);
//        损伤外部原因	INJURYREASONCODE	CY
        prpLRegistDto.setInjuryReasonCode(wholeCaseBaseEntity.getInjuryReasonCode());
//        案件来源也可表示案件类型	CASETYPE	CY		什么情况必传？来源有哪些？(存在字典)
        prpLRegistDto.setCaseType("EZ02");
//                事故类型代码（意健险）	ACCIDENTTYPECODE	Y

        prpLRegistDto.setAccidentTypeCode(AccidentTypeEnum.getCode(reportAccidentEx.getAccidentType()));
//        事故类型说明	ACCIDENTTYPENAME	Y
        prpLRegistDto.setAccidentTypeName(AccidentTypeEnum.getName(reportAccidentEx.getAccidentType()));
//        是否有人伤标记	INJURYFLAG	Y		人伤传是，非人伤传否？(是)
        prpLRegistDto.setInjuryFlag("1");
//                该案件的报案联系方式是否是官微注册会员	ISOFFICIALANDMICRO	CY		无明确字段（没有就填否）
        prpLRegistDto.setIsOfficialAndMicro(null);
//        更新时间	UPDATETIME	CY	只要对表操作就修改updatetime
        prpLRegistDto.setUpdateTime(new Date());
        return prpLRegistDto;
    }
}
