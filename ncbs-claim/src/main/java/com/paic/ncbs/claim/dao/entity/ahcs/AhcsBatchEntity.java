package com.paic.ncbs.claim.dao.entity.ahcs;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@ApiModel("批量上传返回信息")
@Accessors(chain = true)
public class AhcsBatchEntity extends EntityDTO {

    @ApiModelProperty("批次号")
    private String reportBatchNo;

    @ApiModelProperty("文件名")
    private String fileName;

    @ApiModelProperty("文件上传状态")
    private String upFileStatus;

    @ApiModelProperty("上传记录数")
    private Integer batchUploadNums;

    @ApiModelProperty("操作回显")
    private String operateShow;

    @ApiModelProperty("文件ID")
    private String fileId;

    @ApiModelProperty("文件上传code")
    private String upFileCode;

    @ApiModelProperty("文件上传code描述")
    private String upFileCodeDescribe;
    @ApiModelProperty("文件下载url")
    private String fileUrl;

}