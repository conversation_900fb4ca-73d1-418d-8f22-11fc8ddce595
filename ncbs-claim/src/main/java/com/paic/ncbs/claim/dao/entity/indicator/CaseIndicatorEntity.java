package com.paic.ncbs.claim.dao.entity.indicator;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: justinwu 理赔案件指标表
 * @create 2025/3/7 15:44
 */
@Data
@TableName(value = "clm_case_indicator")
public class CaseIndicatorEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private String id;


    /**
     * 创建人员
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "sys_ctime")
    private Date sysCtime;

    /**
     * 修改人员
     */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(value = "sys_utime")
    private Date sysUtime;

    /**
     * 删除标记
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     * 报案号
     */
    @TableField(value = "report_no")
    private String reportNo;

    /**
     * 赔付次数
     */
    @TableField(value = "case_times")
    private Integer caseTimes;

    /**
     * 指标代码
     */
    @TableField(value = "indicator_code")
    private String indicatorCode;

    /**
     * 指标名称
     */
    @TableField(value = "indicator_name")
    private String indicatorName;

    /**
     * 指标值
     */
    @TableField(value = "indicator_value")
    private String indicatorValue;

    /**
     * 指标单位:M-秒
     */
    @TableField(value = "indicator_unit")
    private String indicatorUnit;

    /**
     * 指标公式
     */
    @TableField(value = "indicator_cal")
    private String indicatorCal;

    /**
     * 计算描述
     */
    @TableField(value = "indicator_cal_des")
    private String indicatorCalDes;

    /**
     * 计算时间
     */
    @TableField(value = "cal_time")
    private Date calTime;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;
}
