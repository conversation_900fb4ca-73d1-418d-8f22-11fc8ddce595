package com.paic.ncbs.claim.model.vo.batch;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
  *
  * @Description 退运险线上批量结案
  * <AUTHOR>
  * @Date 2023/7/13 9:26
  **/
@Data
@ApiModel(description = "退运险线上批量结案request")
public class AutoCloseRequestVO {

    @ApiModelProperty(value = "批量结案信息")
    @Valid
    @NotEmpty(message = "批量结案信息不能为空")
    private List<OnlineBatchAutoClose> batchCloseList;

    @NotBlank(message = "第三方批次号不能为空")
    @ApiModelProperty(value = "第三方批次号")
    private String thirdBatchNo;

    @ApiModelProperty(value = "重开批次")
    private Integer reopenNum;
}
