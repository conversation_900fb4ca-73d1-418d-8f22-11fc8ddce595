package com.paic.ncbs.claim.dao.entity.ahcs;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel("查询具体地址")
public class AdressSearchDto {


    @ApiModelProperty("0-国内;1-国外")
    private String overseasOccur;

    @ApiModelProperty("出险区域")
    private String accidentAreaName;

    @ApiModelProperty("出险省份")
    private String accidentProvinceName;

    @ApiModelProperty("出险城市")
    private String accidentCityName;

    @ApiModelProperty("出险县")
    private String accidentCountyName;

    @ApiModelProperty("出险国家")
    private String accidentNationName;



    @ApiModelProperty("出险区域Code")
    private String accidentAreaCode;

    @ApiModelProperty("出险省份Code")
    private String accidentProvinceCode;

    @ApiModelProperty("出险城市CODE")
    private String accidentCityCode;

    @ApiModelProperty("出险县Code")
    private String accidentCountyCode;

    @ApiModelProperty("出险国家Code")
    private String accidentNationCode;







}
