package com.paic.ncbs.claim.dao.mapper.settle;

import com.paic.ncbs.claim.model.dto.settle.FlightDelayDTO;
import com.paic.ncbs.claim.model.vo.settle.SettleFlightDelayVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface FlightDelayMapper {

    public void addFlightDelay(FlightDelayDTO flightDelayDTO);

    public void removeFlightDelay(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("taskCode") String taskCode, @Param("channelProcessId") String channelProcessId);

    public FlightDelayDTO getFlightDelay(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,
                                         @Param("status") String status, @Param("taskCode") String taskCode, @Param("channelProcessId") String channelProcessId);

    public SettleFlightDelayVO getSettleFlightDelay(@Param("reportNo") String reportNo,
                                                    @Param("caseTimes") Integer caseTimes, @Param("status") String status, @Param("taskCode") String taskCode, @Param("idAhcsChannelProcess") String idAhcsChannelProcess);

    public List<FlightDelayDTO> getFlightDelayList(@Param("idAhcsChannelProcess") String idAhcsChannelProcess,
                                                   @Param("taskCode") String taskCode);

    public void addFlightDelayList(@Param("flightDelayList") List<FlightDelayDTO> flightDelayList,
                                   @Param("caseTimes") Integer caseTimes, @Param("userId") String userId,
                                   @Param("channelProcessId") String channelProcessId);

    public Integer getDutyAttrByReportNo(@Param("reportNo") String reportNo);

    public Integer getDutyAttrPolicyConfigByReportNo(@Param("reportNo") String reportNo);

    public Integer getDutyAttrProductConfigByReportNo(@Param("reportNo") String reportNo);

    public void updateAutoFlightDelayStatus(@Param("reportNo") String reportNo,
                                            @Param("caseTimes") Integer caseTimes,
                                            @Param("status") String status,
                                            @Param("taskCode") String taskCode);

    public void updateFlightDelayTicketInfo(FlightDelayDTO flightDelayDTO);

    public void updateFlightDelay(FlightDelayDTO flightDelayDTO);
}
