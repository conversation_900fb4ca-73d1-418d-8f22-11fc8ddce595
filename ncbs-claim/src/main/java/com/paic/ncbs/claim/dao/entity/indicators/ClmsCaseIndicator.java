package com.paic.ncbs.claim.dao.entity.indicators;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* clms_case_indicator实体类
* <AUTHOR>
* @since 2025-04-14
*/
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("clms_case_indicator")
public class ClmsCaseIndicator implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 创建人	
    */
    @TableField(value = "created_by")
    private String createdBy;

    /**
    * 创建时间
    */
    @TableField(value = "sys_ctime")
    private LocalDateTime sysCtime;

    /**
    * 最新修改人员
    */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
    * 最新修改时间
    */
    @TableField(value = "sys_utime")
    private LocalDateTime sysUtime;

    /**
    * 唯一标识
    */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
    * 报案号
    */
    @TableField(value = "report_no")
    private String reportNo;

    /**
    * 赔付次数
    */
    @TableField(value = "case_times")
    private Short caseTimes;

    /**
    * 关联任务层级的主键（如调查ID）
    */
    @TableField(value = "id_linked")
    private String idLinked;

    /**
    * 指标代码
    */
    @TableField(value = "indicator_code")
    private String indicatorCode;

    /**
    * 指标名称
    */
    @TableField(value = "indicator_name")
    private String indicatorName;

    /**
    * 指标值
    */
    @TableField(value = "indicator_value")
    private String indicatorValue;

    /**
    * 指标单位:M-秒
    */
    @TableField(value = "indicator_unit")
    private String indicatorUnit;

    /**
    * 指标公式
    */
    @TableField(value = "indicator_cal")
    private String indicatorCal;

    /**
    * 计算描述
    */
    @TableField(value = "indicator_cal_des")
    private String indicatorCalDes;

    /**
    * 指标值是否活动
    */
    @TableField(value = "value_stable")
    private String valueStable;

    /**
    * 备注
    */
    @TableField(value = "remark")
    private String remark;

}