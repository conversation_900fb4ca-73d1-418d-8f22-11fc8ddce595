package com.paic.ncbs.claim.mq.producer.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.paic.ncbs.claim.dao.entity.ahcs.AdressSearchDto;
import com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.enums.ChargeCodeEnum;
import com.paic.ncbs.claim.common.enums.ClientTypeEnum;
import com.paic.ncbs.claim.common.enums.PaymentTypeEnum;
import com.paic.ncbs.claim.common.enums.ReportModeEnum;
import com.paic.ncbs.claim.common.util.CodeUtil;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.dao.mapper.fee.FeePayMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
import com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO;
import com.paic.ncbs.claim.model.dto.mq.CompensateDto;
import com.paic.ncbs.claim.model.dto.mq.claim.ClaimRepayCalReqDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLClaimPaymentDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLClaimPaymentTraceDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLPrepayDto;
import com.paic.ncbs.claim.model.dto.mq.swf.SwfLogStoreDto;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.mq.producer.MqProducerCompensateService;
import com.paic.ncbs.claim.mq.producer.MqProducerPreCompensateService;
import com.paic.ncbs.claim.mq.producer.MqProducerRegistService;
import com.paic.ncbs.claim.service.endcase.CaseBaseService;
import com.paic.ncbs.claim.service.other.CommonParameterService;
import com.paic.ncbs.claim.service.report.ReportAccidentService;
import com.paic.ncbs.claim.service.report.ReportCustomerInfoService;
import com.paic.ncbs.claim.service.report.ReportInfoService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
//import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.constant.Constants.PAYMENT_ITEM_STATUS_11;
import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

@Service
@Slf4j
public class MqProducerPreCompensateServiceImpl implements MqProducerPreCompensateService {
    @Value("${mq.rabbit.exchange.ncbs.claim.root:ncbsClaimExchange}")
    private String ncbsClaimExchange;
    //核赔环节topic
    @Value("${mq.compensate.topic}")
    private String compensateTopic;
    @Autowired
    private ReportCustomerInfoService reportCustomerInfoService;
    @Autowired
    private ReportInfoService reportInfoService;
    @Autowired
    private PaymentItemMapper paymentItemMapper;
    @Autowired
    private TaskInfoService taskInfoService;
  /*  @Autowired
    private RabbitTemplate rabbitTemplate;*/
    @Autowired
    private OcasMapper ocasMapper;
    @Autowired
    private DepartmentDefineMapper departmentDefineMapper;
    @Autowired
    private FeePayMapper feePayMapper;

    @Autowired
    private CommonParameterService commonService;
    @Autowired
    private CaseBaseService caseBaseService;

    @Autowired
    private MqProducerCompensateService mqProducerCompensateService;
    @Autowired
    private ReportAccidentService reportAccidentService;

    @Lazy
    @Autowired
    private MqProducerRegistService mqProducerRegistService;
    @Override
    @Async("asyncPool")
    public void syncProducerPreCompensateLink(String reportNo, Integer caseTimes) {
        log.info("syncProducerPreCompensateLink===start==");
        // 数据组装
        CompensateDto compensateDto= initProducerCompensateDto(reportNo,caseTimes);
        //发送MQ消息
        JSONObject jsonObj = (JSONObject) JSON.toJSON(compensateDto);
        log.info("syncProducerPreCompensateLink===sendMessage: {}",jsonObj);
      //  rabbitTemplate.convertAndSend(ncbsClaimExchange, compensateTopic, compensateDto);
        log.info("syncProducerPreCompensateLink===end==");
    }

    private CompensateDto initProducerCompensateDto(String reportNo, Integer caseTimes) {
        ReportInfoEntity reportInfo = reportInfoService.getReportInfo(reportNo);
        ReportAccidentEntity reportAccident = reportAccidentService.getReportAccident(reportNo);
        //报案方式
        reportInfo.setReportMode(ReportModeEnum.getName(reportInfo.getReportMode() == null ? "2" : reportInfo.getReportMode()));
        //查询 报案客户 被保险人 信息表
        ReportCustomerInfoEntity customerInfo = reportCustomerInfoService.getReportCustomerInfoByReportNo(reportNo);

        TaskInfoDTO taskInfoDTO = taskInfoService.findLatestByReportNoAndBpmKey(reportNo, caseTimes, BpmConstants.OC_PREPAY_REVIEW);
        List<CaseBaseEntity> caseBaseInfoList = caseBaseService.getCaseBaseInfoByReportNoAndCasetimes(reportNo, String.valueOf(caseTimes));

        //减损金额
        CompensateDto compensateDto = new CompensateDto();

        PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
        paymentItemDTO.setReportNo(reportNo);
        paymentItemDTO.setCaseTimes(caseTimes);
        // 只同步 10 的 同步成功后更新成11
        paymentItemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_10);
        List<PaymentItemDTO> allItems = paymentItemMapper.getPaymentItem(paymentItemDTO);
        // claimType=1 赔款类型：(13-赔款 1J直接理赔费用 "C13", "共保代付赔款" "C1J", "共保代付费用"
        // claimType=2 11-预赔赔款 "11J", "预赔费用" ,"P13", "共保代付预赔赔款" "P1J", "共保代付预赔费用" )
        Map<String, List<PaymentItemDTO>> itemGroupByType = allItems.stream().collect(Collectors.groupingBy(PaymentItemDTO::getClaimType));
        //根据条件过滤 筛出预赔的
        List<PaymentItemDTO> paymentItems = itemGroupByType.get("2");

        // 赔款计算书表==============OK 预赔不传这个对象
        // 计算书号去重
        List<PaymentItemDTO> items = paymentItems.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(()
                -> new TreeSet<>(Comparator.comparing(PaymentItemDTO::getCompensateNo))), ArrayList::new));
        //预赔主表==============OK
        if (CollectionUtils.isNotEmpty(items)){
            List<PrpLPrepayDto> prpLPrepayDtoList = getPrpLPrepayDto(taskInfoDTO,items,caseBaseInfoList,reportInfo);
            compensateDto.setPrpLPrepayDtoList(prpLPrepayDtoList);
        }

        //理赔支付对象表==============OK
        List<PrpLClaimPaymentDto> prpLClaimPaymentDtoList = initPrpLClaimPaymentDto(paymentItems,taskInfoDTO, reportInfo);
        compensateDto.setPrpLClaimPaymentDtoList(prpLClaimPaymentDtoList);

        //理赔支付对象轨迹表==============OK
        List<PrpLClaimPaymentTraceDto> prpLClaimPaymentTraceDtoList = initPrpLClaimPaymentTraceDto(paymentItems,taskInfoDTO, reportInfo);
        compensateDto.setPrpLClaimPaymentTraceDtoList(prpLClaimPaymentTraceDtoList);

        // 理赔工作流转储表==============OK
        List<SwfLogStoreDto> swfLogStoreDtoList = initSwfLogStoreDto(reportNo, customerInfo,taskInfoDTO, reportInfo,items);
        compensateDto.setSwfLogStoreDtoList(swfLogStoreDtoList);
        ClaimRepayCalReqDto claimRepayCalReqDto = mqProducerCompensateService.initClaimRepayCalReqDto(reportAccident, taskInfoDTO, caseBaseInfoList, paymentItems, "2");
        compensateDto.setClaimRepayCalReqDto(claimRepayCalReqDto);
        return compensateDto;
    }


    private boolean checkIsAdvance(String paymentType) {
        // claimType=1 赔款类型：(13-赔款 1J直接理赔费用 ,"C13", "共保代付赔款" "C1J", "共保代付费用"
        // claimType=2 11-预赔赔款 "11J", "预赔费用" ,"P13", "共保代付预赔赔款" "P1J", "共保代付预赔费用" )
        return "13".equals(paymentType) || "11".equals(paymentType) || "1J".equals(paymentType) || "11J".equals(paymentType);
    }

    private String tranChargeCode(String paymentType, String idClmPaymentItem) {
        String chargeCode ;
        switch (paymentType) {
            case "11":
            case "13":
            case "C13":
            case "P13":
                chargeCode = "98";
                break;
            case "1J":
            case "C1J":
            case "11J":
            case "P1J":
                List<FeeInfoDTO> feeInfoDTOList=  feePayMapper.getFeePayByIdClmPaymentInfo(idClmPaymentItem);
                if (CollectionUtils.isNotEmpty(feeInfoDTOList)){
                    FeeInfoDTO feeInfoDTO = feeInfoDTOList.get(0);
                    chargeCode = feeInfoDTO==null?"36": ChargeCodeEnum.getCode(feeInfoDTO.getFeeType());
                } else {
                    chargeCode = "36";
                }
                break;
            default:
                chargeCode = "98";
                break;
        }
        return chargeCode;
    }

    private String tranPaymentType(String paymentType, String idClmPaymentItem) {
        String payType ;
        switch (paymentType) {
            case "11":
            case "13":
            case "C13":
            case "P13":
                payType = "c";
                break;
            case "1J":
            case "C1J":
            case "11J":
            case "P1J":
                List<FeeInfoDTO> feeInfoDTOList=  feePayMapper.getFeePayByIdClmPaymentInfo(idClmPaymentItem);
                if (CollectionUtils.isNotEmpty(feeInfoDTOList)){
                    FeeInfoDTO feeInfoDTO = feeInfoDTOList.get(0);
                    payType = feeInfoDTO==null?"36": ChargeCodeEnum.getCode(feeInfoDTO.getFeeType());
                } else {
                    payType = "36";
                }
                break;
            default:
                payType = "c";
                break;
        }
        return payType;
    }

    private List<PrpLClaimPaymentTraceDto> initPrpLClaimPaymentTraceDto(List<PaymentItemDTO> paymentItems,TaskInfoDTO taskInfoDTO,ReportInfoEntity reportInfo) {
        List<PrpLClaimPaymentTraceDto> prpLClaimPaymentTraceDtoList=new ArrayList<>();
        int n = 1;
        for (int i = 0; i < paymentItems.size(); i++) {
            PaymentItemDTO itemDTO = paymentItems.get(i);
            String paymentType = itemDTO.getPaymentType();
            // 剔除掉 垫付的
            if (!checkIsAdvance(paymentType)) {
                continue;
            }
            PrpLClaimPaymentTraceDto prpLClaimPaymentTraceDto = new PrpLClaimPaymentTraceDto();
//        流序号	TRACKNO	Y
            int i1 = n++;
            prpLClaimPaymentTraceDto.setTrackNo(i1);
            //        收款人证件类型（身份证）	IDENTIFYTYPE	Y
            if ("0".equals(itemDTO.getBankAccountAttribute())){
                prpLClaimPaymentTraceDto.setIdentifyType("71");
            } else {
                prpLClaimPaymentTraceDto.setIdentifyType(mqProducerRegistService.transZRRIdentifyType(itemDTO.getClientCertificateType()));
            }
            //        收款人证件号	IDENTIFYNUMBER	Y
            prpLClaimPaymentTraceDto.setIdentifyNumber(itemDTO.getClientCertificateNo());
//        业务号	BUSINESSNO	Y
            prpLClaimPaymentTraceDto.setBusinessNo(itemDTO.getCompensateNo());
//        报案号	REGISTNO	Y
            prpLClaimPaymentTraceDto.setRegistNo(itemDTO.getReportNo());
//        保单号	POLICYNO	Y
            prpLClaimPaymentTraceDto.setPolicyNo(itemDTO.getPolicyNo());
//        险种代码	RISKCODE	Y
            Map<String, String> productMap = ocasMapper.getPlyBaseInfo(itemDTO.getPolicyNo());
            String productCode = MapUtils.getString(productMap,"productCode");
            prpLClaimPaymentTraceDto.setRiskCode(CodeUtil.subStringRiskCode(productCode));
//        录入信息节点类型	NODETYPE	Y
            prpLClaimPaymentTraceDto.setNodeType("speci");
//        序号	SERIALNO	Y
            prpLClaimPaymentTraceDto.setSerialNo(String.valueOf(i1));
//        录入人代码	OPERATORCODE	Y
            prpLClaimPaymentTraceDto.setOperatorCode(taskInfoDTO.getApplyer());
//        付款方式，如现金、转账、支票等	PAYWAY	Y
            prpLClaimPaymentTraceDto.setPayWay("02");
//        付款币种	CURRENCY	Y
            prpLClaimPaymentTraceDto.setCurrency("CNY");
//        付款类型如赔款、具体费用名称等	PAYTYPE	Y.
            String payType = tranPaymentType(itemDTO.getPaymentType(), itemDTO.getIdClmPaymentItem());
            prpLClaimPaymentTraceDto.setPayType(payType);
//        付款金额	PAYREFFEE	Y
            prpLClaimPaymentTraceDto.setPayRefFee(nvl(itemDTO.getPaymentAmount(),0));
//        付款性质如赔款、费用等	PAYNATURE	Y
            prpLClaimPaymentTraceDto.setPayNature(PaymentTypeEnum.getCode(paymentType));
//        收款人性质1：法人2：自然人	PAYEENATURE	Y
            String accountAttribute = itemDTO.getBankAccountAttribute();
            prpLClaimPaymentTraceDto.setPayeeNature("0".equals(accountAttribute) ? "1" : "2");
//        收款人银行户名或开户行名称	UNITNAME	Y
            prpLClaimPaymentTraceDto.setUnitName(itemDTO.getBankDetail());
//        收款人账户	ACCOUNTCODE	Y
            prpLClaimPaymentTraceDto.setAccountCode(itemDTO.getClientBankCode());
//        收款人户名	PAYFEENAME	Y
            prpLClaimPaymentTraceDto.setPayFeeName(itemDTO.getClientName());
//        付款摘要	REMARK	CY	选填
            prpLClaimPaymentTraceDto.setRemark(null);
//        收款人银行类别码	PAYFEEBANKTYPECODE	CY	非意健险自动全流程需要录入
            prpLClaimPaymentTraceDto.setPayFeeBankTypeCode(itemDTO.getClientBankCode());
//        收款人银行类别名称	PAYFEEBANKTYPENAME	CY	非意健险自动全流程需要录入
            prpLClaimPaymentTraceDto.setPayFeeBankTypeName(itemDTO.getClientBankName());
            AdressSearchDto adressSearchDto  = new AdressSearchDto();
            adressSearchDto.setOverseasOccur(BaseConstant.STRING_0).setAccidentProvinceCode(itemDTO.getProvinceName())
                    .setAccidentCountyCode(itemDTO.getRegionCode()).setAccidentCityCode(itemDTO.getCityName());
            AdressSearchDto detailAdressFormCode = commonService.getDetailAdressFormCode(adressSearchDto);
//        省份	PROVINCE	Y
            prpLClaimPaymentTraceDto.setProvince(detailAdressFormCode.getAccidentProvinceName());
//        城市	CITY	Y
            prpLClaimPaymentTraceDto.setCity(detailAdressFormCode.getAccidentCityName());
//        机构号	ORGANIZATIONID	Y
            prpLClaimPaymentTraceDto.setOrganizationID(reportInfo.getAcceptDepartmentCode());
//        联行号	LASALLEROAD	CY	从资金系统获取
            prpLClaimPaymentTraceDto.setLasalleroad(null);
//        支付对象	PAYOBJECTCODE	Y
            prpLClaimPaymentTraceDto.setPayObjectCode(ClientTypeEnum.getCode(itemDTO.getClientType()));
//        区域名称	COUNTY	Y
            prpLClaimPaymentTraceDto.setCounty(itemDTO.getCityName());
//        区域码	AREACODE	Y
            prpLClaimPaymentTraceDto.setAreaCode(itemDTO.getRegionCode());
//        更新时间	UPDATETIME	CY	只要对表操作就修改updatetime
            prpLClaimPaymentTraceDto.setUpdateTime(new Date());
            prpLClaimPaymentTraceDtoList.add(prpLClaimPaymentTraceDto);
        }
        return prpLClaimPaymentTraceDtoList;
    }

    private List<PrpLClaimPaymentDto> initPrpLClaimPaymentDto( List<PaymentItemDTO> paymentItems,TaskInfoDTO taskInfoDTO,ReportInfoEntity reportInfo) {
        List<PrpLClaimPaymentDto> prpLClaimPaymentDtoList=new ArrayList<>();
        int n = 1;
        for (int i = 0; i < paymentItems.size(); i++) {
            PaymentItemDTO itemDTO = paymentItems.get(i);
            String paymentType = itemDTO.getPaymentType();
            if (!checkIsAdvance(paymentType)) {
                continue;
            }
            PrpLClaimPaymentDto prpLClaimPaymentDto = new PrpLClaimPaymentDto();
            //        业务号	BUSINESSNO	Y
            prpLClaimPaymentDto.setBusinessNo(itemDTO.getCompensateNo());
            //        报案号	REGISTNO	Y
            prpLClaimPaymentDto.setRegistNo(itemDTO.getReportNo());
            //        保单号	POLICYNO	Y
            prpLClaimPaymentDto.setPolicyNo(itemDTO.getPolicyNo());
            Map<String, String> productMap = ocasMapper.getPlyBaseInfo(itemDTO.getPolicyNo());
            String productCode = MapUtils.getString(productMap,"productCode");
            //        险种代码	RISKCODE	Y
            prpLClaimPaymentDto.setRiskCode(CodeUtil.subStringRiskCode(productCode));
            //        录入信息节点类型	NODETYPE	Y
            prpLClaimPaymentDto.setNodeType("speci");
            //        序号	SERIALNO	Y
            int i1 = n++;
            prpLClaimPaymentDto.setSerialNo(String.valueOf(i1));
            itemDTO.setSerialNo(i1);
            itemDTO.setPaymentItemStatus(PAYMENT_ITEM_STATUS_11);
            paymentItemMapper.updatePaymentItem(itemDTO);
            //        录入人代码	OPERATORCODE	Y
            prpLClaimPaymentDto.setOperatorCode(taskInfoDTO.getApplyer());
            //        付款方式，如现金、转账、支票等	PAYWAY	Y
            prpLClaimPaymentDto.setPayWay("4");
            //        付款币种	CURRENCY	Y
            prpLClaimPaymentDto.setCurrency("CNY");
            //        付款类型如赔款、具体费用名称等	PAYTYPE	Y
            String payType = tranPaymentType(itemDTO.getPaymentType(),itemDTO.getIdClmPaymentItem());
            prpLClaimPaymentDto.setPayType(payType);
            //        付款金额	PAYREFFEE	Y
            prpLClaimPaymentDto.setPayRefFee(nvl(itemDTO.getPaymentAmount(),0));
            //        付款性质如赔款、费用等	PAYNATURE	Y
            prpLClaimPaymentDto.setPayNature(PaymentTypeEnum.getCode(paymentType));
            //        收款人性质	PAYEENATURE	Y
            String accountAttribute = itemDTO.getBankAccountAttribute();
            prpLClaimPaymentDto.setPayeeNature("0".equals(accountAttribute) ? "1" : "2");
            //        收款人银行户名或开户行名称	UNITNAME	Y
            prpLClaimPaymentDto.setUnitName(itemDTO.getBankDetail());
            //        收款人证件号码	CARDCODE	Y
            prpLClaimPaymentDto.setCardCode(itemDTO.getClientCertificateNo());
            //        收款人账户	ACCOUNTCODE	Y
            prpLClaimPaymentDto.setAccountCode(itemDTO.getClientBankCode());
            //        收款人户名	PAYFEENAME	Y
            prpLClaimPaymentDto.setPayFeeName(itemDTO.getClientName());
            //        收款人银行类别码	PAYFEEBANKTYPECODE	Y
            prpLClaimPaymentDto.setPayFeeBankTypeCode(itemDTO.getClientBankCode());
            //        收款人银行类别名称	PAYFEEBANKTYPENAME	Y
            prpLClaimPaymentDto.setPayFeeBankTypeName(itemDTO.getClientBankName());

            AdressSearchDto adressSearchDto  = new AdressSearchDto();
            adressSearchDto.setOverseasOccur(BaseConstant.STRING_0).setAccidentProvinceCode(itemDTO.getProvinceName())
                    .setAccidentCountyCode(itemDTO.getRegionCode()).setAccidentCityCode(itemDTO.getCityName());
            AdressSearchDto detailAdressFormCode = commonService.getDetailAdressFormCode(adressSearchDto);
            //        省份	PROVINCE	Y
            prpLClaimPaymentDto.setProvince(detailAdressFormCode.getAccidentProvinceName());
            //        城市	CITY	Y
            prpLClaimPaymentDto.setCity(detailAdressFormCode.getAccidentCityName());
            //        机构号	ORGANIZATIONID	Y
            prpLClaimPaymentDto.setOrganizationID(reportInfo.getAcceptDepartmentCode());
            //        联行号	LASALLEROAD	CY	从资金系统获取
            prpLClaimPaymentDto.setLasalleroad(null);
            //        送集中支付表时间	SENDDATE	CY	送收付之后回写
            prpLClaimPaymentDto.setSendDate(null);
            //        支付时间 年月日时分秒	PAYREFDATE	CY	支付成功之后回写
            prpLClaimPaymentDto.setPayRefDate(null);
            //        会计机构	CENTERCODE	CY	支付成功之后回写
            prpLClaimPaymentDto.setCenterCode(null);
            //        会计月	YEARMONTH	CY	支付成功之后回写
            prpLClaimPaymentDto.setYearMonth(null);
            //        凭证号	VOUCHERNO	CY	支付成功之后回写
            prpLClaimPaymentDto.setVoucherNo(null);
            //        上级会计机构	UPPERCENTERCODE	CY	支付成功之后回写
            prpLClaimPaymentDto.setUpperCenterCode(null);
            //        上级机构凭证号	UPPERVOUCHERNO	CY	支付成功之后回写
            prpLClaimPaymentDto.setUpperVoucherNo(null);
            //        凭证时间	VOUCHERDATE	CY	支付成功之后回写
            prpLClaimPaymentDto.setVoucherDate(null);
            //        从资金系统生获取的主键ID	ZJ_ID	CY	支付成功之后回写
            prpLClaimPaymentDto.setZjId(null);
            //        快处快赔标志	FASTPAYFLAG	CY	意健险自动全流程自动赋值
            prpLClaimPaymentDto.setFastPayFlag(null);
            //        支付对象	PAYOBJECTCODE	Y
            prpLClaimPaymentDto.setPayObjectCode(ClientTypeEnum.getCode(itemDTO.getClientType()));
            //        收款人证件类型（身份证）	IDENTIFYTYPE	Y
            if ("0".equals(itemDTO.getBankAccountAttribute())){
                prpLClaimPaymentDto.setIdentifyType("71");
            } else {
                prpLClaimPaymentDto.setIdentifyType(mqProducerRegistService.transZRRIdentifyType(itemDTO.getClientCertificateType()));
            }
            //        收款人证件号	IDENTIFYNUMBER	Y
            prpLClaimPaymentDto.setIdentifyNumber(itemDTO.getClientCertificateNo());
            //        区域名称	COUNTY	Y
            prpLClaimPaymentDto.setCounty(itemDTO.getCityName());
            //        区域码	AREACODE	Y
            prpLClaimPaymentDto.setAreaCode(itemDTO.getRegionCode());
            //        付款通道	PAYCHANNEL
            prpLClaimPaymentDto.setPayChannel("1");
            //        更新时间	UPDATETIME	CY	 只要对表操作就修改updatetime
            prpLClaimPaymentDto.setUpdateTime(new Date());
            prpLClaimPaymentDtoList.add(prpLClaimPaymentDto);
        }

        return prpLClaimPaymentDtoList;
    }

    @SneakyThrows
    private List<SwfLogStoreDto> initSwfLogStoreDto(String reportNo, ReportCustomerInfoEntity customerInfo, TaskInfoDTO taskInfoDTO,
                                                    ReportInfoEntity reportInfo,List<PaymentItemDTO> items ) {
        List<SwfLogStoreDto> swfLogStoreDtoList=new ArrayList<>();
        for(PaymentItemDTO paymentItemDTO:items){
            SwfLogStoreDto swfLogStoreDto = new SwfLogStoreDto();
            swfLogStoreDto.setLogNo(3);
            swfLogStoreDto.setModelNo(12);
            swfLogStoreDto.setNodeName("特殊赔案");
            swfLogStoreDto.setBusinessNo(paymentItemDTO.getCompensateNo());
            swfLogStoreDto.setHandleDept(taskInfoDTO.getDepartmentCode());
            swfLogStoreDto.setHandlerCode(taskInfoDTO.getAssigner());
            swfLogStoreDto.setHandlerName(taskInfoDTO.getAssigneeName());
            swfLogStoreDto.setFlowInTime(DateUtils.parseToFormatString(new Date(),DateUtils.FULL_DATE_STR));
            swfLogStoreDto.setTimeLimit(0);
            swfLogStoreDto.setHandleTime(DateUtils.parseToFormatString(new Date(),DateUtils.FULL_DATE_STR));
            swfLogStoreDto.setSubmitTime(DateUtils.parseToFormatString(new Date(),DateUtils.FULL_DATE_STR));
            swfLogStoreDto.setPackageID("0");
            swfLogStoreDto.setTaskNo(0);
            swfLogStoreDto.setNodeType("speci");
            swfLogStoreDto.setTitleStr("");
            swfLogStoreDto.setBusinessType("1");
            Map<String, String> productMap = ocasMapper.getPlyBaseInfo(paymentItemDTO.getPolicyNo());
            String productCode = MapUtils.getString(productMap,"productCode");
            swfLogStoreDto.setRiskCode(CodeUtil.subStringRiskCode(productCode));
            swfLogStoreDto.setKeyIn(reportNo);
            swfLogStoreDto.setKeyOut(reportNo);
            swfLogStoreDto.setDeptName(departmentDefineMapper.queryDepartmentNameByDeptCode(reportInfo.getAcceptDepartmentCode()));
            swfLogStoreDto.setMainFlowID("0");
            swfLogStoreDto.setSubFlowID("0");
            swfLogStoreDto.setPosX(0);
            swfLogStoreDto.setPosY(0);
            swfLogStoreDto.setEndFlag("");
            swfLogStoreDto.setBeforeHandlerCode("");
            swfLogStoreDto.setBeforeHandlerName("");
            swfLogStoreDto.setPolicyNo(paymentItemDTO.getPolicyNo());
            swfLogStoreDto.setComCode(reportInfo.getAcceptDepartmentCode());
            swfLogStoreDto.setRegistNo(reportNo);
            swfLogStoreDto.setInsuredName(customerInfo.getName());
            swfLogStoreDto.setEntrustFlag("1");
            swfLogStoreDto.setEntrustNodeStatus("1");
            swfLogStoreDto.setDamageEndDate(new Date());
            swfLogStoreDto.setDamageStartDate(new Date());
            swfLogStoreDto.setUpdateTime(new Date());

            swfLogStoreDto.setFlowID("");
            swfLogStoreDto.setNodeNo(9);
            swfLogStoreDto.setNodeStatus("4");
            swfLogStoreDto.setFlowStatus("0");
            swfLogStoreDtoList.add(swfLogStoreDto);
        }

        return swfLogStoreDtoList;
    }

    @SneakyThrows
    private List<PrpLPrepayDto>  getPrpLPrepayDto(TaskInfoDTO taskInfoDTO, List<PaymentItemDTO> prePays, List<CaseBaseEntity> caseBaseInfoList,ReportInfoEntity reportInfo) {
        List<PrpLPrepayDto> prpLPrepayDtoList = new ArrayList<>();
        for (int i = 0; i < prePays.size(); i++) {
            PrpLPrepayDto prpLPrepayDto = new PrpLPrepayDto();
            PaymentItemDTO pre = prePays.get(i);
            if (!checkIsAdvance(pre.getPaymentType())){
                continue;
            }
            //        预赔登记号	PRECOMPENSATENO	Y		无明确字段(预赔理算书号)
            prpLPrepayDto.setPreCompensateNo(pre.getCompensateNo());
            String policyNo = pre.getPolicyNo();
            Map<String, String> productMap = ocasMapper.getPlyBaseInfo(policyNo);
            String productCode = MapUtils.getString(productMap,"productCode");
            //         立案号码	CLAIMNO	Y		无明确字段（立案时生成的号码）
            CaseBaseEntity caseBaseEntity = caseBaseInfoList.stream().filter(it -> it.getPolicyNo().equals(pre.getPolicyNo())).findFirst().orElse(null);

            prpLPrepayDto.setClaimNo(caseBaseEntity.getRegistNo());
            //        险种	RISKCODE	Y
            prpLPrepayDto.setRiskCode(CodeUtil.subStringRiskCode(productCode));
            //        保单号码	POLICYNO	Y
            prpLPrepayDto.setPolicyNo(policyNo);
            //        币别代码	CURRENCY	Y	默认值“CNY"
            prpLPrepayDto.setCurrency("CNY");
            //        逾期欠款期数  DAD	ARREARAGETIMES	Y	默认值“0”
            prpLPrepayDto.setArrearageTimes(0);
            //        逾期欠款金额  DAD	SUMARREARAGE	Y	默认值“0.00”
            prpLPrepayDto.setSumArrearage(new BigDecimal("0.00"));
            BigDecimal sumBeforePrepaid = paymentItemMapper.getSumBeforePrePay(pre);
            //        已预（垫）付金额	SUMBEFOREPREPAID	Y	默认值“0.00”
            prpLPrepayDto.setSumBeforePrepaid(nvl(sumBeforePrepaid,0));
            //        本次垫付逾期欠款期数 DAD	BLOCKUPTIMES	Y	默认值“0”
            prpLPrepayDto.setBlockUpTimes(0);
            BigDecimal prePaymentAmount = paymentItemMapper.getSumPrePay(pre);
            //        预赔金额	SUMPREPAID	Y
            prpLPrepayDto.setSumPrepaid(prePaymentAmount);
            BigDecimal sumTotalPrepaid = sumBeforePrepaid.add(prePaymentAmount);
            //        总预（垫）付金额 = SumBeforePrePaid+SumPrepaid	SUMTOTALPREPAID	Y
            prpLPrepayDto.setSumTotalPrepaid(sumTotalPrepaid);
            //        出单机构	MAKECOM	Y
            prpLPrepayDto.setMakeCom(reportInfo.getAcceptDepartmentCode());
            //        业务归属机构代码	COMCODE	Y
            prpLPrepayDto.setComCode(reportInfo.getAcceptDepartmentCode());
            //        经办人代码	HANDLERCODE	Y		能否传预赔申请人（可以）
            prpLPrepayDto.setHandlerCode(taskInfoDTO.getApplyer());
            //        归属业务员代码	HANDLER1CODE	Y		能否传预赔申请人（可以）
            prpLPrepayDto.setHandler1Code(taskInfoDTO.getApplyer());
            //        最终核赔人代码	UNDERWRITECODE	CY	案件提交核赔需要核赔人员代码和名称	能否传核赔处理人（可以）
            prpLPrepayDto.setUnderWriteCode(taskInfoDTO.getAssigner());
            //        最终核赔人名称	UNDERWRITENAME	CY
            prpLPrepayDto.setUnderWriteName(taskInfoDTO.getAssigneeName());
            //        统计年月	STATISTICSYM	Y		能否传预赔审核通过日期(预赔提交的时间)
            SimpleDateFormat formatter2 = new SimpleDateFormat("yyyy/MM/dd");
            prpLPrepayDto.setStatisticsYM(formatter2.parse(formatter2.format(new Date())));
            //                操作员代码	OPERATORCODE	Y
            prpLPrepayDto.setOperatorCode(taskInfoDTO.getAssigner());
            //        计算机输入日期	INPUTDATE	Y	系统自动获取当前操作时间
            prpLPrepayDto.setInputDate(new Date());
            //        核赔完成日期	UNDERWRITEENDDATE	Y
            prpLPrepayDto.setUnderWriteEndDate(new Date());
            //        核赔标志	UNDERWRITEFLAG	Y
            prpLPrepayDto.setUnderWriteFlag("1");
            //        特殊赔案类别	CASETYPE	Y
            prpLPrepayDto.setCaseType("7");
            //        已经支付金额	PAYSUMPREPAID	Y		已经支付成功的预赔金额？（是的）
            prpLPrepayDto.setPaySumPrepaid(new BigDecimal("0"));
            //        支付金额的实际时间	PAYTIME	CY	收付系统回写
            prpLPrepayDto.setPayTime(null);
            //        支付方式(支付回写):"00"现金，"01"支票 ，"02"转帐 	PAYTYPE	CY	收付系统回写
            prpLPrepayDto.setPayType("02");
            //        银行户名(支付回写)	CARDNAME	CY	收付系统回写
            prpLPrepayDto.setCardName("");
            //        银行帐号/身份证号(支付回写)	ACCOUNTCODE	CY	收付系统回写
            prpLPrepayDto.setAccountCode("");
            //        预赔申请人	APPNAME	CY	预赔案件需要填写
            prpLPrepayDto.setAppName(pre.getClientName());
            //        预赔申请人身份类别	APPTYPE	CY
            prpLPrepayDto.setAppType(ClientTypeEnum.getZkpreCode(pre.getClientType()));
            //        特殊赔案审核标志	VERFYFLAG	CY	申请了特殊赔案需要填写
            prpLPrepayDto.setVerfyFlag("1");
            //        费用类型	CHARGECODE	Y
            String chargeCode = tranChargeCode(pre.getPaymentType(), pre.getIdClmPaymentItem());
//            String chargeCode = "98";
            prpLPrepayDto.setChargeCode(chargeCode);
            //        对CNY兑换率	EXCHRATE	Y	默认“1.000000”
            prpLPrepayDto.setExCHRate(new BigDecimal("1.000000"));
            //        更新时间	UPDATETIME	CY	只要对表操作就修改updatetime
            prpLPrepayDto.setUpdateTime(new Date());
            prpLPrepayDtoList.add(prpLPrepayDto);
        }

        return prpLPrepayDtoList;
    }

}
