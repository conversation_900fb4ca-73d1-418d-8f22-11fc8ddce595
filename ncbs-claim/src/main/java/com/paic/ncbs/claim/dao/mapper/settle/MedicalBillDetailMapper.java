package com.paic.ncbs.claim.dao.mapper.settle;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.restartcase.BillCopyDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillDetailDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface MedicalBillDetailMapper extends BaseDao<MedicalBillDetailDTO> {

	public void addBillDetailList(List<MedicalBillDetailDTO> billDetails);

	public void removeBillDetailList(@Param("idAhcsBillInfoList") List<String> idAhcsBillInfoList,
									 @Param("userUM") String userUM);

	public void clearBillDetailAmonut(@Param("idAhcsBillInfoList") List<String> idAhcsBillInfoList,
									  @Param("userUM") String userUM);

	/**
	 * 案件重开，数据拷贝
	 * @param paramList
	 */
	void copyForCaseReopen(List<BillCopyDTO> paramList);

	void addBillDetail(MedicalBillDetailDTO medicalBillDetailDTO);
	void addBillDetailAmend(MedicalBillDetailDTO medicalBillDetailDTO);
}
