package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.util.Date;

public class ClmpReportHisEntity extends EntityDTO {

    private static final long serialVersionUID = -1231386358595511808L;

    private String idClmpReportHis;

    private String tacheId;

    private String relationId;

    private String reportNo;

    private String reporterName;

    private String reporterRegisterTel;

    private Date reportDate;

    private Date reportAcceptDate;

    private String reportRegisterUm;

    private String reportMode;

    private String reportType;

    private String abnormalType;

    private String receiverName;

    private String receiverDept;

    private String receiverTel;

    private String migrateFrom = "np";

    public String getIdClmpReportHis() {
        return idClmpReportHis;
    }

    public void setIdClmpReportHis(String idClmpReportHis) {
        this.idClmpReportHis = idClmpReportHis == null ? null : idClmpReportHis.trim();
    }

    public String getTacheId() {
        return tacheId;
    }

    public void setTacheId(String tacheId) {
        this.tacheId = tacheId == null ? null : tacheId.trim();
    }

    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId == null ? null : relationId.trim();
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String getReporterName() {
        return reporterName;
    }

    public void setReporterName(String reporterName) {
        this.reporterName = reporterName == null ? null : reporterName.trim();
    }

    public String getReporterRegisterTel() {
        return reporterRegisterTel;
    }

    public void setReporterRegisterTel(String reporterRegisterTel) {
        this.reporterRegisterTel = reporterRegisterTel == null ? null : reporterRegisterTel.trim();
    }

    public Date getReportDate() {
        return reportDate;
    }

    public void setReportDate(Date reportDate) {
        this.reportDate = reportDate;
    }

    public Date getReportAcceptDate() {
        return reportAcceptDate;
    }

    public void setReportAcceptDate(Date reportAcceptDate) {
        this.reportAcceptDate = reportAcceptDate;
    }

    public String getReportRegisterUm() {
        return reportRegisterUm;
    }

    public void setReportRegisterUm(String reportRegisterUm) {
        this.reportRegisterUm = reportRegisterUm == null ? null : reportRegisterUm.trim();
    }

    public String getReportMode() {
        return reportMode;
    }

    public void setReportMode(String reportMode) {
        this.reportMode = reportMode == null ? null : reportMode.trim();
    }

    public String getReportType() {
        return reportType;
    }

    public void setReportType(String reportType) {
        this.reportType = reportType == null ? null : reportType.trim();
    }

    public String getAbnormalType() {
        return abnormalType;
    }

    public void setAbnormalType(String abnormalType) {
        this.abnormalType = abnormalType == null ? null : abnormalType.trim();
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName == null ? null : receiverName.trim();
    }

    public String getReceiverDept() {
        return receiverDept;
    }

    public void setReceiverDept(String receiverDept) {
        this.receiverDept = receiverDept == null ? null : receiverDept.trim();
    }

    public String getReceiverTel() {
        return receiverTel;
    }

    public void setReceiverTel(String receiverTel) {
        this.receiverTel = receiverTel == null ? null : receiverTel.trim();
    }

    public String getMigrateFrom() {
        return migrateFrom;
    }

    public void setMigrateFrom(String migrateFrom) {
        this.migrateFrom = migrateFrom == null ? null : migrateFrom.trim();
    }
}