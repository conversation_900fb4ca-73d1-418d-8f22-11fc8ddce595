package com.paic.ncbs.claim.dao.entity.pay;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 保单赔付
 */
@Getter
@Setter
public class PolicyPayEntity extends EntityDTO {
    private static final long serialVersionUID = 4953587936193997371L;
    /** 主键*/
    private String idClmPolicyPay;
    /** 报案号*/
    private String reportNo;
    /** 赔案号*/
    private String caseNo;
    /** 赔付次数*/
    private Integer caseTimes;
    /** 保单号*/
    private String policyNo;
    /** 保单总预赔*/
    private BigDecimal policyPrePay;
    /** 保单总费用金额 (不含减损费用)*/
    private BigDecimal policySumFee;
    /** 保单总预估金额*/
    private BigDecimal policySumEstimate;
    /** 保单总赔付金额(赔款金额+费用(不含减损费用))*/
    private BigDecimal policySumPay;
    /** 赔付金额(不包含费用及注销、拒赔)*/
    private BigDecimal policyPay;
    /** 拒赔/注销金额*/
    private BigDecimal refuseAmount;
    /** 备注*/
    private String remark;
    /** 保单总垫付金额 -- 新加应该未使用 */
    private BigDecimal policyAdvancePay;
    /** 冲减金额 -- 新加应该未使用 */
    private BigDecimal writeoffAmount;
    /** 减损费用 -- 新加应该未使用 */
    private BigDecimal decreaseFee;
    /** 垫付转回金额 -- 新加应该未使用 */
    private BigDecimal policyAdvanceReturnPay;
    /** 超限金额 -- 新加应该未使用 */
    private BigDecimal consultAmount;
    /** 数据来源，区分理赔各子领域以及其新老数据，默认n代表新车险理赔数据，c代码原车险理赔数据，np代表新财产险理赔数据，op代表原财产险理赔数据，a代表新农险数据 */
    private String migrateFrom;
    /** 追偿金额 -- 新加应该未使用 */
    private BigDecimal chaseAmount;
    /** 追偿费用支出 -- 新加应该未使用 */
    private BigDecimal chaseFeeOut;
    /** 追偿旧件回收金额；残值回收金额 -- 新加应该未使用 */
    private BigDecimal chaseOldRecoverAmount;
    /** 代位追偿金额 -- 新加应该未使用 */
    private BigDecimal subrogationAmount;
    /** 保单总预陪费用*/
    private BigDecimal policyPreFee;
    /** 保单总预陪费用*/
    private String currencyCode;
    /**  -- 新加应该未使用 */
    private BigDecimal collegiateAmount;
    /**  -- 新加应该未使用 */
    private BigDecimal accommodationAmount;
    /**  营改增赔款冲减合计(营改增) -- 新加应该未使用 */
    private BigDecimal vatPayAmount;
    /**  营改增费用冲减合计(营改增) -- 新加应该未使用 */
    private BigDecimal vatFeeAmount;
    /**  罚息收入 -- 新加应该未使用 */
    private BigDecimal amercement;
    /**  我司份额赔款 -- 新加应该未使用 */
    private BigDecimal pinganPayAmount;
    /**  我司份额费用 -- 新加应该未使用 */
    private BigDecimal pinganFeeAmount;
    /**  追偿费用转回 -- 新加应该未使用 */
    private BigDecimal chaseFeeBack;
    /**  归档时间 -- 新加应该未使用 */
    private Date archiveDate;
    /**  删除时间 -- 新加应该未使用 */
    private Date deletedTime;

}
