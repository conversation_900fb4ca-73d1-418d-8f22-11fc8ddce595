package com.paic.ncbs.claim.dao.mapper.report;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentBaggageEntity;

public interface ReportAccidentBaggageMapper extends BaseDao<ReportAccidentBaggageEntity> {

    int deleteByPrimaryKey(String idAhcsReportAccidentBag);

    int insert(ReportAccidentBaggageEntity record);

    int insertSelective(ReportAccidentBaggageEntity record);

    ReportAccidentBaggageEntity selectByPrimaryKey(String idAhcsReportAccidentBag);

    int updateByPrimaryKeySelective(ReportAccidentBaggageEntity record);

    int updateByPrimaryKey(ReportAccidentBaggageEntity record);

    ReportAccidentBaggageEntity getReportAccidentBaggageByReportNo(String reportNo);
}