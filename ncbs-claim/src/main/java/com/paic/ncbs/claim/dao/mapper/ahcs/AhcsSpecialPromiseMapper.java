package com.paic.ncbs.claim.dao.mapper.ahcs;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsSpecialPromiseEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AhcsSpecialPromiseMapper extends BaseDao<AhcsSpecialPromiseEntity> {

    int deleteByPrimaryKey(String idAhcsSpecialPromise);

    int insert(AhcsSpecialPromiseEntity record);

    int insertSelective(AhcsSpecialPromiseEntity record);

    AhcsSpecialPromiseEntity selectByPrimaryKey(String idAhcsSpecialPromise);

    int updateByPrimaryKeySelective(AhcsSpecialPromiseEntity record);

    int updateByPrimaryKey(AhcsSpecialPromiseEntity record);

    int deleteByIdAhcsPolicyInfo(String idAhcsPolicyInfo);

    int insertList(@Param("specialPromiseEntities") List<AhcsSpecialPromiseEntity> specialPromiseEntities);

    List<AhcsSpecialPromiseEntity> getInfoByPolicyId(String idAhcsPolicyInfo);

    void deleteByReportNoAndPolicyNo(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo);

    void insertBatch(@Param("list") List<AhcsSpecialPromiseEntity> list);
}