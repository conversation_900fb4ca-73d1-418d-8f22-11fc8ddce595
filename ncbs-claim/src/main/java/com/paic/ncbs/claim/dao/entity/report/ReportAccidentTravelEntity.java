package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.math.BigDecimal;

/**
 * 意健险旅行变更信息表
 */
public class ReportAccidentTravelEntity extends EntityDTO {

    private static final long serialVersionUID = 6509849337072493751L;

    private String idAhcsReportAccidentTravel;

    private String reportNo;

    private String changeReason;

    private String changeType;

    private BigDecimal costEstimate;

    private String travelExtend;

    private BigDecimal ticketPrice;

    public BigDecimal getTicketPrice() {
        return ticketPrice;
    }

    public void setTicketPrice(BigDecimal ticketPrice) {
        this.ticketPrice = ticketPrice;
    }

    public String getTravelExtend() {
        return travelExtend;
    }

    public void setTravelExtend(String travelExtend) {
        this.travelExtend = travelExtend;
    }

    public static long getSerialversionuid() {
        return serialVersionUID;
    }

    public String getIdAhcsReportAccidentTravel() {
        return idAhcsReportAccidentTravel;
    }

    public void setIdAhcsReportAccidentTravel(String idAhcsReportAccidentTravel) {
        this.idAhcsReportAccidentTravel = idAhcsReportAccidentTravel == null ? null : idAhcsReportAccidentTravel.trim();
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String getChangeReason() {
        return changeReason;
    }

    public void setChangeReason(String changeReason) {
        this.changeReason = changeReason == null ? null : changeReason.trim();
    }

    public String getChangeType() {
        return changeType;
    }

    public void setChangeType(String changeType) {
        this.changeType = changeType == null ? null : changeType.trim();
    }

    public BigDecimal getCostEstimate() {
        return costEstimate;
    }

    public void setCostEstimate(BigDecimal costEstimate) {
        this.costEstimate = costEstimate;
    }
}