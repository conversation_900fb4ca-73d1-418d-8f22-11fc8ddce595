package com.paic.ncbs.claim.model.vo.chase;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChaseApplyInfoVO {


    private String reportNo;


    private Integer caseTimes;


    private String applyRemark;

    private List<String> chaseSubTypeCode;

    private List<ChasePolicyPayVO> chasePolicyPays;


    private List<ChasePolicyFeeVO> chasePolicyFees;



    private ChaseFeeOutVO chaseFeeOutVO;



    private ChaseApplySeqInfoVO chaseApplySeqInfo;

}
