package com.paic.ncbs.claim.dao.mapper.endcase;

import com.paic.ncbs.claim.model.dto.policy.PolicyHistoryDTO;
import com.paic.ncbs.claim.model.dto.user.DepartmentDTO;
import com.paic.ncbs.claim.model.vo.endcase.CaseParamVO;
import com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.Date;
import java.util.List;

@MapperScan
public interface WholeCaseMapper {

    Integer getCaseTimesByReportNo(@Param("reportNo") String reportNo);

    WholeCaseVO getWholeCaseListByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    String getCaseTypeByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    void modifyDocumentStatus(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,
                              @Param("status") String status, @Param("userId") String userId);

    String isSelfCase(@Param("reportNo") String reportNo);

    int isPostponeAwait(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    void modifyIsSelfHelp(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("isSelfHelp") String isSelfHelp, @Param("userId") String userId);

    WholeCaseVO getWhleCaseVoByReportNo(WholeCaseVO wholeCaseVO);

    String getReportRegisterTel(@Param("reportNo") String reportNo);

    String getClientNo(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo);

    List<PolicyHistoryDTO> getPolicyHistory(@Param("clientNo")String clientNo);

    List<CaseParamVO> getCaseWithCondition(
            @Param("beginReportDate") String beginReportDate,
            @Param("endReportDate") String endReportDate,
            @Param("endCaseDateBegin") Date endCaseDateBegin,
            @Param("endCaseDateEnd") Date endCaseDateEnd,
            @Param("departments") List<DepartmentDTO> departments);

    List<CaseParamVO> getReportListByCaseNo(@Param("caseNo") String caseNo);

    Integer getCustomerAccidentCount(@Param("clientNo") String clientNo);
}
