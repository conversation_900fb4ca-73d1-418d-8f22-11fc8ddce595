package com.paic.ncbs.claim.dao.entity.reinsurance;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 理赔发送再保记录表实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SendReinsuranceRecord implements Serializable {

    private static final long serialVersionUID = 437816863566775989L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 报案号
     */
    private String reportNo;

    /**
     * 赔付次数
     */
    private Integer caseTimes;

    /**
     * 赔案号
     */
    private String caseNo;

    /**
     * 立案号
     */
    private String registNo;

    /**
     * 保单号
     */
    private String policyNo;

    /**
     * 理赔业务环节
     */
    private String claimType;

    /**
     * 案件环节描述
     */
    private String claimTypeDesc;

    /**
     * 请求参数
     */
    private String requestParam;

    /**
     * 响应参数
     */
    private String responseParam;

    /**
     * 请求是否成功，Y-成功 N-失败
     */
    private String isSuccess;

    /**
     * 创建人员
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 修改人员
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private Date updatedDate;

}

