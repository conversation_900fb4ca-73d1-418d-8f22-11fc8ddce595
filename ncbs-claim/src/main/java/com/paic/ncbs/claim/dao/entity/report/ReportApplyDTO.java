package com.paic.ncbs.claim.dao.entity.report;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
* clms_report_apply实体类
* <AUTHOR>
* @since 2025-07-17
*/
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("clms_report_apply")
    public class ReportApplyDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键ID
    */
    @TableField(value = "id")
    private String id;

    /**
    * 报案申请号
    */
    @TableField(value = "apply_no")
    private String applyNo;

    /**
     * 报案申请号
     */
    @TableField(value = "report_no")
    private String reportNo;

    /**
    * 保单号
    */
    @TableField(value = "policy_no")
    private String policyNo;

    /**
    * 损失类型 3-人伤 4-健康 5-财产 6-其他
    */
    @TableField(value = "loss_class")
    private String lossClass;

    /**
    * 出险原因大类
    */
    @TableField(value = "accident_cause_level1")
    private String accidentCauseLevel1;

    /**
    * 出险原因明细类
    */
    @TableField(value = "accident_cause_level2")
    private String accidentCauseLevel2;

    /**
    * 出险经过
    */
    @TableField(value = "accident_detail")
    private String accidentDetail;

    /**
    * 出险时间
    */
    @TableField(value = "accident_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date accidentDate;

    /**
    * 事故地点(0：境内/1：境外)
    */
    @TableField(value = "whether_out_side_accident")
    private String whetherOutSideAccident;

    /**
    * 出险省份
    */
    @TableField(value = "accident_province")
    private String accidentProvince;

    /**
    * 出险城市
    */
    @TableField(value = "accident_city")
    private String accidentCity;

    /**
    * 出险县
    */
    @TableField(value = "accident_county")
    private String accidentCounty;

    /**
    * 出险区域
    */
    @TableField(value = "accident_area")
    private String accidentArea;

    /**
    * 出险国家
    */
    @TableField(value = "accident_nation")
    private String accidentNation;

    /**
    * 出险区域
    */
    @TableField(value = "accident_place")
    private String accidentPlace;

    /**
    * 是否单证齐全（Y/N）
    */
    @TableField(value = "is_suffice")
    private String isSuffice;

    /**
     * 是否有效
     */
    @TableField(value = "valid_flag")
    private String validFlag;

    /**
     * 是否有效
     */
    @TableField(value = "cancel_reason")
    private String cancelReason;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sysEtime = new Date();

    /**
    * 创建人
    */
    @TableField(value = "created_by")
    private String createdBy;

    /**
    * 创建时间/任务开始时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sysCtime = new Date();

    /**
    * 最新修改人员
    */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
    * 最新修改时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sysUtime = new Date();

}