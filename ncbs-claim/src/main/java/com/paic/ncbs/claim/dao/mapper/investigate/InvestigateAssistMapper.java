package com.paic.ncbs.claim.dao.mapper.investigate;


import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateAssistVO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateAssistDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;


@MapperScan
public interface InvestigateAssistMapper extends BaseDao<InvestigateAssistDTO> {


    int addInvestigateAssist(InvestigateAssistDTO investigateAssist);


    int modifyInvestigateAssist(InvestigateAssistDTO investigateAssist);


	InvestigateAssistDTO getInvestigateAssistOngoingByTaskId(
            @Param("idAhcsInvestigateTask") String idAhcsInvestigateTask);
	

	List<InvestigateAssistVO> getInvestigateAssistByTaskId(@Param("idAhcsInvestigateTask") String idAhcsInvestigateTask);


	InvestigateAssistDTO getInvestigateAssistGonoByTaskId(@Param("idAhcsInvestigateTask") String idAhcsInvestigateTask);

}