package com.paic.ncbs.claim.dao.mapper.trace;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import com.paic.ncbs.claim.model.dto.trace.ClmsPersHospitalDTO;
import com.paic.ncbs.claim.model.dto.trace.ClmsPersInjuredDTO;
import com.paic.ncbs.claim.model.vo.trace.ClmsPersHospitalVO;
import com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 *
 * 表clms_pers_hospital对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@MapperScan
public interface ClmsPersHospitalMapper extends BaseDao<ClmsPersHospitalDTO> {
    /**
     * 查询医院信息
     * @param personTranceRequestVo
     * @return
     */
    public List<ClmsPersHospitalVO> selectClmsPersHospital(PersonTranceRequestVo personTranceRequestVo);

    /**
     * 查询医院信息
     * @param reportNo,caseTimes
     * @return
     */
    public List<ClmsPersHospitalVO> selectClmsPersHospitals(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes);

    /**
     * 修改
     */
    public void updateSelectiveByPrimaryKey(ClmsPersHospitalDTO clmsPersHospitalDTO);

    /**
     * 数据copy插入数据
     */
    public void  copyForCaseReopen(CaseReopenCopyDTO caseReopenCopyDTO);

    /**
     * 数据删除
     * @param personTranceRequestVo
     */
    public void deleteclmsPersHospital(PersonTranceRequestVo personTranceRequestVo);
}