package com.paic.ncbs.claim.mq.producer;

import com.paic.ncbs.claim.dao.entity.report.*;
import com.paic.ncbs.claim.model.dto.report.ReportBaseInfoResData;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.dao.entity.report.*;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLClaimDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLClaimLossDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLClaimLossTraceDto;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;

import java.util.List;

/**
 *      立案环节
 */
public interface MqProducerClaimService {

    void syncClaimLink(String reportNo, Integer caseTimes);

    List<PrpLClaimLossTraceDto> initPrpLClaimLossTraceDto(List<PolicyInfoDTO> policyInfoDTOList, ReportInfoEntity reportInfo, WholeCaseBaseDTO wholeCaseBaseEntity, ReportCustomerInfoEntity customerInfo, List<PolicyPayDTO> policys, List<EstimatePolicyDTO> policyList);

    List<PrpLClaimLossDto>  initPrpLClaimLossDto(ReportInfoEntity reportInfo, WholeCaseBaseDTO wholeCaseBaseEntity, List<PolicyInfoDTO> policyInfoDTOList, ReportCustomerInfoEntity customerInfo, List<PolicyPayDTO> policys,List<EstimatePolicyDTO> policyList);

    List<PrpLClaimDto> initPrpLClaimDto(String flag, ReportInfoEntity reportInfo, ReportCustomerInfoEntity customerInfo, ReportAccidentEntity reportAccident, List<PolicyInfoDTO> policyInfoDTOList, WholeCaseBaseDTO wholeCaseBaseEntity, ReportInfoExEntity reportInfoExEntity, TaskInfoDTO taskInfoDTO, ReportAccidentExEntity reportAccidentEx, ReportBaseInfoResData reportBaseInfo);
}
