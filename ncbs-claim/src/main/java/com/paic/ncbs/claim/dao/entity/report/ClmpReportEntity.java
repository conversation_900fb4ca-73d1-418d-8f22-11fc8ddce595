package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.math.BigDecimal;
import java.util.Date;

public class ClmpReportEntity extends EntityDTO {

    private static final long serialVersionUID = 1053517330207581625L;

    private String idClmpReport;

    private String idClmReportInfo;

    private String reportNo;

    private Date reportAcceptDate;

    private String reportType;

    private String receiverName;

    private String receiverTel;

    private String abnormalType;

    private String receiverDept;

    private String clientEmotion;

    private String clientEmotionText;

    private String migrateFrom = "np";

    private String jdeCode;

    private String isSelfClaim;

    private String isMainHandler;

    private BigDecimal mainLimitAmount;

    private String limitCurrencyCode;

    private Date normalTransterDate;

    private String normalTransterUm;

    private String isInternetCall;

    private String joiningTrader;

    private String reporterEmail;

    private String reportModeDetail;

    public String getReporterEmail() {
        return reporterEmail;
    }

    public void setReporterEmail(String reporterEmail) {
        this.reporterEmail = reporterEmail;
    }

    public String getReportModeDetail() {
        return reportModeDetail;
    }

    public void setReportModeDetail(String reportModeDetail) {
        this.reportModeDetail = reportModeDetail;
    }

    public String getIdClmpReport() {
        return idClmpReport;
    }

    public void setIdClmpReport(String idClmpReport) {
        this.idClmpReport = idClmpReport == null ? null : idClmpReport.trim();
    }

    public String getIdClmReportInfo() {
        return idClmReportInfo;
    }

    public void setIdClmReportInfo(String idClmReportInfo) {
        this.idClmReportInfo = idClmReportInfo == null ? null : idClmReportInfo.trim();
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public Date getReportAcceptDate() {
        return reportAcceptDate;
    }

    public void setReportAcceptDate(Date reportAcceptDate) {
        this.reportAcceptDate = reportAcceptDate;
    }

    public String getReportType() {
        return reportType;
    }

    public void setReportType(String reportType) {
        this.reportType = reportType == null ? null : reportType.trim();
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName == null ? null : receiverName.trim();
    }

    public String getReceiverTel() {
        return receiverTel;
    }

    public void setReceiverTel(String receiverTel) {
        this.receiverTel = receiverTel == null ? null : receiverTel.trim();
    }

    public String getAbnormalType() {
        return abnormalType;
    }

    public void setAbnormalType(String abnormalType) {
        this.abnormalType = abnormalType == null ? null : abnormalType.trim();
    }

    public String getReceiverDept() {
        return receiverDept;
    }

    public void setReceiverDept(String receiverDept) {
        this.receiverDept = receiverDept == null ? null : receiverDept.trim();
    }

    public String getClientEmotion() {
        return clientEmotion;
    }

    public void setClientEmotion(String clientEmotion) {
        this.clientEmotion = clientEmotion == null ? null : clientEmotion.trim();
    }

    public String getClientEmotionText() {
        return clientEmotionText;
    }

    public void setClientEmotionText(String clientEmotionText) {
        this.clientEmotionText = clientEmotionText == null ? null : clientEmotionText.trim();
    }

    public String getMigrateFrom() {
        return migrateFrom;
    }

    public void setMigrateFrom(String migrateFrom) {
        this.migrateFrom = migrateFrom == null ? null : migrateFrom.trim();
    }

    public String getJdeCode() {
        return jdeCode;
    }

    public void setJdeCode(String jdeCode) {
        this.jdeCode = jdeCode == null ? null : jdeCode.trim();
    }

    public String getIsSelfClaim() {
        return isSelfClaim;
    }

    public void setIsSelfClaim(String isSelfClaim) {
        this.isSelfClaim = isSelfClaim == null ? null : isSelfClaim.trim();
    }

    public String getIsMainHandler() {
        return isMainHandler;
    }

    public void setIsMainHandler(String isMainHandler) {
        this.isMainHandler = isMainHandler == null ? null : isMainHandler.trim();
    }

    public BigDecimal getMainLimitAmount() {
        return mainLimitAmount;
    }

    public void setMainLimitAmount(BigDecimal mainLimitAmount) {
        this.mainLimitAmount = mainLimitAmount;
    }

    public String getLimitCurrencyCode() {
        return limitCurrencyCode;
    }

    public void setLimitCurrencyCode(String limitCurrencyCode) {
        this.limitCurrencyCode = limitCurrencyCode == null ? null : limitCurrencyCode.trim();
    }

    public Date getNormalTransterDate() {
        return normalTransterDate;
    }

    public void setNormalTransterDate(Date normalTransterDate) {
        this.normalTransterDate = normalTransterDate;
    }

    public String getNormalTransterUm() {
        return normalTransterUm;
    }

    public void setNormalTransterUm(String normalTransterUm) {
        this.normalTransterUm = normalTransterUm == null ? null : normalTransterUm.trim();
    }

    public String getIsInternetCall() {
        return isInternetCall;
    }

    public void setIsInternetCall(String isInternetCall) {
        this.isInternetCall = isInternetCall;
    }

    public String getJoiningTrader() {
        return joiningTrader;
    }

    public void setJoiningTrader(String joiningTrader) {
        this.joiningTrader = joiningTrader;
    }
}