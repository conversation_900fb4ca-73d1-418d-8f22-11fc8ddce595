package com.paic.ncbs.claim.dao.mapper.pet;

import com.paic.ncbs.claim.model.dto.pet.PetHospitalDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface PetHospitalMapper {

    void addPetHospitalList(@Param("petHospitalList") List<PetHospitalDTO> petHospitalList);

    List<PetHospitalDTO> getPetHospitalList(PetHospitalDTO petHospitalDTO);

    void delPetHospital(@Param("petHospitalCodeList") List<String> petHospitalCodeList,@Param("updatedBy")String updatedBy);

    Integer getMaxId();
}