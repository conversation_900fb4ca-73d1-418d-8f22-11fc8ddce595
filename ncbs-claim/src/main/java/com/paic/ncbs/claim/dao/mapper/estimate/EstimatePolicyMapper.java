package com.paic.ncbs.claim.dao.mapper.estimate;

import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyInfoDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicySumDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.List;

@MapperScan
public interface EstimatePolicyMapper {

    List<EstimatePolicyDTO> getByReportNoAndCaseTimes(@Param("reportNo") String reportNo,
													  @Param("caseTimes") Integer caseTimes);

    List<EstimatePolicyDTO> getEstimatePolicyFromCopy(@Param("reportNo") String reportNo,
                                                      @Param("caseTimes") Integer caseTimes);

    void addBatchEstimatePolicy(@Param("paramList") List<EstimatePolicyDTO> estimatePolicyListUpdate);

    void modifyBatchEstimatePolicy(@Param("paramList") List<EstimatePolicyDTO> estimatePolicyList);

    public List<EstimatePolicyDTO> getEstimatePolicyList(@Param("reportNo") String reportNo,
                                                         @Param("caseTimes") Integer caseTimes);

    BigDecimal getEstimateAmount(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    BigDecimal getAmount(@Param("reportNo") String reportNo, @Param("caseNo") String caseNo,
                         @Param("caseTimes") Integer caseTimes);

    BigDecimal getEstimateAmountByCondition(@Param("policyNo") String policyNo, @Param("caseNo") String caseNo,
                                            @Param("caseTimes") Integer caseTimes);

    List<EstimatePolicyDTO> getEstimatePolicyByHistoryList(@Param("reportNo") String reportNo,
                                                           @Param("caseTimes") Integer caseTimes);

    int checkExists(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,
                    @Param("estimateType") String estimateType);

    List<String> getPolicyDeptCodeList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    public String getPolicyDeptCode(@Param("caseNo") String caseNo);

    List<EstimatePolicySumDTO> getEstimatePolicySum(@Param("reportNo") String reportNo,
													@Param("caseTimes") Integer caseTimes);

    BigDecimal getRegisterAmount(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    void modifyPolicyAmount(@Param("policyList") List<EstimatePolicyDTO> planList);

    List<EstimatePolicyInfoDTO> getEstimateDataByReportNo(@Param("reportNo") String reportNo,
                                                          @Param("caseTimes") Integer caseTimes);

    void delEstimateDataByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    BigDecimal getLatestRegisterAmount(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,
                                       @Param("estimateType") String estimateType);

    String getIdAhcsEstimateDutyRecord(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,
                                       @Param("estimateType") String estimateType);

    BigDecimal getEstimatePolicyAmount(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    String getProductClass(@Param("productCode") String productCode, @Param("productVersion") String productVersion);

    BigDecimal getAmountByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    BigDecimal getEstimateAmountByPolicyNo(@Param("policyNo") String policyNo,@Param("reportNo") String reportNo,
                                           @Param("caseTimes") Integer caseTimes);

    String getIsRegister(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    BigDecimal getRegisterAmountByPolicyNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes ,@Param("policyNo") String policyNo);

    List<EstimatePolicyDTO> getEstimateDataByPolicy(@Param("policyNo") String policyNo, @Param("caseTimes") Integer caseTimes,@Param("reportNo") String reportNo);


    List<EstimatePolicyDTO> getByReportNoAndCaseTimesForRestartCase(@Param("reportNo") String reportNo,
                                                      @Param("caseTimes") Integer caseTimes);

    /**
     * 案件重开，数据拷贝
     * @param paramList
     */
    void copyForCaseReopen(List<EstimatePolicyInfoDTO> paramList);

    String getIdByCaseNo(@Param("caseNo") String caseNo);
}
