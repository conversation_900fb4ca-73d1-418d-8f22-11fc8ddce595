package com.paic.ncbs.claim.dao.mapper.pet;

import com.paic.ncbs.claim.model.dto.pet.PetInjureDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface PetInjureMapper {

    void addPetInjure(PetInjureDTO petInjureDTO);

    PetInjureDTO getPetInjure(PetInjureDTO petInjureDTO);

    void delPetInjure(PetInjureDTO petInjureDTO);

    void delPetInjureById(@Param("idClmsPetInjure")String idClmsPetInjure, @Param("updatedBy")String updatedBy);

    void delPetInjures(String reportNo, Integer caseTimes);

    PetInjureDTO getPetInjures(String reportNo, Integer caseTimes);

}