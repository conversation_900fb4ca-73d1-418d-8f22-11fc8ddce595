package com.paic.ncbs.claim.model.vo.pet;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.settle.PetDetailDTO;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class PetInjureVO {
    private String idClmsPetInjure;
    private String reportNo;
    private Integer caseTimes;
    private List<TypeAndAmountVO> typeAndAmountList;
    private String accidentPet;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date accidentDate;
    private String accidentOverseas;
    private String accidentProvinceCode;
    private String accidentCityCode;
    private String accidentCountryCode;
    private String accidentContinentCode;
    private String accidentPlace;
    private String taskCode;
    private String status;
    private String targetJudgeFlag;
    private String diseaseClass;
    private String treatmentDetail;
    private String diseaseDiagnosis;
    private String diagnosisDetail;
    private String detailDesc;
    private List<PetInjureExVO> petInjureExList;
    private List<PetTreatmentDetailVO> petTreatmentDetailList;
    private String userId;

    private BigDecimal sumQuantity;
    private BigDecimal sumUnitPrice;
    private BigDecimal sumTotalPrice;
    private BigDecimal sumCustomerAmount;
    private BigDecimal sumPayAmount;
    private PetDetailDTO petDetail;
    private List<PetLossVO> petLossList;

    public PetInjureVO() {
    }

    public PetInjureVO(String reportNo, Integer caseTimes, String taskCode) {
        this.reportNo = reportNo;
        this.caseTimes = caseTimes;
        this.taskCode = taskCode;
    }

    public List<PetLossVO> getPetLossList() {
        return petLossList;
    }

    public void setPetLossList(List<PetLossVO> petLossList) {
        this.petLossList = petLossList;
    }

    public String getIdClmsPetInjure() {
        return idClmsPetInjure;
    }

    public void setIdClmsPetInjure(String idClmsPetInjure) {
        this.idClmsPetInjure = idClmsPetInjure;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public Integer getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Integer caseTimes) {
        this.caseTimes = caseTimes;
    }

    public List<TypeAndAmountVO> getTypeAndAmountList() {
        return typeAndAmountList;
    }

    public void setTypeAndAmountList(List<TypeAndAmountVO> typeAndAmountList) {
        this.typeAndAmountList = typeAndAmountList;
    }

    public String getAccidentPet() {
        return accidentPet;
    }

    public void setAccidentPet(String accidentPet) {
        this.accidentPet = accidentPet;
    }

    public Date getAccidentDate() {
        return accidentDate;
    }

    public void setAccidentDate(Date accidentDate) {
        this.accidentDate = accidentDate;
    }

    public String getAccidentOverseas() {
        return accidentOverseas;
    }

    public void setAccidentOverseas(String accidentOverseas) {
        this.accidentOverseas = accidentOverseas;
    }

    public String getAccidentProvinceCode() {
        return accidentProvinceCode;
    }

    public void setAccidentProvinceCode(String accidentProvinceCode) {
        this.accidentProvinceCode = accidentProvinceCode;
    }

    public String getAccidentCityCode() {
        return accidentCityCode;
    }

    public void setAccidentCityCode(String accidentCityCode) {
        this.accidentCityCode = accidentCityCode;
    }

    public String getAccidentCountryCode() {
        return accidentCountryCode;
    }

    public void setAccidentCountryCode(String accidentCountryCode) {
        this.accidentCountryCode = accidentCountryCode;
    }

    public String getAccidentContinentCode() {
        return accidentContinentCode;
    }

    public void setAccidentContinentCode(String accidentContinentCode) {
        this.accidentContinentCode = accidentContinentCode;
    }

    public String getAccidentPlace() {
        return accidentPlace;
    }

    public void setAccidentPlace(String accidentPlace) {
        this.accidentPlace = accidentPlace;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTargetJudgeFlag() {
        return targetJudgeFlag;
    }

    public void setTargetJudgeFlag(String targetJudgeFlag) {
        this.targetJudgeFlag = targetJudgeFlag;
    }

    public String getDiseaseClass() {
        return diseaseClass;
    }

    public void setDiseaseClass(String diseaseClass) {
        this.diseaseClass = diseaseClass;
    }

    public String getTreatmentDetail() {
        return treatmentDetail;
    }

    public void setTreatmentDetail(String treatmentDetail) {
        this.treatmentDetail = treatmentDetail;
    }

    public String getDiseaseDiagnosis() {
        return diseaseDiagnosis;
    }

    public void setDiseaseDiagnosis(String diseaseDiagnosis) {
        this.diseaseDiagnosis = diseaseDiagnosis;
    }

    public String getDiagnosisDetail() {
        return diagnosisDetail;
    }

    public void setDiagnosisDetail(String diagnosisDetail) {
        this.diagnosisDetail = diagnosisDetail;
    }

    public String getDetailDesc() {
        return detailDesc;
    }

    public void setDetailDesc(String detailDesc) {
        this.detailDesc = detailDesc;
    }

    public List<PetInjureExVO> getPetInjureExList() {
        return petInjureExList;
    }

    public void setPetInjureExList(List<PetInjureExVO> petInjureExList) {
        this.petInjureExList = petInjureExList;
    }

    public List<PetTreatmentDetailVO> getPetTreatmentDetailList() {
        return petTreatmentDetailList;
    }

    public void setPetTreatmentDetailList(List<PetTreatmentDetailVO> petTreatmentDetailList) {
        this.petTreatmentDetailList = petTreatmentDetailList;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public BigDecimal getSumQuantity() {
        return sumQuantity;
    }

    public void setSumQuantity(BigDecimal sumQuantity) {
        this.sumQuantity = sumQuantity;
    }

    public BigDecimal getSumUnitPrice() {
        return sumUnitPrice;
    }

    public void setSumUnitPrice(BigDecimal sumUnitPrice) {
        this.sumUnitPrice = sumUnitPrice;
    }

    public BigDecimal getSumTotalPrice() {
        return sumTotalPrice;
    }

    public void setSumTotalPrice(BigDecimal sumTotalPrice) {
        this.sumTotalPrice = sumTotalPrice;
    }

    public BigDecimal getSumCustomerAmount() {
        return sumCustomerAmount;
    }

    public void setSumCustomerAmount(BigDecimal sumCustomerAmount) {
        this.sumCustomerAmount = sumCustomerAmount;
    }

    public BigDecimal getSumPayAmount() {
        return sumPayAmount;
    }

    public void setSumPayAmount(BigDecimal sumPayAmount) {
        this.sumPayAmount = sumPayAmount;
    }

    public PetDetailDTO getPetDetail() {
        return petDetail;
    }

    public void setPetDetail(PetDetailDTO petDetail) {
        this.petDetail = petDetail;
    }
}
