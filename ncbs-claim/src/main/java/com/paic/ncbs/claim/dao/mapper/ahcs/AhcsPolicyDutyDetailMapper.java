package com.paic.ncbs.claim.dao.mapper.ahcs;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyDetailEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AhcsPolicyDutyDetailMapper extends BaseDao<AhcsPolicyDutyDetailEntity> {

	int deleteByPrimaryKey(String idAhcsPolicyDutyDetail);

    int insert(AhcsPolicyDutyDetailEntity record);

    int insertSelective(AhcsPolicyDutyDetailEntity record);

    AhcsPolicyDutyDetailEntity selectByPrimaryKey(String idAhcsPolicyDutyDetail);

    int updateByPrimaryKeySelective(AhcsPolicyDutyDetailEntity record);

    int updateByPrimaryKey(AhcsPolicyDutyDetailEntity record);

    int deleteByIdAhcsPolicyDuty(String idAhcsPolicyDuty);

    int insertList(@Param("policyDutyDetailEntities") List<AhcsPolicyDutyDetailEntity> policyDutyDetailEntities);

    List<AhcsPolicyDutyDetailEntity> getInfoByDutyId(String idAhcsPolicyDuty);

    void deleteByReportNoAndPolicyNo(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo);

    void insertBatch(@Param("list") List<AhcsPolicyDutyDetailEntity> policyDutyDetailEntities);
}