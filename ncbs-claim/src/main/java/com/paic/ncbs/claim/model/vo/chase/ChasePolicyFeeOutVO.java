package com.paic.ncbs.claim.model.vo.chase;


import com.paic.ncbs.claim.model.dto.fee.FeePayDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


@NoArgsConstructor
@AllArgsConstructor
@Data
public class ChasePolicyFeeOutVO {



    //@NotNull(message = "追偿费用支出不能为空") 版本不兼容
    private FeePayDTO feePayDTO;


    private String lvl2DepartmentCode;


    private BigDecimal chasePolicyFeeOutTotalAmount;

}
