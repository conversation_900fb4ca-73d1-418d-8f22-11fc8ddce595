package com.paic.ncbs.claim.dao.entity.antimoneylaundering;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 控股人信息
 */
@Data
public class ClmsShareholderInfoEntity extends EntityDTO {

    /**
     * 主键
     */
    private String id;
    /**
     * 报案号
     */
    private String reportNo;
    /**
     * 赔付次数
     */
    private int caseTimes;

    /**
     * 股东姓名
     */
    private String shareholderName;

    /**
     * 股东类型：股权或表决权；人事、财务控制；高级管理人
     */
    private String shareholderType;
    /**
     * 股权占比精确两位小数
     */
    private BigDecimal shareholderRatio;
    /**
     * 股东证件类型
     */
    private String shareholderCardType;

    /**
     * 股东证件证件号
     */
    private String shareholderCardNo;
    /**
     * 股东证件有效起期
     */
    private Date shareholderCardStart;

    /**
     * 股东证件有效止期
     */
    private Date shareholderCardEnd;

    /**
     * 股东证件附件id
     */
    private String shareholderCardFileId;
    /**
     * 股东住址
     */
    private String shareholderAddress;
    /**
     * 股东证件是否长期有效
     */
    private String shareholderLongTermFlag;

    /**
     *理赔反洗钱公司信息表主键
     */
    private String idClmsAmlCompanyInfo;

    /**
     * 有效状态 0-有效，1作废
     */
    private String effectiveStatus;

}
