package com.paic.ncbs.claim.dao.entity.clms;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 法律责任及其他信息(ClmsLegalLiabilityClassInfo)实体类
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:46
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ClmsLegalLiabilityClassInfo implements Serializable {
    private static final long serialVersionUID = -19225294893453155L;
    /**
     * 主键
     */
    private String id;
    /**
     * 报案号
     */
    private String reportNo;
    /**
     * 赔付次数
     */
    private Integer caseTimes;
    /**
     * 费用类型- 诉讼费、执行费、司法鉴定费、律师代理费、其他
     */
    private String feeType;
    /**
     * 赔偿金额
     */
    private BigDecimal payAmount;
    /**
     * 支付对象
     */
    private String payObject;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人员
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDate;
    /**
     * 修改人员
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDate;


}

