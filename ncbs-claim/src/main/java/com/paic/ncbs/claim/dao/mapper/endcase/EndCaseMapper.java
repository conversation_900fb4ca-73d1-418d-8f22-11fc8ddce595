package com.paic.ncbs.claim.dao.mapper.endcase;

import com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@MapperScan
public interface EndCaseMapper {

     WholeCaseBaseDTO getWholeCaseBaseDTO(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

     void modifyWholeCase(WholeCaseBaseDTO wholeCaseBaseDTO);

    void modifyWholeCaseByAuto(WholeCaseBaseDTO wholeCaseBaseDTO);

     Integer getSendMailPolicy(@Param("policyNo") String policyNo, @Param("policyCerNo") String policyCerNo);

     void updateCaseBase(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("caseStatus") String caseStatus);

    List<PolicyInfoDTO> getCasePolicy(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    List<String> getNoSettleFormulaPolicyList(@Param("searchDay") Integer searchDay);

     BigDecimal getEndAmountByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

     List<WholeCaseBaseDTO> findDayEndCase();

     Date getAccidentDate(@Param("reportNo") String reportNo);

    WholeCaseBaseDTO getWholeDocumentFullDate(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    void updateWholeDocumentFullDate(WholeCaseBaseDTO wholeCaseBaseDTO);

}
