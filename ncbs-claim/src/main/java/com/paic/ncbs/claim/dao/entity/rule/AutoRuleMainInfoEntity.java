package com.paic.ncbs.claim.dao.entity.rule;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
    * 理赔自动核赔记录表
    */
public class AutoRuleMainInfoEntity extends EntityDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
    *  主键
    */
    private String idAutoRuleMain;

    /**
    * 报案号
    */
    private String reportNo;

    /**
    * 赔付次数
    */
    private Integer caseTimes;

    /**
    * 规则类型：
    */
    private String ruleType;

    /**
    * 自核规则是否满足通过（Y-是，N-否）
    */
    private String isRulePass;

    /**
    * 满足自核规则（Y-是，N-否）,执行结果更新
    */
    private String isAutoPass;

    /**
    * 通过时间
    */
    private Date autoPassDate;

    /**
    * 是否有效(Y：是/N：否)
    */
    private String isEffective;

    /**
     * 规则明细信息
     */
    private List<AutoRuleDetailInfoEntity> autoRuleDetailInfoEntities;

    public String getIdAutoRuleMain() {
        return idAutoRuleMain;
    }

    public void setIdAutoRuleMain(String idAutoRuleMain) {
        this.idAutoRuleMain = idAutoRuleMain;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public Integer getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Integer caseTimes) {
        this.caseTimes = caseTimes;
    }

    public String getRuleType() {
        return ruleType;
    }

    public void setRuleType(String ruleType) {
        this.ruleType = ruleType;
    }

    public String getIsRulePass() {
        return isRulePass;
    }

    public void setIsRulePass(String isRulePass) {
        this.isRulePass = isRulePass;
    }

    public String getIsAutoPass() {
        return isAutoPass;
    }

    public void setIsAutoPass(String isAutoPass) {
        this.isAutoPass = isAutoPass;
    }

    public Date getAutoPassDate() {
        return autoPassDate;
    }

    public void setAutoPassDate(Date autoPassDate) {
        this.autoPassDate = autoPassDate;
    }

    public String getIsEffective() {
        return isEffective;
    }

    public void setIsEffective(String isEffective) {
        this.isEffective = isEffective;
    }

    public List<AutoRuleDetailInfoEntity> getAutoRuleDetailInfoEntities() {
        return autoRuleDetailInfoEntities;
    }

    public void setAutoRuleDetailInfoEntities(List<AutoRuleDetailInfoEntity> autoRuleDetailInfoEntities) {
        this.autoRuleDetailInfoEntities = autoRuleDetailInfoEntities;
    }
}