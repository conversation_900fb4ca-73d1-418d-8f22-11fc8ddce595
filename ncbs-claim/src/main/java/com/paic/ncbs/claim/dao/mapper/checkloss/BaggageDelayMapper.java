package com.paic.ncbs.claim.dao.mapper.checkloss;

import com.paic.ncbs.claim.model.vo.settle.SettleBaggageDelayVO;
import com.paic.ncbs.claim.model.dto.checkloss.BaggageDelayDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface BaggageDelayMapper {

    public void addBaggageDelay(BaggageDelayDTO baggageDelayDTO);

    public void removeBaggageDelay(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("taskCode") String taskCode, @Param("channelProcessId") String channelProcessId);

    void updateEffective(BaggageDelayDTO baggageDelayDTO);

    public BaggageDelayDTO getBaggageDelay(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("status") String status, @Param("taskCode") String taskCode, @Param("channelProcessId") String channelProcessId);

    public SettleBaggageDelayVO getSettleBaggageDelay(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("status") String status, @Param("taskCode") String taskCode, @Param("idAhcsChannelProcess") String idAhcsChannelProcess);

    public List<BaggageDelayDTO> getBaggageDelayList(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskCode") String taskCode);

    public void addBaggageDelayList(@Param("baggageDelayList") List<BaggageDelayDTO> baggageDelayList,
                                    @Param("caseTimes") Integer caseTimes, @Param("userId") String userId, @Param("channelProcessId") String channelProcessId);

    public void updateBaggageDelay(BaggageDelayDTO baggageDelayDTO);

}
