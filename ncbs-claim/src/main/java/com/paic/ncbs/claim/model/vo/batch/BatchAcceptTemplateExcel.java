package com.paic.ncbs.claim.model.vo.batch;

import com.paic.ncbs.claim.common.annotation.ModelProp;
import lombok.Data;

import java.io.Serializable;

@Data
public class BatchAcceptTemplateExcel implements Serializable {

    @ModelProp(name = "序号",colIndex = 0)
    private String  num ;

    @ModelProp(name = "保单号",colIndex = 1)
    private String policyNos;

    @ModelProp(name = "被保险人姓名",colIndex = 2)
    private String  holderName ;

    @ModelProp(name = "身份证(文本格式)",colIndex = 3)
    private String  certificateNo ;

    @ModelProp(name = "出险时间", colIndex = 4)
    private String accidentDate ;

    @ModelProp(name = "报案人", colIndex = 5)
    private String reporterName ;

    @ModelProp(name = "联系人", colIndex = 6)
    private String linkMan ;

    @ModelProp(name = "联系电话", colIndex = 7)
    private String linkManTelephone ;

    @ModelProp(name = "事故类型", colIndex = 8)
    private String accidentType ;

    @ModelProp(name = "出险类型模块", colIndex = 9)
    private String accidentTypeName ;

    @ModelProp(name = "出险类型", colIndex = 10)
    private String accidentKind ;

    @ModelProp(name = "备注", colIndex = 11)
    private String remark ;

    private String returnMsg ;

    private String reportNo ;

    private String departCode ;














}
