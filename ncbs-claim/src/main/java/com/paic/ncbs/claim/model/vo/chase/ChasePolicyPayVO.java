package com.paic.ncbs.claim.model.vo.chase;

import java.math.BigDecimal;
import java.util.List;

public class ChasePolicyPayVO extends PolicyBaseVO{
	

	private BigDecimal policyChasePay = BigDecimal.ZERO;
	

	private List<ChasePlanPayVO> chasePlanPays;

	public List<ChasePlanPayVO> getChasePlanPays() {
		return chasePlanPays;
	}

	public void setChasePlanPays(List<ChasePlanPayVO> chasePlanPays) {
		this.chasePlanPays = chasePlanPays;
	}

	public BigDecimal getPolicyChasePay() {
		return policyChasePay;
	}

	public void setPolicyChasePay(BigDecimal policyChasePay) {
		this.policyChasePay = policyChasePay;
	}
	
}
