package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.math.BigDecimal;

/**
 * 意健险宠物险信息表
 */
public class ReportAccidentPetEntity extends EntityDTO {


    /**
     * 主键
     */
    private String idAhcsReportAccidentPet;

    /**
     * 报案号
     */
    private String reportNo;

    /**
     * 标的宠物信息
     */
    private String petDesc;

    /**
     * 宠物医疗保障项目
     */
    private String medicalProjects;
    /**
     * 客户预约号
     */
    private String userReservationNo;

    /**
     * 宠物医院编号
     */
    private String hospitalNo;

    /**
     * 宠物病情主诉
     */
    private String illnessDesc;

    /**
     * 初步诊断结果
     */
    private String firstDiagnosisResult;

    /**
     * 单证信息
     */
    private String documentInfo;

    /**
     * 治疗信息
     */
    private String medicalInfo;
    /**
     * 费用预估
     */
    private BigDecimal costEstimate;
    /**
     * 宠物损伤类型
     */
    private String petInjuredType;
    /**
     * 宠物类型
     */
    private String accidentPet;
    /**
     * 治疗状态
     */
    private String treatCondition;
    /**
     * 宠物医院名称
     */
    private String hospitalName;
    /**
     * 宠物医院地点
     */
    private String hospitalPlace;
    /**
     * 病情分类
     */
    private String diseaseClassification;
    /**
     * 处理治疗
     */
    private String treatment;
    /**
     * 诊断说明
     */
    private String diagnosisDetail;

    public String getIdAhcsReportAccidentPet() {
        return idAhcsReportAccidentPet;
    }

    public void setIdAhcsReportAccidentPet(String idAhcsReportAccidentPet) {
        this.idAhcsReportAccidentPet = idAhcsReportAccidentPet;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getPetDesc() {
        return petDesc;
    }

    public void setPetDesc(String petDesc) {
        this.petDesc = petDesc;
    }

    public String getMedicalProjects() {
        return medicalProjects;
    }

    public void setMedicalProjects(String medicalProjects) {
        this.medicalProjects = medicalProjects;
    }

    public String getUserReservationNo() {
        return userReservationNo;
    }

    public void setUserReservationNo(String userReservationNo) {
        this.userReservationNo = userReservationNo;
    }

    public String getHospitalNo() {
        return hospitalNo;
    }

    public void setHospitalNo(String hospitalNo) {
        this.hospitalNo = hospitalNo;
    }

    public String getIllnessDesc() {
        return illnessDesc;
    }

    public void setIllnessDesc(String illnessDesc) {
        this.illnessDesc = illnessDesc;
    }

    public String getFirstDiagnosisResult() {
        return firstDiagnosisResult;
    }

    public void setFirstDiagnosisResult(String firstDiagnosisResult) {
        this.firstDiagnosisResult = firstDiagnosisResult;
    }

    public String getDocumentInfo() {
        return documentInfo;
    }

    public void setDocumentInfo(String documentInfo) {
        this.documentInfo = documentInfo;
    }

    public String getMedicalInfo() {
        return medicalInfo;
    }

    public void setMedicalInfo(String medicalInfo) {
        this.medicalInfo = medicalInfo;
    }

    public BigDecimal getCostEstimate() {
        return costEstimate;
    }

    public void setCostEstimate(BigDecimal costEstimate) {
        this.costEstimate = costEstimate;
    }

    public String getPetInjuredType() {
        return petInjuredType;
    }

    public void setPetInjuredType(String petInjuredType) {
        this.petInjuredType = petInjuredType;
    }

    public String getAccidentPet() {
        return accidentPet;
    }

    public void setAccidentPet(String accidentPet) {
        this.accidentPet = accidentPet;
    }

    public String getTreatCondition() {
        return treatCondition;
    }

    public void setTreatCondition(String treatCondition) {
        this.treatCondition = treatCondition;
    }

    public String getHospitalName() {
        return hospitalName;
    }

    public void setHospitalName(String hospitalName) {
        this.hospitalName = hospitalName;
    }

    public String getHospitalPlace() {
        return hospitalPlace;
    }

    public void setHospitalPlace(String hospitalPlace) {
        this.hospitalPlace = hospitalPlace;
    }

    public String getDiseaseClassification() {
        return diseaseClassification;
    }

    public void setDiseaseClassification(String diseaseClassification) {
        this.diseaseClassification = diseaseClassification;
    }

    public String getTreatment() {
        return treatment;
    }

    public void setTreatment(String treatment) {
        this.treatment = treatment;
    }

    public String getDiagnosisDetail() {
        return diagnosisDetail;
    }

    public void setDiagnosisDetail(String diagnosisDetail) {
        this.diagnosisDetail = diagnosisDetail;
    }
}
