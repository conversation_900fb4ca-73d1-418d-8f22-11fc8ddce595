package com.paic.ncbs.claim.dao.mapper.estimate;

import com.paic.ncbs.claim.model.dto.estimate.EstimateIntermediateData;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-09-12
 * @description
 */
public interface EstimateIntermediateDataMapper {

    int insert(EstimateIntermediateData data);

    List<EstimateIntermediateData> queryListByCondition(EstimateIntermediateData param);

    void batchInsert(List<EstimateIntermediateData> estimateIntermediateDataList);
}
