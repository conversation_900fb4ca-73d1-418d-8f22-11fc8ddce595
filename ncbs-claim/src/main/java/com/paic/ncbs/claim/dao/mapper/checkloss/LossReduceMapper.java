package com.paic.ncbs.claim.dao.mapper.checkloss;

import com.paic.ncbs.claim.model.dto.checkloss.LossReduceDTO;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface LossReduceMapper {

    void saveLossReduce( LossReduceDTO lossReduceDTO);

    LossReduceDTO getLossReduce(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes);

    void updateEffective(LossReduceDTO lossReduceDTO);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);
}
