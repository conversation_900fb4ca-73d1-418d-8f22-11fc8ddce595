package com.paic.ncbs.claim.dao.mapper.antimoneylaundering;

import com.paic.ncbs.claim.dao.entity.antimoneylaundering.ClmsAmlCompanyInfoEntity;
import com.paic.ncbs.claim.dao.entity.antimoneylaundering.ClmsShareholderInfoEntity;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * 公司反洗钱mapper
 */
@MapperScan
public interface ClmsAmlCompanyInfoMapper {
    /**
     * 保存信息
     * @param entity
     */
    public void saveAmlCompanyInfo(ClmsAmlCompanyInfoEntity entity);

    /**
     * 保存股东信息
     * @param lists
     */
    public void saveShareholderInfos(List<ClmsShareholderInfoEntity> lists);

    /**
     * 根据客户号，报案号，赔付次数查询
     * @return
     */
    ClmsAmlCompanyInfoEntity getAmlCompanyInfo(ClmsAmlCompanyInfoEntity entity);

    /**
     * 根据id更新删除标志为Y
     * @param id
     */
    public void updateCompanyStatus(String id);

    /**
     * 更新公司反洗钱数据
     * @param entity
     */
    public void updateAmlCompanyInfo(ClmsAmlCompanyInfoEntity entity);

    /**
     * 根据主键更新股东信息 删除标志为Y
     * @param id
     */
    public void updateShareholderData(String id);


    /**
     * 根据id查询
     * @param id
     * @return
     */
    public ClmsAmlCompanyInfoEntity getAmlCompanyInfoById(String id);

    /**
     * 根据公司反洗钱信息表主键查询股东信息
     * @param id
     * @return
     */
    List<ClmsShareholderInfoEntity> getShareHolderInfos(String id);

    /**
     * 根据报案号和赔付次数判断是否有数据
     * @param reportNo
     * @param caseTimes
     * @return
     */
    Integer getAmlCompanyInfoCount(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);

    /**
     * 根据姓名，证件类型，证件号查询反洗钱信息
     * @param entity
     * @return
     */
    ClmsAmlCompanyInfoEntity getAmlCompanyByNameAndCardTypeAndCardNo(ClmsAmlCompanyInfoEntity entity);

    /**
     * 根据客户号查询反洗钱信息
     * @param entity
     * @return
     */
    ClmsAmlCompanyInfoEntity getAmlCompanyByCustomerNo(ClmsAmlCompanyInfoEntity entity);
}
