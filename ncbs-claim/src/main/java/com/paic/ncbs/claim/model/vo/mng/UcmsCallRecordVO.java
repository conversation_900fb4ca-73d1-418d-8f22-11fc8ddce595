package com.paic.ncbs.claim.model.vo.mng;

public class UcmsCallRecordVO {


    private Long bridgeDate;


    private String callDuration;


    private String callUuid;


    private String fromNumber;


    private String fromVirtualNumber;


    private Long hangupDate;


    private String outId;


    private String recordPath;


    private Long startCallDate;


    private String talkTime;


    private String tenantId;


    private String toNumber;


    private String toVirtualNumber;


    private String hangupCase;

    public Long getBridgeDate() {
        return bridgeDate;
    }

    public void setBridgeDate(Long bridgeDate) {
        this.bridgeDate = bridgeDate;
    }

    public String getCallDuration() {
        return callDuration;
    }

    public void setCallDuration(String callDuration) {
        this.callDuration = callDuration;
    }

    public String getCallUuid() {
        return callUuid;
    }

    public void setCallUuid(String callUuid) {
        this.callUuid = callUuid;
    }

    public String getFromNumber() {
        return fromNumber;
    }

    public void setFromNumber(String fromNumber) {
        this.fromNumber = fromNumber;
    }

    public String getFromVirtualNumber() {
        return fromVirtualNumber;
    }

    public void setFromVirtualNumber(String fromVirtualNumber) {
        this.fromVirtualNumber = fromVirtualNumber;
    }

    public Long getHangupDate() {
        return hangupDate;
    }

    public void setHangupDate(Long hangupDate) {
        this.hangupDate = hangupDate;
    }

    public String getOutId() {
        return outId;
    }

    public void setOutId(String outId) {
        this.outId = outId;
    }

    public String getRecordPath() {
        return recordPath;
    }

    public void setRecordPath(String recordPath) {
        this.recordPath = recordPath;
    }

    public Long getStartCallDate() {
        return startCallDate;
    }

    public void setStartCallDate(Long startCallDate) {
        this.startCallDate = startCallDate;
    }

    public String getTalkTime() {
        return talkTime;
    }

    public void setTalkTime(String talkTime) {
        this.talkTime = talkTime;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getToNumber() {
        return toNumber;
    }

    public void setToNumber(String toNumber) {
        this.toNumber = toNumber;
    }

    public String getToVirtualNumber() {
        return toVirtualNumber;
    }

    public void setToVirtualNumber(String toVirtualNumber) {
        this.toVirtualNumber = toVirtualNumber;
    }

    public String getHangupCase() {
        return hangupCase;
    }

    public void setHangupCase(String hangupCase) {
        this.hangupCase = hangupCase;
    }
}
