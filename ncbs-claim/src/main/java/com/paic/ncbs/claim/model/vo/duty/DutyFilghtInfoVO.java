package com.paic.ncbs.claim.model.vo.duty;

import com.paic.ncbs.claim.model.dto.checkloss.FlightAllInfoDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 核责页面-航班延误数据展示VO
 */
public class DutyFilghtInfoVO {

    //航段信息数据
    private List<FlightAllInfoDTO> flightAllInfoList;

    //机票是否使用 
    private String isTicketUsed;

    //是否购票 
    private String isBuyTicket;

    //延误计算方式
    private String calculateMode;

    // 原航班号
    private String originalFlightNo;
    // 原计划起飞时间
    private String originalDepartTime;
    // 原实际到达时间
    private String originalArrivalTime;
    // 航班状态
    private String queryFlightStatus;;
    // 实际航班号
    private String realFlightNo;
    // 实际起飞时间
    private String realDepartTime;
    // 实际到达时间
    private String realArrivalTime;

    //  private String  departPlaceName ;
    //  private String  arrivalPlaceName ;
    //  private String  departPlace  ;
    //  private String  arrivalPlace ;

    //出发地   
    private String departPlace;

    //出发地名称 
    private String departPlaceName;

    //目的地
    private String arrivalPlace;

    //目的地名称
    private String arrivalPlaceName;

    //延误时长            
    private BigDecimal delayDuration;

    // 页面选择的航班状态
    private String[] flightStatusArr;//: ["AHCS_FLIGHT_STATUS-01"," AHCS_FLIGHT_STATUS-02"]

    // 电子客票号
    private String ticketNo;

    public List<FlightAllInfoDTO> getFlightAllInfoList() {
        return flightAllInfoList;
    }

    public void setFlightAllInfoList(List<FlightAllInfoDTO> flightAllInfoList) {
        this.flightAllInfoList = flightAllInfoList;
    }

    public String getIsTicketUsed() {
        return isTicketUsed;
    }

    public void setIsTicketUsed(String isTicketUsed) {
        this.isTicketUsed = isTicketUsed;
    }

    public String getIsBuyTicket() {
        return isBuyTicket;
    }

    public void setIsBuyTicket(String isBuyTicket) {
        this.isBuyTicket = isBuyTicket;
    }

    public String getCalculateMode() {
        return calculateMode;
    }

    public void setCalculateMode(String calculateMode) {
        this.calculateMode = calculateMode;
    }

    public String getDepartPlace() {
        return departPlace;
    }

    public void setDepartPlace(String departPlace) {
        this.departPlace = departPlace;
    }

    public String getArrivalPlace() {
        return arrivalPlace;
    }

    public void setArrivalPlace(String arrivalPlace) {
        this.arrivalPlace = arrivalPlace;
    }

    public BigDecimal getDelayDuration() {
        return delayDuration;
    }

    public void setDelayDuration(BigDecimal delayDuration) {
        this.delayDuration = delayDuration;
    }

    public String getTicketNo() {
        return ticketNo;
    }

    public void setTicketNo(String ticketNo) {
        this.ticketNo = ticketNo;
    }

    public String getDepartPlaceName() {
        return departPlaceName;
    }

    public void setDepartPlaceName(String departPlaceName) {
        this.departPlaceName = departPlaceName;
    }

    public String getArrivalPlaceName() {
        return arrivalPlaceName;
    }

    public void setArrivalPlaceName(String arrivalPlaceName) {
        this.arrivalPlaceName = arrivalPlaceName;
    }

    public String[] getFlightStatusArr() {
        return flightStatusArr;
    }

    public void setFlightStatusArr(String[] flightStatusArr) {
        this.flightStatusArr = flightStatusArr;
    }

    public String getOriginalFlightNo() {
        return originalFlightNo;
    }

    public void setOriginalFlightNo(String originalFlightNo) {
        this.originalFlightNo = originalFlightNo;
    }

    public String getOriginalDepartTime() {
        return originalDepartTime;
    }

    public void setOriginalDepartTime(String originalDepartTime) {
        this.originalDepartTime = originalDepartTime;
    }

    public String getOriginalArrivalTime() {
        return originalArrivalTime;
    }

    public void setOriginalArrivalTime(String originalArrivalTime) {
        this.originalArrivalTime = originalArrivalTime;
    }

    public String getQueryFlightStatus() {
        return queryFlightStatus;
    }

    public void setQueryFlightStatus(String queryFlightStatus) {
        this.queryFlightStatus = queryFlightStatus;
    }

    public String getRealFlightNo() {
        return realFlightNo;
    }

    public void setRealFlightNo(String realFlightNo) {
        this.realFlightNo = realFlightNo;
    }

    public String getRealDepartTime() {
        return realDepartTime;
    }

    public void setRealDepartTime(String realDepartTime) {
        this.realDepartTime = realDepartTime;
    }

    public String getRealArrivalTime() {
        return realArrivalTime;
    }

    public void setRealArrivalTime(String realArrivalTime) {
        this.realArrivalTime = realArrivalTime;
    }

}
