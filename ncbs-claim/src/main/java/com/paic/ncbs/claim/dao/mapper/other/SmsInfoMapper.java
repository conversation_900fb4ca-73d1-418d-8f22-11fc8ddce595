package com.paic.ncbs.claim.dao.mapper.other;

import com.paic.ncbs.claim.model.dto.message.SmsInfoDTO;
import com.paic.ncbs.claim.model.vo.record.SmsRecordVO;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface SmsInfoMapper {

	void addSmsInfo(SmsInfoDTO smsDTO);

	void updateSmsInfo(SmsInfoDTO smsDTO);

	List<SmsRecordVO> querySmsRecordByReportNo(SmsInfoDTO smsDTO);

	SmsRecordVO querySmsInfo(String reportNo,String smsStatus);

	void updateClmsSmsInfo(SmsInfoDTO smsDTO);
}
