package com.paic.ncbs.claim.dao.mapper.risk;


import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyHolderEntity;
import com.paic.ncbs.claim.model.dto.risk.RiskInfoDTO;
import com.paic.ncbs.claim.model.vo.risk.PolicyReportVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;


@MapperScan
public interface RiskInfoMapper {

	void addRiskInfo(RiskInfoDTO riskInfoDTO);

    List<RiskInfoDTO> getRiskInfo(RiskInfoDTO riskInfoDTO);

    List<PolicyReportVO> getReportCount(@Param("reportNo") String reportNo);

    List<AhcsPolicyHolderEntity> getPolicyHolder(@Param("reportNo") String reportNo);

}
