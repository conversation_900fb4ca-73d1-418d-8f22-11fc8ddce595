package com.paic.ncbs.claim.mq.producer;

import com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO;
import com.paic.ncbs.claim.model.dto.mq.claim.ClaimRepayCalReqDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLCertifyCollectDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLReceiptDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLReceiptTrackDto;
import com.paic.ncbs.claim.model.dto.mq.swf.SwfLogStoreDto;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.duty.PeopleHurtVO;
import lombok.SneakyThrows;

import java.util.List;

/**
 *      核赔环节
 */
public interface MqProducerCompensateService {
   void syncProducerCompensateLink(String reportNo, Integer caseTimes);

    @SneakyThrows
    SwfLogStoreDto initCertifyCollectSwfLogStoreDto(String reportNo, ReportCustomerInfoEntity customerInfo, TaskInfoDTO taskInfoDTO,
                                                    ReportInfoEntity reportInfo, PrpLCertifyCollectDto prpLCertifyCollectDto);

    ClaimRepayCalReqDto initClaimRepayCalReqDto(ReportAccidentEntity reportAccident, TaskInfoDTO taskInfoDTO, List<CaseBaseEntity> caseBaseInfoList, List<PaymentItemDTO> items, String certiType);

    List<PrpLReceiptTrackDto> initPrpLReceiptTrackDto(PolicyInfoDTO policyInfoDTO, PaymentItemDTO itemDTO,
                                                      List<MedicalBillInfoDTO> medicalBillInfoList, PeopleHurtVO peopleHurtVO);

    List<PrpLReceiptDto> initPrpLReceiptDto(PolicyInfoDTO policyInfoDTO, PaymentItemDTO itemDTO,
                                            List<MedicalBillInfoDTO> medicalBillInfoList, PeopleHurtVO peopleHurtVO);
}
