package com.paic.ncbs.claim.model.vo.report;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

public class TongChengAccidentExtendInfo {

    private String orderNo;
    private String departureTime;
    private BigDecimal ticketPrice;
    private String tNo;
    private String cashSeqNo;
    private String cashAccount;
    private String claimStatus;
    @ApiModelProperty("险种代码")
    private String planCode;
    private String dutyCode;
    private String accidentType;
    private String transportation;

    public String getAccidentType() {
        return accidentType;
    }

    public void setAccidentType(String accidentType) {
        this.accidentType = accidentType;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getDepartureTime() {
        return departureTime;
    }

    public void setDepartureTime(String departureTime) {
        this.departureTime = departureTime;
    }

    public BigDecimal getTicketPrice() {
        return ticketPrice;
    }

    public void setTicketPrice(BigDecimal ticketPrice) {
        this.ticketPrice = ticketPrice;
    }

    public String gettNo() {
        return tNo;
    }

    public void settNo(String tNo) {
        this.tNo = tNo;
    }

    public String getCashSeqNo() {
        return cashSeqNo;
    }

    public void setCashSeqNo(String cashSeqNo) {
        this.cashSeqNo = cashSeqNo;
    }

    public String getCashAccount() {
        return cashAccount;
    }

    public void setCashAccount(String cashAccount) {
        this.cashAccount = cashAccount;
    }

    public String getClaimStatus() {
        return claimStatus;
    }

    public void setClaimStatus(String claimStatus) {
        this.claimStatus = claimStatus;
    }

    public String getPlanCode() {
        return planCode;
    }

    public void setPlanCode(String planCode) {
        this.planCode = planCode;
    }

    public String getDutyCode() {
        return dutyCode;
    }

    public void setDutyCode(String dutyCode) {
        this.dutyCode = dutyCode;
    }

    public String getTransportation() {
        return transportation;
    }

    public void setTransportation(String transportation) {
        this.transportation = transportation;
    }
}
