package com.paic.ncbs.claim.feign;

import com.paic.ncbs.claim.feign.fallback.UwFeignFallback;
import com.paic.ncbs.claim.model.vo.senconduw.UwsLetterSellBackDTO;
import com.paic.ncbs.claim.model.vo.senconduw.UwTaskVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 *  核保
 * <AUTHOR>
 */
@FeignClient(name = "ncbs-uws", fallbackFactory = UwFeignFallback.class)
@Component
public interface UwFeign {


	/**
	 * 查询产品信息
	 * @param param
	 * @return
	 */
	@PostMapping("/uws/app/manual/udr/createTask")
	String createTask(@RequestBody UwTaskVO param);


	/**
	 * 函件回销
	 * @param uwsLetterSellBackDTO
	 * @return
	 */
	@PostMapping("/uws/app/letterInfo/letterSellBack")
	String letterSellBack(@RequestBody UwsLetterSellBackDTO uwsLetterSellBackDTO);

}
