package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

public class ClmpTradeDataMappingEntity extends EntityDTO {

    private static final long serialVersionUID = 6820825794986404145L;

    private String idClmpTradeDataMapping;

    private String batchReportTypeCode;

    private String batchReportTypeDesc;

    private String tradeDataKey;

    private String tradeDataKeyDesc;

    private String attrMapping;

    public String getIdClmpTradeDataMapping() {
        return idClmpTradeDataMapping;
    }

    public void setIdClmpTradeDataMapping(String idClmpTradeDataMapping) {
        this.idClmpTradeDataMapping = idClmpTradeDataMapping == null ? null : idClmpTradeDataMapping.trim();
    }

    public String getBatchReportTypeCode() {
        return batchReportTypeCode;
    }

    public void setBatchReportTypeCode(String batchReportTypeCode) {
        this.batchReportTypeCode = batchReportTypeCode == null ? null : batchReportTypeCode.trim();
    }

    public String getBatchReportTypeDesc() {
        return batchReportTypeDesc;
    }

    public void setBatchReportTypeDesc(String batchReportTypeDesc) {
        this.batchReportTypeDesc = batchReportTypeDesc == null ? null : batchReportTypeDesc.trim();
    }

    public String getTradeDataKey() {
        return tradeDataKey;
    }

    public void setTradeDataKey(String tradeDataKey) {
        this.tradeDataKey = tradeDataKey == null ? null : tradeDataKey.trim();
    }

    public String getTradeDataKeyDesc() {
        return tradeDataKeyDesc;
    }

    public void setTradeDataKeyDesc(String tradeDataKeyDesc) {
        this.tradeDataKeyDesc = tradeDataKeyDesc == null ? null : tradeDataKeyDesc.trim();
    }

    public String getAttrMapping() {
        return attrMapping;
    }

    public void setAttrMapping(String attrMapping) {
        this.attrMapping = attrMapping == null ? null : attrMapping.trim();
    }

}