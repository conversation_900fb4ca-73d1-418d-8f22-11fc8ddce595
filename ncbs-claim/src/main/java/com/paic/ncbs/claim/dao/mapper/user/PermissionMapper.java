package com.paic.ncbs.claim.dao.mapper.user;

import com.paic.ncbs.claim.model.dto.user.PermissionDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.List;

@MapperScan
public interface PermissionMapper {

    void addPermissionList(@Param("list") List<PermissionDTO> list);

    void updatePermission(PermissionDTO permissionDTO);

    void delPermission(@Param("deptCode") String deptCode,@Param("typeCode") String typeCode);

    List<PermissionDTO> getPermissionList(PermissionDTO permissionDTO);

    Integer getPermissionGrade(@Param("typeCode") String typeCode,@Param("deptCode") String deptCode,@Param("amount") BigDecimal amount);

}