package com.paic.ncbs.claim.model.vo.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@ApiModel(description = "宠物险信息VO")
public class ReportAccidentPetVO {

    @ApiModelProperty(value = "宠物医院编号")
    private String hospitalNo;

    @ApiModelProperty(value = "费用预估")
    private BigDecimal costEstimate;

    @ApiModelProperty(value = "宠物损伤类型", example = "0.其他宠物损伤 1.宠物医疗 2.传染病 3.宠物死亡、伤残 4.宠物走失 5.宠物三责财产损失")
    private String petInjuredType;

    @ApiModelProperty(value = "出险宠物(1,犬 2,猫)")
    private String accidentPet;

    @ApiModelProperty(value = "MS_2201:未治疗,MS_2202:治疗中,MS_2203:治疗结束")
    private String treatCondition;

    @ApiModelProperty(value = "宠物医院名称")
    private String hospitalName;

    @ApiModelProperty(value = "医院地点")
    private String hospitalPlace;

    public String getHospitalNo() {
        return hospitalNo;
    }

    public void setHospitalNo(String hospitalNo) {
        this.hospitalNo = hospitalNo;
    }

    public BigDecimal getCostEstimate() {
        return costEstimate;
    }

    public void setCostEstimate(BigDecimal costEstimate) {
        this.costEstimate = costEstimate;
    }

    public String getPetInjuredType() {
        return petInjuredType;
    }

    public void setPetInjuredType(String petInjuredType) {
        this.petInjuredType = petInjuredType;
    }

    public String getAccidentPet() {
        return accidentPet;
    }

    public void setAccidentPet(String accidentPet) {
        this.accidentPet = accidentPet;
    }

    public String getTreatCondition() {
        return treatCondition;
    }

    public void setTreatCondition(String treatCondition) {
        this.treatCondition = treatCondition;
    }

    public String getHospitalName() {
        return hospitalName;
    }

    public void setHospitalName(String hospitalName) {
        this.hospitalName = hospitalName;
    }

    public String getHospitalPlace() {
        return hospitalPlace;
    }

    public void setHospitalPlace(String hospitalPlace) {
        this.hospitalPlace = hospitalPlace;
    }
}
