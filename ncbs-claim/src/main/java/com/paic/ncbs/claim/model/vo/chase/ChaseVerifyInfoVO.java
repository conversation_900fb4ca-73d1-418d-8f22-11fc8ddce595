package com.paic.ncbs.claim.model.vo.chase;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChaseVerifyInfoVO {

	private String reportNo;
	

	private Integer caseTimes;
	

	private String verifyOptions;
	

	private String verifyRemark;

	private List<String> chaseSubTypeCode;

    private String recordTaskId;


    private List<String> mistakeCodeList;

}
