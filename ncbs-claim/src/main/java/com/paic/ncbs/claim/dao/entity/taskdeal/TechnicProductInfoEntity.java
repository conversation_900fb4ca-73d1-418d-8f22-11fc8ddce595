package com.paic.ncbs.claim.dao.entity.taskdeal;

import java.util.Date;

public class TechnicProductInfoEntity {

    private String idTechnicProductInfo;

    private String technicProductCode;

    private String technicProductName;

    private Date effectiveDate;

    private Date invalidateDate;

    private String status;

    private String productClass;

    private String createdBy;

    private Date createdDate;

    private String updatedBy;

    private Date updatedDate;

    private String targetType;

    private String normalType;

    private String isAllPurposeCard;

    private String isRemitUnderwrite;

    private String productSubclass;

    private String claimClass;

    public String getIdTechnicProductInfo() {
        return idTechnicProductInfo;
    }

    public void setIdTechnicProductInfo(String idTechnicProductInfo) {
        this.idTechnicProductInfo = idTechnicProductInfo == null ? null : idTechnicProductInfo.trim();
    }

    public String getTechnicProductCode() {
        return technicProductCode;
    }

    public void setTechnicProductCode(String technicProductCode) {
        this.technicProductCode = technicProductCode == null ? null : technicProductCode.trim();
    }

    public String getTechnicProductName() {
        return technicProductName;
    }

    public void setTechnicProductName(String technicProductName) {
        this.technicProductName = technicProductName == null ? null : technicProductName.trim();
    }

    public Date getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(Date effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public Date getInvalidateDate() {
        return invalidateDate;
    }

    public void setInvalidateDate(Date invalidateDate) {
        this.invalidateDate = invalidateDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getProductClass() {
        return productClass;
    }

    public void setProductClass(String productClass) {
        this.productClass = productClass == null ? null : productClass.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(Date updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getTargetType() {
        return targetType;
    }

    public void setTargetType(String targetType) {
        this.targetType = targetType == null ? null : targetType.trim();
    }

    public String getNormalType() {
        return normalType;
    }

    public void setNormalType(String normalType) {
        this.normalType = normalType == null ? null : normalType.trim();
    }

    public String getIsAllPurposeCard() {
        return isAllPurposeCard;
    }

    public void setIsAllPurposeCard(String isAllPurposeCard) {
        this.isAllPurposeCard = isAllPurposeCard == null ? null : isAllPurposeCard.trim();
    }

    public String getIsRemitUnderwrite() {
        return isRemitUnderwrite;
    }

    public void setIsRemitUnderwrite(String isRemitUnderwrite) {
        this.isRemitUnderwrite = isRemitUnderwrite == null ? null : isRemitUnderwrite.trim();
    }

    public String getProductSubclass() {
        return productSubclass;
    }

    public void setProductSubclass(String productSubclass) {
        this.productSubclass = productSubclass == null ? null : productSubclass.trim();
    }

    public String getClaimClass() {
        return claimClass;
    }

    public void setClaimClass(String claimClass) {
        this.claimClass = claimClass == null ? null : claimClass.trim();
    }
}