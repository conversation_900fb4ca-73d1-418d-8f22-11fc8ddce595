package com.paic.ncbs.claim.dao.entity.estimate;

import java.math.BigDecimal;
import java.util.Date;

public class PackageInfoEntity {

    private String idPackageInfo;

    private String idMarketproductInfo;

    private String packageCode;

    private String packageName;

    private Date invalidateDate;

    private Date effectiveDate;

    private String status;

    private String createdBy;

    private Date createdDate;

    private String updatedBy;

    private Date updatedDate;

    private String targetType;

    private BigDecimal totalCompensationMaxAmount;

    private BigDecimal compensationMaxAmount;

    public String getIdPackageInfo() {
        return idPackageInfo;
    }

    public void setIdPackageInfo(String idPackageInfo) {
        this.idPackageInfo = idPackageInfo == null ? null : idPackageInfo.trim();
    }

    public String getIdMarketproductInfo() {
        return idMarketproductInfo;
    }

    public void setIdMarketproductInfo(String idMarketproductInfo) {
        this.idMarketproductInfo = idMarketproductInfo == null ? null : idMarketproductInfo.trim();
    }

    public String getPackageCode() {
        return packageCode;
    }

    public void setPackageCode(String packageCode) {
        this.packageCode = packageCode == null ? null : packageCode.trim();
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName == null ? null : packageName.trim();
    }

    public Date getInvalidateDate() {
        return invalidateDate;
    }

    public void setInvalidateDate(Date invalidateDate) {
        this.invalidateDate = invalidateDate;
    }

    public Date getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(Date effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(Date updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getTargetType() {
        return targetType;
    }

    public void setTargetType(String targetType) {
        this.targetType = targetType == null ? null : targetType.trim();
    }

    public BigDecimal getTotalCompensationMaxAmount() {
        return totalCompensationMaxAmount;
    }

    public void setTotalCompensationMaxAmount(BigDecimal totalCompensationMaxAmount) {
        this.totalCompensationMaxAmount = totalCompensationMaxAmount;
    }

    public BigDecimal getCompensationMaxAmount() {
        return compensationMaxAmount;
    }

    public void setCompensationMaxAmount(BigDecimal compensationMaxAmount) {
        this.compensationMaxAmount = compensationMaxAmount;
    }
}