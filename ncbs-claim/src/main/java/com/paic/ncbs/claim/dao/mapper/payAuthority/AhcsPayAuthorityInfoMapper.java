package com.paic.ncbs.claim.dao.mapper.payAuthority;


import com.paic.ncbs.claim.model.dto.ahcs.AhcsPayAuthorityInfoDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface AhcsPayAuthorityInfoMapper {

    Integer getAhcsPayAuthorityInfoCount(@Param("reportNo") String reportNo,
                                         @Param("caseTimes") Integer caseTimes,
                                         @Param("idClmPaymentInfo") String idClmPaymentInfo);

    void saveAhcsPayAuthorityInfo(AhcsPayAuthorityInfoDTO ahcsPayAuthorityInfoDTO);

}
