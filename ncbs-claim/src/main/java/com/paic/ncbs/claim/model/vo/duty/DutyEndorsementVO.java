package com.paic.ncbs.claim.model.vo.duty;

import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@ApiModel("DutyEndorsementVO")
public class DutyEndorsementVO {

    @ApiModelProperty("责任名称")
    private String dutyName;

    @ApiModelProperty("责任金额")
    private BigDecimal dutyAmount;

    @ApiModelProperty("调整文本区域")
    private String adjustmentTextArea;

    @ApiModelProperty("责任代码")
    private String dutyCode;

    public String getDutyName() {
        return dutyName;
    }

    public void setDutyName(String dutyName) {
        this.dutyName = dutyName;
    }

    public BigDecimal getDutyAmount() {
        return dutyAmount;
    }

    public void setDutyAmount(BigDecimal dutyAmount) {
        this.dutyAmount = BigDecimalUtils.getScaleRoundHalfUp(dutyAmount,2);
    }

    public String getAdjustmentTextArea() {
        return adjustmentTextArea;
    }

    public void setAdjustmentTextArea(String adjustmentTextArea) {
        this.adjustmentTextArea = adjustmentTextArea;
    }

    public String getDutyCode() {
        return dutyCode;
    }

    public void setDutyCode(String dutyCode) {
        this.dutyCode = dutyCode;
    }

}
