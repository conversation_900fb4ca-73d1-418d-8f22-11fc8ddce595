package com.paic.ncbs.claim.dao.entity.prepay;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

/**
 * 预赔责任明细表(ClmsPolicyPrepayDutyDetail)实体类
 *
 * <AUTHOR>
 * @since 2023-12-15 11:01:24
 */
@Data
public class ClmsPolicyPrepayDutyDetailEntity implements Serializable {
    private static final long serialVersionUID = -44146858201942229L;
    /**
     * 主键
     */
    private String id;
    /**
     * 报案号
     */
    private String reportNo;
    /**
     * 赔付次数
     */
    private Integer caseTimes;
    /**
     * 预赔次数
     */
    private Integer subTimes;
    /**
     * 赔案号
     */
    private String caseNo;
    /**
     * 责任编码
     */
    private String dutyCode;
    /**
     * 责任明细代码
     */
    private String dutyDetailCode;
    /**
     * 责任明细名称
     */
    private String dutyDetailName;
    /**
     * 责任明细分类ZRMXFL
     */
    private String dutyDetailType;
    /**
     * 责任明细保额
     */
    private BigDecimal dutyDetailAmount;

    /**
     * 责任明细预赔金额
     */
    private BigDecimal prepayAmount;
    /**
     * 创建人员
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDate;
    /**
     * 修改人员
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDate;

    private String planCode;
    private String planName;



}

