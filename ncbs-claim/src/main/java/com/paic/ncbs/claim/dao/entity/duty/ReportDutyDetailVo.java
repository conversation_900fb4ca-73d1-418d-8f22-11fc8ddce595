package com.paic.ncbs.claim.dao.entity.duty;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ReportDutyDetailVo {
    private String reportNo;
    private String policyNo;
    private String planCode;
    private String planName;
    private String dutyCode;
    private String dutyName;
    private BigDecimal dutyAmount;
    private String isDutySharedAmount;
    private String dutySharedAmountMerge;
    private String dutyDetailCode;
    private String dutyDetailName;
    //免赔额
    private BigDecimal deductible;
}
