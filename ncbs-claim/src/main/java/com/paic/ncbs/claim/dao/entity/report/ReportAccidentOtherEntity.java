package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.math.BigDecimal;

/**
 * 意键险其他非人伤信息表
 */
public class ReportAccidentOtherEntity extends EntityDTO {

    private static final long serialVersionUID = -3035901866406575917L;

    private String idAhcsReportAccidentOther;

    private String reportNo;

    private String otherType;

    private BigDecimal costEstimate;

    public String getIdAhcsReportAccidentOther() {
        return idAhcsReportAccidentOther;
    }

    public void setIdAhcsReportAccidentOther(String idAhcsReportAccidentOther) {
        this.idAhcsReportAccidentOther = idAhcsReportAccidentOther == null ? null : idAhcsReportAccidentOther.trim();
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String getOtherType() {
        return otherType;
    }

    public void setOtherType(String otherType) {
        this.otherType = otherType == null ? null : otherType.trim();
    }

    public BigDecimal getCostEstimate() {
        return costEstimate;
    }

    public void setCostEstimate(BigDecimal costEstimate) {
        this.costEstimate = costEstimate;
    }

}