package com.paic.ncbs.claim.dao.mapper.trace;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceFeeDTO;
import com.paic.ncbs.claim.model.dto.trace.ClmsTraceRecordDTO;
import com.paic.ncbs.claim.model.vo.trace.ClmsPersTraceFeeVO;
import com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 *
 * 表clms_pers_trace_fee对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@MapperScan
public interface ClmsPersTraceFeeMapper extends BaseDao<ClmsPersTraceFeeDTO> {

    /**
     * 查询人伤跟踪损失费用明细表
     * @param personTranceRequestVo
     * @return
     */
    public List<ClmsPersTraceFeeVO> selectClmsPersTraceFee(PersonTranceRequestVo personTranceRequestVo);

    /**
     * 修改
     */
    public void updateSelectiveByPrimaryKey(ClmsPersTraceFeeDTO clmsPersTraceFeeDTO);

    /**
     * 数据copy插入数据
     */
    public void  copyForCaseReopen(CaseReopenCopyDTO caseReopenCopyDTO);

    /**
     * 删除
     * @param personTranceRequestVo
     */
    public void deleteClmsPersTraceFee(PersonTranceRequestVo personTranceRequestVo);
}