package com.paic.ncbs.claim.model.vo.facade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("减损类型VO")
@Data
public class LossReduceTypeVO {

    @ApiModelProperty("")
    private String value;

    @ApiModelProperty("用户标签")
    private String label;

    @ApiModelProperty("减损类型VO列表")
    private List<LossReduceTypeVO> children;

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public List<LossReduceTypeVO> getChildren() {
        return children;
    }

    public void setChildren(List<LossReduceTypeVO> children) {
        this.children = children;
    }
}
