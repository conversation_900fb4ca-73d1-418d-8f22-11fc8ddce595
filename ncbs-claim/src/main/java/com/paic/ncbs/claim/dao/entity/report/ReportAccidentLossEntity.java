package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.math.BigDecimal;

/**
 * 意键险财产损失信息表
 */
public class ReportAccidentLossEntity extends EntityDTO {

    private static final long serialVersionUID = 7578173702397454586L;

    private String idAhcsReportAccidentLoss;

    private String reportNo;

    private String lossType;

    private BigDecimal costEstimate;

    private String accidentReason;

    private String accidentReasonDesc;

    public String getIdAhcsReportAccidentLoss() {
        return idAhcsReportAccidentLoss;
    }

    public void setIdAhcsReportAccidentLoss(String idAhcsReportAccidentLoss) {
        this.idAhcsReportAccidentLoss = idAhcsReportAccidentLoss == null ? null : idAhcsReportAccidentLoss.trim();
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String getLossType() {
        return lossType;
    }

    public void setLossType(String lossType) {
        this.lossType = lossType == null ? null : lossType.trim();
    }

    public BigDecimal getCostEstimate() {
        return costEstimate;
    }

    public void setCostEstimate(BigDecimal costEstimate) {
        this.costEstimate = costEstimate;
    }

    public String getAccidentReason() {
        return accidentReason;
    }

    public void setAccidentReason(String accidentReason) {
        this.accidentReason = accidentReason;
    }

    public String getAccidentReasonDesc() {
        return accidentReasonDesc;
    }

    public void setAccidentReasonDesc(String accidentReasonDesc) {
        this.accidentReasonDesc = accidentReasonDesc;
    }
}