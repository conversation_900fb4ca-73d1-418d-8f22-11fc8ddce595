package com.paic.ncbs.claim.dao.mapper.dynamic;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.dao.entity.dynamic.DynamicFieldsResultEntity;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;


/**
 * <p>
 * 动态字段结果表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
public interface DynamicFieldResultMapper extends BaseMapper<DynamicFieldsResultEntity> {

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);

}