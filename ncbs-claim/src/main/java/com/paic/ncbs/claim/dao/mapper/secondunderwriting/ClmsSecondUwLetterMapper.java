package com.paic.ncbs.claim.dao.mapper.secondunderwriting;

import com.paic.ncbs.claim.dao.entity.clms.ClmsSecondUwLetterEntity;
import com.paic.ncbs.claim.model.dto.secondunderwriting.UwConclusionDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ClmsSecondUwLetterMapper {

    /**
     * 批量插入
     * @param entitys
     * @return
     */
    int insertBatch(List<ClmsSecondUwLetterEntity> entitys);

    /**
     * 根据理赔二核申请表主键idClmsSecondUnderwriting查询函件信息
     * @param idClmsSecondUnderwriting
     * @return
     */
    List<ClmsSecondUwLetterEntity> getLists(String  idClmsSecondUnderwriting);


    /**
     * 批量更新二核函件信息表
     * @param updateEntityList
     */
    void updateBatch(List<ClmsSecondUwLetterEntity> updateEntityList);

    /**
     * 查询理赔二核结论，用于同步批改
     * @param idSecondUWLetter
     * @return
     */
    List<UwConclusionDTO> getUwConclusionList(@Param("idSecondUWLetter") String idSecondUWLetter);
}
