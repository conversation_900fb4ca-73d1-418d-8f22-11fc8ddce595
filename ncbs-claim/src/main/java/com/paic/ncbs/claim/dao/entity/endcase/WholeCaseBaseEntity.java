package com.paic.ncbs.claim.dao.entity.endcase;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.util.Date;

/**
 * 整案信息表
 */
public class WholeCaseBaseEntity extends EntityDTO {

    private static final long serialVersionUID = 8819979536678681429L;

    private String idClmWholeCaseBase;

    private String reportNo;

    private String indemnityConclusion;

    private String isAgentCase;

    private String hugeAccidentCode;

    private String documentGroupId;

    private String wholeCaseStatus;

    private String migrateFrom;

    private Short caseTimes;

    private String indemnityModel;

    private String allowQuickFinish;

    private Date endCaseDate;

    private Date registerDate;

    private String isRegister;

    private Date settleEndDate;

    private String caseCancelReason;

    private String caseFinisherUm;

    private String registerUm;

    private String settlerUm;

    private Date documentFullDate;

    private String receiveVoucherUm;

    private Date settleStartDate;

    private String caseGudePageNo;

    private String isHugeAccident;

    private String isImportantCase;

    private String rejectReasonCode;

    private String cancelReasonCode;

    private String caseRejectReason;

    private String mediationExplain;

    private Date archiveDate;

    private String archiveBy;

    private String caseType;

    private String firstMindDesc;

    private String firstAuditingUser;

    private Date firstAuditingDate;

    private String agentType;

    private String verifyUm;

    private Date verifyDate;

    private String hugeAccidentId;

    private String registNo;

    private String endCaseNo;

    private String injuryReasonCode;

    // 案件标识（用于监管报送区分）：01-线上线下人伤报案案件; 02-非人伤退运险批量结案; 03-非人伤责任险案件。
    private String caseIdentification;

    public String getCaseIdentification() {
        return caseIdentification;
    }

    public void setCaseIdentification(String caseIdentification) {
        this.caseIdentification = caseIdentification;
    }

    public String getRegistNo() {
        return registNo;
    }

    public void setRegistNo(String registNo) {
        this.registNo = registNo;
    }

    public String getEndCaseNo() {
        return endCaseNo;
    }

    public void setEndCaseNo(String endCaseNo) {
        this.endCaseNo = endCaseNo;
    }

    public String getIdClmWholeCaseBase() {
        return idClmWholeCaseBase;
    }

    public void setIdClmWholeCaseBase(String idClmWholeCaseBase) {
        this.idClmWholeCaseBase = idClmWholeCaseBase == null ? null : idClmWholeCaseBase.trim();
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String getIndemnityConclusion() {
        return indemnityConclusion;
    }

    public void setIndemnityConclusion(String indemnityConclusion) {
        this.indemnityConclusion = indemnityConclusion == null ? null : indemnityConclusion.trim();
    }

    public String getIsAgentCase() {
        return isAgentCase;
    }

    public void setIsAgentCase(String isAgentCase) {
        this.isAgentCase = isAgentCase == null ? null : isAgentCase.trim();
    }

    public String getHugeAccidentCode() {
        return hugeAccidentCode;
    }

    public void setHugeAccidentCode(String hugeAccidentCode) {
        this.hugeAccidentCode = hugeAccidentCode == null ? null : hugeAccidentCode.trim();
    }

    public String getDocumentGroupId() {
        return documentGroupId;
    }

    public void setDocumentGroupId(String documentGroupId) {
        this.documentGroupId = documentGroupId == null ? null : documentGroupId.trim();
    }

    public String getWholeCaseStatus() {
        return wholeCaseStatus;
    }

    public void setWholeCaseStatus(String wholeCaseStatus) {
        this.wholeCaseStatus = wholeCaseStatus == null ? null : wholeCaseStatus.trim();
    }

    public String getMigrateFrom() {
        return migrateFrom;
    }

    public void setMigrateFrom(String migrateFrom) {
        this.migrateFrom = migrateFrom == null ? null : migrateFrom.trim();
    }

    public Short getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Short caseTimes) {
        this.caseTimes = caseTimes;
    }

    public String getIndemnityModel() {
        return indemnityModel;
    }

    public void setIndemnityModel(String indemnityModel) {
        this.indemnityModel = indemnityModel == null ? null : indemnityModel.trim();
    }

    public String getAllowQuickFinish() {
        return allowQuickFinish;
    }

    public void setAllowQuickFinish(String allowQuickFinish) {
        this.allowQuickFinish = allowQuickFinish == null ? null : allowQuickFinish.trim();
    }

    public Date getEndCaseDate() {
        return endCaseDate;
    }

    public void setEndCaseDate(Date endCaseDate) {
        this.endCaseDate = endCaseDate;
    }

    public Date getRegisterDate() {
        return registerDate;
    }

    public void setRegisterDate(Date registerDate) {
        this.registerDate = registerDate;
    }

    public String getIsRegister() {
        return isRegister;
    }

    public void setIsRegister(String isRegister) {
        this.isRegister = isRegister == null ? null : isRegister.trim();
    }

    public Date getSettleEndDate() {
        return settleEndDate;
    }

    public void setSettleEndDate(Date settleEndDate) {
        this.settleEndDate = settleEndDate;
    }

    public String getCaseCancelReason() {
        return caseCancelReason;
    }

    public void setCaseCancelReason(String caseCancelReason) {
        this.caseCancelReason = caseCancelReason == null ? null : caseCancelReason.trim();
    }

    public String getCaseFinisherUm() {
        return caseFinisherUm;
    }

    public void setCaseFinisherUm(String caseFinisherUm) {
        this.caseFinisherUm = caseFinisherUm == null ? null : caseFinisherUm.trim();
    }

    public String getRegisterUm() {
        return registerUm;
    }

    public void setRegisterUm(String registerUm) {
        this.registerUm = registerUm == null ? null : registerUm.trim();
    }

    public String getSettlerUm() {
        return settlerUm;
    }

    public void setSettlerUm(String settlerUm) {
        this.settlerUm = settlerUm == null ? null : settlerUm.trim();
    }

    public Date getDocumentFullDate() {
        return documentFullDate;
    }

    public void setDocumentFullDate(Date documentFullDate) {
        this.documentFullDate = documentFullDate;
    }

    public String getReceiveVoucherUm() {
        return receiveVoucherUm;
    }

    public void setReceiveVoucherUm(String receiveVoucherUm) {
        this.receiveVoucherUm = receiveVoucherUm == null ? null : receiveVoucherUm.trim();
    }

    public Date getSettleStartDate() {
        return settleStartDate;
    }

    public void setSettleStartDate(Date settleStartDate) {
        this.settleStartDate = settleStartDate;
    }

    public String getCaseGudePageNo() {
        return caseGudePageNo;
    }

    public void setCaseGudePageNo(String caseGudePageNo) {
        this.caseGudePageNo = caseGudePageNo == null ? null : caseGudePageNo.trim();
    }

    public String getIsHugeAccident() {
        return isHugeAccident;
    }

    public void setIsHugeAccident(String isHugeAccident) {
        this.isHugeAccident = isHugeAccident == null ? null : isHugeAccident.trim();
    }

    public String getIsImportantCase() {
        return isImportantCase;
    }

    public void setIsImportantCase(String isImportantCase) {
        this.isImportantCase = isImportantCase == null ? null : isImportantCase.trim();
    }

    public String getRejectReasonCode() {
        return rejectReasonCode;
    }

    public void setRejectReasonCode(String rejectReasonCode) {
        this.rejectReasonCode = rejectReasonCode == null ? null : rejectReasonCode.trim();
    }

    public String getCancelReasonCode() {
        return cancelReasonCode;
    }

    public void setCancelReasonCode(String cancelReasonCode) {
        this.cancelReasonCode = cancelReasonCode == null ? null : cancelReasonCode.trim();
    }

    public String getCaseRejectReason() {
        return caseRejectReason;
    }

    public void setCaseRejectReason(String caseRejectReason) {
        this.caseRejectReason = caseRejectReason == null ? null : caseRejectReason.trim();
    }

    public String getMediationExplain() {
        return mediationExplain;
    }

    public void setMediationExplain(String mediationExplain) {
        this.mediationExplain = mediationExplain == null ? null : mediationExplain.trim();
    }

    public Date getArchiveDate() {
        return archiveDate;
    }

    public void setArchiveDate(Date archiveDate) {
        this.archiveDate = archiveDate;
    }

    public String getArchiveBy() {
        return archiveBy;
    }

    public void setArchiveBy(String archiveBy) {
        this.archiveBy = archiveBy == null ? null : archiveBy.trim();
    }

    public String getCaseType() {
        return caseType;
    }

    public void setCaseType(String caseType) {
        this.caseType = caseType == null ? null : caseType.trim();
    }

    public String getFirstMindDesc() {
        return firstMindDesc;
    }

    public void setFirstMindDesc(String firstMindDesc) {
        this.firstMindDesc = firstMindDesc == null ? null : firstMindDesc.trim();
    }

    public String getFirstAuditingUser() {
        return firstAuditingUser;
    }

    public void setFirstAuditingUser(String firstAuditingUser) {
        this.firstAuditingUser = firstAuditingUser == null ? null : firstAuditingUser.trim();
    }

    public Date getFirstAuditingDate() {
        return firstAuditingDate;
    }

    public void setFirstAuditingDate(Date firstAuditingDate) {
        this.firstAuditingDate = firstAuditingDate;
    }

    public String getAgentType() {
        return agentType;
    }

    public void setAgentType(String agentType) {
        this.agentType = agentType == null ? null : agentType.trim();
    }

    public String getVerifyUm() {
        return verifyUm;
    }

    public void setVerifyUm(String verifyUm) {
        this.verifyUm = verifyUm == null ? null : verifyUm.trim();
    }

    public Date getVerifyDate() {
        return verifyDate;
    }

    public void setVerifyDate(Date verifyDate) {
        this.verifyDate = verifyDate;
    }

    public String getHugeAccidentId() {
        return hugeAccidentId;
    }

    public void setHugeAccidentId(String hugeAccidentId) {
        this.hugeAccidentId = hugeAccidentId;
    }


    public String getInjuryReasonCode() {
        return injuryReasonCode;
    }

    public void setInjuryReasonCode(String injuryReasonCode) {
        this.injuryReasonCode = injuryReasonCode;
    }
}