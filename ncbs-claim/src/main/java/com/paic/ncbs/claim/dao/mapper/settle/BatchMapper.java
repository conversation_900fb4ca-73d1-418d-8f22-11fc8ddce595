package com.paic.ncbs.claim.dao.mapper.settle;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.settle.BatchDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface BatchMapper extends BaseDao<BatchDTO> {

    void insertBatch(@Param("batchDto") BatchDTO batch);

    BatchDTO getBatch(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    BatchDTO getBatchInfo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    void updateBatch(@Param("batchDto") BatchDTO batch);

    void deleteBatch(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    String getSettleUserUm(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    void updateSettleTime(BatchDTO batch);
}
