package com.paic.ncbs.claim.dao.mapper.report;

import com.paic.ncbs.claim.model.dto.report.EstimateLossDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.List;

@MapperScan
public interface EstimateLossMapper {

    List<EstimateLossDTO> getAllEstimatLossConfig();

    void setAllEstimatLossConfig(@Param("estimateLossList")List<EstimateLossDTO> estimateLossList);

    EstimateLossDTO getEstimatLossConfig(@Param("insuredApplyType") String insuredApplyType);

    BigDecimal sumDutyAmount(@Param("reportNo") String reportNo,@Param("dutyDetailType")String dutyDetailType);

}
