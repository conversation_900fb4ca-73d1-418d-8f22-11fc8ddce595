package com.paic.ncbs.claim.dao.mapper.mq;

import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLRegistDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLRegistRPolicyDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLRegistTextDto;
import com.paic.ncbs.claim.model.dto.mq.selfDto.PolicyplanDto;
import com.paic.ncbs.claim.model.dto.mq.swf.SwfLogStoreDto;

public interface MqProducerRegistMapper {

    PrpLRegistDto queryPrpLRegistDto(String reportNo);

    PolicyplanDto queryPolicyplanDto(String reportNo);

    PrpLRegistTextDto queryPrpLRegistTextDto(String reportNo);

    PrpLRegistRPolicyDto queryPrpLRegistRPolicyDto(String reportNo);

    SwfLogStoreDto querySwfLogStoreDto(String reportNo);
}
