package com.paic.ncbs.claim.model.vo.trace;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * 通过ins-framework-mybatis工具自动生成，请勿手工修改。表clms_pers_injured_part的PO对象<br/>
 * 对应表名：clms_pers_injured_part,备注：人伤医疗诊断表
 *
 */
@ApiModel("人伤医疗诊断信息")
@Data
public class ClmsPersInjuredPartVO  implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：id,备注：主键 */
	@TableField(value="id")
	private Integer id;
	/** 对应字段：report_no,备注：报案号 */
	@TableField(value="report_no")
	private String reportNo;
	/** 对应字段：case_times,备注：赔付次数 */
	@TableField(value="case_times")
	private Integer caseTimes;
	/** 对应字段：injured_id,备注：伤亡人员表id */
	@TableField(value="injured_id")
	private Integer injuredId;
	/** 对应字段：injured_part_code,备注：受伤部位代码 */
	@TableField(value="injured_part_code")
	private String injuredPartCode;
	/** 对应字段：injured_partname,备注：受伤部位名称 */
	@TableField(value="injured_partname")
	private String injuredPartname;
	/** 对应字段：injured_diagnosis_code,备注：诊断代码 */
	@TableField(value="injured_diagnosis_code")
	private String injuredDiagnosisCode;
	/** 对应字段：injured_diagnosis_name,备注：诊断名称 */
	@TableField(value="injured_diagnosis_name")
	private String injuredDiagnosisName;
	/** 对应字段：treatment,备注：医疗 */
	@TableField(value="treatment")
	private String treatment;
	/** 对应字段：treat_way,备注：治疗方式 */
	@TableField(value="treat_way")
	private String treatWay;
	/** 对应字段：treat_route,备注：治疗途径 */
	@TableField(value="treat_route")
	private String treatRoute;
	/** 对应字段：disease_diagnosis_code,备注：疾病代码 */
	@TableField(value="disease_diagnosis_code")
	private String diseaseDiagnosisCode;
	/** 对应字段：surgical_name_code,备注：手术代码 */
	@TableField(value="surgical_name_code")
	private String surgicalNameCode;
	/** 对应字段：surgical_name_code,备注：手术名称 */
	@TableField(value="surgical_name")
	private String surgicalName;
	/** 对应字段：confirmed_date,备注：确诊日期 */
	@TableField(value="confirmed_date")
	private Date confirmedDate;
	/** 对应字段：specific_diagnosis,备注：特异性 */
	@TableField(value="specific_diagnosis")
	private String specificDiagnosis;
	/** 对应字段：created_by,备注：创建人 */
	@TableField(value="created_by")
	private String createdBy;
	/** 对应字段：sys_ctime,备注：创建时间 */
	@TableField(value="sys_ctime")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date sysCtime;
	/** 对应字段：updated_by,备注：最新修改人员 */
	@TableField(value="updated_by")
	private String updatedBy;
	/** 对应字段：sys_utime,备注：最新修改时间 */
	@TableField(value="sys_utime")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date sysUtime;
	/** 对应字段：valid_flag,备注：有效标志 */
	@TableField(value="valid_flag")
	private String validFlag;
	//疾病名称
	private String diseaseDiagnosisName;

}
