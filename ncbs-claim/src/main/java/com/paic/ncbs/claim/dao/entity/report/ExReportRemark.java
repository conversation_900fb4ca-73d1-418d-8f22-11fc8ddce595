package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;
/**
 * 异常报案转正说明（该表大概率不会要了，不涉及此业务）
 */

public class ExReportRemark extends EntityDTO {

    private static final long serialVersionUID = -2710628232782539822L;

    private String idReportTrans;

    private String idReportExc;

    private String reportNo;

    private String operator;

    private String sendTime;

    private String remark;

    public String getIdReportTrans() {
        return idReportTrans;
    }

    public void setIdReportTrans(String idReportTrans) {
        this.idReportTrans = idReportTrans;
    }

    public String getIdReportExc() {
        return idReportExc;
    }

    public void setIdReportExc(String idReportExc) {
        this.idReportExc = idReportExc;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getSendTime() {
        return sendTime;
    }

    public void setSendTime(String sendTime) {
        this.sendTime = sendTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}