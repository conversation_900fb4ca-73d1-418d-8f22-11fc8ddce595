package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.math.BigDecimal;
import java.util.Date;

public class ClmpReportAccidentEntity extends EntityDTO {

    private static final long serialVersionUID = 6619971182035358927L;

    private String idClmpReportAccident;

    private String reportNo;

    private String policyNo;

    private BigDecimal lossAmount;

    private String lossCurrencyCode;

    private Date accidentDate;

    private String accidentCauseLevel1;

    private String accidentCauseLevel2;

    private String accidentDetail;

    private String lossDesc;

    private String isOverseasAccident;

    private String isRemoteAccident;

    private String accidentProvinceCode;

    private String accidentCityCode;

    private String accidentCountyCode;

    private String accidentTownCode;

    private String accidentPlace;

    private String remark;

    private String isAlarm;

    private String isFlood;

    private String migrateFrom = "np";

    private String accidentCauseLevel2Explain;

    private String peopleInjuryAmount;

    private Date firstClaimDate;

    private String isPeriodHappen;

    private String legalLiabilityOne;

    private String legalLiabilityTwo;

    private String legalLiabilityDeclare;

    private String accidentCauseLevel3;

    private String carMark;

    private String vin;

    private String overseaContinentCode;

    private String overseaNationCode;

    private String transportWay;

    private String carryWay;

    private Date beginTransportDate;

    private String transportLineStartAddress;

    private String transportLineEndAddress;

    private Long actualBurden;

    private String internationRegisterNo;

    private String transportWayRemark;

    private String idClmpSurveyShipRecom;

    private String shipRecomName;

    private String idClmpSurveyShipFac;

    private String shipFacName;

    private String shipArea;

    private String trafficType;

    private String debtorName;

    private String debtorAddr;

    private String debtorNo;

    private Date waitStartDate;

    private BigDecimal waitPeriod;

    private Date waitEndDate;

    private BigDecimal creditLimit;

    private String isDisputeCase;

    private Date disputeCaseConfirmDate;

    private Date disputeCaseEndDate;

    private String isBankInvestment;

    private String shipName;

    private String hospitalCode;

    private String hospitalName;

    private String vaccineType;

    private String isConclusion;

    private String vaccineConclusion;

    public String getIdClmpReportAccident() {
        return idClmpReportAccident;
    }

    public void setIdClmpReportAccident(String idClmpReportAccident) {
        this.idClmpReportAccident = idClmpReportAccident == null ? null : idClmpReportAccident.trim();
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo == null ? null : policyNo.trim();
    }

    public BigDecimal getLossAmount() {
        return lossAmount;
    }

    public void setLossAmount(BigDecimal lossAmount) {
        this.lossAmount = lossAmount;
    }

    public String getLossCurrencyCode() {
        return lossCurrencyCode;
    }

    public void setLossCurrencyCode(String lossCurrencyCode) {
        this.lossCurrencyCode = lossCurrencyCode == null ? null : lossCurrencyCode.trim();
    }

    public Date getAccidentDate() {
        return accidentDate;
    }

    public void setAccidentDate(Date accidentDate) {
        this.accidentDate = accidentDate;
    }

    public String getAccidentCauseLevel1() {
        return accidentCauseLevel1;
    }

    public void setAccidentCauseLevel1(String accidentCauseLevel1) {
        this.accidentCauseLevel1 = accidentCauseLevel1 == null ? null : accidentCauseLevel1.trim();
    }

    public String getAccidentCauseLevel2() {
        return accidentCauseLevel2;
    }

    public void setAccidentCauseLevel2(String accidentCauseLevel2) {
        this.accidentCauseLevel2 = accidentCauseLevel2 == null ? null : accidentCauseLevel2.trim();
    }

    public String getAccidentDetail() {
        return accidentDetail;
    }

    public void setAccidentDetail(String accidentDetail) {
        this.accidentDetail = accidentDetail == null ? null : accidentDetail.trim();
    }

    public String getLossDesc() {
        return lossDesc;
    }

    public void setLossDesc(String lossDesc) {
        this.lossDesc = lossDesc == null ? null : lossDesc.trim();
    }

    public String getIsOverseasAccident() {
        return isOverseasAccident;
    }

    public void setIsOverseasAccident(String isOverseasAccident) {
        this.isOverseasAccident = isOverseasAccident == null ? null : isOverseasAccident.trim();
    }

    public String getIsRemoteAccident() {
        return isRemoteAccident;
    }

    public void setIsRemoteAccident(String isRemoteAccident) {
        this.isRemoteAccident = isRemoteAccident == null ? null : isRemoteAccident.trim();
    }

    public String getAccidentProvinceCode() {
        return accidentProvinceCode;
    }

    public void setAccidentProvinceCode(String accidentProvinceCode) {
        this.accidentProvinceCode = accidentProvinceCode == null ? null : accidentProvinceCode.trim();
    }

    public String getAccidentCityCode() {
        return accidentCityCode;
    }

    public void setAccidentCityCode(String accidentCityCode) {
        this.accidentCityCode = accidentCityCode == null ? null : accidentCityCode.trim();
    }

    public String getAccidentCountyCode() {
        return accidentCountyCode;
    }

    public void setAccidentCountyCode(String accidentCountyCode) {
        this.accidentCountyCode = accidentCountyCode == null ? null : accidentCountyCode.trim();
    }

    public String getAccidentTownCode() {
        return accidentTownCode;
    }

    public void setAccidentTownCode(String accidentTownCode) {
        this.accidentTownCode = accidentTownCode == null ? null : accidentTownCode.trim();
    }

    public String getAccidentPlace() {
        return accidentPlace;
    }

    public void setAccidentPlace(String accidentPlace) {
        this.accidentPlace = accidentPlace == null ? null : accidentPlace.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getIsAlarm() {
        return isAlarm;
    }

    public void setIsAlarm(String isAlarm) {
        this.isAlarm = isAlarm == null ? null : isAlarm.trim();
    }

    public String getIsFlood() {
        return isFlood;
    }

    public void setIsFlood(String isFlood) {
        this.isFlood = isFlood == null ? null : isFlood.trim();
    }

    public String getMigrateFrom() {
        return migrateFrom;
    }

    public void setMigrateFrom(String migrateFrom) {
        this.migrateFrom = migrateFrom == null ? null : migrateFrom.trim();
    }

    public String getAccidentCauseLevel2Explain() {
        return accidentCauseLevel2Explain;
    }

    public void setAccidentCauseLevel2Explain(String accidentCauseLevel2Explain) {
        this.accidentCauseLevel2Explain = accidentCauseLevel2Explain == null ? null : accidentCauseLevel2Explain.trim();
    }

    public String getPeopleInjuryAmount() {
        return peopleInjuryAmount;
    }

    public void setPeopleInjuryAmount(String peopleInjuryAmount) {
        this.peopleInjuryAmount = peopleInjuryAmount == null ? null : peopleInjuryAmount.trim();
    }

    public Date getFirstClaimDate() {
        return firstClaimDate;
    }

    public void setFirstClaimDate(Date firstClaimDate) {
        this.firstClaimDate = firstClaimDate;
    }

    public String getIsPeriodHappen() {
        return isPeriodHappen;
    }

    public void setIsPeriodHappen(String isPeriodHappen) {
        this.isPeriodHappen = isPeriodHappen == null ? null : isPeriodHappen.trim();
    }

    public String getLegalLiabilityOne() {
        return legalLiabilityOne;
    }

    public void setLegalLiabilityOne(String legalLiabilityOne) {
        this.legalLiabilityOne = legalLiabilityOne == null ? null : legalLiabilityOne.trim();
    }

    public String getLegalLiabilityTwo() {
        return legalLiabilityTwo;
    }

    public void setLegalLiabilityTwo(String legalLiabilityTwo) {
        this.legalLiabilityTwo = legalLiabilityTwo == null ? null : legalLiabilityTwo.trim();
    }

    public String getLegalLiabilityDeclare() {
        return legalLiabilityDeclare;
    }

    public void setLegalLiabilityDeclare(String legalLiabilityDeclare) {
        this.legalLiabilityDeclare = legalLiabilityDeclare == null ? null : legalLiabilityDeclare.trim();
    }

    public String getAccidentCauseLevel3() {
        return accidentCauseLevel3;
    }

    public void setAccidentCauseLevel3(String accidentCauseLevel3) {
        this.accidentCauseLevel3 = accidentCauseLevel3 == null ? null : accidentCauseLevel3.trim();
    }

    public String getCarMark() {
        return carMark;
    }

    public void setCarMark(String carMark) {
        this.carMark = carMark == null ? null : carMark.trim();
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin == null ? null : vin.trim();
    }

    public String getOverseaContinentCode() {
        return overseaContinentCode;
    }

    public void setOverseaContinentCode(String overseaContinentCode) {
        this.overseaContinentCode = overseaContinentCode == null ? null : overseaContinentCode.trim();
    }

    public String getOverseaNationCode() {
        return overseaNationCode;
    }

    public void setOverseaNationCode(String overseaNationCode) {
        this.overseaNationCode = overseaNationCode == null ? null : overseaNationCode.trim();
    }

    public String getTransportWay() {
        return transportWay;
    }

    public void setTransportWay(String transportWay) {
        this.transportWay = transportWay == null ? null : transportWay.trim();
    }

    public String getCarryWay() {
        return carryWay;
    }

    public void setCarryWay(String carryWay) {
        this.carryWay = carryWay;
    }

    public Date getBeginTransportDate() {
        return beginTransportDate;
    }

    public void setBeginTransportDate(Date beginTransportDate) {
        this.beginTransportDate = beginTransportDate;
    }

    public String getTransportLineStartAddress() {
        return transportLineStartAddress;
    }

    public void setTransportLineStartAddress(String transportLineStartAddress) {
        this.transportLineStartAddress = transportLineStartAddress == null ? null : transportLineStartAddress.trim();
    }

    public String getTransportLineEndAddress() {
        return transportLineEndAddress;
    }

    public void setTransportLineEndAddress(String transportLineEndAddress) {
        this.transportLineEndAddress = transportLineEndAddress == null ? null : transportLineEndAddress.trim();
    }

    public Long getActualBurden() {
        return actualBurden;
    }

    public void setActualBurden(Long actualBurden) {
        this.actualBurden = actualBurden;
    }

    public String getInternationRegisterNo() {
        return internationRegisterNo;
    }

    public void setInternationRegisterNo(String internationRegisterNo) {
        this.internationRegisterNo = internationRegisterNo == null ? null : internationRegisterNo.trim();
    }

    public String getTransportWayRemark() {
        return transportWayRemark;
    }

    public void setTransportWayRemark(String transportWayRemark) {
        this.transportWayRemark = transportWayRemark == null ? null : transportWayRemark.trim();
    }

    public String getIdClmpSurveyShipRecom() {
        return idClmpSurveyShipRecom;
    }

    public void setIdClmpSurveyShipRecom(String idClmpSurveyShipRecom) {
        this.idClmpSurveyShipRecom = idClmpSurveyShipRecom == null ? null : idClmpSurveyShipRecom.trim();
    }

    public String getShipRecomName() {
        return shipRecomName;
    }

    public void setShipRecomName(String shipRecomName) {
        this.shipRecomName = shipRecomName == null ? null : shipRecomName.trim();
    }

    public String getIdClmpSurveyShipFac() {
        return idClmpSurveyShipFac;
    }

    public void setIdClmpSurveyShipFac(String idClmpSurveyShipFac) {
        this.idClmpSurveyShipFac = idClmpSurveyShipFac == null ? null : idClmpSurveyShipFac.trim();
    }

    public String getShipFacName() {
        return shipFacName;
    }

    public void setShipFacName(String shipFacName) {
        this.shipFacName = shipFacName == null ? null : shipFacName.trim();
    }

    public String getShipArea() {
        return shipArea;
    }

    public void setShipArea(String shipArea) {
        this.shipArea = shipArea == null ? null : shipArea.trim();
    }

    public String getTrafficType() {
        return trafficType;
    }

    public void setTrafficType(String trafficType) {
        this.trafficType = trafficType == null ? null : trafficType.trim();
    }

    public String getDebtorName() {
        return debtorName;
    }

    public void setDebtorName(String debtorName) {
        this.debtorName = debtorName == null ? null : debtorName.trim();
    }

    public String getDebtorAddr() {
        return debtorAddr;
    }

    public void setDebtorAddr(String debtorAddr) {
        this.debtorAddr = debtorAddr == null ? null : debtorAddr.trim();
    }

    public String getDebtorNo() {
        return debtorNo;
    }

    public void setDebtorNo(String debtorNo) {
        this.debtorNo = debtorNo == null ? null : debtorNo.trim();
    }

    public Date getWaitStartDate() {
        return waitStartDate;
    }

    public void setWaitStartDate(Date waitStartDate) {
        this.waitStartDate = waitStartDate;
    }

    public BigDecimal getWaitPeriod() {
        return waitPeriod;
    }

    public void setWaitPeriod(BigDecimal waitPeriod) {
        this.waitPeriod = waitPeriod;
    }

    public Date getWaitEndDate() {
        return waitEndDate;
    }

    public void setWaitEndDate(Date waitEndDate) {
        this.waitEndDate = waitEndDate;
    }

    public BigDecimal getCreditLimit() {
        return creditLimit;
    }

    public void setCreditLimit(BigDecimal creditLimit) {
        this.creditLimit = creditLimit;
    }

    public String getIsDisputeCase() {
        return isDisputeCase;
    }

    public void setIsDisputeCase(String isDisputeCase) {
        this.isDisputeCase = isDisputeCase == null ? null : isDisputeCase.trim();
    }

    public Date getDisputeCaseConfirmDate() {
        return disputeCaseConfirmDate;
    }

    public void setDisputeCaseConfirmDate(Date disputeCaseConfirmDate) {
        this.disputeCaseConfirmDate = disputeCaseConfirmDate;
    }

    public Date getDisputeCaseEndDate() {
        return disputeCaseEndDate;
    }

    public void setDisputeCaseEndDate(Date disputeCaseEndDate) {
        this.disputeCaseEndDate = disputeCaseEndDate;
    }

    public String getIsBankInvestment() {
        return isBankInvestment;
    }

    public void setIsBankInvestment(String isBankInvestment) {
        this.isBankInvestment = isBankInvestment == null ? null : isBankInvestment.trim();
    }

    public String getShipName() {
        return shipName;
    }

    public void setShipName(String shipName) {
        this.shipName = shipName == null ? null : shipName.trim();
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode == null ? null : hospitalCode.trim();
    }

    public String getHospitalName() {
        return hospitalName;
    }

    public void setHospitalName(String hospitalName) {
        this.hospitalName = hospitalName == null ? null : hospitalName.trim();
    }

    public String getVaccineType() {
        return vaccineType;
    }

    public void setVaccineType(String vaccineType) {
        this.vaccineType = vaccineType == null ? null : vaccineType.trim();
    }

    public String getIsConclusion() {
        return isConclusion;
    }

    public void setIsConclusion(String isConclusion) {
        this.isConclusion = isConclusion == null ? null : isConclusion.trim();
    }

    public String getVaccineConclusion() {
        return vaccineConclusion;
    }

    public void setVaccineConclusion(String vaccineConclusion) {
        this.vaccineConclusion = vaccineConclusion == null ? null : vaccineConclusion.trim();
    }
}