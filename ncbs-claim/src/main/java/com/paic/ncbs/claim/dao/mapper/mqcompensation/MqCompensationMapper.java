package com.paic.ncbs.claim.dao.mapper.mqcompensation;

import com.paic.ncbs.claim.dao.entity.mq.MqMessageRecordEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2023-06-12 10:01
 */
public interface MqCompensationMapper {

    List<MqMessageRecordEntity> getFailureRecordList();

    void updateMqSendStatus(@Param("mqMessageRecordEntity") MqMessageRecordEntity mqMessageRecordEntity);

    void addEntity(@Param("mqMessageRecordEntity") MqMessageRecordEntity mqMessageRecordEntity);
}
