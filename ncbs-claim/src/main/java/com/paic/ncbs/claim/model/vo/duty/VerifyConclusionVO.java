package com.paic.ncbs.claim.model.vo.duty;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel("VerifyConclusionVO-核责结论VO")
public class VerifyConclusionVO {

    @ApiModelProperty("核赔说明")
    private String auditingCommont;

    @ApiModelProperty("赔付结论（1：赔付 4：拒赔）")
    private String indemnityConclusion;

    @ApiModelProperty("赔付方式（indemnityConclusion=1、indemnityModel=5）->表示协议赔付;赔付方式（indemnityConclusion=1、indemnityModel=6）->表示通融赔付")
    private String indemnityModel;

    @ApiModelProperty("原因结论编码")
    private String conclusionCauseCode;

    @ApiModelProperty("原因结论详细描述")
    private String conclusionCauseDesc;

    @ApiModelProperty("批单信息json串")
    private String endorsementRemark;

    @ApiModelProperty("拒赔金额")
    private BigDecimal rejectAmount;

    // clms_loss_reduce
    @ApiModelProperty("减损金额")
    private BigDecimal reduceAmount;

    @ApiModelProperty("拒赔通知时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date rejectNotifyDate;

    @ApiModelProperty("客户号")
    private List<DutyRejectDetailVO> dutyRejectDetailList;

    @ApiModelProperty("客户号")
    private List<DutyRejectDetailVO> policyNoList;

    @ApiModelProperty("损失对象代码")
    private String lossObjectNo;

    public BigDecimal getReduceAmount() {
        return reduceAmount;
    }

    public void setReduceAmount(BigDecimal reduceAmount) {
        this.reduceAmount = reduceAmount;
    }

    public List<DutyRejectDetailVO> getPolicyNoList() {
        return policyNoList;
    }

    public void setPolicyNoList(List<DutyRejectDetailVO> policyNoList) {
        this.policyNoList = policyNoList;
    }

    public List<DutyRejectDetailVO> getDutyRejectDetailList() {
        return dutyRejectDetailList;
    }

    public void setDutyRejectDetailList(List<DutyRejectDetailVO> dutyRejectDetailList) {
        this.dutyRejectDetailList = dutyRejectDetailList;
    }

    public String getEndorsementRemark() {
        return endorsementRemark;
    }

    public void setEndorsementRemark(String endorsementRemark) {
        this.endorsementRemark = endorsementRemark;
    }

    public String getAuditingCommont() {
        return auditingCommont;
    }

    public void setAuditingCommont(String auditingCommont) {
        this.auditingCommont = auditingCommont;
    }

    public String getIndemnityConclusion() {
        return indemnityConclusion;
    }

    public void setIndemnityConclusion(String indemnityConclusion) {
        this.indemnityConclusion = indemnityConclusion;
    }

    public String getIndemnityModel() {
        return indemnityModel;
    }

    public void setIndemnityModel(String indemnityModel) {
        this.indemnityModel = indemnityModel;
    }

    public String getConclusionCauseCode() {
        return conclusionCauseCode;
    }

    public void setConclusionCauseCode(String conclusionCauseCode) {
        this.conclusionCauseCode = conclusionCauseCode;
    }

    public String getConclusionCauseDesc() {
        return conclusionCauseDesc;
    }

    public void setConclusionCauseDesc(String conclusionCauseDesc) {
        this.conclusionCauseDesc = conclusionCauseDesc;
    }

    public BigDecimal getRejectAmount() {
        return rejectAmount;
    }

    public void setRejectAmount(BigDecimal rejectAmount) {
        this.rejectAmount = rejectAmount;
    }

    public Date getRejectNotifyDate() {
        return rejectNotifyDate;
    }

    public void setRejectNotifyDate(Date rejectNotifyDate) {
        this.rejectNotifyDate = rejectNotifyDate;
    }

    public String getLossObjectNo() {
        return lossObjectNo;
    }

    public void setLossObjectNo(String lossObjectNo) {
        this.lossObjectNo = lossObjectNo;
    }
}
