package com.paic.ncbs.claim.dao.mapper.fileupload;

import com.paic.ncbs.claim.model.dto.fileupload.ExemptDocumentDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface ExemptDocumentMapper {

    public List<ExemptDocumentDTO> getExemptDocument(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

}
