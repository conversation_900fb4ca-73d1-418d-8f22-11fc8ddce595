package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.util.Date;

/**
 * 意键险航班延误
 */
public class ReportAccidentFlightEntity extends EntityDTO {

    private static final long serialVersionUID = 6534377796465858976L;

    /**
     * 主键
     */
    private String idAhcsReportAccidentFlight;

    /**
     * 报案号
     */
    private String reportNo;

    /**
     * 0-国内;1-国外
     */
    private String overseasOccur;

    /**
     * 出发地
     */
    private String departurePlace;

    /**
     * 目的地
     */
    private String destination;

    /**
     * 出发地省份编码
     */
    private String departureProvince;

    /**
     * 出发地市编码
     */
    private String departureCity;

    /**
     * 出发地机场三字码
     */
    private String departureAirport;

    /**
     * 出发地区域
     */
    private String departureArea;

    /**
     * 出发地国家
     */
    private String departureNation;

    /**
     * 目的地省份编码
     */
    private String destinationProvince;

    /**
     * 目的地市编码
     */
    private String destinationCity;

    /**
     * 目的地机场三字码
     */
    private String destinationAirport;

    /**
     * 目的地区域
     */
    private String destinationArea;

    /**
     * 目的地国家
     */
    private String destinationNation;

    /**
     * 航班取消(是：Y 否：N)
     */
    private String isFlightCancellation;

    /**
     * 航班迫降(是：Y 否：N)
     */
    private String isFlightLand;
    /**
     * 航班延误
     */
    private String isFlightDelay;

    /**
     * 迫降机场
     */
    private String landingAirport;

    /**
     * 代替航班
     */
    private String replaceFlight;

    /**
     * 起飞时间
     */
    private Date takeoffDate;

    /**
     * 电子客票号
     */
    private String eleTicketNo;
    /**
     * 客票状态
     */
    private String ticketStatus;//(1：已登机2：已使用3：人工审核)

    /**
     * 航班号
     */
    private String flightNo;

    /**
     * 行程取消(Y:是，N:否)
     */
    private String isJourneyCancel;
    /**
     * 出发地是否境内境外(0-国内;1-国外)
     */
    private String departureOverseasOccur;
    /**
     * 目的地是否境内境外 (0-国内;1-国外)
     */
    private String destinationOverseasOccur;
    /**
     * 事故原因（1.天气原因、2.航空管制、3.机械故障、4.机场罢工、5.其他）
     */
    private String accidentReason;
    /**
     * 延误时长（分钟）
     */
    private int delayTime;
    /**
     * 用户延误时长（分钟）
     */
    private int delayTimeUser;

    public String getTicketStatus() {
        return ticketStatus;
    }

    public void setTicketStatus(String ticketStatus) {
        this.ticketStatus = ticketStatus;
    }

    public String getIdAhcsReportAccidentFlight() {
        return idAhcsReportAccidentFlight;
    }

    public void setIdAhcsReportAccidentFlight(String idAhcsReportAccidentFlight) {
        this.idAhcsReportAccidentFlight = idAhcsReportAccidentFlight == null ? null : idAhcsReportAccidentFlight.trim();
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String getOverseasOccur() {
        return overseasOccur;
    }

    public void setOverseasOccur(String overseasOccur) {
        this.overseasOccur = overseasOccur == null ? null : overseasOccur.trim();
    }

    public String getDeparturePlace() {
        return departurePlace;
    }

    public void setDeparturePlace(String departurePlace) {
        this.departurePlace = departurePlace == null ? null : departurePlace.trim();
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination == null ? null : destination.trim();
    }

    public String getDepartureProvince() {
        return departureProvince;
    }

    public void setDepartureProvince(String departureProvince) {
        this.departureProvince = departureProvince == null ? null : departureProvince.trim();
    }

    public String getDepartureCity() {
        return departureCity;
    }

    public void setDepartureCity(String departureCity) {
        this.departureCity = departureCity == null ? null : departureCity.trim();
    }

    public String getDepartureAirport() {
        return departureAirport;
    }

    public void setDepartureAirport(String departureAirport) {
        this.departureAirport = departureAirport == null ? null : departureAirport.trim();
    }

    public String getDepartureArea() {
        return departureArea;
    }

    public void setDepartureArea(String departureArea) {
        this.departureArea = departureArea == null ? null : departureArea.trim();
    }

    public String getDepartureNation() {
        return departureNation;
    }

    public void setDepartureNation(String departureNation) {
        this.departureNation = departureNation == null ? null : departureNation.trim();
    }

    public String getDestinationProvince() {
        return destinationProvince;
    }

    public void setDestinationProvince(String destinationProvince) {
        this.destinationProvince = destinationProvince == null ? null : destinationProvince.trim();
    }

    public String getDestinationCity() {
        return destinationCity;
    }

    public void setDestinationCity(String destinationCity) {
        this.destinationCity = destinationCity == null ? null : destinationCity.trim();
    }

    public String getDestinationAirport() {
        return destinationAirport;
    }

    public void setDestinationAirport(String destinationAirport) {
        this.destinationAirport = destinationAirport == null ? null : destinationAirport.trim();
    }

    public String getDestinationArea() {
        return destinationArea;
    }

    public void setDestinationArea(String destinationArea) {
        this.destinationArea = destinationArea == null ? null : destinationArea.trim();
    }

    public String getDestinationNation() {
        return destinationNation;
    }

    public void setDestinationNation(String destinationNation) {
        this.destinationNation = destinationNation == null ? null : destinationNation.trim();
    }

    public String getIsFlightCancellation() {
        return isFlightCancellation;
    }

    public void setIsFlightCancellation(String isFlightCancellation) {
        this.isFlightCancellation = isFlightCancellation == null ? null : isFlightCancellation.trim();
    }

    public String getIsFlightLand() {
        return isFlightLand;
    }

    public void setIsFlightLand(String isFlightLand) {
        this.isFlightLand = isFlightLand == null ? null : isFlightLand.trim();
    }

    public String getLandingAirport() {
        return landingAirport;
    }

    public void setLandingAirport(String landingAirport) {
        this.landingAirport = landingAirport == null ? null : landingAirport.trim();
    }

    public String getReplaceFlight() {
        return replaceFlight;
    }

    public void setReplaceFlight(String replaceFlight) {
        this.replaceFlight = replaceFlight == null ? null : replaceFlight.trim();
    }

    public Date getTakeoffDate() {
        return takeoffDate;
    }

    public void setTakeoffDate(Date takeoffDate) {
        this.takeoffDate = takeoffDate;
    }

    public String getEleTicketNo() {
        return eleTicketNo;
    }

    public void setEleTicketNo(String eleTicketNo) {
        this.eleTicketNo = eleTicketNo == null ? null : eleTicketNo.trim();
    }

    public String getFlightNo() {
        return flightNo;
    }

    public void setFlightNo(String flightNo) {
        this.flightNo = flightNo == null ? null : flightNo.trim();
    }

    public String getIsJourneyCancel() {
        return isJourneyCancel;
    }

    public void setIsJourneyCancel(String isJourneyCancel) {
        this.isJourneyCancel = isJourneyCancel == null ? null : isJourneyCancel.trim();
    }

    public String getDepartureOverseasOccur() {
        return departureOverseasOccur;
    }

    public void setDepartureOverseasOccur(String departureOverseasOccur) {
        this.departureOverseasOccur = departureOverseasOccur;
    }

    public String getDestinationOverseasOccur() {
        return destinationOverseasOccur;
    }

    public void setDestinationOverseasOccur(String destinationOverseasOccur) {
        this.destinationOverseasOccur = destinationOverseasOccur;
    }

    public String getAccidentReason() {
        return accidentReason;
    }

    public void setAccidentReason(String accidentReason) {
        this.accidentReason = accidentReason;
    }

    public int getDelayTime() {
        return delayTime;
    }

    public void setDelayTime(int delayTime) {
        this.delayTime = delayTime;
    }

    public String getIsFlightDelay() {
        return isFlightDelay;
    }

    public void setIsFlightDelay(String isFlightDelay) {
        this.isFlightDelay = isFlightDelay;
    }

    public int getDelayTimeUser() {
        return delayTimeUser;
    }

    public void setDelayTimeUser(int delayTimeUser) {
        this.delayTimeUser = delayTimeUser;
    }
}