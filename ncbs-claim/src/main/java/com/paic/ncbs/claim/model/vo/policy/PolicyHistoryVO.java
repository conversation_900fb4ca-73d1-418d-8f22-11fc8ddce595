package com.paic.ncbs.claim.model.vo.policy;


import com.paic.ncbs.claim.model.dto.policy.PolicyHistoryDTO;
import io.swagger.annotations.ApiModel;

import java.util.ArrayList;
import java.util.List;

@ApiModel("PolicyHistoryVO-保单历史VO")
public class PolicyHistoryVO {

	
	private List<PolicyHistoryDTO> policyHistorys = new ArrayList<PolicyHistoryDTO>();
	
	
	private List<PolicyHistoryDTO> otherPolicyHistorys = new ArrayList<PolicyHistoryDTO>();

	public List<PolicyHistoryDTO> getPolicyHistorys() {
		return policyHistorys;
	}

	public void setPolicyHistorys(List<PolicyHistoryDTO> policyHistorys) {
		this.policyHistorys = policyHistorys;
	}

	public List<PolicyHistoryDTO> getOtherPolicyHistorys() {
		return otherPolicyHistorys;
	}

	public void setOtherPolicyHistorys(List<PolicyHistoryDTO> otherPolicyHistorys) {
		this.otherPolicyHistorys = otherPolicyHistorys;
	}
	
}
