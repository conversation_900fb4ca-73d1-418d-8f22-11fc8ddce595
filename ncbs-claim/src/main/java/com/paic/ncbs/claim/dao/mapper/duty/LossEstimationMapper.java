package com.paic.ncbs.claim.dao.mapper.duty;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.model.dto.duty.LossEstimationDTO;
import com.paic.ncbs.claim.model.vo.duty.LossEstimationVO;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface LossEstimationMapper extends BaseMapper<LossEstimationDTO> {
    /**
     * 删除
     * @param reportNo
     * @param caseTimes
     * @param taskId
     */
    void removeLossEstimation(String reportNo, int caseTimes, String taskId);

    /**
     *
     * @param reportNo
     * @param caseTimes
     * @param taskId
     * @return
     */
    List<LossEstimationVO> getLossEstimationVOs(String reportNo, int caseTimes, String taskId);

    void updateLossEstimationTaskId(String reportNo, int caseTimes, String idFlagHistoryChange, String taskId);

    List<LossEstimationVO> getLastLossEstimationVOs(String reportNo);

    List<LossEstimationDTO> getLastLossEstimationVOByCaseTimes(String reportNo, int caseTimes);
}
