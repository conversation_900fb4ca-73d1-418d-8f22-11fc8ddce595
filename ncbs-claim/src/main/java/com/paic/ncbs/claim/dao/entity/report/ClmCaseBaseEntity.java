package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.math.BigDecimal;
import java.util.Date;

public class ClmCaseBaseEntity extends EntityDTO {

    private static final long serialVersionUID = -6131340378883653481L;

    private String idClmCaseBase;

    private String reportNo;

    private BigDecimal caseTimes;

    private String policyNo;

    private String caseNo;

    private Date registerDate;

    private Date settleDate;

    private Date endCaseDate;

    private String caseStatus;

    private String indemnityConclusion;

    private String caseFinisherUm;

    private String registerUm;

    private String settlerUm;

    private String migrateFrom = "np";

    private String departmentCode;

    private String documentGroupId;

    public String getIdClmCaseBase() {
        return idClmCaseBase;
    }

    public void setIdClmCaseBase(String idClmCaseBase) {
        this.idClmCaseBase = idClmCaseBase == null ? null : idClmCaseBase.trim();
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public BigDecimal getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(BigDecimal caseTimes) {
        this.caseTimes = caseTimes;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo == null ? null : policyNo.trim();
    }

    public String getCaseNo() {
        return caseNo;
    }

    public void setCaseNo(String caseNo) {
        this.caseNo = caseNo == null ? null : caseNo.trim();
    }

    public Date getRegisterDate() {
        return registerDate;
    }

    public void setRegisterDate(Date registerDate) {
        this.registerDate = registerDate;
    }

    public Date getSettleDate() {
        return settleDate;
    }

    public void setSettleDate(Date settleDate) {
        this.settleDate = settleDate;
    }

    public Date getEndCaseDate() {
        return endCaseDate;
    }

    public void setEndCaseDate(Date endCaseDate) {
        this.endCaseDate = endCaseDate;
    }

    public String getCaseStatus() {
        return caseStatus;
    }

    public void setCaseStatus(String caseStatus) {
        this.caseStatus = caseStatus == null ? null : caseStatus.trim();
    }

    public String getIndemnityConclusion() {
        return indemnityConclusion;
    }

    public void setIndemnityConclusion(String indemnityConclusion) {
        this.indemnityConclusion = indemnityConclusion == null ? null : indemnityConclusion.trim();
    }

    public String getCaseFinisherUm() {
        return caseFinisherUm;
    }

    public void setCaseFinisherUm(String caseFinisherUm) {
        this.caseFinisherUm = caseFinisherUm == null ? null : caseFinisherUm.trim();
    }

    public String getRegisterUm() {
        return registerUm;
    }

    public void setRegisterUm(String registerUm) {
        this.registerUm = registerUm == null ? null : registerUm.trim();
    }

    public String getSettlerUm() {
        return settlerUm;
    }

    public void setSettlerUm(String settlerUm) {
        this.settlerUm = settlerUm == null ? null : settlerUm.trim();
    }

    public String getMigrateFrom() {
        return migrateFrom;
    }

    public void setMigrateFrom(String migrateFrom) {
        this.migrateFrom = migrateFrom == null ? null : migrateFrom.trim();
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode == null ? null : departmentCode.trim();
    }

    public String getDocumentGroupId() {
        return documentGroupId;
    }

    public void setDocumentGroupId(String documentGroupId) {
        this.documentGroupId = documentGroupId == null ? null : documentGroupId.trim();
    }
}