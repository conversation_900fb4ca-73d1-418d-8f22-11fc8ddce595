package com.paic.ncbs.claim.model.vo.chase;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


public class ChaseTaskInfoVO {

	private String idAhcsChaseApply;

	private String applyUM;

	private String applyName;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date applyDate;

	private String verifyUM;

	private String verifyName;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date verifyDate;

	public String getIdAhcsChaseApply() {
		return idAhcsChaseApply;
	}

	public void setIdAhcsChaseApply(String idAhcsChaseApply) {
		this.idAhcsChaseApply = idAhcsChaseApply;
	}

	public String getApplyUM() {
		return applyUM;
	}

	public void setApplyUM(String applyUM) {
		this.applyUM = applyUM;
	}

	public String getApplyName() {
		return applyName;
	}

	public void setApplyName(String applyName) {
		this.applyName = applyName;
	}
	
	public Date getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}

	public String getVerifyUM() {
		return verifyUM;
	}

	public void setVerifyUM(String verifyUM) {
		this.verifyUM = verifyUM;
	}

	public String getVerifyName() {
		return verifyName;
	}

	public void setVerifyName(String verifyName) {
		this.verifyName = verifyName;
	}

	public Date getVerifyDate() {
		return verifyDate;
	}

	public void setVerifyDate(Date verifyDate) {
		this.verifyDate = verifyDate;
	}
}

