package com.paic.ncbs.claim.dao.mapper.ahcs;

import com.paic.ncbs.claim.dao.entity.report.BatchReportTempEntity;
import com.paic.ncbs.claim.model.dto.ahcs.BatchAutoCloseDTO;
import com.paic.ncbs.claim.model.dto.ahcs.OnlineBatchDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface BatchAutoCloseMapper {

    void addBatchCloseList(@Param("batchCloseList") List<BatchAutoCloseDTO> batchCloseList);

    List<BatchReportTempEntity> getBatchCloseList(@Param("batchNo") String batchNo);

    List<OnlineBatchDTO> getByThirdBatchNo(@Param("thirdBatchNo") String thirdBatchNo,@Param("reopenNum") Integer reopenNum);

    BatchAutoCloseDTO getByReportNo(@Param("reportNo") String reportNo);

    List<BatchAutoCloseDTO> getByReportNos(@Param("reportNos") List<String> reportNos);

    List<BatchAutoCloseDTO> getProblemBatchCloseList();

    List<BatchAutoCloseDTO> compensationByThirdBatchNo(@Param("thirdBatchNo") String thirdBatchNo);

    List<BatchAutoCloseDTO> compensationByReportNo(@Param("reportNo") String reportNo);
}