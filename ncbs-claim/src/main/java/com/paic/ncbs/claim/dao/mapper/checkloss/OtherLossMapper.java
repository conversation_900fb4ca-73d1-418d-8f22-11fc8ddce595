package com.paic.ncbs.claim.dao.mapper.checkloss;


import com.paic.ncbs.claim.model.dto.duty.OtherLossDTO;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import com.paic.ncbs.claim.model.vo.settle.SettleOtherLossVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface OtherLossMapper {
	
	 
	public void addOtherLoss(OtherLossDTO otherLossDTO);
	
	 
	public void removeOtherLoss(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes, @Param("taskCode") String taskCode, @Param("channelProcessId") String channelProcessId);

	void updateEffective(OtherLossDTO otherLossDTO);
	
	 
	public List<OtherLossDTO> getOtherLoss(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes, @Param("taskCode") String taskCode, @Param("channelProcessId") String channelProcessId);
	
	 
	public SettleOtherLossVO getSettleOtherLoss(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes, @Param("status") String status, @Param("taskCode") String taskCode, @Param("idAhcsChannelProcess") String idAhcsChannelProcess);
	
	
	 
	public List<OtherLossDTO> getOtherLossList(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskCode") String taskCode);
	
	 
	public void addOtherLossList(@Param("otherLossList") List<OtherLossDTO> otherLossList, 
			@Param("caseTimes") Integer caseTimes, @Param("userId") String userId, @Param("channelProcessId") String channelProcessId);
	
	 
	public void updateOtherLoss(OtherLossDTO otherLossDTO);

	/**
	 * 案件重开，数据拷贝
	 * @param dto
	 */
	void copyForCaseReopen(CaseReopenCopyDTO dto);

	public List<OtherLossDTO> getOtherLossListbyReportNo(@Param("reportNo") String reportNo,  @Param("caseTimes") Integer caseTimes);
}
