package com.paic.ncbs.claim.dao.mapper.estimate;

import com.paic.ncbs.claim.model.dto.estimate.EstimateChangePlanDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateChangePolicyFormDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyHistoryDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.List;

@MapperScan
public interface EstimateDutyHistoryMapper {

    void addHistoryRecord(@Param("paramList") List<EstimateDutyHistoryDTO> estimateDutyHistoryDTOList);

    void updateHistoryRecord(@Param("caseNoList") List<String> caseNoList, @Param("caseTimes") Integer caseTimes);

    BigDecimal getSumPay(@Param("policyNo") String policyNo, @Param("dutyCode") String dutyCode);

    List<EstimateDutyHistoryDTO> getEstimateDutyHistoryList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    List<EstimateChangePolicyFormDTO> getRestartEstimateDutyList(@Param("caseNo") String reportNo,@Param("caseTimes") Integer caseTimes);

    List<EstimateChangePlanDTO> getEstimatePlanCodeList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);


    /**
     * 获取上一次未决修正的金额
     * @param idFlagHistoryChange
     * @return
     */
    List<EstimateDutyHistoryDTO> getDutyHistoryListByIdFlag(String idFlagHistoryChange);

    void updateEstimateDateByCaseNo(@Param("caseNo") String caseNo);
}
