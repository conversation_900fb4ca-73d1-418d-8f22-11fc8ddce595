package com.paic.ncbs.claim.dao.mapper.ahcs;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsInsuredPersonExtEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AhcsInsuredPersonExtMapper extends BaseDao<AhcsInsuredPersonExtEntity> {

    int deleteByPrimaryKey(String idAhcsInsuredPersonExt);

    int insert(AhcsInsuredPersonExtEntity record);

    int insertSelective(AhcsInsuredPersonExtEntity record);

    AhcsInsuredPersonExtEntity selectByPrimaryKey(String idAhcsInsuredPersonExt);

    AhcsInsuredPersonExtEntity selectByIdAhcsInsuredPerson(String idAhcsInsuredPerson);

    int updateByPrimaryKeySelective(AhcsInsuredPersonExtEntity record);

    int updateByPrimaryKey(AhcsInsuredPersonExtEntity record);

    int deleteByIdAhcsInsuredPerson(String idAhcsInsuredPerson);

    void insertList(@Param("list") List<AhcsInsuredPersonExtEntity> list);

    void deleteByReportNoAndPolicyNo(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo);
}