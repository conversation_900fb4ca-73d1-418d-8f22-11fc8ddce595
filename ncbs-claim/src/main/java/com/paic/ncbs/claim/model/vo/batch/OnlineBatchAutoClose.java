package com.paic.ncbs.claim.model.vo.batch;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
  *
  * @Description 退运险线上批量结案
  * <AUTHOR>
  * @Date 2023/7/13 9:26
  **/
@Data
@ApiModel(description = "退运险线上批量结案")
public class OnlineBatchAutoClose {

    @ApiModelProperty(value = "事故日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "事故日期不能为空")
    private Date accidentDate ;
    
    @ApiModelProperty(value = "被保险人姓名")
    @NotBlank(message = "被保险人姓名不能为空")
    private String clientName ;

    @ApiModelProperty(value = "被保险人证件类型")
//    @NotBlank(message = "被保险人证件类型不能为空")
    private String certificateType ;

    @ApiModelProperty(value = "被保险人证件号码")
//    @NotBlank(message = "被保险人证件号码不能为空")
    private String certificateNo ;

    @ApiModelProperty(value = "保单号")
    @NotBlank(message = "保单号不能为空")
    private String policyNo ;

    @ApiModelProperty(value = "支付方式- 区分支付给谁")
    @NotBlank(message = "支付方式不能为空")
    private String paymentMode ;

    @ApiModelProperty(value = "险种编码")
    @NotBlank(message = "险种编码不能为空")
    private String planCode ;

    @ApiModelProperty(value = "责任编码")
    @NotBlank(message = "责任编码不能为空")
    private String dutyCode ;

    @ApiModelProperty(value = "责任明细编码")
//    @NotBlank(message = "责任明细编码不能为空")
    private String dutyDetailCode ;

    @ApiModelProperty(value = "出险省份")
    @NotBlank(message = "出险省份不能为空")
    private String accidentProvince;

    @ApiModelProperty(value = "出险城市")
//    @NotBlank(message = "出险城市不能为空")
    private String accidentCity;

    @ApiModelProperty(value = "出险县")
//    @NotBlank(message = "出险县不能为空")
    private String accidentCounty;

    @ApiModelProperty(value = "出险区域")
    @NotBlank(message = "出险区域不能为空")
    private String accidentPlace;

    @ApiModelProperty(value = "联系人信息")
    @Valid
    @NotNull(message = "联系人信息不能为空")
    private OnlineBatchLinkMan linkMan;

    @ApiModelProperty(value = "领款人信息")
    @Valid
    @NotNull(message = "领款人信息不能为空")
    private OnlinePaymentInfo paymentInfo;

    @ApiModelProperty(value = "零注申请信息")
    private OnlineZeroCancelInfo zeroCancelInfo;

    @ApiModelProperty(value = "结算的快递公司编码")
    private String expressCompanyCode;

    // 美团点评新增 start
    @ApiModelProperty(value = "出险原因一级分类")
    private String accidentCauseLevel1;

    @ApiModelProperty(value = "出险原因二级分类")
    private String accidentCauseLevel2;

    @ApiModelProperty(value = "报案来源")
    private String reportMode;

    @ApiModelProperty(value = "出险类型")
    private String caseClass;

    @ApiModelProperty(value = "第三方来源：1-线上退运险、2-大众点评责任险")
    private String threeSource;

    @ApiModelProperty(value = "事故类型")
    private String accidentType;

    @ApiModelProperty(value = "案件标识")
    private String caseIdentification;

    @ApiModelProperty(value = "案件类型")
    private String caseType;

    @ApiModelProperty(value = "重开批次")
    private Integer reopenNum;
    // 美团点评新增 end
}
