package com.paic.ncbs.claim.dao.mapper.settle;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.duty.DiagnoseHospitalBillAssociationDTO;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface DiagnoseHospitalBillAssociationMapper extends BaseDao<DiagnoseHospitalBillAssociationDTO> {

    /**
     * 保存诊断医院发票关联信息
     * @param dto
     */
    void saveDiagnoseHospitalBillAssociation(DiagnoseHospitalBillAssociationDTO dto);

    /**
     * 根据idAhcsBillInfo将历史数据置为失效状态
     * @param idAhcsBillInfo
     */
    void updateDiagnoseHospitalBillAssociation(String idAhcsBillInfo);

    /**
     * 根据idAhcsBillInfo查询诊断信息
     * @param idAhcsBillInfo
     * @return
     */
    List<DiagnoseHospitalBillAssociationDTO> getDiagnoseHospitalBillAssociation(String idAhcsBillInfo);

    /**
     * 根据idAhcsBillInfo查询所有诊断信息
     * @param idAhcsBillInfo
     * @return
     */
    List<DiagnoseHospitalBillAssociationDTO> getAllDiagnoseHospitalBillAssociation(String idAhcsBillInfo);

    /**
     * 根据billInfo的主键查询list
     * @param idAhcsBillInfoList
     * @return
     */
    List<DiagnoseHospitalBillAssociationDTO> getDiagnoseHospitalBillAssociationList(List<String> idAhcsBillInfoList);

    /**
     * 重开复制表
     * @param diagnoseCopyList
     */
    void copyForCaseReopen(List<DiagnoseHospitalBillAssociationDTO> diagnoseCopyList);
}
