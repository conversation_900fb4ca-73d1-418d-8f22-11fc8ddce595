package com.paic.ncbs.claim.dao.entity.ahcs;

import com.paic.ncbs.claim.model.dto.settle.SpecialPromiseDTO;
import com.paic.ncbs.claim.model.convert.BeanConvert;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import org.springframework.beans.BeanUtils;

/**
 * 特约
 */
public class AhcsSpecialPromiseEntity extends EntityDTO {

	private static final long serialVersionUID = -1577314894819193297L;

    private String idAhcsSpecialPromise;

    private String idAhcsPolicyInfo;

    private String promiseCode;

    private String promiseDesc;

    private String promiseType;

    private String businessType;

    private String contentType;

    private String sortIndex;

    private String riskGroupName;

    public String getRiskGroupName() {
        return riskGroupName;
    }

    public void setRiskGroupName(String riskGroupName) {
        this.riskGroupName = riskGroupName;
    }

    public String getIdAhcsSpecialPromise() {
        return idAhcsSpecialPromise;
    }

    public void setIdAhcsSpecialPromise(String idAhcsSpecialPromise) {
        this.idAhcsSpecialPromise = idAhcsSpecialPromise == null ? null : idAhcsSpecialPromise.trim();
    }

    public String getIdAhcsPolicyInfo() {
        return idAhcsPolicyInfo;
    }

    public void setIdAhcsPolicyInfo(String idAhcsPolicyInfo) {
        this.idAhcsPolicyInfo = idAhcsPolicyInfo == null ? null : idAhcsPolicyInfo.trim();
    }

    public String getPromiseCode() {
        return promiseCode;
    }

    public void setPromiseCode(String promiseCode) {
        this.promiseCode = promiseCode == null ? null : promiseCode.trim();
    }

    public String getPromiseDesc() {
        return promiseDesc;
    }

    public void setPromiseDesc(String promiseDesc) {
        this.promiseDesc = promiseDesc == null ? null : promiseDesc.trim();
    }

    public String getPromiseType() {
        return promiseType;
    }

    public void setPromiseType(String promiseType) {
        this.promiseType = promiseType == null ? null : promiseType.trim();
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType == null ? null : businessType.trim();
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType == null ? null : contentType.trim();
    }

    public String getSortIndex() {
        return sortIndex;
    }

    public void setSortIndex(String sortIndex) {
        this.sortIndex = sortIndex;
    }

    public SpecialPromiseDTO convertToDTO(){
        SpecialPromiseEntityConvert convert = new SpecialPromiseEntityConvert();
        return convert.convert(this);
    }

    private static class SpecialPromiseEntityConvert implements BeanConvert<AhcsSpecialPromiseEntity, SpecialPromiseDTO>{

        @Override
        public SpecialPromiseDTO convert(AhcsSpecialPromiseEntity ahcsSpecialPromiseEntity) {
            SpecialPromiseDTO dto = new SpecialPromiseDTO();
            BeanUtils.copyProperties(ahcsSpecialPromiseEntity,dto);
            return dto;
        }
    }
}