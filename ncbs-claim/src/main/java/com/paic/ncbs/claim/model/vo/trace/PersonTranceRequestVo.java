package com.paic.ncbs.claim.model.vo.trace;

import lombok.Data;

@Data
public class PersonTranceRequestVo {
    /**
     * 报案号
     */
    private String reportNo;
    /**
     * 任务号
     */
    private String taskId;
    /**
     * 赔付次数
     */
    private Integer caseTimes;
    /**
     * 任务节点
     */
    private String taskDefinitionBpmKey;
    /**
     * 损失明细主表id
     */
    private Integer traceExpId;
    /**
     * 有效标志
     */
    private String flag;
    /**
     * 暂存，提交标志
     */
    private String underwriteFlag;

    private String bpmKey;
    private String updatedBy;
}
