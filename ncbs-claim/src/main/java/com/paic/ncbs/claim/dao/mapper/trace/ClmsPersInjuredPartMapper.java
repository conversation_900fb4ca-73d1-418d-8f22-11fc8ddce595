package com.paic.ncbs.claim.dao.mapper.trace;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import com.paic.ncbs.claim.model.dto.trace.ClmsPersInjuredPartDTO;
import com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceExpDTO;
import com.paic.ncbs.claim.model.vo.trace.ClmsPersInjuredPartVO;
import com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 *
 * 表clms_pers_injured_part对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@MapperScan
public interface ClmsPersInjuredPartMapper extends BaseDao<ClmsPersInjuredPartDTO> {
    /**
     * 查询人伤医疗诊断信息
     * @param personTranceRequestVo
     * @return
     */
    public List<ClmsPersInjuredPartVO> selectClmsPersInjuredPart(PersonTranceRequestVo personTranceRequestVo);

    /**
     * 修改
     */
    public void updateSelectiveByPrimaryKey(ClmsPersInjuredPartDTO clmsPersInjuredPartDTO);

    /**
     * 数据copy插入数据
     */
    public void  copyForCaseReopen(CaseReopenCopyDTO caseReopenCopyDTO);

    /**
     * 删除
     */
    public void deleteClmsPersInjuredPart(PersonTranceRequestVo personTranceRequestVo);
}