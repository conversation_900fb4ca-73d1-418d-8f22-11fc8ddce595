package com.paic.ncbs.claim.dao.entity.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.common.util.RapeDateUtil;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;
import java.util.Map;


@ApiModel(description = "报案客户信息表")
public class ReportCustomerInfoEntity extends EntityDTO {

    private static final long serialVersionUID = 2047660235597645951L;


    @ApiModelProperty(value = "主键")
    private String idAhcsReportCustomer;

    @ApiModelProperty(value = "客户号")
    private String clientNo;

    @ApiModelProperty(value = "客户姓名")
    private String name;

    @ApiModelProperty(value = "证件类型")
    private String certificateType;

    @ApiModelProperty(value = "证件号码")
    private String certificateNo;

    @ApiModelProperty(value = "年龄")
    private int age;

    @ApiModelProperty(value = "性别")
    private String sexCode;

    @ApiModelProperty(value = "出生日期")
    @JsonFormat(pattern = RapeDateUtil.SIMPLE_DATE_STR)
    private Date birthday;


    @ApiModelProperty(value = "客户类别")
    private String clientCategoty;

    @ApiModelProperty(value = "客户类别的中文名")
    private String clientCategotyName;

    @ApiModelProperty(value = "客户类型")
    private String clientType;

    @ApiModelProperty(value = "客户类型的中文名")
    private String clientTypeName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "报案号")
    private String reportNo;

    @ApiModelProperty(value = "评级时间")
    private Date levelDate;

    @ApiModelProperty(value = "风险等级", example = "1:高风险 2:较高风险 3:中风险 4:较低风险 5:低风险")
    private String groupRiskLevel;

    @ApiModelProperty(value = "风险等级依据")
    private String groupLastAccording;

    @ApiModelProperty(value = "微信的openId")
    private String openId;

    @ApiModelProperty(value = "是否团体客户", example = "Y：是 ，N或NULL：否")
    private String isOrganization;

    private List<Map<String, Object>> classificationInfoList;

    @ApiModelProperty(value = "客户分群", example = "超大，中大，小微")
    private String clientCluster;

    @ApiModelProperty(value = "随车标签")
    private String followCarLabel;
    private String personnelCode;
    private String mobileTelephone;

    /**
     * 续保保单号
     */
    private String lastPolicyNo;
    /**
     * 续保标志：Y-是 ，N-否
     */
    private String isRenew;

    public String getClientCluster() {
        return clientCluster;
    }

    public void setClientCluster(String clientCluster) {
        this.clientCluster = clientCluster;
    }

    public Date getLevelDate() {
        return levelDate;
    }

    public void setLevelDate(Date levelDate) {
        this.levelDate = levelDate;
    }

    public String getGroupRiskLevel() {
        return groupRiskLevel;
    }

    public void setGroupRiskLevel(String groupRiskLevel) {
        this.groupRiskLevel = groupRiskLevel;
    }

    public String getGroupLastAccording() {
        return groupLastAccording;
    }

    public void setGroupLastAccording(String groupLastAccording) {
        this.groupLastAccording = groupLastAccording;
    }

    public String getClientCategotyName() {
        return clientCategotyName;
    }

    public void setClientCategotyName(String clientCategotyName) {
        this.clientCategotyName = clientCategotyName;
    }

    public String getClientTypeName() {
        return clientTypeName;
    }

    public void setClientTypeName(String clientTypeName) {
        this.clientTypeName = clientTypeName;
    }

    public String getIdAhcsReportCustomer() {
        return idAhcsReportCustomer;
    }

    public void setIdAhcsReportCustomer(String idAhcsReportCustomer) {
        this.idAhcsReportCustomer = idAhcsReportCustomer == null ? null : idAhcsReportCustomer.trim();
    }

    public String getClientNo() {
        return clientNo;
    }

    public void setClientNo(String clientNo) {
        this.clientNo = clientNo == null ? null : clientNo.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(String certificateType) {
        this.certificateType = certificateType == null ? null : certificateType.trim();
    }

    public String getCertificateNo() {
        return certificateNo;
    }

    public void setCertificateNo(String certificateNo) {
        this.certificateNo = certificateNo == null ? null : certificateNo.trim();
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public String getSexCode() {
        return sexCode;
    }

    public void setSexCode(String sexCode) {
        this.sexCode = sexCode == null ? null : sexCode.trim();
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public String getClientCategoty() {
        return clientCategoty;
    }

    public void setClientCategoty(String clientCategoty) {
        this.clientCategoty = clientCategoty == null ? null : clientCategoty.trim();
    }

    public String getClientType() {
        return clientType;
    }

    public void setClientType(String clientType) {
        this.clientType = clientType == null ? null : clientType.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public List<Map<String, Object>> getClassificationInfoList() {
        return classificationInfoList;
    }

    public void setClassificationInfoList(List<Map<String, Object>> classificationInfoList) {
        this.classificationInfoList = classificationInfoList;
    }

    public String getIsOrganization() {
        return isOrganization;
    }

    public void setIsOrganization(String isOrganization) {
        this.isOrganization = isOrganization;
    }

    public String getFollowCarLabel() {
        return followCarLabel;
    }

    public void setFollowCarLabel(String followCarLabel) {
        this.followCarLabel = followCarLabel;
    }

    public String getPersonnelCode() {
        return personnelCode;
    }

    public void setPersonnelCode(String personnelCode) {
        this.personnelCode = personnelCode;
    }

    public String getMobileTelephone() {
        return mobileTelephone;
    }

    public void setMobileTelephone(String mobileTelephone) {
        this.mobileTelephone = mobileTelephone;
    }

    public String getLastPolicyNo() {
        return lastPolicyNo;
    }

    public void setLastPolicyNo(String lastPolicyNo) {
        this.lastPolicyNo = lastPolicyNo;
    }

    public String getIsRenew() {
        return isRenew;
    }

    public void setIsRenew(String isRenew) {
        this.isRenew = isRenew;
    }

    public String getIsTransferInsure() {
        return isTransferInsure;
    }

    public void setIsTransferInsure(String isTransferInsure) {
        this.isTransferInsure = isTransferInsure;
    }

    /**
     * 转保标志：Y-是 ，N-否
     */
    private String isTransferInsure;

    /**
     * 转保保单
     */
    private String transferInsurancePolicyNo;

    /**
     * 转保止期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date transferInsuranceEndDate;

    /**
     * 转保产品名称
     */
    private String transferInsuranceProductName;

    public String getTransferInsurancePolicyNo() {
        return transferInsurancePolicyNo;
    }

    public void setTransferInsurancePolicyNo(String transferInsurancePolicyNo) {
        this.transferInsurancePolicyNo = transferInsurancePolicyNo;
    }

    public Date getTransferInsuranceEndDate() {
        return transferInsuranceEndDate;
    }

    public void setTransferInsuranceEndDate(Date transferInsuranceEndDate) {
        this.transferInsuranceEndDate = transferInsuranceEndDate;
    }

    public String getTransferInsuranceProductName() {
        return transferInsuranceProductName;
    }

    public void setTransferInsuranceProductName(String transferInsuranceProductName) {
        this.transferInsuranceProductName = transferInsuranceProductName;
    }

    public String getRiskGroupType() {
        return riskGroupType;
    }

    public void setRiskGroupType(String riskGroupType) {
        this.riskGroupType = riskGroupType;
    }

    private String riskGroupType;
}