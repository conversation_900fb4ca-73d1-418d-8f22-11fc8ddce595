package com.paic.ncbs.claim.model.vo.doc;


import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
//y
@ApiModel("责任赔付信息")
public class PrintDutyPayInfoVO extends EntityDTO {

	private static final long serialVersionUID = 8381170521601990233L;
	
	@ApiModelProperty("保单号")
	private String policyNo;

	@ApiModelProperty("责任编码")
	private String dutyCode;

	@ApiModelProperty("责任名称")
	private String dutyName;

	@ApiModelProperty("赔偿限额")
	private BigDecimal baseAmountPay;

	@ApiModelProperty("责任赔付/预陪/垫付 金额/已经赔付")
	private BigDecimal alreadyPay;
	
	@ApiModelProperty("现应赔付/剩余")
	private BigDecimal remainMoney;

	public String getPolicyNo() {
		return policyNo;
	}

	public void setPolicyNo(String policyNo) {
		this.policyNo = policyNo;
	}

	public String getDutyCode() {
		return dutyCode;
	}

	public void setDutyCode(String dutyCode) {
		this.dutyCode = dutyCode;
	}

	public String getDutyName() {
		return dutyName;
	}

	public void setDutyName(String dutyName) {
		this.dutyName = dutyName;
	}

	public BigDecimal getBaseAmountPay() {
		return baseAmountPay;
	}

	public void setBaseAmountPay(BigDecimal baseAmountPay) {
		this.baseAmountPay = baseAmountPay;
	}

	public BigDecimal getAlreadyPay() {
		return alreadyPay;
	}

	public void setAlreadyPay(BigDecimal alreadyPay) {
		this.alreadyPay = alreadyPay;
	}

	public BigDecimal getRemainMoney() {
		return remainMoney;
	}

	public void setRemainMoney(BigDecimal remainMoney) {
		this.remainMoney = remainMoney;
	}
	
}
