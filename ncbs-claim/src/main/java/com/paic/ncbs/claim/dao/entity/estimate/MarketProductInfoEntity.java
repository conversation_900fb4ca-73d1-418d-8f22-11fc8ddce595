package com.paic.ncbs.claim.dao.entity.estimate;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.util.Date;

/**
 * 产品定义表
 */
public class MarketProductInfoEntity extends EntityDTO {

    private static final long serialVersionUID = -1587591529228255705L;

    private String idMarketproductInfo;

    private String marketproductCode;

    private String marketproductName;

    private String marketproductDesc;

    private String status;

    private String marketproductType;

    private String isSelfCard;

    private String version;

    private String productClass;

    private String hasBatchTarget;

    private String isCombined;

    private String departmentCode;

    private String targetType;

    private String idTechnicProductInfo;

    private String combindRelation;

    private String notAllowedSepSale;

    private String shareInsuredAmount;

    private String policyType;

    private String notBatchPolicy;

    private String masterMarketproduct;

    private Date effectiveDate;

    private Date invalidateDate;

    private String productCategory;

    private String isDutyFree;

    private String relatedInsuredPerson;

    private String isAllPurposeCard;

    private String policyDocument;

    private String endorseDocument;

    private String insuranceDocument;

    private String isAddAdditionalTarget;

    private String recordType;

    private String recordRemark;

    private String productPrintName;

    private String isRemitQuote;

    private String isSpecifyVehicle;

    private String autoAdjust;

    private String allowPackageCombine;

    private String pageType;
    /**
     *  支持保险起期前赔付 1支持 0不支持
     */
    private String claimBeforeInsurance;

    private String isAutoClaim;

    private String isFamilyAccount;

    public String getIdMarketproductInfo() {
        return idMarketproductInfo;
    }

    public void setIdMarketproductInfo(String idMarketproductInfo) {
        this.idMarketproductInfo = idMarketproductInfo == null ? null : idMarketproductInfo.trim();
    }

    public String getMarketproductCode() {
        return marketproductCode;
    }

    public void setMarketproductCode(String marketproductCode) {
        this.marketproductCode = marketproductCode == null ? null : marketproductCode.trim();
    }

    public String getMarketproductName() {
        return marketproductName;
    }

    public void setMarketproductName(String marketproductName) {
        this.marketproductName = marketproductName == null ? null : marketproductName.trim();
    }

    public String getMarketproductDesc() {
        return marketproductDesc;
    }

    public void setMarketproductDesc(String marketproductDesc) {
        this.marketproductDesc = marketproductDesc == null ? null : marketproductDesc.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getMarketproductType() {
        return marketproductType;
    }

    public void setMarketproductType(String marketproductType) {
        this.marketproductType = marketproductType == null ? null : marketproductType.trim();
    }

    public String getIsSelfCard() {
        return isSelfCard;
    }

    public void setIsSelfCard(String isSelfCard) {
        this.isSelfCard = isSelfCard == null ? null : isSelfCard.trim();
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version == null ? null : version.trim();
    }

    public String getProductClass() {
        return productClass;
    }

    public void setProductClass(String productClass) {
        this.productClass = productClass == null ? null : productClass.trim();
    }

    public String getHasBatchTarget() {
        return hasBatchTarget;
    }

    public void setHasBatchTarget(String hasBatchTarget) {
        this.hasBatchTarget = hasBatchTarget == null ? null : hasBatchTarget.trim();
    }

    public String getIsCombined() {
        return isCombined;
    }

    public void setIsCombined(String isCombined) {
        this.isCombined = isCombined == null ? null : isCombined.trim();
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode == null ? null : departmentCode.trim();
    }

    public String getTargetType() {
        return targetType;
    }

    public void setTargetType(String targetType) {
        this.targetType = targetType == null ? null : targetType.trim();
    }

    public String getIdTechnicProductInfo() {
        return idTechnicProductInfo;
    }

    public void setIdTechnicProductInfo(String idTechnicProductInfo) {
        this.idTechnicProductInfo = idTechnicProductInfo == null ? null : idTechnicProductInfo.trim();
    }

    public String getCombindRelation() {
        return combindRelation;
    }

    public void setCombindRelation(String combindRelation) {
        this.combindRelation = combindRelation == null ? null : combindRelation.trim();
    }

    public String getNotAllowedSepSale() {
        return notAllowedSepSale;
    }

    public void setNotAllowedSepSale(String notAllowedSepSale) {
        this.notAllowedSepSale = notAllowedSepSale == null ? null : notAllowedSepSale.trim();
    }

    public String getShareInsuredAmount() {
        return shareInsuredAmount;
    }

    public void setShareInsuredAmount(String shareInsuredAmount) {
        this.shareInsuredAmount = shareInsuredAmount == null ? null : shareInsuredAmount.trim();
    }

    public String getPolicyType() {
        return policyType;
    }

    public void setPolicyType(String policyType) {
        this.policyType = policyType == null ? null : policyType.trim();
    }

    public String getNotBatchPolicy() {
        return notBatchPolicy;
    }

    public void setNotBatchPolicy(String notBatchPolicy) {
        this.notBatchPolicy = notBatchPolicy == null ? null : notBatchPolicy.trim();
    }

    public String getMasterMarketproduct() {
        return masterMarketproduct;
    }

    public void setMasterMarketproduct(String masterMarketproduct) {
        this.masterMarketproduct = masterMarketproduct == null ? null : masterMarketproduct.trim();
    }

    public Date getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(Date effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public Date getInvalidateDate() {
        return invalidateDate;
    }

    public void setInvalidateDate(Date invalidateDate) {
        this.invalidateDate = invalidateDate;
    }

    public String getProductCategory() {
        return productCategory;
    }

    public void setProductCategory(String productCategory) {
        this.productCategory = productCategory == null ? null : productCategory.trim();
    }

    public String getIsDutyFree() {
        return isDutyFree;
    }

    public void setIsDutyFree(String isDutyFree) {
        this.isDutyFree = isDutyFree == null ? null : isDutyFree.trim();
    }

    public String getRelatedInsuredPerson() {
        return relatedInsuredPerson;
    }

    public void setRelatedInsuredPerson(String relatedInsuredPerson) {
        this.relatedInsuredPerson = relatedInsuredPerson == null ? null : relatedInsuredPerson.trim();
    }

    public String getIsAllPurposeCard() {
        return isAllPurposeCard;
    }

    public void setIsAllPurposeCard(String isAllPurposeCard) {
        this.isAllPurposeCard = isAllPurposeCard == null ? null : isAllPurposeCard.trim();
    }

    public String getPolicyDocument() {
        return policyDocument;
    }

    public void setPolicyDocument(String policyDocument) {
        this.policyDocument = policyDocument == null ? null : policyDocument.trim();
    }

    public String getEndorseDocument() {
        return endorseDocument;
    }

    public void setEndorseDocument(String endorseDocument) {
        this.endorseDocument = endorseDocument == null ? null : endorseDocument.trim();
    }

    public String getInsuranceDocument() {
        return insuranceDocument;
    }

    public void setInsuranceDocument(String insuranceDocument) {
        this.insuranceDocument = insuranceDocument == null ? null : insuranceDocument.trim();
    }

    public String getIsAddAdditionalTarget() {
        return isAddAdditionalTarget;
    }

    public void setIsAddAdditionalTarget(String isAddAdditionalTarget) {
        this.isAddAdditionalTarget = isAddAdditionalTarget == null ? null : isAddAdditionalTarget.trim();
    }

    public String getRecordType() {
        return recordType;
    }

    public void setRecordType(String recordType) {
        this.recordType = recordType == null ? null : recordType.trim();
    }

    public String getRecordRemark() {
        return recordRemark;
    }

    public void setRecordRemark(String recordRemark) {
        this.recordRemark = recordRemark == null ? null : recordRemark.trim();
    }

    public String getProductPrintName() {
        return productPrintName;
    }

    public void setProductPrintName(String productPrintName) {
        this.productPrintName = productPrintName == null ? null : productPrintName.trim();
    }

    public String getIsRemitQuote() {
        return isRemitQuote;
    }

    public void setIsRemitQuote(String isRemitQuote) {
        this.isRemitQuote = isRemitQuote == null ? null : isRemitQuote.trim();
    }

    public String getIsSpecifyVehicle() {
        return isSpecifyVehicle;
    }

    public void setIsSpecifyVehicle(String isSpecifyVehicle) {
        this.isSpecifyVehicle = isSpecifyVehicle == null ? null : isSpecifyVehicle.trim();
    }

    public String getAutoAdjust() {
        return autoAdjust;
    }

    public void setAutoAdjust(String autoAdjust) {
        this.autoAdjust = autoAdjust == null ? null : autoAdjust.trim();
    }

    public String getAllowPackageCombine() {
        return allowPackageCombine;
    }

    public void setAllowPackageCombine(String allowPackageCombine) {
        this.allowPackageCombine = allowPackageCombine == null ? null : allowPackageCombine.trim();
    }

    public String getPageType() {
        return pageType;
    }

    public void setPageType(String pageType) {
        this.pageType = pageType == null ? null : pageType.trim();
    }

    public String getClaimBeforeInsurance() {
        return claimBeforeInsurance;
    }

    public void setClaimBeforeInsurance(String claimBeforeInsurance) {
        this.claimBeforeInsurance = claimBeforeInsurance == null ? null : claimBeforeInsurance.trim();
    }

    public String getIsAutoClaim() {
        return isAutoClaim;
    }

    public void setIsAutoClaim(String isAutoClaim) {
        this.isAutoClaim = isAutoClaim == null ? null : isAutoClaim.trim();
    }

    public String getIsFamilyAccount() {
        return isFamilyAccount;
    }

    public void setIsFamilyAccount(String isFamilyAccount) {
        this.isFamilyAccount = isFamilyAccount;
    }
}