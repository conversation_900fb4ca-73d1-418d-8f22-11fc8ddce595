package com.paic.ncbs.claim.model.vo.doc;



import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

//y
@ApiModel("打印付款信息")
public class PrintPayInfoVO extends EntityDTO {

    private static final long serialVersionUID = 8381171112601990233L;

    @ApiModelProperty("客户名称")
    private String clientName;

    @ApiModelProperty("支付总金额")
    private BigDecimal paymentAmount;

    @ApiModelProperty("客户银行名称")
    private String clientBankName;

    @ApiModelProperty("客户银行账户")
    private String clientBankAccount;

    @ApiModelProperty("赔款类型名称")
    private String paymentTypeName;

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(BigDecimal paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public String getClientBankName() {
        return clientBankName;
    }

    public void setClientBankName(String clientBankName) {
        this.clientBankName = clientBankName;
    }

    public String getClientBankAccount() {
        return clientBankAccount;
    }

    public void setClientBankAccount(String clientBankAccount) {
        this.clientBankAccount = clientBankAccount;
    }

    public String getPaymentTypeName() {
        return paymentTypeName;
    }

    public void setPaymentTypeName(String paymentTypeName) {
        this.paymentTypeName = paymentTypeName;
    }
}
