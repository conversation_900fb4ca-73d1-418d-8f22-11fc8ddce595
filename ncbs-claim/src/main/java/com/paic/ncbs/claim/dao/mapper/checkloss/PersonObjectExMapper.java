package com.paic.ncbs.claim.dao.mapper.checkloss;

import com.paic.ncbs.claim.model.dto.duty.PersonObjectExDTO;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface PersonObjectExMapper {

    public void savePersonOtherInfo(PersonObjectExDTO personObjectExDTO);

    public void removePersonOtherInfo(String idAhcsChannelProcess);

    public PersonObjectExDTO getPersonOtherInfoByIdChannelProcess(String idAhcsChannelProcess);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);
}
