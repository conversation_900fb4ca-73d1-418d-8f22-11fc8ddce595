package com.paic.ncbs.claim.model.vo.ahcs;

import com.paic.ncbs.claim.model.dto.fee.FeePayDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.dto.settle.CoinsureInfoDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.prepayinfo.PrePayInfoDTO;

import java.util.List;
import java.util.Map;

public class PrePayVO {
	
	private List<EstimatePolicyDTO> estimatePolicyList;
	//预赔数据
	private PrePayInfoDTO payInfoDTO;
	//预赔理赔费用
	private List<FeePayDTO> feePays;
	//是否审批环节 Y-是 N-否
	private String isApprFlag;
	//支付信息IDList
	private List<String> idClmPaymentList;
	
	private String reportNo;
	
	private Integer caseTimes;
	//预赔次数
	private Integer subTimes;
	//预赔申请类型
	private List<String> prepayTypes;
	//赔款支付项
	private List<PaymentItemComData> paymentItemComDataList;
	//赔款保单集合
	private List<Map<String,String>> policyNoList;

	private List<PaymentItemComData> paymentInfoLis;

    /**
     * 共保信息
     */
    private List<CoinsureInfoDTO> coInsureInfoList;

	public List<PaymentItemComData> getPaymentInfoLis() {
		return paymentInfoLis;
	}
	public void setPaymentInfoLis(List<PaymentItemComData> paymentInfoLis) {
		this.paymentInfoLis = paymentInfoLis;
	}
	public List<Map<String,String>> getPolicyNoList() {
		return policyNoList;
	}
	public void setPolicyNoList(List<Map<String,String>> policyNoList) {
		this.policyNoList = policyNoList;
	}
	public List<PaymentItemComData> getPaymentItemComDataList() {
		return paymentItemComDataList;
	}
	public void setPaymentItemComDataList(List<PaymentItemComData> paymentItemComDataList) {
		this.paymentItemComDataList = paymentItemComDataList;
	}
	public List<String> getIdClmPaymentList() {
		return idClmPaymentList;
	}
	public void setIdClmPaymentList(List<String> idClmPaymentList) {
		this.idClmPaymentList = idClmPaymentList;
	}
	public List<String> getPrepayTypes() {
		return prepayTypes;
	}
	public void setPrepayTypes(List<String> prepayTypes) {
		this.prepayTypes = prepayTypes;
	}
	public List<EstimatePolicyDTO> getEstimatePolicyList() {
		return estimatePolicyList;
	}
	public void setEstimatePolicyList(List<EstimatePolicyDTO> estimatePolicyList) {
		this.estimatePolicyList = estimatePolicyList;
	}
	public String getReportNo() {
		return reportNo;
	}
	public void setReportNo(String reportNo) {
		this.reportNo = reportNo;
	}

	public Integer getCaseTimes() {
		return caseTimes;
	}
	public void setCaseTimes(Integer caseTimes) {
		this.caseTimes = caseTimes;
	}
	public Integer getSubTimes() {
		return subTimes;
	}
	public void setSubTimes(Integer subTimes) {
		this.subTimes = subTimes;
	}
	public String getIsApprFlag() {
		return isApprFlag;
	}
	public void setIsApprFlag(String isApprFlag) {
		this.isApprFlag = isApprFlag;
	}
	public List<FeePayDTO> getFeePays() {
		return feePays;
	}
	public void setFeePays(List<FeePayDTO> feePays) {
		this.feePays = feePays;
	}
	public PrePayInfoDTO getPayInfoDTO() {
		return payInfoDTO;
	}
	public void setPayInfoDTO(PrePayInfoDTO payInfoDTO) {
		this.payInfoDTO = payInfoDTO;
	}

    public List<CoinsureInfoDTO> getCoInsureInfoList() {
        return coInsureInfoList;
    }

    public void setCoInsureInfoList(List<CoinsureInfoDTO> coInsureInfoList) {
        this.coInsureInfoList = coInsureInfoList;
    }
}
