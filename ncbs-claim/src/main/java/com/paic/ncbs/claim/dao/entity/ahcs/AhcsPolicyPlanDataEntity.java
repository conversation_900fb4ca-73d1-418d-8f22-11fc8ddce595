package com.paic.ncbs.claim.dao.entity.ahcs;

import java.util.Date;

/**
 * 保单险种信息表
 */
public class AhcsPolicyPlanDataEntity {

    private String createdBy;

    private Date createdDate;

    private String updatedBy;

    private Date updatedDate;

    private String idAhcsPolicyPlanData;

    private String idAhcsPolicyPlan;

    private String idAhcsPolicyInfo;

    private String planName;

    private String planCode;

    private String rescueCompany;

    private String isMain;

    private String groupCode;

    private String orgPlanCode;

    private String orgPlanName;

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(Date updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getIdAhcsPolicyPlan() {
        return idAhcsPolicyPlan;
    }

    public void setIdAhcsPolicyPlan(String idAhcsPolicyPlan) {
        this.idAhcsPolicyPlan = idAhcsPolicyPlan == null ? null : idAhcsPolicyPlan.trim();
    }

    public String getIdAhcsPolicyInfo() {
        return idAhcsPolicyInfo;
    }

    public void setIdAhcsPolicyInfo(String idAhcsPolicyInfo) {
        this.idAhcsPolicyInfo = idAhcsPolicyInfo == null ? null : idAhcsPolicyInfo.trim();
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName == null ? null : planName.trim();
    }

    public String getPlanCode() {
        return planCode;
    }

    public void setPlanCode(String planCode) {
        this.planCode = planCode == null ? null : planCode.trim();
    }

    public String getRescueCompany() {
        return rescueCompany;
    }

    public void setRescueCompany(String rescueCompany) {
        this.rescueCompany = rescueCompany == null ? null : rescueCompany.trim();
    }

    public String getIsMain() {
        return isMain;
    }

    public void setIsMain(String isMain) {
        this.isMain = isMain == null ? null : isMain.trim();
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode == null ? null : groupCode.trim();
    }

    public String getOrgPlanCode() {
        return orgPlanCode;
    }

    public void setOrgPlanCode(String orgPlanCode) {
        this.orgPlanCode = orgPlanCode == null ? null : orgPlanCode.trim();
    }

    public String getOrgPlanName() {
        return orgPlanName;
    }

    public void setOrgPlanName(String orgPlanName) {
        this.orgPlanName = orgPlanName == null ? null : orgPlanName.trim();
    }

    public String getIdAhcsPolicyPlanData() {
        return idAhcsPolicyPlanData;
    }

    public void setIdAhcsPolicyPlanData(String idAhcsPolicyPlanData) {
        this.idAhcsPolicyPlanData = idAhcsPolicyPlanData;
    }
}