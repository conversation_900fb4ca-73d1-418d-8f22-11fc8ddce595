package com.paic.ncbs.claim.dao.entity.duty;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
    * 财产损失明细信息表
    */
@Getter
@Setter
@TableName(value = "clms_prop_detail_loss")
public class PropDetailLossEntity implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private String id;

    /**
     * 上级ID
     */
    @TableField(value = "id_prop_loss")
    private String idPropLoss;

    /**
     * 报案号
     */
    @TableField(value = "report_no")
    private String reportNo;

    /**
     * 赔付次数
     */
    @TableField(value = "case_times")
    private Integer caseTimes;

    /**
     * 序号
     */
    @TableField(value = "serial_no")
    private Integer serialNo;

    /**
     * 项目类型
     */
    @TableField(value = "loss_type")
    private String lossType;

    /**
     * 项目类型名称
     */
    @TableField(value = "loss_type_name")
    private String lossTypeName;

    /**
     * 项目描述
     */
    @TableField(value = "loss_desc")
    private String lossDesc;

    /**
     * 报损金额
     */
    @TableField(value = "report_amount")
    private BigDecimal reportAmount;

    /**
     * 定损金额
     */
    @TableField(value = "estimate_amount")
    private BigDecimal estimateAmount;

    /**
     * 	残值
     */
    @TableField(value = "remnant")
    private BigDecimal remnant;

    /**
     * 投保比例	
     */
    @TableField(value = "insurance_rate")
    private BigDecimal insuranceRate;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 创建人员
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_date")
    private Date createdDate;

    /**
     * 修改人员
     */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_date")
    private Date updatedDate;

    private static final long serialVersionUID = 1L;
}