package com.paic.ncbs.claim.dao.entity.other;

import java.util.Date;

public class ExchangeRateEntity {
    private String createdBy;

    private Date createdDate;

    private String updatedBy;

    private Date updatedDate;

    private String exchangeRate;

    private Date invalidateDate;

    private String rowId;

    private String currency1Code;

    private String currency2Code;

    private Date effectiveDate;

    public String getCurrency1Code() {
        return currency1Code;
    }

    public void setCurrency1Code(String currency1Code) {
        this.currency1Code = currency1Code == null ? null : currency1Code.trim();
    }

    public String getCurrency2Code() {
        return currency2Code;
    }

    public void setCurrency2Code(String currency2Code) {
        this.currency2Code = currency2Code == null ? null : currency2Code.trim();
    }

    public Date getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(Date effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(Date updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(String exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public Date getInvalidateDate() {
        return invalidateDate;
    }

    public void setInvalidateDate(Date invalidateDate) {
        this.invalidateDate = invalidateDate;
    }

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId == null ? null : rowId.trim();
    }
}