package com.paic.ncbs.claim.dao.mapper.indicator;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.dao.entity.indicator.CaseIndicatorEntity;
import com.paic.ncbs.claim.model.dto.indicator.CaseIndicatorQueryDTO;
import com.paic.ncbs.claim.model.dto.indicator.RegistrationIndicatorDTO;
import com.paic.ncbs.claim.model.dto.indicator.ReopenIndicatorDTO;
import com.paic.ncbs.claim.model.dto.indicator.SettleIndicatorDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 案件指标Mapper
 * @author: justinwu
 * @create 2025/3/7 16:04
 */
public interface CaseIndicatorMapper extends BaseMapper<CaseIndicatorEntity> {

    public List<SettleIndicatorDTO> getUnSettleCaseList(CaseIndicatorQueryDTO caseIndicatorQueryDTO);

    public List<SettleIndicatorDTO> getSettleCaseList(CaseIndicatorQueryDTO caseIndicatorQueryDTO);

    public List<RegistrationIndicatorDTO> getUnRegCaseList(CaseIndicatorQueryDTO caseIndicatorQueryDTO);

    public List<RegistrationIndicatorDTO> getRegCaseList(CaseIndicatorQueryDTO caseIndicatorQueryDTO);
    public List<ReopenIndicatorDTO> getReopenCaseList(CaseIndicatorQueryDTO caseIndicatorQueryDTO);

    public List<String> selectInvestigateReportNo(@Param("seconds") int seconds, @Param("indicatorCode") String indicatorCode, @Param("serverCode") String serverCode);
}
