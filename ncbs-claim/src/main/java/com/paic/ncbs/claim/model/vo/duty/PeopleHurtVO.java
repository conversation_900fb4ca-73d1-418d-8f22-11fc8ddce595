package com.paic.ncbs.claim.model.vo.duty;

import com.paic.ncbs.claim.model.dto.duty.PersonDiseaseVO;
import com.paic.ncbs.claim.model.vo.checkloss.DisabilityAppraisalVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@ApiModel("人伤VO")
@Data
@Accessors(chain = true)
public class PeopleHurtVO {

    @ApiModelProperty("通道任务类型，对应clm_COMMON_PARAMETER collection_code=CHANNEL_TYPE")
    private String channelType;

    @ApiModelProperty("通道任务表主键")
    private String idAhcsChannelProcess;

    @ApiModelProperty("意外信息")
    private PersonAccidentVO accidentVO;

    @ApiModelProperty("疾病信息")
    private PersonDiseaseVO diseaseVO;

    @ApiModelProperty("诊断信息VO")
    private PersonDiagnoseVO diagnoseVO;

    @ApiModelProperty("残疾")
    private PersonDisabilityVO disabilityVO;

    @ApiModelProperty("死亡信息")
    private PersonDeathVO deathVO;

    @ApiModelProperty("重大疾病")
    private BigDiseaseVO bigDiseaseVO;

    @ApiModelProperty("重大疾病详细编码")
    private List<String> bigDiseaseDetailCode;

    @ApiModelProperty("伤残鉴定VO")
    private List<DisabilityAppraisalVO> disabilityAppraisalVO;

    @ApiModelProperty("医院信息VO")
    private PersonHospitalVO hospitalVO;

    @ApiModelProperty("人员扩展信息表VO")
    private PersonObjectExVO objectExVO;

    @ApiModelProperty("津贴信息表VO")
    private PersonBenefitVO personBenefitVO;

    @ApiModelProperty("其他人伤信息VO")
    private PersonOtherLossVO personOtherLossVO;
    @ApiModelProperty("没有病例的情况 TPA不会有诊断信息，TPA会用这个字段传值给核心，核心用这个字段当作诊断信息，诊断信息和accidentCausesDetails可以同时存在")
    private List<String>  accidentCausesDetails;

    @ApiModelProperty("环节号")
    private String taskId;

    @ApiModelProperty("状态。1：发送，0：暂存")
    private String status;


}
