package com.paic.ncbs.claim.mq.producer.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.model.dto.mq.CompensateDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLPrepayDto;
import com.paic.ncbs.claim.mq.producer.MqProducerPreClaimPaymentInformationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

//import org.springframework.amqp.rabbit.core.RabbitTemplate;

@Service
@Slf4j
public class MqProducerPreClaimPaymentInformationServiceImpl implements MqProducerPreClaimPaymentInformationService {
    @Value("${mq.rabbit.exchange.ncbs.claim.root:ncbsClaimExchange}")
    private String ncbsClaimExchange;
    //立案环节topic
    @Value("${mq.compensate.topic}")
    private String compensateTopic;
   /* @Autowired
    private RabbitTemplate rabbitTemplate;*/
    @Override
    @Async("asyncPool")
    public void syncPreClaimPaymentInformationLink(String compensateNo, Date payTime) {
        // 数据组装
        CompensateDto compensateDto= new CompensateDto();
        List<PrpLPrepayDto> prpLPrepayDtoList = new ArrayList<>();

        PrpLPrepayDto prpLPrepayDto = new PrpLPrepayDto();
        prpLPrepayDto.setPreCompensateNo(compensateNo);
        prpLPrepayDto.setPayTime(payTime);
        prpLPrepayDtoList.add(prpLPrepayDto);
        compensateDto.setPrpLPrepayDtoList(prpLPrepayDtoList);
        //发送MQ消息
        JSONObject jsonObj = (JSONObject) JSON.toJSON(compensateDto);
        log.info("syncPreClaimPaymentInformationLink===sendMessage: {}",jsonObj);
      //  rabbitTemplate.convertAndSend(ncbsClaimExchange, compensateTopic, compensateDto);
    }


}

