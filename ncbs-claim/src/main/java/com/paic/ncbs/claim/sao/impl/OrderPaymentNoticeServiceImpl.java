package com.paic.ncbs.claim.sao.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.base.util.MeshSendUtils;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.openapi.OrderPaymentRequestDTO;
import com.paic.ncbs.claim.model.dto.openapi.PaymentNoticeDTO;
import com.paic.ncbs.claim.model.dto.pay.*;
import com.paic.ncbs.claim.sao.OrderPaymentNoticeService;
import com.paic.ncbs.claim.service.doc.PrintService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;

@Slf4j
@RefreshScope
@Service
public class OrderPaymentNoticeServiceImpl implements OrderPaymentNoticeService {
    @Value("${ncbs.pay.url:http://10.18.31.239:8868/fin/fin-web/web/api/transData/transToPayment}")
    private String orderPayNoticeUrl;
    @Value("${ncbs.pay.passWord:testPassword}")
    private String passWord;

    @Value("${ncbs.pay.userCode:test}")
    private String userCode;

    @Value("${switch.mesh}")
    private Boolean switchMesh;

    @Autowired
    PrintService printService;

    @Autowired
    RestTemplate restTemplate;
    @Autowired
    private PaymentItemMapper paymentItemMapper;
    //指令支付的情况通知收付
    @Override
    public String orderPaymentNotice(OrderPaymentRequestDTO dto) {
        //1:检查入参
        checkInputData(dto);
        //2:组装数据
        PaymentNoticeDTO noticeDTO =setData(dto);
        //调用收付信息接口 通知收付系统支付
        String result =sendPayMent(noticeDTO);
        return result;
    }

    @Override
    public void batchOrderPaymentNotice(Integer days) {
        //获取到超过10天未支付的指令支付数据
        //案件结案的时间点起，计算9*24+1小时
        Date enddate=DateUtil.offsetDay(new Date(),-days);
        Date enddateTime=DateUtil.offsetHour(enddate,-1);
        List< PaymentItemDTO> paymentItemDTOS =  paymentItemMapper.getBatchOrderPayDataInfo(enddateTime);
        if(CollectionUtils.isEmpty(paymentItemDTOS)){
            return;
        }
        for (PaymentItemDTO itemDTO : paymentItemDTOS) {
            PaymentNoticeDTO noticeDTO = new PaymentNoticeDTO();
            noticeDTO.setClaimNo(itemDTO.getReportNo());
            noticeDTO.setLossNo(itemDTO.getCaseNo());
            noticeDTO.setLossSeqNo(itemDTO.getCaseTimes().toString());
            noticeDTO.setPaySerialNo(itemDTO.getIdClmPaymentItem());
            try{
                String result =sendPayMent(noticeDTO);
                LogUtil.info("通知收付指令支付返回结果={}",result);
            }catch(Exception e){
               LogUtil.info("指令支付批处理通知收付失败={}", JsonUtils.toJsonString(noticeDTO));
            }
        }


    }


    /**
     * 通知收付系统
     * @param paymentNoticeDTO
     */
    private String sendPayMent(PaymentNoticeDTO paymentNoticeDTO) {
        String returnMessage="";
        OrderPaymentNoticeDTO orderPaymentNoticeDTO = new OrderPaymentNoticeDTO();
        PayResult payResult = new PayResult();
        PayInfoHead payInfoHead = new PayInfoHead();
        payInfoHead.setPassWord(passWord);
        //指令支付通知传什么值？
        payInfoHead.setRequestType(PayInfoHead.TYPE_Q24);
        payInfoHead.setUserCode(userCode);
        PayInfoNotice<OrderPaymentNoticeDTO> payInfoNotice = new PayInfoNotice<>();
        payInfoNotice.setHead(payInfoHead);
        List<PaymentNoticeDTO> requestDataList=new ArrayList<>();
        requestDataList.add(paymentNoticeDTO);
        orderPaymentNoticeDTO.setLossPayPlan(requestDataList);
        payInfoNotice.setBody(orderPaymentNoticeDTO);
        HttpHeaders header = new HttpHeaders();
        header.add("Content-Type", "application/json;charset:utf-8");
        log.info("指令支付orderPaymentNotice:" + JSON.toJSONString(payInfoNotice));
        HttpEntity<Object> httpEntity = new HttpEntity<>(payInfoNotice, header);
        try{
            String result;
            if (switchMesh){
                Map<String, String> headers = new HashMap<>();
                headers.put("Content-Type", "application/json;charset:utf-8");
                result = MeshSendUtils.post(orderPayNoticeUrl + "?" + printService.getSignature(), JSON.toJSONString(payInfoNotice),headers);
            }else {
                result = restTemplate.postForObject(orderPayNoticeUrl + "?" + printService.getSignature(), httpEntity, String.class);
            }
            log.info("orderPaymentNotice-result={}",result);
            if (StringUtils.isNotEmpty(result)) {
                returnMessage=JSON.toJSONString(payResult);

            }else{
                log.info("orderPaymentNotice-fail: 调用通知指令支付接口异常");
                throw new GlobalBusinessException("调用通知指令支付接口异常！");
            }
        }catch (Exception e){
            log.error("", e);
            throw new GlobalBusinessException("收付费指令通知接口调用异常！"+e.getCause()+e.getMessage());
        }
        return returnMessage;
    }

    /**
     * 组装数据
     * @param dto
     */
    private PaymentNoticeDTO setData(OrderPaymentRequestDTO dto) {
        PaymentItemDTO itemDTO = paymentItemMapper.getOrderPayMentInfo(dto.getReportNo(),dto.getCaseTimes());
        if(ObjectUtil.isEmpty(itemDTO)){
            throw new GlobalBusinessException("支付信息不存在！");
        }
        PaymentNoticeDTO noticeDTO = new PaymentNoticeDTO();
        noticeDTO.setClaimNo(itemDTO.getReportNo());
        noticeDTO.setLossNo(itemDTO.getCaseNo());
        noticeDTO.setLossSeqNo(itemDTO.getCaseTimes().toString());
        noticeDTO.setPaySerialNo(itemDTO.getIdClmPaymentItem());
        return noticeDTO;
    }

    /**
     * 参数校验
     * @param dto
     */
    private void checkInputData(OrderPaymentRequestDTO dto) {
        if(StringUtils.isEmptyStr(dto.getReportNo())){
            throw new GlobalBusinessException("报案号不能为空！");
        }
        if(StringUtils.isEmptyStr(dto.getCaseTimes())){
            dto.setCaseTimes(1);
        }
    }
}
