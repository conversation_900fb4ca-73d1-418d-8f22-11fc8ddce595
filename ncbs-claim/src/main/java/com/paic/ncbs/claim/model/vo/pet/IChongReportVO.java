package com.paic.ncbs.claim.model.vo.pet;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.dao.entity.report.LinkManEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentPetEntity;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @description:
 **/
public class IChongReportVO implements Serializable{

    private static final long serialVersionUID = 8420328744610195523L;

    private String reportAccept;//报案人或者渠道
    private String reportPhone;//报案人手机号（宠物主人）
    private String insuredApplyStatus;//事故者现状
    private String certificateType;//证件类型
    private String certificateNo;//证件号
    private String policyNo;//保单号
    private String clientName;//客户姓名(被保险人)

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date accidentDate;//事故日期
    private String accidentDetail;//事故描述
    private String accidentPlace;//事故地点
    private String clientMobile;//手机号码
    private String remark;//备注
    private String tradeCode;//合作伙伴编码
    private ReportAccidentPetExtendVO accidentExtendVO;//宠物扩展信息;
    private ReportAccidentPetEntity reportAccidentPet;//宠物基本信息

    private BigDecimal remainPremium;//期缴剩余保费
    private String payUrl;//支付路径
    private List<LinkManEntity> linkManList = new ArrayList<>();

    @ApiModelProperty(value = "出险省份")
    private String accidentProvince;
    @ApiModelProperty(value = "出险城市")
    private String accidentCity;
    @ApiModelProperty(value = "出险县")
    private String accidentCounty;
    @ApiModelProperty(value = "宠物医院省份")
    private String petHosProvinceCode;
    @ApiModelProperty(value = "宠物医院城市")
    private String petHosCityCode;
    @ApiModelProperty(value = "宠物医院县")
    private String petHosCountryCode;
    @ApiModelProperty(value = "宠物医院编码")
    private String petHosCode;
    @ApiModelProperty(value = "宠物医院名称")
    private String petHosName;

    public String getReportAccept() {
        return reportAccept;
    }

    public void setReportAccept(String reportAccept) {
        this.reportAccept = reportAccept;
    }

    public String getReportPhone() {
        return reportPhone;
    }

    public void setReportPhone(String reportPhone) {
        this.reportPhone = reportPhone;
    }

    public String getInsuredApplyStatus() {
        return insuredApplyStatus;
    }

    public void setInsuredApplyStatus(String insuredApplyStatus) {
        this.insuredApplyStatus = insuredApplyStatus;
    }

    public String getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(String certificateType) {
        this.certificateType = certificateType;
    }

    public String getCertificateNo() {
        return certificateNo;
    }

    public void setCertificateNo(String certificateNo) {
        this.certificateNo = certificateNo;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public Date getAccidentDate() {
        return accidentDate;
    }

    public void setAccidentDate(Date accidentDate) {
        this.accidentDate = accidentDate;
    }

    public String getAccidentDetail() {
        return accidentDetail;
    }

    public void setAccidentDetail(String accidentDetail) {
        this.accidentDetail = accidentDetail;
    }

    public String getAccidentPlace() {
        return accidentPlace;
    }

    public void setAccidentPlace(String accidentPlace) {
        this.accidentPlace = accidentPlace;
    }

    public String getClientMobile() {
        return clientMobile;
    }

    public void setClientMobile(String clientMobile) {
        this.clientMobile = clientMobile;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTradeCode() {
        return tradeCode;
    }

    public void setTradeCode(String tradeCode) {
        this.tradeCode = tradeCode;
    }

    public ReportAccidentPetExtendVO getAccidentExtendVO() {
        return accidentExtendVO;
    }

    public void setAccidentExtendVO(ReportAccidentPetExtendVO accidentExtendVO) {
        this.accidentExtendVO = accidentExtendVO;
    }

    public ReportAccidentPetEntity getReportAccidentPet() {
        return reportAccidentPet;
    }

    public void setReportAccidentPet(ReportAccidentPetEntity reportAccidentPet) {
        this.reportAccidentPet = reportAccidentPet;
    }

    public BigDecimal getRemainPremium() {
        return remainPremium;
    }

    public void setRemainPremium(BigDecimal remainPremium) {
        this.remainPremium = remainPremium;
    }

    public String getPayUrl() {
        return payUrl;
    }

    public void setPayUrl(String payUrl) {
        this.payUrl = payUrl;
    }

    public List<LinkManEntity> getLinkManList() {
        return linkManList;
    }

    public void setLinkManList(List<LinkManEntity> linkManList) {
        this.linkManList = linkManList;
    }

    public String getAccidentProvince() {
        return accidentProvince;
    }

    public void setAccidentProvince(String accidentProvince) {
        this.accidentProvince = accidentProvince;
    }

    public String getAccidentCity() {
        return accidentCity;
    }

    public void setAccidentCity(String accidentCity) {
        this.accidentCity = accidentCity;
    }

    public String getAccidentCounty() {
        return accidentCounty;
    }

    public void setAccidentCounty(String accidentCounty) {
        this.accidentCounty = accidentCounty;
    }

    public String getPetHosProvinceCode() {
        return petHosProvinceCode;
    }

    public void setPetHosProvinceCode(String petHosProvinceCode) {
        this.petHosProvinceCode = petHosProvinceCode;
    }

    public String getPetHosCityCode() {
        return petHosCityCode;
    }

    public void setPetHosCityCode(String petHosCityCode) {
        this.petHosCityCode = petHosCityCode;
    }

    public String getPetHosCountryCode() {
        return petHosCountryCode;
    }

    public void setPetHosCountryCode(String petHosCountryCode) {
        this.petHosCountryCode = petHosCountryCode;
    }

    public String getPetHosCode() {
        return petHosCode;
    }

    public void setPetHosCode(String petHosCode) {
        this.petHosCode = petHosCode;
    }

    public String getPetHosName() {
        return petHosName;
    }

    public void setPetHosName(String petHosName) {
        this.petHosName = petHosName;
    }
}
