package com.paic.ncbs.claim.dao.mapper.secondunderwriting;

import com.paic.ncbs.claim.dao.entity.clms.ClmsPolicyHistoryUwInfoEntity;
import com.paic.ncbs.claim.dao.entity.clms.ClmsSeconduwPlanConclusionEntity;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * 理赔二核险种层核保结论表(ClmsSeconduwPlanConclusionEntity)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-14 14:07:13
 */
@MapperScan
public interface ClmsSeconduwPlanConclusionMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ClmsSeconduwPlanConclusionEntity queryById(String id);
    
    /**
     * 通过报案号查询单条数据
     *
     * @param reportNo 主键
     * @return 实例对象
     */
    ClmsSeconduwPlanConclusionEntity queryByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes);

    /**
     * 新增数据
     *
     * @param clmsSeconduwPlanConclusionEntity 实例对象
     * @return 影响行数
     */
    int insert(ClmsSeconduwPlanConclusionEntity clmsSeconduwPlanConclusionEntity);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<ClmsSeconduwPlanConclusionEntity> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ClmsSeconduwPlanConclusionEntity> entities);

    /**
     * 修改数据
     *
     * @param clmsSeconduwPlanConclusionEntity 实例对象
     * @return 影响行数
     */
    int update(ClmsSeconduwPlanConclusionEntity clmsSeconduwPlanConclusionEntity);
    
    /**
     * 根据报案号修改数据
     *
     * @param reportNo 实例对象
     * @return 影响行数
     */
    int updateByReportNo( @Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);


}

