package com.paic.ncbs.claim.sao;

import com.paic.ncbs.claim.model.dto.openapi.OrderPaymentRequestDTO;
import com.paic.ncbs.claim.model.dto.pay.OrderPaymentNoticeDTO;

/**
 * 指令支付通知接口
 */
public interface OrderPaymentNoticeService {
    /**
     * 通知收付
     * @param dto
     * @return
     */
    public String orderPaymentNotice(OrderPaymentRequestDTO dto);

    /**
     * 批量处理超过配置天数没有通知收付指令支付的
     * @param orderPayWaitDays
     */
    void batchOrderPaymentNotice(Integer orderPayWaitDays);
}
