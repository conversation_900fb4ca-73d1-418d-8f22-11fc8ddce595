package com.paic.ncbs.claim.mq.producer.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.CommonConstant;
import com.paic.ncbs.claim.common.constant.NcbsConstant;
import com.paic.ncbs.claim.common.enums.InsuredApplyStatusEnum;
import com.paic.ncbs.claim.common.util.CodeUtil;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.report.*;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.duty.OperationDefineMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimatePolicyMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoExMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
import com.paic.ncbs.claim.model.dto.duty.PersonDiagnoseDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.mq.RegistDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLAcciPersonDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLRegistDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLRegistRPolicyDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLRegistTextDto;
import com.paic.ncbs.claim.model.dto.mq.swf.SwfLogStoreDto;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.report.ReportBaseInfoResData;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.duty.PeopleHurtVO;
import com.paic.ncbs.claim.mq.producer.MqProducerRegistCancelService;
import com.paic.ncbs.claim.mq.producer.MqProducerRegistService;
import com.paic.ncbs.claim.service.duty.DutySurveyService;
import com.paic.ncbs.claim.service.endcase.CaseBaseService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.endcase.WholeCaseService;
import com.paic.ncbs.claim.service.report.ReportAccidentExService;
import com.paic.ncbs.claim.service.report.ReportAccidentService;
import com.paic.ncbs.claim.service.report.ReportCustomerInfoService;
import com.paic.ncbs.claim.service.report.ReportInfoService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

//import org.springframework.amqp.rabbit.core.RabbitTemplate;

/**
 * 报案环节同步第三方系统第三方系统信息
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MqProducerRegistServiceImpl implements MqProducerRegistService {
    @Value("${mq.rabbit.exchange.ncbs.claim.root:ncbsClaimExchange}")
    private String ncbsClaimExchange;
    //报案环节topic
    @Value("${mq.regist.topic}")
    private String registTopic;
    @Autowired
    private ReportAccidentService reportAccidentService;
    @Autowired
    private ReportAccidentExService reportAccidentExService;
    @Autowired
    private ReportCustomerInfoService reportCustomerInfoService;

    @Autowired
    private EstimatePolicyMapper estimatePolicyMapper;
    @Autowired
    private ReportInfoService reportInfoService;
    @Autowired
    private MqProducerRegistCancelService mqProducerRegistCancelService;
    @Autowired
    private PolicyInfoMapper policyInfoMapper;
    @Autowired
    private ReportInfoExMapper reportInfoExMapper;
    /*@Autowired
    private RabbitTemplate rabbitTemplate;*/
    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;
    @Autowired
    private OcasMapper ocasMapper;
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private DepartmentDefineMapper departmentDefineMapper;
    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private WholeCaseService wholeCaseService;
    @Autowired
    private DutySurveyService dutySurveyService;
    @Autowired
    private OperationDefineMapper operationDefineMapper;
    @Autowired
    private CaseBaseService caseBaseService;
    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;

    @Override
    @Async("asyncPool")
    public void syncRegistLink(String reportNo, Integer caseTimes) {
        LogUtil.info("syncRegistLink-info:{},{}",reportNo,caseTimes);
        // 数据组装
        RegistDto registDto = initPrpLRegistDto(reportNo,caseTimes);
        //发送MQ消息
        JSONObject jsonObj = (JSONObject) JSON.toJSON(registDto);
        log.info("syncRegistLink===sendMessage: {}",jsonObj);
      //  rabbitTemplate.convertAndSend(ncbsClaimExchange, registTopic, registDto);
    }

    private RegistDto initPrpLRegistDto(String reportNo, Integer caseTimes) {

        //数据组
        RegistDto registDto = new RegistDto();
        List<PrpLRegistRPolicyDto> prpLRegistRPolicyDtoList = new ArrayList<>();
        ReportInfoEntity reportInfo = reportInfoService.getReportInfo(reportNo);
        List<ReportInfoExEntity> reportInfoExEntityList = reportInfoExMapper.getReportInfoEx(reportNo);
        ReportInfoExEntity reportInfoExEntity = reportInfoExEntityList.get(0);
        List<PolicyInfoDTO> policyInfoDTOList = policyInfoMapper.getPolicyInfoListByReportNo(reportNo);
        PolicyInfoDTO policyInfoDTO = policyInfoDTOList.get(0);
        WholeCaseBaseDTO wholeCaseBaseEntity = wholeCaseBaseService.getWholeCaseBase(reportNo,caseTimes);

        //查询 报案客户 被保险人 信息表
        ReportCustomerInfoEntity customerInfo = reportCustomerInfoService.getReportCustomerInfoByReportNo(reportNo);
        //查询 意键险报案信息扩展表
        ReportAccidentExEntity reportAccidentEx = reportAccidentExService.getReportAccidentEx(reportNo);
        //查询 事故信息表
        ReportAccidentEntity reportAccident = reportAccidentService.getReportAccident(reportNo);
//        List<EstimatePolicyDTO> policyList = estimatePolicyMapper.getEstimatePolicyByHistoryList(reportNo, caseTimes);
        List<EstimatePolicyDTO> policyList = estimatePolicyMapper.getByReportNoAndCaseTimes(reportNo, caseTimes);
        EstimatePolicyDTO estimatePolicyDTO = policyList.get(CommonConstant.ZERO);
        if (CollectionUtils.isNotEmpty(policyList)){
            for (int i = 0; i < policyList.size(); i++) {
                PrpLRegistRPolicyDto prpLRegistRPolicyDto = new PrpLRegistRPolicyDto();
                prpLRegistRPolicyDto.setRegistNo(reportNo);
                prpLRegistRPolicyDto.setPolicyNo(policyList.get(i).getPolicyNo());
                prpLRegistRPolicyDto.setClaimNo(null);
                Map<String, String> plyBaseInfo = ocasMapper.getPlyBaseInfo(policyList.get(i).getPolicyNo());
                String productCode = MapUtils.getString(plyBaseInfo,"productCode");
                String businessType = MapUtils.getString(plyBaseInfo,"businessType");
                prpLRegistRPolicyDto.setPolicyType(businessType);
                prpLRegistRPolicyDto.setFlowID("");
                prpLRegistRPolicyDto.setRemark(reportInfo.getRemark());
                prpLRegistRPolicyDto.setValidStatus("1");
                prpLRegistRPolicyDto.setRiskCode(CodeUtil.subStringRiskCode(productCode));
                prpLRegistRPolicyDto.setRegistFlag("1");
                prpLRegistRPolicyDto.setRegistCancelDate(null);
                prpLRegistRPolicyDto.setCompensateNo(null);
                prpLRegistRPolicyDto.setSerialNo(i+1);
                prpLRegistRPolicyDto.setUpdateTime(new Date());
                prpLRegistRPolicyDtoList.add(prpLRegistRPolicyDto);
            }
        }
        ReportBaseInfoResData reportBaseInfo = wholeCaseService.getReportBaseInfo(reportNo, caseTimes);
        // 人伤全量数据
        PeopleHurtVO peopleHurtVO = dutySurveyService.getPeopleHurtVO(reportNo, caseTimes);
        //组装 PrpLAcciPersonDto
        List<CaseBaseEntity> caseBaseInfoList = caseBaseService.getCaseBaseInfoByReportNoAndCasetimes(reportNo, String.valueOf(caseTimes));
        List<PrpLAcciPersonDto> prpLAcciPersonDtoList = getPrpLAcciPersonDto(reportNo, customerInfo, reportAccidentEx, reportAccident,policyInfoDTO,reportBaseInfo,peopleHurtVO,caseBaseInfoList,"01",null);
        //报案基本信息
        PrpLRegistDto prpLRegistDto = mqProducerRegistCancelService.initPrpLRegistDto(reportNo, reportInfo, customerInfo, reportInfoExEntity, reportAccident, policyInfoDTO,wholeCaseBaseEntity,reportAccidentEx,null);
        registDto.setPrpLRegistDto(prpLRegistDto);
        registDto.setPrpLAcciPersonDtoList(prpLAcciPersonDtoList);
        // 文字记录表
        PrpLRegistTextDto prpLRegistTextDto = getPrpLRegistTextDto(reportNo);
        registDto.setPrpLRegistTextDto(prpLRegistTextDto);
        registDto.setPrpLRegistRPolicyDtoList(prpLRegistRPolicyDtoList);
        List<SwfLogStoreDto>  swfLogStoreDtoList = getSwfLogStoreDto(reportNo,caseTimes,estimatePolicyDTO,customerInfo,reportInfo,caseBaseInfoList,policyInfoDTO);
        registDto.setSwfLogStoreDtoList(swfLogStoreDtoList);
        return registDto;
    }

    @SneakyThrows
    private List<SwfLogStoreDto>  getSwfLogStoreDto(String reportNo, Integer caseTimes, EstimatePolicyDTO estimatePolicyDTO, ReportCustomerInfoEntity customerInfo, ReportInfoEntity reportInfo,List<CaseBaseEntity> caseBaseInfoList, PolicyInfoDTO policyInfoDTO) {
            List<SwfLogStoreDto>  swfLogStoreDtoList=new ArrayList<>();
            SwfLogStoreDto swfLogStoreDto = new SwfLogStoreDto();
            TaskInfoDTO taskInfoDTO = taskInfoMapper.findLatestByReportNoAndBpmKey(reportNo, caseTimes, BpmConstants.OC_REPORT_TRACK);
            swfLogStoreDto.setFlowID("");
            swfLogStoreDto.setLogNo(1);
            swfLogStoreDto.setModelNo(12);
            swfLogStoreDto.setNodeNo(1);
            swfLogStoreDto.setNodeName("报案");
            swfLogStoreDto.setBusinessNo(reportNo);
            if (taskInfoDTO == null){
                // 批量结案的 没有任务信息
                swfLogStoreDto.setHandleDept(reportInfo.getAcceptDepartmentCode());
                swfLogStoreDto.setDeptName(departmentDefineMapper.queryDepartmentNameByDeptCode(reportInfo.getAcceptDepartmentCode()));
                swfLogStoreDto.setComCode(reportInfo.getAcceptDepartmentCode());
                swfLogStoreDto.setHandlerCode(reportInfo.getReportRegisterUm());
                swfLogStoreDto.setHandlerName(userInfoService.getUserNameById(reportInfo.getReportRegisterUm()));
                if (StringUtils.isEmptyStr(swfLogStoreDto.getHandlerCode())){
                    swfLogStoreDto.setHandlerCode(NcbsConstant.ZK_ADMIN_UM);
                    swfLogStoreDto.setHandlerName(NcbsConstant.ZK_ADMIN_NAME);
                }
            } else {
                swfLogStoreDto.setHandleDept(taskInfoDTO.getDepartmentCode());
                String assigner = taskInfoDTO.getAssigner();
                if (StringUtils.isEmptyStr(assigner)){
                    assigner = reportInfo.getReportRegisterUm();
                }
                swfLogStoreDto.setDeptName(departmentDefineMapper.queryDepartmentNameByDeptCode(reportInfo.getAcceptDepartmentCode()));
                swfLogStoreDto.setComCode(reportInfo.getAcceptDepartmentCode());
                swfLogStoreDto.setHandlerCode(assigner);
                swfLogStoreDto.setHandlerName(userInfoService.getUserNameById(assigner));
                if (StringUtils.isEmptyStr(swfLogStoreDto.getHandlerCode())){
                    swfLogStoreDto.setHandlerCode(NcbsConstant.ZK_ADMIN_UM);
                    swfLogStoreDto.setHandlerName(NcbsConstant.ZK_ADMIN_NAME);
                }
            }
            String dateStr = DateUtils.parseToFormatString(new Date(), DateUtils.FULL_DATE_STR);
            swfLogStoreDto.setFlowInTime(dateStr);
            swfLogStoreDto.setTimeLimit(0);
            swfLogStoreDto.setHandleTime(dateStr);
            swfLogStoreDto.setSubmitTime(dateStr);
            swfLogStoreDto.setNodeStatus("4");
            swfLogStoreDto.setFlowStatus("0");
            swfLogStoreDto.setPackageID("0");
            swfLogStoreDto.setTaskNo(0);
            swfLogStoreDto.setNodeType("regis");
            swfLogStoreDto.setTitleStr("");
            swfLogStoreDto.setBusinessType("1");
            Map<String, String> productMap = ocasMapper.getPlyBaseInfo(policyInfoDTO.getPolicyNo());
            String productCode = MapUtils.getString(productMap,"productCode");
            swfLogStoreDto.setRiskCode(CodeUtil.subStringRiskCode(productCode));
            swfLogStoreDto.setKeyIn(reportNo);
            swfLogStoreDto.setKeyOut(reportNo);
            swfLogStoreDto.setMainFlowID("0");
            swfLogStoreDto.setSubFlowID("0");
            swfLogStoreDto.setPosX(0);
            swfLogStoreDto.setPosY(0);
            swfLogStoreDto.setEndFlag("");
            swfLogStoreDto.setBeforeHandlerCode("");
            swfLogStoreDto.setBeforeHandlerName("");
            swfLogStoreDto.setPolicyNo(policyInfoDTO.getPolicyNo());
            swfLogStoreDto.setRegistNo(reportNo);
            swfLogStoreDto.setInsuredName(customerInfo.getName());
            swfLogStoreDto.setEntrustFlag("1");
            swfLogStoreDto.setEntrustNodeStatus("1");
            swfLogStoreDto.setDamageEndDate(new Date());
            swfLogStoreDto.setDamageStartDate(new Date());
            swfLogStoreDto.setUpdateTime(new Date());
        swfLogStoreDtoList.add(swfLogStoreDto);
        return swfLogStoreDtoList;
    }

    private PrpLRegistTextDto getPrpLRegistTextDto(String reportNo) {
        PrpLRegistTextDto prpLRegistTextDto = new PrpLRegistTextDto();
        prpLRegistTextDto.setRegistNo(reportNo);
        prpLRegistTextDto.setTextType("1");
        prpLRegistTextDto.setLineNo(1);
        prpLRegistTextDto.setContext("新建报案");
        prpLRegistTextDto.setFlag("");
        prpLRegistTextDto.setUpdateTime(new Date());
        return prpLRegistTextDto;
    }

    @Override
    public List<PrpLAcciPersonDto> getPrpLAcciPersonDto(String reportNo, ReportCustomerInfoEntity customerInfo,
                                                        ReportAccidentExEntity reportAccidentEx, ReportAccidentEntity reportAccident,
                                                        PolicyInfoDTO policyInfoDTO,ReportBaseInfoResData reportBaseInfo,
                                                        PeopleHurtVO peopleHurtVO,List<CaseBaseEntity> caseBaseInfoList,String type,
                                                        List<PaymentItemDTO> items) {
        List<PrpLAcciPersonDto> prpLAcciPersonDtoList=new ArrayList<>();
        int i = 1;
        for(CaseBaseEntity caseBaseEntity:caseBaseInfoList){
            PrpLAcciPersonDto prpLAcciPersonDto = new PrpLAcciPersonDto();
            if("01".equals(type)){
                prpLAcciPersonDto.setCertiNo(reportNo);
                prpLAcciPersonDto.setPolicyNo(caseBaseEntity.getPolicyNo());
            }else if("03".equals(type)){
                prpLAcciPersonDto.setCertiNo(caseBaseEntity.getRegistNo());
                prpLAcciPersonDto.setPolicyNo(caseBaseEntity.getPolicyNo());
            }else if("04".equals(type)){
                PaymentItemDTO item=items.stream().filter(it->it.getPolicyNo().equals(caseBaseEntity.getPolicyNo())).findFirst().orElse(null);
                if(null==item){
                    continue;
                }
                prpLAcciPersonDto.setCertiNo(item.getCompensateNo());
                prpLAcciPersonDto.setPolicyNo(caseBaseEntity.getPolicyNo());
            }
            prpLAcciPersonDto.setCertiType(type);
            prpLAcciPersonDto.setSerialNo(i);
            String riskGroupNo = ocasMapper.getRiskGroupNo(caseBaseEntity.getPolicyNo(), customerInfo.getCertificateNo(), customerInfo.getName());
            prpLAcciPersonDto.setPlanGroupNo(Integer.valueOf(riskGroupNo));
            String riskPersonNo = ocasMapper.getRiskPersonNo(caseBaseEntity.getPolicyNo(), customerInfo.getCertificateNo(), customerInfo.getName());
            prpLAcciPersonDto.setFamilyNo(Integer.valueOf(riskPersonNo));
            prpLAcciPersonDto.setAcciCode(riskPersonNo);
            prpLAcciPersonDto.setAcciName(customerInfo.getName());
            prpLAcciPersonDto.setSex("F".equalsIgnoreCase(customerInfo.getSexCode()) ? "2" : "1");
            prpLAcciPersonDto.setAge(customerInfo.getAge());
            prpLAcciPersonDto.setIdentifyType(transZRRIdentifyType(customerInfo.getCertificateType()));
            prpLAcciPersonDto.setIdentifyNumber(customerInfo.getCertificateNo());
            prpLAcciPersonDto.setPhone(reportBaseInfo.getLinkManList().get(0).getLinkManTelephone());
            prpLAcciPersonDto.setRelationCode(reportBaseInfo.getLinkManRelation());
            prpLAcciPersonDto.setRelationName(reportBaseInfo.getLinkManRelationName());
            prpLAcciPersonDto.setApplyMoney(BigDecimal.ZERO);
            if (null != peopleHurtVO.getHospitalVO() && !CollectionUtils.isEmpty(peopleHurtVO.getHospitalVO().getPersonHospitalList())){
                prpLAcciPersonDto.setTherapyHospital(peopleHurtVO.getHospitalVO().getPersonHospitalList().get(0).getHospitalName());
                prpLAcciPersonDto.setFixedHospital(peopleHurtVO.getHospitalVO().getPersonHospitalList().get(0).getHospitalName());
                prpLAcciPersonDto.setFixedHospitalCode(peopleHurtVO.getHospitalVO().getPersonHospitalList().get(0).getHospitalCode());
            }
            prpLAcciPersonDto.setPersonSituation(InsuredApplyStatusEnum.getCode(reportAccidentEx.getInsuredApplyStatus()));
            prpLAcciPersonDto.setMedicFee(BigDecimal.ZERO);
            prpLAcciPersonDto.setEstimateAllMedicFee(BigDecimal.ZERO);

            if (peopleHurtVO.getDiagnoseVO()!= null && peopleHurtVO.getDiagnoseVO().getDiagnoseDTOs() != null) {
                List<PersonDiagnoseDTO> diagnoseDTOs = peopleHurtVO.getDiagnoseVO().getDiagnoseDTOs();
                PersonDiagnoseDTO personDiagnoseDTO = diagnoseDTOs.get(0);
                //        疾病名称	DISEASENAME          	Y	详情见意健险疾病代码表
                prpLAcciPersonDto.setDiseaseCode(personDiagnoseDTO.getDiagnoseCode());
                prpLAcciPersonDto.setDiseaseName(personDiagnoseDTO.getDiagnoseName());
                String surgicalCode = personDiagnoseDTO.getSurgicalCode();
                if (StringUtils.isNotEmpty(surgicalCode)){
                    //根据报案号查询机构编码
                    String acceptDepartmentCode = ahcsPolicyInfoMapper.selectDepartmentCodeByReportNo(reportNo);
                    //根据机构编码确认机构是全国、北京(211)还是上海(231)
                    String orgType = NcbsConstant.ORG_TYPE_ONE;
//                    if(StringUtils.isNotEmpty(acceptDepartmentCode) && acceptDepartmentCode.startsWith(NcbsConstant.BEIJING)){
//                        orgType = NcbsConstant.ORG_TYPE_TWO;
//                    }
                    String operationName = operationDefineMapper.getTherapyOperationByCode(surgicalCode,orgType);
                    prpLAcciPersonDto.setOperationCode(surgicalCode);
                    prpLAcciPersonDto.setOperationName(operationName);
                }
            } else {
                prpLAcciPersonDto.setDiseaseCode("");
                prpLAcciPersonDto.setDiseaseName("");
            }
            prpLAcciPersonDto.setInHospDate(null);
            prpLAcciPersonDto.setOutHospDate(null);
//            prpLAcciPersonDto.setLinkAddressProvince("");
//            prpLAcciPersonDto.setLinkAddressCity("");
//            prpLAcciPersonDto.setLinkAddressArea("");
            //  目前新系统没有联系人省市区地址
            prpLAcciPersonDto.setLinkAddressProvince(reportAccident.getProvinceCode());
            prpLAcciPersonDto.setLinkAddressCity(reportAccident.getAccidentCityCode());
            prpLAcciPersonDto.setLinkAddressArea(reportAccident.getAccidentCountyCode());
            prpLAcciPersonDto.setBirthday(customerInfo.getBirthday());
            prpLAcciPersonDto.setJtDiseaseCode(null);
            prpLAcciPersonDto.setJtDiseaseName(null);
            prpLAcciPersonDto.setUpdateTime(new Date());
            prpLAcciPersonDtoList.add(prpLAcciPersonDto);
            i++;
        }

        return prpLAcciPersonDtoList;
    }

    /**
     * 自然人证件类型，和第三方系统不对应的类型
     * 户口薄	02===13
     * 护照	03===02
     * 军官证	04===12
     * 军人/武警身份证	08=======11
     * 外国护照	09=======14
     * 港澳台通行证	41=======04
     * 港澳台居民居住证	550======09
     * 外国人永久居留身份证	553======08
     * @param identifyType
     * @return
     */
    @Override
    public String transZRRIdentifyType (String identifyType) {
        if (identifyType== null){
            return "01";
        }
        switch (identifyType) {
            case "13":
                identifyType = "02";
                break;
            case "02":
                identifyType = "03";
                break;
            case "12":
                identifyType = "04";
                break;
            case "11":
                identifyType = "08";
                break;
            case "14":
                identifyType = "09";
                break;
            case "04":
                identifyType = "41";
                break;
            case "09":
                identifyType = "550";
                break;
            case "08":
                identifyType = "553";
                break;
            case "10":
                identifyType = "511";
                break;
            case "95":
                identifyType = "99";
                break;
            default:
        }
        return identifyType;
    }


}
