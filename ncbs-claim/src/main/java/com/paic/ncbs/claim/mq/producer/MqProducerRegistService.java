package com.paic.ncbs.claim.mq.producer;

import com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentExEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.model.dto.report.ReportBaseInfoResData;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLAcciPersonDto;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.vo.duty.PeopleHurtVO;

import java.util.List;

/**
 *      报案环节
 */
public interface MqProducerRegistService {
    /**
     * 通过mq同步报案环节信息
     * @param reportNo
     */
   void syncRegistLink(String reportNo, Integer caseTimes);

    List<PrpLAcciPersonDto> getPrpLAcciPersonDto(String reportNo, ReportCustomerInfoEntity customerInfo, ReportAccidentExEntity reportAccidentEx, ReportAccidentEntity reportAccident, PolicyInfoDTO policyInfoDTO, ReportBaseInfoResData reportBaseInfo, PeopleHurtVO peopleHurtVO, List<CaseBaseEntity> caseBaseInfoList, String type, List<PaymentItemDTO> items);

    String transZRRIdentifyType(String identifyType);
}
