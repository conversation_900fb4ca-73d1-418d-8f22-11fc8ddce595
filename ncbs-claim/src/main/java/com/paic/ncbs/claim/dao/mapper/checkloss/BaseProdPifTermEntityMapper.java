package com.paic.ncbs.claim.dao.mapper.checkloss;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.checkloss.BaseProdPifTermEntity;
import com.paic.ncbs.claim.model.dto.checkloss.SmallTermDTO;

import java.util.List;

public interface BaseProdPifTermEntityMapper extends BaseDao<BaseProdPifTermEntity> {

    int deleteByPrimaryKey(String idProdPifTerm);

    int insert(BaseProdPifTermEntity record);

    int insertSelective(BaseProdPifTermEntity record);

    BaseProdPifTermEntity selectByPrimaryKey(String idProdPifTerm);

    int updateByPrimaryKeySelective(BaseProdPifTermEntity record);

    int updateByPrimaryKey(BaseProdPifTermEntity record);

    BaseProdPifTermEntity getDocumentGroupId(String termCode);

    List<SmallTermDTO> getTermNamesByTermCodes(List<String> termCodesList);
}