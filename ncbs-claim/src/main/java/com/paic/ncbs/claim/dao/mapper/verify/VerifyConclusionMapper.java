package com.paic.ncbs.claim.dao.mapper.verify;

import com.paic.ncbs.claim.model.dto.verify.AdditionDocSendDTO;
import com.paic.ncbs.claim.model.dto.verify.VerifyConclusionDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface VerifyConclusionMapper {

    public void saveDuty(VerifyConclusionDTO dutyDTO);

    public VerifyConclusionDTO getDuty(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes,
                                       @Param("status") String status, @Param("taskCode") String taskCode);

    public void removeDuty(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes, @Param("taskCode") String taskCode);

    void updateEffective(VerifyConclusionDTO verifyConclusionDTO);

    public void addDuty(VerifyConclusionDTO dutyDTO);

    public String getVerifyCnclusionUm(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes, @Param("taskCode") String taskCode);

    public void saveAdditionDocSendInfo(AdditionDocSendDTO additionDocSendDTO);

    public List<AdditionDocSendDTO> getAdditionDocSendInfo(@Param("reportNo") String reportNo,
														   @Param("caseTimes") int caseTimes, @Param("uploadStatus") String uploadStatus, @Param("recallFlag") String recallFlag, @Param("relationId") String relationId);

    public void deleteAdditionDocSendInfo(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes);

    public void updateUploadTime(AdditionDocSendDTO additionDocSendDTO);

    public int getDutyCaseTimesByReportNo(@Param("reportNo") String reportNo);

    public String getDutyRefuseReason(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes);

    public List<String> getSmallTypeName(@Param("smallCodeArr") String[] smallCodeArr);

    public void updateAfterOtMailSend(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    public String getVerifyCommont(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    public String getConclusionCauseDesc(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);
}
