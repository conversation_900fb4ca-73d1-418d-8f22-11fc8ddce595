package com.paic.ncbs.claim.model.vo.record;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class OperationRecordVO implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 报案号
     */
    private String reportNo;

    /**
     * 操作类型，0-操作，1-调度，2-通知
     */
    private String operationType;

    /**
     * 操作时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date operationTime;

    /**
     * 操作时间-毫秒
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8" )
    private Date operationTimeMillis;

    /**
     * 操作人
     */
    private String operationUser;

    /**
     * 主节点
     */
    private String mainNode;

    /**
     * 操作节点
     */
    private String operationNode;

    /**
     * 描述
     */
    private String description;

    /**
     * 备注
     */
    private String remark;

}