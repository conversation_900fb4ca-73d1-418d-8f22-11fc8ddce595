package com.paic.ncbs.claim.dao.mapper.communicate;

import com.paic.ncbs.claim.model.dto.communicate.CommunicateDetailDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;
import java.util.Map;


@MapperScan
public interface CommunicateDetailMapper {

    public List<CommunicateDetailDTO> getCommunicateDetailDTOListById(@Param("idAhcsCommunicateBase") String idAhcsCommunicateBase);













    public void batchInsertCommunicateDetailDTO(@Param("list") List<CommunicateDetailDTO> communicateDetailDTOList);


    public int getCommunicateDetailDTOLastDealUm(@Param("idAhcsCommunicateBase") String idAhcsCommunicateBase);


    public String getMaxCommunicateDetailById(@Param("idAhcsCommunicateBase") String idAhcsCommunicateBase);


    public void insertCommunicateDetailDTO(CommunicateDetailDTO communicateDetailDTO);


    public void updateCommunicateDetailDTO(CommunicateDetailDTO communicateDetailDTO);


    public CommunicateDetailDTO getCommunicateDetailDTOByIdAndDealUm(@Param("idAhcsCommunicateBase") String idAhcsCommunicateBase, @Param("dealUm") String dealUm);


    public void batchUpdateCommunicateDetailDao(@Param("updateCommunicateDetailDTOList") List<CommunicateDetailDTO> updateCommunicateDetailDTOList);


    public List<String> getDealUmListById(@Param("idAhcsCommunicateBase") String idAhcsCommunicateBase);


    public List<Map<String, String>> getUserCommunicateTaskData(@Param("userId") String userId, @Param("reportNo") String reportNo);


    public String getCommunicateTaskStatus(@Param("reportNo") String reportNo);


    public void updateAccepterUMInfo(@Param("senderUm") String senderUm, @Param("accepterUm") String accepterUm);


    public List<CommunicateDetailDTO> getCommunicateTaskListByUm(@Param("senderUm") String senderUm);


    public List<CommunicateDetailDTO> getCommunicateTaskListBySysDate(@Param("senderUm") String senderUm);


    public List<CommunicateDetailDTO> getCommunicateDetailDTOList(@Param("idAhcsCommunicateBase") String idAhcsCommunicateBase);


    public Integer checkInitiatorUmForDealUm(@Param("idAhcsCommunicateBase") String idAhcsCommunicateBase, @Param("userId") String userId);


    public Integer checkDealUnForInitiatorUm(@Param("idAhcsCommunicateBase") String idAhcsCommunicateBase, @Param("dealUmList") List<String> dealUmList);


    public void deleteCommunicateDetailById(@Param("deleteCommunicateBaseIdList") List<String> idAhcsCommunicateBaseList);


    public void updateAssignInfoForCommunicate(CommunicateDetailDTO communicateDetailDTO);

}
