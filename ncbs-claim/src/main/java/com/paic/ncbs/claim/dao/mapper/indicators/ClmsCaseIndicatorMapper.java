package com.paic.ncbs.claim.dao.mapper.indicators;

import com.paic.ncbs.claim.dao.entity.indicators.ClmsCaseIndicator;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.dao.entity.indicators.ClmsCaseIndicatorLog;
import com.paic.ncbs.claim.model.vo.doc.TimeOutCaseExcelDataVo;
import com.paic.ncbs.claim.model.vo.doc.TimeOutExportVo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 理赔案件时效表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
public interface ClmsCaseIndicatorMapper extends BaseMapper<ClmsCaseIndicator> {
    /**
     * 首次结案时效-非活动数据生成
     *
     * @param lastLog 上次计算成功日志
     * @param endTime 本次计算截止时间
     * @return 影响行数
     */
    int stable4firstEndCase(@Param("lastLog") ClmsCaseIndicatorLog lastLog, @Param("endTime") LocalDateTime endTime);

    /**
     * 首次结案时效-活动数据生成
     *
     * @param lastLog 上次计算成功日志
     * @param endTime 本次计算截止时间
     * @return 影响行数
     */
    int unstable4firstEndCase(@Param("lastLog") ClmsCaseIndicatorLog lastLog, @Param("endTime") LocalDateTime endTime);

    /**
     * 立案时效-非活动数据生成
     *
     * @param lastLog 上次计算成功日志
     * @param endTime 本次计算截止时间
     * @return 影响行数
     */
    int stable4claimRegistration(@Param("lastLog") ClmsCaseIndicatorLog lastLog, @Param("endTime") LocalDateTime endTime);

    /**
     * 立案时效-活动数据生成
     *
     * @param lastLog 上次计算成功日志
     * @param endTime 本次计算截止时间
     * @return 影响行数
     */
    int unstable4claimRegistration(@Param("lastLog") ClmsCaseIndicatorLog lastLog, @Param("endTime") LocalDateTime endTime);

    /**
     * 重开结案时效
     *
     * @param lastLog 上次计算成功日志
     * @param endTime 本次计算截止时间
     * @return 影响行数
     */
    int reopenEndCase(@Param("lastLog") ClmsCaseIndicatorLog lastLog, @Param("endTime") LocalDateTime endTime);

    /**
     * 公估调查时效-非活动数据生成
     *
     * @param lastLog 上次计算成功日志
     * @param endTime 本次计算截止时间
     * @return 影响行数
     */
    int stable4outInvestigate(@Param("lastLog") ClmsCaseIndicatorLog lastLog, @Param("endTime") LocalDateTime endTime);

    /**
     * 巩固调查时效-活动数据生成
     *
     * @param lastLog 上次计算成功日志
     * @param endTime 本次计算截止时间
     * @return 影响行数
     */
    int unstable4outInvestigate(@Param("lastLog") ClmsCaseIndicatorLog lastLog, @Param("endTime") LocalDateTime endTime);

    /**
     * 沟通时效-非活动数据生成
     *
     * @param lastLog 上次计算成功日志
     * @param endTime 本次计算截止时间
     * @return 影响行数
     */
    int stable4communicate(@Param("lastLog") ClmsCaseIndicatorLog lastLog, @Param("endTime") LocalDateTime endTime);

    /**
     * 沟通时效-活动数据生成
     *
     * @param lastLog 上次计算成功日志
     * @param endTime 本次计算截止时间
     * @return 影响行数
     */
    int unstable4communicate(@Param("lastLog") ClmsCaseIndicatorLog lastLog, @Param("endTime") LocalDateTime endTime);

    /**
     * 二核时效-非活动数据生成
     *
     * @param lastLog 上次计算成功日志
     * @param endTime 本次计算截止时间
     * @return 影响行数
     */
    int stable4secondUnderwriting(@Param("lastLog") ClmsCaseIndicatorLog lastLog, @Param("endTime") LocalDateTime endTime);

    /**
     * 二核时效-活动数据生成
     *
     * @param lastLog 上次计算成功日志
     * @param endTime 本次计算截止时间
     * @return 影响行数
     */
    int unstable4secondUnderwriting(@Param("lastLog") ClmsCaseIndicatorLog lastLog, @Param("endTime") LocalDateTime endTime);

    List<TimeOutCaseExcelDataVo> getTimeOutCaseList(TimeOutExportVo timeOutExportVo);
}
