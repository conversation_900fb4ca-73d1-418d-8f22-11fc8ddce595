package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.math.BigDecimal;
import java.util.Date;

public class ClmpPolicyEntity extends EntityDTO {
    private static final long serialVersionUID = -1100500399624312964L;

    private Date selfCardInsuredBeginTime;

    private Date selfCardInsuredEndTime;

    private Date selfCardActiveDate;

    private Date policyInputDate;

    private String endorseReasonName;

    private Date lastEndorseDate;

    private String isRenewalFlag;

    private String endorseComment;

    private String endorseNo;

    private String partnerCode;

    private String clientNo;

    private String businessSourceDetailCode;

    private String channelSourceDetailCode;

    private String rackNo;

    private String carMark;

    private String isNameApply;

    private String idClmpPolicy;

    private String policyNo;

    private String selfCardNo;

    private String reportNo;

    private String departmentCode;

    private String insuredName;

    private String insuredAddress;

    private Date insuredBeginTime;

    private Date insuredEndTime;

    private String coinsuranceMark;

    private String coinsuranceStatus;

    private BigDecimal nSumPrm;

    private String premiumCurrency;

    private String productClassCode;

    private String productCode;

    private String productName;

    private String insruanceApplicantCode;

    private String insuranceAppName;

    private String saleName;

    private String saleCode;

    private String cedingTypeCode;

    private BigDecimal insuredValue;

    private String insuredCode;

    private String assumpsit;

    private String benefitCode;

    private String benefitName;

    private String clientType;

    private String vipCode;

    private String channelSourceCode;

    private String policyType;

    private BigDecimal coverage;

    private String migrateFrom = "np";

    private String coverageCurrency;

    private String insuredPersonnelType;

    private String targetAddress;

    private String businessType;

    private Date ascendBeginTime;

    private String projectName;

    private String projectAddress;

    private Date buildBeginDate;

    private Date buildEndDate;

    private Date promiseBeginDate;

    private Date promiseEndDate;

    private String oneIndustry;

    private String twoIndustry;

    private String threeIndustry;

    private String fourIndustry;

    private String oneBuildIndustry;

    private String twoBuildIndustry;

    private String threeBuildIndustry;

    private String fourBuildIndustry;

    private String fiveBuildIndustry;

    private String insuranceAppType;

    private String insuranceAppcertType;

    private String insuranceAppcertNo;

    private String systemId;

    private String endorseSystemId;

    private String isMainHandler;

    private BigDecimal mainLimitAmount;

    private String limitCurrencyCode;

    private String insuredType;

    private BigDecimal acceptInsuranceProportion;

    private String businessSourceCode;

    private String isRenewal;

    private String fiveIndustry;

    private Date inputDate;

    private String orderNo;

    private BigDecimal waitPeriod;

    private String rimsControl;

    private String electronicPolicyNo;

    private String applyApproach;

    private String customerTypeCode;

    private String customerCode;

    private String customerName;

    private String policyBatchNo;

    private String checkCode;

    private String agentBrokerCode;

    private String agentBrokerType;

    private String agentBrokerName;

    private BigDecimal retentionPortion;

    private String retentionPortionType;

    private String clauseCode;

    private String isBinder;

    private String isGiftInsurance;

    private String noticePeriod;

    private String primaryIntroducerCode;

    private String channelSourceName;

    private String schemeName;

    private String projName;

    public String getAgentBrokerCode() {
        return agentBrokerCode;
    }

    public void setAgentBrokerCode(String agentBrokerCode) {
        this.agentBrokerCode = agentBrokerCode;
    }

    public String getAgentBrokerType() {
        return agentBrokerType;
    }

    public void setAgentBrokerType(String agentBrokerType) {
        this.agentBrokerType = agentBrokerType;
    }

    public String getAgentBrokerName() {
        return agentBrokerName;
    }

    public void setAgentBrokerName(String agentBrokerName) {
        this.agentBrokerName = agentBrokerName;
    }

    public String getCheckCode() {
        return checkCode;
    }

    public void setCheckCode(String checkCode) {
        this.checkCode = checkCode;
    }

    public String getIdClmpPolicy() {
        return idClmpPolicy;
    }

    public void setIdClmpPolicy(String idClmpPolicy) {
        this.idClmpPolicy = idClmpPolicy == null ? null : idClmpPolicy.trim();
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo == null ? null : policyNo.trim();
    }

    public String getSelfCardNo() {
        return selfCardNo;
    }

    public void setSelfCardNo(String selfCardNo) {
        this.selfCardNo = selfCardNo == null ? null : selfCardNo.trim();
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode == null ? null : departmentCode.trim();
    }

    public String getInsuredName() {
        return insuredName;
    }

    public void setInsuredName(String insuredName) {
        this.insuredName = insuredName == null ? null : insuredName.trim();
    }

    public String getInsuredAddress() {
        return insuredAddress;
    }

    public void setInsuredAddress(String insuredAddress) {
        this.insuredAddress = insuredAddress == null ? null : insuredAddress.trim();
    }

    public Date getInsuredBeginTime() {
        return insuredBeginTime;
    }

    public void setInsuredBeginTime(Date insuredBeginTime) {
        this.insuredBeginTime = insuredBeginTime;
    }

    public Date getInsuredEndTime() {
        return insuredEndTime;
    }

    public void setInsuredEndTime(Date insuredEndTime) {
        this.insuredEndTime = insuredEndTime;
    }

    public String getCoinsuranceMark() {
        return coinsuranceMark;
    }

    public void setCoinsuranceMark(String coinsuranceMark) {
        this.coinsuranceMark = coinsuranceMark == null ? null : coinsuranceMark.trim();
    }

    public String getCoinsuranceStatus() {
        return coinsuranceStatus;
    }

    public void setCoinsuranceStatus(String coinsuranceStatus) {
        this.coinsuranceStatus = coinsuranceStatus == null ? null : coinsuranceStatus.trim();
    }

    public BigDecimal getnSumPrm() {
        return nSumPrm;
    }

    public void setnSumPrm(BigDecimal nSumPrm) {
        this.nSumPrm = nSumPrm;
    }

    public String getPremiumCurrency() {
        return premiumCurrency;
    }

    public void setPremiumCurrency(String premiumCurrency) {
        this.premiumCurrency = premiumCurrency == null ? null : premiumCurrency.trim();
    }

    public String getProductClassCode() {
        return productClassCode;
    }

    public void setProductClassCode(String productClassCode) {
        this.productClassCode = productClassCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode == null ? null : productCode.trim();
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    public String getInsruanceApplicantCode() {
        return insruanceApplicantCode;
    }

    public void setInsruanceApplicantCode(String insruanceApplicantCode) {
        this.insruanceApplicantCode = insruanceApplicantCode == null ? null : insruanceApplicantCode.trim();
    }

    public String getInsuranceAppName() {
        return insuranceAppName;
    }

    public void setInsuranceAppName(String insuranceAppName) {
        this.insuranceAppName = insuranceAppName == null ? null : insuranceAppName.trim();
    }

    public String getSaleName() {
        return saleName;
    }

    public void setSaleName(String saleName) {
        this.saleName = saleName == null ? null : saleName.trim();
    }

    public String getSaleCode() {
        return saleCode;
    }

    public void setSaleCode(String saleCode) {
        this.saleCode = saleCode == null ? null : saleCode.trim();
    }

    public String getCedingTypeCode() {
        return cedingTypeCode;
    }

    public void setCedingTypeCode(String cedingTypeCode) {
        this.cedingTypeCode = cedingTypeCode == null ? null : cedingTypeCode.trim();
    }

    public BigDecimal getInsuredValue() {
        return insuredValue;
    }

    public void setInsuredValue(BigDecimal insuredValue) {
        this.insuredValue = insuredValue;
    }

    public String getInsuredCode() {
        return insuredCode;
    }

    public void setInsuredCode(String insuredCode) {
        this.insuredCode = insuredCode == null ? null : insuredCode.trim();
    }

    public String getAssumpsit() {
        return assumpsit;
    }

    public void setAssumpsit(String assumpsit) {
        this.assumpsit = assumpsit == null ? null : assumpsit.trim();
    }

    public String getBenefitCode() {
        return benefitCode;
    }

    public void setBenefitCode(String benefitCode) {
        this.benefitCode = benefitCode == null ? null : benefitCode.trim();
    }

    public String getBenefitName() {
        return benefitName;
    }

    public void setBenefitName(String benefitName) {
        this.benefitName = benefitName == null ? null : benefitName.trim();
    }

    public String getClientType() {
        return clientType;
    }

    public void setClientType(String clientType) {
        this.clientType = clientType == null ? null : clientType.trim();
    }

    public String getVipCode() {
        return vipCode;
    }

    public void setVipCode(String vipCode) {
        this.vipCode = vipCode == null ? null : vipCode.trim();
    }

    public String getChannelSourceCode() {
        return channelSourceCode;
    }

    public void setChannelSourceCode(String channelSourceCode) {
        this.channelSourceCode = channelSourceCode == null ? null : channelSourceCode.trim();
    }

    public String getPolicyType() {
        return policyType;
    }

    public void setPolicyType(String policyType) {
        this.policyType = policyType == null ? null : policyType.trim();
    }

    public BigDecimal getCoverage() {
        return coverage;
    }

    public void setCoverage(BigDecimal coverage) {
        this.coverage = coverage;
    }

    public String getMigrateFrom() {
        return migrateFrom;
    }

    public void setMigrateFrom(String migrateFrom) {
        this.migrateFrom = migrateFrom == null ? null : migrateFrom.trim();
    }

    public String getCoverageCurrency() {
        return coverageCurrency;
    }

    public void setCoverageCurrency(String coverageCurrency) {
        this.coverageCurrency = coverageCurrency == null ? null : coverageCurrency.trim();
    }

    public String getInsuredPersonnelType() {
        return insuredPersonnelType;
    }

    public void setInsuredPersonnelType(String insuredPersonnelType) {
        this.insuredPersonnelType = insuredPersonnelType == null ? null : insuredPersonnelType.trim();
    }

    public String getTargetAddress() {
        return targetAddress;
    }

    public void setTargetAddress(String targetAddress) {
        this.targetAddress = targetAddress == null ? null : targetAddress.trim();
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType == null ? null : businessType.trim();
    }

    public Date getAscendBeginTime() {
        return ascendBeginTime;
    }

    public void setAscendBeginTime(Date ascendBeginTime) {
        this.ascendBeginTime = ascendBeginTime;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getProjectAddress() {
        return projectAddress;
    }

    public void setProjectAddress(String projectAddress) {
        this.projectAddress = projectAddress == null ? null : projectAddress.trim();
    }

    public Date getBuildBeginDate() {
        return buildBeginDate;
    }

    public void setBuildBeginDate(Date buildBeginDate) {
        this.buildBeginDate = buildBeginDate;
    }

    public Date getBuildEndDate() {
        return buildEndDate;
    }

    public void setBuildEndDate(Date buildEndDate) {
        this.buildEndDate = buildEndDate;
    }

    public Date getPromiseBeginDate() {
        return promiseBeginDate;
    }

    public void setPromiseBeginDate(Date promiseBeginDate) {
        this.promiseBeginDate = promiseBeginDate;
    }

    public Date getPromiseEndDate() {
        return promiseEndDate;
    }

    public void setPromiseEndDate(Date promiseEndDate) {
        this.promiseEndDate = promiseEndDate;
    }

    public String getOneIndustry() {
        return oneIndustry;
    }

    public void setOneIndustry(String oneIndustry) {
        this.oneIndustry = oneIndustry == null ? null : oneIndustry.trim();
    }

    public String getTwoIndustry() {
        return twoIndustry;
    }

    public void setTwoIndustry(String twoIndustry) {
        this.twoIndustry = twoIndustry == null ? null : twoIndustry.trim();
    }

    public String getThreeIndustry() {
        return threeIndustry;
    }

    public void setThreeIndustry(String threeIndustry) {
        this.threeIndustry = threeIndustry == null ? null : threeIndustry.trim();
    }

    public String getFourIndustry() {
        return fourIndustry;
    }

    public void setFourIndustry(String fourIndustry) {
        this.fourIndustry = fourIndustry == null ? null : fourIndustry.trim();
    }

    public String getInsuranceAppType() {
        return insuranceAppType;
    }

    public void setInsuranceAppType(String insuranceAppType) {
        this.insuranceAppType = insuranceAppType == null ? null : insuranceAppType.trim();
    }

    public String getInsuranceAppcertType() {
        return insuranceAppcertType;
    }

    public void setInsuranceAppcertType(String insuranceAppcertType) {
        this.insuranceAppcertType = insuranceAppcertType == null ? null : insuranceAppcertType.trim();
    }

    public String getInsuranceAppcertNo() {
        return insuranceAppcertNo;
    }

    public void setInsuranceAppcertNo(String insuranceAppcertNo) {
        this.insuranceAppcertNo = insuranceAppcertNo == null ? null : insuranceAppcertNo.trim();
    }

    public String getSystemId() {
        return systemId;
    }

    public void setSystemId(String systemId) {
        this.systemId = systemId == null ? null : systemId.trim();
    }

    public String getEndorseSystemId() {
        return endorseSystemId;
    }

    public void setEndorseSystemId(String endorseSystemId) {
        this.endorseSystemId = endorseSystemId == null ? null : endorseSystemId.trim();
    }

    public String getIsMainHandler() {
        return isMainHandler;
    }

    public void setIsMainHandler(String isMainHandler) {
        this.isMainHandler = isMainHandler == null ? null : isMainHandler.trim();
    }

    public BigDecimal getMainLimitAmount() {
        return mainLimitAmount;
    }

    public void setMainLimitAmount(BigDecimal mainLimitAmount) {
        this.mainLimitAmount = mainLimitAmount;
    }

    public String getLimitCurrencyCode() {
        return limitCurrencyCode;
    }

    public void setLimitCurrencyCode(String limitCurrencyCode) {
        this.limitCurrencyCode = limitCurrencyCode == null ? null : limitCurrencyCode.trim();
    }

    public String getInsuredType() {
        return insuredType;
    }

    public void setInsuredType(String insuredType) {
        this.insuredType = insuredType == null ? null : insuredType.trim();
    }

    public BigDecimal getAcceptInsuranceProportion() {
        return acceptInsuranceProportion;
    }

    public void setAcceptInsuranceProportion(BigDecimal acceptInsuranceProportion) {
        this.acceptInsuranceProportion = acceptInsuranceProportion;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode == null ? null : businessSourceCode.trim();
    }

    public String getIsRenewal() {
        return isRenewal;
    }

    public void setIsRenewal(String isRenewal) {
        this.isRenewal = isRenewal == null ? null : isRenewal.trim();
    }

    public String getFiveIndustry() {
        return fiveIndustry;
    }

    public void setFiveIndustry(String fiveIndustry) {
        this.fiveIndustry = fiveIndustry == null ? null : fiveIndustry.trim();
    }

    public Date getInputDate() {
        return inputDate;
    }

    public void setInputDate(Date inputDate) {
        this.inputDate = inputDate;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    public BigDecimal getWaitPeriod() {
        return waitPeriod;
    }

    public void setWaitPeriod(BigDecimal waitPeriod) {
        this.waitPeriod = waitPeriod;
    }

    public String getRimsControl() {
        return rimsControl;
    }

    public void setRimsControl(String rimsControl) {
        this.rimsControl = rimsControl == null ? null : rimsControl.trim();
    }

    public String getElectronicPolicyNo() {
        return electronicPolicyNo;
    }

    public void setElectronicPolicyNo(String electronicPolicyNo) {
        this.electronicPolicyNo = electronicPolicyNo == null ? null : electronicPolicyNo.trim();
    }

    public String getApplyApproach() {
        return applyApproach;
    }

    public void setApplyApproach(String applyApproach) {
        this.applyApproach = applyApproach == null ? null : applyApproach.trim();
    }

    public String getCustomerTypeCode() {
        return customerTypeCode;
    }

    public void setCustomerTypeCode(String customerTypeCode) {
        this.customerTypeCode = customerTypeCode == null ? null : customerTypeCode.trim();
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode == null ? null : customerCode.trim();
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    public String getPolicyBatchNo() {
        return policyBatchNo;
    }

    public void setPolicyBatchNo(String policyBatchNo) {
        this.policyBatchNo = policyBatchNo == null ? null : policyBatchNo.trim();
    }

    public String getClientNo() {
        return clientNo;
    }

    public void setClientNo(String clientNo) {
        this.clientNo = clientNo;
    }

    public String getBusinessSourceDetailCode() {
        return businessSourceDetailCode;
    }

    public void setBusinessSourceDetailCode(String businessSourceDetailCode) {
        this.businessSourceDetailCode = businessSourceDetailCode;
    }

    public String getChannelSourceDetailCode() {
        return channelSourceDetailCode;
    }

    public void setChannelSourceDetailCode(String channelSourceDetailCode) {
        this.channelSourceDetailCode = channelSourceDetailCode;
    }

    public String getRackNo() {
        return rackNo;
    }

    public void setRackNo(String rackNo) {
        this.rackNo = rackNo;
    }

    public String getCarMark() {
        return carMark;
    }

    public void setCarMark(String carMark) {
        this.carMark = carMark;
    }

    public String getOneBuildIndustry() {
        return oneBuildIndustry;
    }

    public void setOneBuildIndustry(String oneBuildIndustry) {
        this.oneBuildIndustry = oneBuildIndustry;
    }

    public String getTwoBuildIndustry() {
        return twoBuildIndustry;
    }

    public void setTwoBuildIndustry(String twoBuildIndustry) {
        this.twoBuildIndustry = twoBuildIndustry;
    }

    public String getThreeBuildIndustry() {
        return threeBuildIndustry;
    }

    public void setThreeBuildIndustry(String threeBuildIndustry) {
        this.threeBuildIndustry = threeBuildIndustry;
    }

    public String getFourBuildIndustry() {
        return fourBuildIndustry;
    }

    public void setFourBuildIndustry(String fourBuildIndustry) {
        this.fourBuildIndustry = fourBuildIndustry;
    }

    public String getFiveBuildIndustry() {
        return fiveBuildIndustry;
    }

    public void setFiveBuildIndustry(String fiveBuildIndustry) {
        this.fiveBuildIndustry = fiveBuildIndustry;
    }

    public String getIsNameApply() {
        return isNameApply;
    }

    public void setIsNameApply(String isNameApply) {
        this.isNameApply = isNameApply;
    }

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }

    public Date getSelfCardInsuredBeginTime() {
        return selfCardInsuredBeginTime;
    }

    public void setSelfCardInsuredBeginTime(Date selfCardInsuredBeginTime) {
        this.selfCardInsuredBeginTime = selfCardInsuredBeginTime;
    }

    public Date getSelfCardInsuredEndTime() {
        return selfCardInsuredEndTime;
    }

    public void setSelfCardInsuredEndTime(Date selfCardInsuredEndTime) {
        this.selfCardInsuredEndTime = selfCardInsuredEndTime;
    }

    public Date getSelfCardActiveDate() {
        return selfCardActiveDate;
    }

    public void setSelfCardActiveDate(Date selfCardActiveDate) {
        this.selfCardActiveDate = selfCardActiveDate;
    }

    public Date getPolicyInputDate() {
        return policyInputDate;
    }

    public void setPolicyInputDate(Date policyInputDate) {
        this.policyInputDate = policyInputDate;
    }

    public String getEndorseReasonName() {
        return endorseReasonName;
    }

    public void setEndorseReasonName(String endorseReasonName) {
        this.endorseReasonName = endorseReasonName;
    }

    public Date getLastEndorseDate() {
        return lastEndorseDate;
    }

    public void setLastEndorseDate(Date lastEndorseDate) {
        this.lastEndorseDate = lastEndorseDate;
    }

    public String getIsRenewalFlag() {
        return isRenewalFlag;
    }

    public void setIsRenewalFlag(String isRenewalFlag) {
        this.isRenewalFlag = isRenewalFlag;
    }

    public String getEndorseComment() {
        return endorseComment;
    }

    public void setEndorseComment(String endorseComment) {
        this.endorseComment = endorseComment;
    }

    public BigDecimal getRetentionPortion() {
        return retentionPortion;
    }

    public void setRetentionPortion(BigDecimal retentionPortion) {
        this.retentionPortion = retentionPortion;
    }

    public String getRetentionPortionType() {
        return retentionPortionType;
    }

    public void setRetentionPortionType(String retentionPortionType) {
        this.retentionPortionType = retentionPortionType;
    }

    public String getEndorseNo() {
        return endorseNo;
    }

    public void setEndorseNo(String endorseNo) {
        this.endorseNo = endorseNo;
    }

    public String getClauseCode() {
        return clauseCode;
    }

    public void setClauseCode(String clauseCode) {
        this.clauseCode = clauseCode;
    }

    public String getIsBinder() {
        return isBinder;
    }

    public void setIsBinder(String isBinder) {
        this.isBinder = isBinder;
    }

    public String getIsGiftInsurance() {
        return isGiftInsurance;
    }

    public void setIsGiftInsurance(String isGiftInsurance) {
        this.isGiftInsurance = isGiftInsurance;
    }

    public String getNoticePeriod() {
        return noticePeriod;
    }

    public void setNoticePeriod(String noticePeriod) {
        this.noticePeriod = noticePeriod;
    }

    public String getPrimaryIntroducerCode() {
        return primaryIntroducerCode;
    }

    public void setPrimaryIntroducerCode(String primaryIntroducerCode) {
        this.primaryIntroducerCode = primaryIntroducerCode;
    }

    public String getChannelSourceName() {
        return channelSourceName;
    }

    public void setChannelSourceName(String channelSourceName) {
        this.channelSourceName = channelSourceName;
    }

    public String getSchemeName() {
        return schemeName;
    }

    public void setSchemeName(String schemeName) {
        this.schemeName = schemeName;
    }

    public String getProjName() {
        return projName;
    }

    public void setProjName(String projName) {
        this.projName = projName;
    }
}