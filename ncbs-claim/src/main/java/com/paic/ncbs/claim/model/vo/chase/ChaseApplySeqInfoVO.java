package com.paic.ncbs.claim.model.vo.chase;



import com.paic.ncbs.claim.model.dto.chase.ChaseApplySeqDTO;

import java.math.BigDecimal;
import java.util.List;

public class ChaseApplySeqInfoVO {
	

	private String chaseType;
	

	private BigDecimal chaseApplyPaySum;


	private BigDecimal chaseApplyFeeSum;


	private BigDecimal chaseApplySum;
	

	private List<ChaseApplySeqDTO> chaseApplySeqs;

	public String getChaseType() {
		return chaseType;
	}

	public void setChaseType(String chaseType) {
		this.chaseType = chaseType;
	}

	public List<ChaseApplySeqDTO> getChaseApplySeqs() {
		return chaseApplySeqs;
	}

	public void setChaseApplySeqs(List<ChaseApplySeqDTO> chaseApplySeqs) {
		this.chaseApplySeqs = chaseApplySeqs;
	}

	public BigDecimal getChaseApplyPaySum() {
		return chaseApplyPaySum;
	}

	public void setChaseApplyPaySum(BigDecimal chaseApplyPaySum) {
		this.chaseApplyPaySum = chaseApplyPaySum;
	}

	public BigDecimal getChaseApplyFeeSum() {
		return chaseApplyFeeSum;
	}

	public void setChaseApplyFeeSum(BigDecimal chaseApplyFeeSum) {
		this.chaseApplyFeeSum = chaseApplyFeeSum;
	}

	public BigDecimal getChaseApplySum() {
		return chaseApplySum;
	}

	public void setChaseApplySum(BigDecimal chaseApplySum) {
		this.chaseApplySum = chaseApplySum;
	}
	
}
