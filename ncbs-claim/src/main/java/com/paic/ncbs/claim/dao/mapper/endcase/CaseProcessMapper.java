package com.paic.ncbs.claim.dao.mapper.endcase;

import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import com.paic.ncbs.claim.model.vo.endcase.CaseLoadVO;
import com.paic.ncbs.claim.model.vo.endcase.WorkLoadQueryVO;
import com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.Date;
import java.util.List;

@MapperScan
public interface CaseProcessMapper {

    void addCaseProcess(CaseProcessDTO caseProcessDTO);

    int getCaseProcess(CaseProcessDTO caseProcessDTO);

    int getCheckLossCaseProcess(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    CaseProcessDTO getCaseProcessDTO(CaseProcessDTO caseProcess);

    CaseProcessDTO caseProcessDTOCondition(CaseProcessDTO caseProcess);

    void updateCaseProcessByReportNo(CaseProcessDTO caseProcessDTO);

     CaseProcessDTO getCompanyCodeAndName(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

     CaseProcessDTO getCaseCompanyCode(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

     CaseProcessDTO getCaseCommissionInfo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

     // 查询重开案件列表
    List<CaseProcessDTO> getCaseByReportNo(@Param("reportNo") String reportNo);

     void addCaseProcessDTO(CaseProcessDTO caseProcessDTO);

     void updateRegisterDept(CaseProcessDTO caseProcessDTO);

     String getCaseProcessStatus(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

     List<CaseProcessDTO> getAllEndCase();

     List<CaseProcessDTO> getEndCaseNoPriv();

     void updatePrivilegeGroupName(CaseProcessDTO caseProcessDTO);

     Date getReportDate(@Param("reportNo") String reportNo);

     Date getArchiveTimeByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

     CaseProcessDTO getReportProcessStatus(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

     CaseProcessDTO getCaseProcessStatusMc(@Param("reportNo") String reportNo, @Param("businessKey") String businessKey, @Param("taskDefinitionKey") String taskDefinitionKey);

     List<CaseProcessDTO> getFixCaseList(@Param("batchNo") String batchNo);

     void batchModifyCaseProcessDTO(@Param("paramList") List<CaseProcessDTO> caseProcessList);

     List<CaseProcessDTO> getHisCaseList(@Param("batchNo") String batchNo);

     void updateFixHistoryData(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("batchNo") String batchNo);

    List<CaseProcessDTO> getListByReportNoAndCaseTimesList(List<CaseProcessDTO> reportNoAndCaseTimesList);

    void updateClaimCaseTypeById(@Param("idAhcsCaseProcess") String idAhcsCaseProcess, @Param("claimCaseType") String claimCaseType);

    List<CaseLoadVO> requestAllCase(WorkLoadQueryVO workLoadQuery);

     String getCompanyCodeByReportNo(@Param("reportNo") String reportNo);

     String getIsNewProcess(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    void updateClaimCaseType(CaseProcessDTO caseProcessDTO);

    String getTrialDepartment(@Param("deptCode") String deptCode);

    List<String> getAllTrialDepartment(List<String> deptCodes);


    Date getEndCaseDateByCaseProcess(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    void updateCaseStatus(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,
                          @Param("caseStatus") String caseStatus);

    Date queryArchiveTimeByReportNoAndCaseTimes(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);

    String getCaseProcessStatusNew(String reportNo);

    /**
     * 根据报案号，赔付次数 查询案件流程状态
     * @param reportNo
     * @param caseTimes
     * @return
     */
    CaseProcessDTO getCaseProcessInfo(String reportNo, Integer caseTimes);
}
