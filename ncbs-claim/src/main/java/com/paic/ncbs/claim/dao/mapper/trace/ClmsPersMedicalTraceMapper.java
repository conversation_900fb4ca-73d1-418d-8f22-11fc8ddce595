package com.paic.ncbs.claim.dao.mapper.trace;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.trace.ClmsPersMedicalTraceDTO;
import com.paic.ncbs.claim.model.vo.trace.ClmsPersMedicalTraceVO;
import com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 *
 * 表clms_pers_medical_trace对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@MapperScan
public interface ClmsPersMedicalTraceMapper extends BaseDao<ClmsPersMedicalTraceDTO> {
    /**
     * 人伤跟踪医疗记录信息
     * @param personTranceRequestVo
     * @return
     */
    public List<ClmsPersMedicalTraceVO> selectClmsPersMedicalTrace(PersonTranceRequestVo personTranceRequestVo);

    /**
     * 删除
     * @param personTranceRequestVo
     */
    public void deleteClmsPersMedicalTrace(PersonTranceRequestVo personTranceRequestVo);


}