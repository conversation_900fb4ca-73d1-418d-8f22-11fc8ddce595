package com.paic.ncbs.claim.dao.entity.rule;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.io.Serializable;

/**
    * 理赔自核规则明细记录表
    */
public class AutoRuleDetailInfoEntity extends EntityDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
    private String idAutoRuleInfo;

    /**
    * 报案号
    */
    private String reportNo;

    /**
    * 赔付次数
    */
    private Integer caseTimes;

    /**
    * 序号
    */
    private Integer serialNo;

    /**
    * 关联主表主键
    */
    private String idAutoRuleMain;

    /**
    * 发票号
    */
    private String billNo;

    /**
    * 批次号
    */
    private Integer batchNo;

    /**
    * 规则结果代码
    */
    private String ruleCode;

    /**
    * 规则结果描述
    */
    private String ruleMessage;

    public String getIdAutoRuleInfo() {
        return idAutoRuleInfo;
    }

    public void setIdAutoRuleInfo(String idAutoRuleInfo) {
        this.idAutoRuleInfo = idAutoRuleInfo;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public Integer getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Integer caseTimes) {
        this.caseTimes = caseTimes;
    }

    public Integer getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(Integer serialNo) {
        this.serialNo = serialNo;
    }

    public String getIdAutoRuleMain() {
        return idAutoRuleMain;
    }

    public void setIdAutoRuleMain(String idAutoRuleMain) {
        this.idAutoRuleMain = idAutoRuleMain;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public Integer getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(Integer batchNo) {
        this.batchNo = batchNo;
    }

    public String getRuleCode() {
        return ruleCode;
    }

    public void setRuleCode(String ruleCode) {
        this.ruleCode = ruleCode;
    }

    public String getRuleMessage() {
        return ruleMessage;
    }

    public void setRuleMessage(String ruleMessage) {
        this.ruleMessage = ruleMessage;
    }
}