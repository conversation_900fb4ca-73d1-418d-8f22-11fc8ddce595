package com.paic.ncbs.claim.dao.entity.report;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PolicyAbstractEntity {

    private List<PolicyInfo> policyInfoList;
    private String clientNo;
    private String insurantName;

    private String clientTypeDesc;

    private String clientAttribute;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PolicyInfo {
        private String policyNo;
        private String selfCardNo;
        private String elecSubPolicyNo;
        private String productCode;
        private String policyBussinessType;
        private String policyType;
        private String productClass;
        private String departmentCode;
        private String insurantName;
        private String applyApproach;
        private List<InsurantInfo> insurantInfoList;
        private List<Plan> planInfoList;
        private List<RiskVehicleInfo> riskVehicleInfoList;
        private List<CombinedProduct> combinedProductList;

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Plan {
            private String planCode;
            private String planName;
        }

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class InsurantInfo {
            private String personnelCode;
            private String name;
            private String clientNo;
            private String clientType;
            private String certificateNo;
            private String certificateType;
        }

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class RiskVehicleInfo {

            private String vehicleLicenceCode;

            private String vehicleFrameNo;
        }

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class CombinedProduct {
            private String combinedProductName;
            private String combinedProductVersion;
            private String combinedProductCode;
        }
    }

}
