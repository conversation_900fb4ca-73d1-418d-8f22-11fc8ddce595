package com.paic.ncbs.claim.mq.producer.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.report.*;
import com.paic.ncbs.claim.model.dto.report.ReportBaseInfoResData;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.NcbsConstant;
import com.paic.ncbs.claim.common.enums.ReportModeEnum;
import com.paic.ncbs.claim.common.util.CodeUtil;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.dao.entity.report.*;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoExMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.mq.EndCaseDto;
import com.paic.ncbs.claim.model.dto.mq.claim.ClaimEndCaseReqDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLCaseNoDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLClaimDto;
import com.paic.ncbs.claim.model.dto.mq.swf.SwfLogStoreDto;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.mq.producer.MqProducerClaimCancelService;
import com.paic.ncbs.claim.mq.producer.MqProducerClaimService;
import com.paic.ncbs.claim.mq.producer.MqProducerEndcaseService;
import com.paic.ncbs.claim.service.endcase.CaseBaseService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.endcase.WholeCaseService;
import com.paic.ncbs.claim.service.report.ReportAccidentExService;
import com.paic.ncbs.claim.service.report.ReportAccidentService;
import com.paic.ncbs.claim.service.report.ReportCustomerInfoService;
import com.paic.ncbs.claim.service.report.ReportInfoService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
//import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class MqProducerEndcaseServiceImpl implements MqProducerEndcaseService {
    @Value("${mq.rabbit.exchange.ncbs.claim.root:ncbsClaimExchange}")
    private String ncbsClaimExchange;
    //结案环节topic
    @Value("${mq.endcase.topic}")
    private String endcaseTopic;
    @Autowired
    private ReportCustomerInfoService reportCustomerInfoService;
    @Autowired
    private ReportAccidentService reportAccidentService;
    @Autowired
    private ReportAccidentExService reportAccidentExService;
    @Autowired
    private MqProducerClaimCancelService mqProducerClaimCancelService;
    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;
    @Autowired
    private ReportInfoService reportInfoService;
    @Autowired
    private ReportInfoExMapper reportInfoExMapper;
    @Autowired
    private PolicyInfoMapper policyInfoMapper;
    @Autowired
    private CaseBaseService caseBaseService;
    /*@Autowired
    private RabbitTemplate rabbitTemplate;*/
    @Autowired
    private MqProducerClaimService mqProducerClaimService;
    @Autowired
    private OcasMapper ocasMapper;
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private DepartmentDefineMapper departmentDefineMapper;
    @Autowired
    private WholeCaseService wholeCaseService;
    @Autowired
    private PaymentItemMapper paymentItemMapper;
    @Override
    public void syncProducerEndcaseLink(String reportNo, Integer caseTimes) {
        // 数据组装
        EndCaseDto endCaseDto = initEndCaseDtoDto(reportNo,caseTimes);
        //发送MQ消息
        JSONObject jsonObj = (JSONObject) JSON.toJSON(endCaseDto);
        log.info("syncProducerEndcaseLink===sendMessage: {}",jsonObj);
       // rabbitTemplate.convertAndSend(ncbsClaimExchange, endcaseTopic, endCaseDto);
    }

    @SneakyThrows
    private EndCaseDto initEndCaseDtoDto(String reportNo, Integer caseTimes) {

        ReportInfoEntity reportInfo = reportInfoService.getReportInfo(reportNo);
        //报案方式
        reportInfo.setReportMode(ReportModeEnum.getName(reportInfo.getReportMode() == null ? "2" : reportInfo.getReportMode()));
        //查询 报案客户 被保险人 信息表
        ReportCustomerInfoEntity customerInfo = reportCustomerInfoService.getReportCustomerInfoByReportNo(reportNo);
        //查询 意键险报案信息扩展表
//        ReportAccidentExEntity reportAccidentEx = reportAccidentExService.getReportAccidentEx(reportNo);
        //查询 事故信息表
        ReportAccidentEntity reportAccident = reportAccidentService.getReportAccident(reportNo);
//        List<EstimatePolicyDTO> policyList = estimatePolicyMapper.getEstimatePolicyByHistoryList(reportNo, caseTimes);
//        List<EstimatePolicyDTO> policyList = estimatePolicyMapper.getByReportNoAndCaseTimes(reportNo, caseTimes);
        WholeCaseBaseDTO wholeCaseBaseEntity = wholeCaseBaseService.getWholeCaseBase(reportNo,caseTimes);
        List<ReportInfoExEntity> reportInfoExEntityList = reportInfoExMapper.getReportInfoEx(reportNo);
        //查询保单信息表
        List<PolicyInfoDTO> policyInfoDTOList = policyInfoMapper.getPolicyInfoListByReportNo(reportNo);
//        List<PolicyPayDTO> policyPayDTOList=policyPayMapper.selectByReportNo(reportNo, caseTimes);
//        EstimatePolicyDTO estimatePolicyDTO = policyList.get(0);
        PolicyInfoDTO policyInfoDTO = policyInfoDTOList.get(0);
        ReportInfoExEntity reportInfoExEntity = reportInfoExEntityList.get(0);
//        PolicyPayDTO policyPayDTO=policyPayDTOList.get(0);
        //查询 意键险报案信息扩展表
        ReportAccidentExEntity reportAccidentEx = reportAccidentExService.getReportAccidentEx(reportNo);
        ReportBaseInfoResData reportBaseInfo = wholeCaseService.getReportBaseInfo(reportNo, caseTimes);
        EndCaseDto endCaseDto=new EndCaseDto();

        List<PrpLCaseNoDto> prpLCaseNoDtoList = getPrpLCaseNoDto(wholeCaseBaseEntity);
        endCaseDto.setPrpLCaseNoDtoList(prpLCaseNoDtoList);
        List<SwfLogStoreDto> swfLogStoreDtoList=new ArrayList<>();
        TaskInfoDTO taskInfoDTO = taskInfoMapper.findLatestByReportNoAndBpmKey(reportNo, caseTimes, BpmConstants.OC_SETTLE_REVIEW);
        List<CaseBaseEntity> caseBaseInfoList = caseBaseService.getCaseBaseInfoByReportNoAndCasetimes(reportNo, String.valueOf(caseTimes));
        for (CaseBaseEntity caseBaseEntity:caseBaseInfoList){
            SwfLogStoreDto swfLogStoreDto=  new SwfLogStoreDto();
            swfLogStoreDto.setLogNo(5);
            swfLogStoreDto.setModelNo(12);
            swfLogStoreDto.setNodeName("结案");
            swfLogStoreDto.setBusinessNo(caseBaseEntity.getCaseNo());
            if (taskInfoDTO == null){
                // 批量结案的 没有任务信息
                swfLogStoreDto.setDeptName(departmentDefineMapper.queryDepartmentNameByDeptCode(reportInfo.getAcceptDepartmentCode()));
                swfLogStoreDto.setComCode(reportInfo.getAcceptDepartmentCode());
                swfLogStoreDto.setHandleDept(reportInfo.getAcceptDepartmentCode());
                swfLogStoreDto.setHandlerCode(NcbsConstant.ZK_ADMIN_UM);
                swfLogStoreDto.setHandlerName(NcbsConstant.ZK_ADMIN_NAME);
            } else {
                swfLogStoreDto.setDeptName(departmentDefineMapper.queryDepartmentNameByDeptCode(reportInfo.getAcceptDepartmentCode()));
                swfLogStoreDto.setComCode(reportInfo.getAcceptDepartmentCode());
                swfLogStoreDto.setHandleDept(taskInfoDTO.getDepartmentCode());
                swfLogStoreDto.setHandlerCode(taskInfoDTO.getAssigner());
                swfLogStoreDto.setHandlerName(taskInfoDTO.getAssigneeName());
            }
            swfLogStoreDto.setFlowInTime(DateUtils.parseToFormatString(new Date(),DateUtils.FULL_DATE_STR));
            swfLogStoreDto.setTimeLimit(0);
            swfLogStoreDto.setHandleTime(DateUtils.parseToFormatString(new Date(),DateUtils.FULL_DATE_STR));
            swfLogStoreDto.setSubmitTime(DateUtils.parseToFormatString(new Date(),DateUtils.FULL_DATE_STR));
            swfLogStoreDto.setPackageID("0");
            swfLogStoreDto.setTaskNo(0);
            swfLogStoreDto.setNodeType("endca");
            swfLogStoreDto.setTitleStr("");
            swfLogStoreDto.setBusinessType("1");
            Map<String, String> productMap = ocasMapper.getPlyBaseInfo(caseBaseEntity.getPolicyNo());
            String productCode = MapUtils.getString(productMap,"productCode");
            swfLogStoreDto.setRiskCode(CodeUtil.subStringRiskCode(productCode));
            swfLogStoreDto.setKeyIn(reportNo);
            swfLogStoreDto.setKeyOut(reportNo);
            swfLogStoreDto.setMainFlowID("0");
            swfLogStoreDto.setSubFlowID("0");
            swfLogStoreDto.setPosX(0);
            swfLogStoreDto.setPosY(0);
            swfLogStoreDto.setEndFlag("1");
            swfLogStoreDto.setBeforeHandlerCode("");
            swfLogStoreDto.setBeforeHandlerName("");
            swfLogStoreDto.setPolicyNo(caseBaseEntity.getPolicyNo());
            swfLogStoreDto.setRegistNo(reportNo);
            swfLogStoreDto.setInsuredName(customerInfo.getName());
            swfLogStoreDto.setEntrustFlag("1");
            swfLogStoreDto.setEntrustNodeStatus("1");
            swfLogStoreDto.setDamageEndDate(new Date());
            swfLogStoreDto.setDamageStartDate(new Date());
            swfLogStoreDto.setUpdateTime(new Date());

            swfLogStoreDto.setFlowID("0");
            swfLogStoreDto.setNodeNo(6);
            swfLogStoreDto.setNodeStatus("4");
            swfLogStoreDto.setFlowStatus("0");
            swfLogStoreDtoList.add(swfLogStoreDto);
        }


        endCaseDto.setSwfLogStoreDtoList(swfLogStoreDtoList);

        List<ClaimEndCaseReqDto> claimEndCaseReqDtos = mqProducerClaimCancelService.getClaimEndCaseReqDto(caseBaseInfoList,taskInfoDTO,reportInfo);
        List<PrpLClaimDto> prpLClaimDtos = mqProducerClaimService.initPrpLClaimDto("end", reportInfo, customerInfo, reportAccident, policyInfoDTOList, wholeCaseBaseEntity, reportInfoExEntity, taskInfoDTO, reportAccidentEx, reportBaseInfo);
        endCaseDto.setPrpLClaimDtoList(prpLClaimDtos);
        endCaseDto.setClaimEndCaseReqDtoList(claimEndCaseReqDtos);
        return endCaseDto;
    }

    private List<PrpLCaseNoDto> getPrpLCaseNoDto(WholeCaseBaseDTO wholeCaseBaseEntity) {
        List<PrpLCaseNoDto> prpLCaseNoDtoList = new ArrayList<>();
        List<CaseBaseEntity> caseBaseInfoList = caseBaseService.getCaseBaseInfoByReportNoAndCasetimes(wholeCaseBaseEntity.getReportNo(), "1");
        for (CaseBaseEntity caseBaseEntity : caseBaseInfoList) {
            PrpLCaseNoDto prpLCaseNoDto = new PrpLCaseNoDto();
            PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
            paymentItemDTO.setReportNo(wholeCaseBaseEntity.getReportNo());
            paymentItemDTO.setCaseTimes(wholeCaseBaseEntity.getCaseTimes());
            paymentItemDTO.setPolicyNo(caseBaseEntity.getPolicyNo());
            paymentItemDTO.setClaimType("1");
            List<PaymentItemDTO> items = paymentItemMapper.getPaymentItem(paymentItemDTO);
            // 结案号生成逻辑对不上，此处重新生成
            if (CollectionUtils.isEmpty(items)) {
                continue;
            }

            prpLCaseNoDto.setCertiNo(items.get(0).getCompensateNo());
//        单证类型（赔款）	CERTITYPE	Y	默认“C”
            prpLCaseNoDto.setCertiType("C");
//        赔案号	CASENO	Y  结案号生成逻辑对不上，此处传我们原先的赔案号
            prpLCaseNoDto.setCaseNo(caseBaseEntity.getCaseNo());
//        立案号码	CLAIMNO	Y		无明确字段（同上立案号）
            prpLCaseNoDto.setClaimNo(caseBaseEntity.getRegistNo());
//        委托赔案标志（非委托赔案）	ENTRUSTFLAG	Y	默认“01”H
            prpLCaseNoDto.setEntrustFlag("01");
//        更新时间	UPDATETIME	CY	只要对表操作就修改updatetime
            prpLCaseNoDto.setUpdateTime(new Date());
            prpLCaseNoDtoList.add(prpLCaseNoDto);
        }
        return prpLCaseNoDtoList;
    }

}
