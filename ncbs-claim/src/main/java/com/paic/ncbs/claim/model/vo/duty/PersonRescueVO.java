package com.paic.ncbs.claim.model.vo.duty;


import com.paic.ncbs.claim.model.dto.duty.PersonRescueDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;
@ApiModel("救援信息VO")
public class PersonRescueVO {

	@ApiModelProperty("模块号")
	private String moduleCode;

	@ApiModelProperty("救援信息DTO列表")
	private List<PersonRescueDTO> personRescueList;

	@ApiModelProperty("总金额")
	private BigDecimal totalAmount;

	@ApiModelProperty("状态（0：未审核，1：已审核）")
	private String status;

	public String getModuleCode() {
		return moduleCode;
	}

	public void setModuleCode(String moduleCode) {
		this.moduleCode = moduleCode;
	}

	public List<PersonRescueDTO> getPersonRescueList() {
		return personRescueList;
	}

	public void setPersonRescueList(List<PersonRescueDTO> personRescueList) {
		this.personRescueList = personRescueList;
	}

	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}
	
}
