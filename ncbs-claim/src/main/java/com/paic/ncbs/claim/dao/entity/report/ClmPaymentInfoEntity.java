package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.util.Date;

public class ClmPaymentInfoEntity extends EntityDTO {

    private static final long serialVersionUID = -5350794936775168362L;

    private String idClmPaymentInfo;

    private String reportNo;

    private String idClmChannelProcess;

    private String collectPayApproach;

    private String bankAccountAttribute;

    private String cityName;

    private String clientBankAccount;

    private String clientBankCode;

    private String clientBankName;

    private String clientCertificateNo;

    private String clientCertificateType;

    private String clientMobile;

    private String clientName;

    private String clientType;

    private String provinceName;

    private String regionCode;

    private String paymentInfoStatus;

    private Date validBeginDate;

    private Date validEndDate;

    private String remark;

    private String migrateFrom = "np";

    private Short caseTimes;

    private Short subTimes;

    private String isMergePay;

    private String paymentInfoType;

    private String documentGroupId;

    private String counterPaymentReason;

    private String otherReason;

    private String dataSource;

    private String organizeCode;

    private String isAgreeImmediatelyPay;

    private String isTaxpayer;

    private String paymentUsage;

    public String getIdClmPaymentInfo() {
        return idClmPaymentInfo;
    }

    public void setIdClmPaymentInfo(String idClmPaymentInfo) {
        this.idClmPaymentInfo = idClmPaymentInfo == null ? null : idClmPaymentInfo.trim();
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String getIdClmChannelProcess() {
        return idClmChannelProcess;
    }

    public void setIdClmChannelProcess(String idClmChannelProcess) {
        this.idClmChannelProcess = idClmChannelProcess == null ? null : idClmChannelProcess.trim();
    }

    public String getCollectPayApproach() {
        return collectPayApproach;
    }

    public void setCollectPayApproach(String collectPayApproach) {
        this.collectPayApproach = collectPayApproach == null ? null : collectPayApproach.trim();
    }

    public String getBankAccountAttribute() {
        return bankAccountAttribute;
    }

    public void setBankAccountAttribute(String bankAccountAttribute) {
        this.bankAccountAttribute = bankAccountAttribute == null ? null : bankAccountAttribute.trim();
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName == null ? null : cityName.trim();
    }

    public String getClientBankAccount() {
        return clientBankAccount;
    }

    public void setClientBankAccount(String clientBankAccount) {
        this.clientBankAccount = clientBankAccount == null ? null : clientBankAccount.trim();
    }

    public String getClientBankCode() {
        return clientBankCode;
    }

    public void setClientBankCode(String clientBankCode) {
        this.clientBankCode = clientBankCode == null ? null : clientBankCode.trim();
    }

    public String getClientBankName() {
        return clientBankName;
    }

    public void setClientBankName(String clientBankName) {
        this.clientBankName = clientBankName == null ? null : clientBankName.trim();
    }

    public String getClientCertificateNo() {
        return clientCertificateNo;
    }

    public void setClientCertificateNo(String clientCertificateNo) {
        this.clientCertificateNo = clientCertificateNo == null ? null : clientCertificateNo.trim();
    }

    public String getClientCertificateType() {
        return clientCertificateType;
    }

    public void setClientCertificateType(String clientCertificateType) {
        this.clientCertificateType = clientCertificateType == null ? null : clientCertificateType.trim();
    }

    public String getClientMobile() {
        return clientMobile;
    }

    public void setClientMobile(String clientMobile) {
        this.clientMobile = clientMobile == null ? null : clientMobile.trim();
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName == null ? null : clientName.trim();
    }

    public String getClientType() {
        return clientType;
    }

    public void setClientType(String clientType) {
        this.clientType = clientType == null ? null : clientType.trim();
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName == null ? null : provinceName.trim();
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode == null ? null : regionCode.trim();
    }

    public String getPaymentInfoStatus() {
        return paymentInfoStatus;
    }

    public void setPaymentInfoStatus(String paymentInfoStatus) {
        this.paymentInfoStatus = paymentInfoStatus == null ? null : paymentInfoStatus.trim();
    }

    public Date getValidBeginDate() {
        return validBeginDate;
    }

    public void setValidBeginDate(Date validBeginDate) {
        this.validBeginDate = validBeginDate;
    }

    public Date getValidEndDate() {
        return validEndDate;
    }

    public void setValidEndDate(Date validEndDate) {
        this.validEndDate = validEndDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getMigrateFrom() {
        return migrateFrom;
    }

    public void setMigrateFrom(String migrateFrom) {
        this.migrateFrom = (migrateFrom == null) ? "na" : migrateFrom.trim();
    }

    public Short getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Short caseTimes) {
        this.caseTimes = caseTimes;
    }

    public Short getSubTimes() {
        return subTimes;
    }

    public void setSubTimes(Short subTimes) {
        this.subTimes = subTimes;
    }

    public String getIsMergePay() {
        return isMergePay;
    }

    public void setIsMergePay(String isMergePay) {
        this.isMergePay = isMergePay == null ? null : isMergePay.trim();
    }

    public String getPaymentInfoType() {
        return paymentInfoType;
    }

    public void setPaymentInfoType(String paymentInfoType) {
        this.paymentInfoType = paymentInfoType == null ? null : paymentInfoType.trim();
    }

    public String getDocumentGroupId() {
        return documentGroupId;
    }

    public void setDocumentGroupId(String documentGroupId) {
        this.documentGroupId = documentGroupId == null ? null : documentGroupId.trim();
    }

    public String getCounterPaymentReason() {
        return counterPaymentReason;
    }

    public void setCounterPaymentReason(String counterPaymentReason) {
        this.counterPaymentReason = counterPaymentReason == null ? null : counterPaymentReason.trim();
    }

    public String getOtherReason() {
        return otherReason;
    }

    public void setOtherReason(String otherReason) {
        this.otherReason = otherReason == null ? null : otherReason.trim();
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource == null ? null : dataSource.trim();
    }

    public String getOrganizeCode() {
        return organizeCode;
    }

    public void setOrganizeCode(String organizeCode) {
        this.organizeCode = organizeCode == null ? null : organizeCode.trim();
    }

    public String getIsAgreeImmediatelyPay() {
        return isAgreeImmediatelyPay;
    }

    public void setIsAgreeImmediatelyPay(String isAgreeImmediatelyPay) {
        this.isAgreeImmediatelyPay = isAgreeImmediatelyPay == null ? null : isAgreeImmediatelyPay.trim();
    }

    public String getIsTaxpayer() {
        return isTaxpayer;
    }

    public void setIsTaxpayer(String isTaxpayer) {
        this.isTaxpayer = isTaxpayer == null ? null : isTaxpayer.trim();
    }

    public String getPaymentUsage() {
        return paymentUsage;
    }

    public void setPaymentUsage(String paymentUsage) {
        this.paymentUsage = paymentUsage;
    }

}