package com.paic.ncbs.claim.model.vo.pet;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:
 **/
public class ReportAccidentPetExtendVO {

    private String id;//宠物档案ID

    private Date therapyDate;//就诊登记时间

    private String petName;//	宠物名字
    private String petBreed;//	品种
    private String petHairColour;//毛色文本
    private BigDecimal petWeight;//体重 单位kg
    private String isSterilize;//是否已绝育 N/Y
    private String isImmune;//	是否已免疫	N/Y
    private String isPetRepellent;//是否已驱虫	 N/Y
    private String isDeath;//	是否已死亡	N/Y

    private Date petBirthday;//生日
    private String petSex;///	性别  {"01":"公","02":"母"}
    private String petNo;//	宠物编号(医院)
    private String clinicalExamination;//临床检查
    private String InspectionAnalysis;//检验分析

    private Date hospitalizedDate;//	入院日期
    private String cagePosition;//	笼位

    private String laboratoryNos;//	化验单号  非必填，多个以逗号”,”隔开
    private String inputBy;//	录单人
    private String petImageId;//宠物AI照片上传IOBS后的id
    private String manualReview;//人工核验宠物是否一致	 	{"1":"是","0":"否"}

    private String policyNo;//保单号
    private Integer  treatmentTimes;//基础医疗理赔次数限制
    private Integer operationTimes;//手术赔次数限制

    private String isDirectCompensation;//是否支持直赔：Y-是；N-否
    private String productCode;//产品代码

    private String hospitalRiskStatus;//医院风控状态
    private String customerRiskStatus; // 用户风控状态


    public Date getTherapyDate() {
        return therapyDate;
    }

    public void setTherapyDate(Date therapyDate) {
        this.therapyDate = therapyDate;
    }

    public String getPetName() {
        return petName;
    }

    public void setPetName(String petName) {
        this.petName = petName;
    }

    public String getPetBreed() {
        return petBreed;
    }

    public void setPetBreed(String petBreed) {
        this.petBreed = petBreed;
    }

    public String getPetHairColour() {
        return petHairColour;
    }

    public void setPetHairColour(String petHairColour) {
        this.petHairColour = petHairColour;
    }

    public BigDecimal getPetWeight() {
        return petWeight;
    }

    public void setPetWeight(BigDecimal petWeight) {
        this.petWeight = petWeight;
    }

    public String getIsSterilize() {
        return isSterilize;
    }

    public void setIsSterilize(String isSterilize) {
        this.isSterilize = isSterilize;
    }

    public String getIsImmune() {
        return isImmune;
    }

    public void setIsImmune(String isImmune) {
        this.isImmune = isImmune;
    }

    public String getIsPetRepellent() {
        return isPetRepellent;
    }

    public void setIsPetRepellent(String isPetRepellent) {
        this.isPetRepellent = isPetRepellent;
    }

    public String getIsDeath() {
        return isDeath;
    }

    public void setIsDeath(String isDeath) {
        this.isDeath = isDeath;
    }

    public Date getPetBirthday() {
        return petBirthday;
    }

    public void setPetBirthday(Date petBirthday) {
        this.petBirthday = petBirthday;
    }

    public String getPetSex() {
        return petSex;
    }

    public void setPetSex(String petSex) {
        this.petSex = petSex;
    }

    public String getPetNo() {
        return petNo;
    }

    public void setPetNo(String petNo) {
        this.petNo = petNo;
    }

    public String getClinicalExamination() {
        return clinicalExamination;
    }

    public void setClinicalExamination(String clinicalExamination) {
        this.clinicalExamination = clinicalExamination;
    }

    public String getInspectionAnalysis() {
        return InspectionAnalysis;
    }

    public void setInspectionAnalysis(String inspectionAnalysis) {
        InspectionAnalysis = inspectionAnalysis;
    }

    public Date getHospitalizedDate() {
        return hospitalizedDate;
    }

    public void setHospitalizedDate(Date hospitalizedDate) {
        this.hospitalizedDate = hospitalizedDate;
    }

    public String getCagePosition() {
        return cagePosition;
    }

    public void setCagePosition(String cagePosition) {
        this.cagePosition = cagePosition;
    }

    public String getLaboratoryNos() {
        return laboratoryNos;
    }

    public void setLaboratoryNos(String laboratoryNos) {
        this.laboratoryNos = laboratoryNos;
    }

    public String getInputBy() {
        return inputBy;
    }

    public void setInputBy(String inputBy) {
        this.inputBy = inputBy;
    }

    public String getPetImageId() {
        return petImageId;
    }

    public void setPetImageId(String petImageId) {
        this.petImageId = petImageId;
    }

    public String getManualReview() {
        return manualReview;
    }

    public void setManualReview(String manualReview) {
        this.manualReview = manualReview;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public Integer getTreatmentTimes() {
        return treatmentTimes;
    }

    public void setTreatmentTimes(Integer treatmentTimes) {
        this.treatmentTimes = treatmentTimes;
    }

    public Integer getOperationTimes() {
        return operationTimes;
    }

    public void setOperationTimes(Integer operationTimes) {
        this.operationTimes = operationTimes;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIsDirectCompensation() {
        return isDirectCompensation;
    }

    public void setIsDirectCompensation(String isDirectCompensation) {
        this.isDirectCompensation = isDirectCompensation;
    }

    public String getHospitalRiskStatus() {
        return hospitalRiskStatus;
    }

    public void setHospitalRiskStatus(String hospitalRiskStatus) {
        this.hospitalRiskStatus = hospitalRiskStatus;
    }

    public String getCustomerRiskStatus() {
        return customerRiskStatus;
    }

    public void setCustomerRiskStatus(String customerRiskStatus) {
        this.customerRiskStatus = customerRiskStatus;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }


}
