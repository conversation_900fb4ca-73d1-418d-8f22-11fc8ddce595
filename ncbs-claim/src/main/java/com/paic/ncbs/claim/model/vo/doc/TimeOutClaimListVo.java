package com.paic.ncbs.claim.model.vo.doc;

import lombok.Data;

@Data
public class TimeOutClaimListVo {
    private String reportNo; //报案号
    private String reportDate; //报案时间
    private String timeOutType; //超时类型 1 未估损2 未结案 3 未记录理赔日志"
    private String dealCode; //处理人工号
    private String timeoutDay; //超时时长
    private String systemCode; //系统来源 默认Global
    private String resultCode; //返回代码 0000-成功 9999-失败
    private String resultMessage; //返回信息 错误信息
    private String responseTime; //返回时间 返回时间
    private String comName;//归属机构名称
}
