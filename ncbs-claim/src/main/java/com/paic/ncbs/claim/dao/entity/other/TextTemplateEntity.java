package com.paic.ncbs.claim.dao.entity.other;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

/**
 * 文本模板
 */
public class TextTemplateEntity extends EntityDTO {
    /**
     * 主键
     */
    private String idAhcsTextTemplate;

    /**
     * 模板编码(ENDORSE——批单，MAIL——邮件，MES——短信，MESSAGE——系统消息)
     */
    private String templateCode;

    /**
     * 模板内容
     */
    private String templateContent;

    /**
     * 模板名字
     */
    private String templateName;

    /**
     * 模板描述
     */
    private String templateDesc;

    /**
     * 是否有效 Y/N
     */
    private String status;

    public String getIdAhcsTextTemplate() {
        return idAhcsTextTemplate;
    }

    public void setIdAhcsTextTemplate(String idAhcsTextTemplate) {
        this.idAhcsTextTemplate = idAhcsTextTemplate;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    public String getTemplateContent() {
        return templateContent;
    }

    public void setTemplateContent(String templateContent) {
        this.templateContent = templateContent;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public String getTemplateDesc() {
        return templateDesc;
    }

    public void setTemplateDesc(String templateDesc) {
        this.templateDesc = templateDesc;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}