package com.paic.ncbs.claim.model.vo.risk;




import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyHolderEntity;
import com.paic.ncbs.claim.model.dto.report.ReportBaseInfoResData;
import com.paic.ncbs.claim.model.dto.user.DepartmentDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("案件信息")
public class AccidentPolicyVO {
	@ApiModelProperty("报案基本信息")
	private ReportBaseInfoResData reportBaseInfoResData;

	@ApiModelProperty("报案信息")
	private List<PolicyReportVO> policyReportVOList;

	@ApiModelProperty("机构信息列表")
	List<DepartmentDTO> departmentDTOList;

	private List<AhcsPolicyHolderEntity> holderEntity;

	public ReportBaseInfoResData getReportBaseInfoResData() {
		return reportBaseInfoResData;
	}

	public void setReportBaseInfoResData(ReportBaseInfoResData reportBaseInfoResData) {
		this.reportBaseInfoResData = reportBaseInfoResData;
	}

	public List<PolicyReportVO> getPolicyReportVOList() {
		return policyReportVOList;
	}

	public void setPolicyReportVOList(List<PolicyReportVO> policyReportVOList) {
		this.policyReportVOList = policyReportVOList;
	}

	public List<DepartmentDTO> getDepartmentDTOList() {
		return departmentDTOList;
	}

	public void setDepartmentDTOList(List<DepartmentDTO> departmentDTOList) {
		this.departmentDTOList = departmentDTOList;
	}

	public List<AhcsPolicyHolderEntity> getHolderEntity() {
		return holderEntity;
	}

	public void setHolderEntity(List<AhcsPolicyHolderEntity> holderEntity) {
		this.holderEntity = holderEntity;
	}
}
