package com.paic.ncbs.claim.dao.entity.other;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.util.Date;

public class ClaimProductClassEntity extends EntityDTO {

    private static final long serialVersionUID = -8966942236208775521L;

    private String idProductClass;

    private String productClassCode;

    private String productClassName;

    private Date effectiveDate;

    private Date invalidateDate;

    private String businessProductLineCode;

    private String productCode;

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getIdProductClass() {
        return idProductClass;
    }

    public void setIdProductClass(String idProductClass) {
        this.idProductClass = idProductClass == null ? null : idProductClass.trim();
    }

    public String getProductClassCode() {
        return productClassCode;
    }

    public void setProductClassCode(String productClassCode) {
        this.productClassCode = productClassCode == null ? null : productClassCode.trim();
    }

    public String getProductClassName() {
        return productClassName;
    }

    public void setProductClassName(String productClassName) {
        this.productClassName = productClassName == null ? null : productClassName.trim();
    }

    public Date getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(Date effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public Date getInvalidateDate() {
        return invalidateDate;
    }

    public void setInvalidateDate(Date invalidateDate) {
        this.invalidateDate = invalidateDate;
    }

    public String getBusinessProductLineCode() {
        return businessProductLineCode;
    }

    public void setBusinessProductLineCode(String businessProductLineCode) {
        this.businessProductLineCode = businessProductLineCode == null ? null : businessProductLineCode.trim();
    }
}