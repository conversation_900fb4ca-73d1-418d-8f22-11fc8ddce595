package com.paic.ncbs.claim.model.vo.ahcs;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

@ApiModel("支付客户信息")
public class PaymentClientInfoFullVO {
    @ApiModelProperty("信息类型(类型:个人=1,公司=0,公司授权=2,个人授权=3,领款代办=4,授权代办=5)")
    private String infoAttribute;
    @ApiModelProperty("客户名字")
    private String clientName;
    @ApiModelProperty("性别F女  M男")
    private String sex;
    @ApiModelProperty("职业")
    private String carrer;
    @ApiModelProperty("证件号")
    private String clientCertificateNo;
    @ApiModelProperty("国籍")
    private String country;
    @ApiModelProperty("")
    private String isValidLongTerm;
    @ApiModelProperty("证件类型")
    private String clientCertificateType;
    @ApiModelProperty("住所地/常住地/工作单位地址")
    private String address;
    @ApiModelProperty("证件有效期限起始日")
    private String validBeginDate;
    @ApiModelProperty("证件有效期限结束日")
    private String validEndDate;
    @ApiModelProperty("经营范围")
    private String businessScope;
    @ApiModelProperty("营业执照号码")
    private String businessLicenceNo;
    @ApiModelProperty("税务登记证号码")
    private String taxCertificateNo;
    @ApiModelProperty("组织机构代码证号码")
    private String orgCodeCertificateNo;
    @ApiModelProperty("法定代表人或负责人姓名")
    private String artificialName;
    @ApiModelProperty("法定代表人或负责人证件类型")
    private String artificialCertificateType;
    @ApiModelProperty("法定代表人或负责人证件号")
    private String artificialCertificateNo;
    @ApiModelProperty("团体证件有效起始日")
    private String businessLicenceBeginDate;
    @ApiModelProperty("团体证件有效结束日")
    private String businessLicenceEndDate;
    @ApiModelProperty("税务登记证有效期限起始日")
    private String taxValidBeginDate;
    @ApiModelProperty("税务登记证有效期限结束日")
    private String taxValidEndDate;
    @ApiModelProperty("组织机构代码证有效期限起始日")
    private String orgValidBeginDate;
    @ApiModelProperty("组织机构代码证有效期限结束日")
    private String orgValidEndDate;
    @ApiModelProperty("法定代表人或负责人证件有效期限起始日")
    private String artificialBeginDate;
    @ApiModelProperty("法定代表人或负责人证件有效期限结束日")
    private String artificialEndDate;
    @ApiModelProperty("")
    private String isArtificialLongTerm;
    @ApiModelProperty("联系方式")
    private String contact;
    @ApiModelProperty("统一社会信用代码")
    private String unifiedSocialCreditCode;
    @ApiModelProperty("统一社会信用代码营业有效期限起始日")
    private String socialCreditValidBeginDate;
    @ApiModelProperty("统一社会信用代码营业有效期限结束日")
    private String socialCreditValidEndDate;
    @ApiModelProperty("统一社会信用代码营业期限是否长期有效")
    private String isSocialCreditLongTerm;
    @ApiModelProperty("组织机构代码证有效期限是否长期有效")
    private String isOrgLongTerm;
    @ApiModelProperty("营业执照有效期限是否长期有效")
    private String isBusinessLicenceLongTerm;
    @ApiModelProperty("税务登记证有效期限是否长期有效")
    private String isTaxLongTerm;
    @ApiModelProperty("控股股东/实际控制人姓名")
    private String ctrlShareholders;
    @ApiModelProperty("控股股东/实际控制人姓名证件类型")
    private String ctrlCreditType;
    @ApiModelProperty("控股股东/实际控制人姓名证件号码")
    private String ctrlCreditCode;
    @ApiModelProperty("控股股东/实际控制人姓名有效期限起始日")
    private String ctrlCreditValidBeginDate;
    @ApiModelProperty("控股股东/实际控制人姓名有效期限结束日")
    private String ctrlCreditValidEndDate;
    @ApiModelProperty("授权办理人姓名")
    private String authorizer;
    @ApiModelProperty("授权办理人证件类型")
    private String authorizerCertificateType;
    @ApiModelProperty("授权办理人证件号")
    private String authorizerCertificateNo;
    @ApiModelProperty("授权办理人联系电话")
    private String authorizerContact;
    @ApiModelProperty("法定代表人或负责人证件有效期限是否长期有效")
    private String isAuthorizerLongTerm;
    @ApiModelProperty("授权办理人证件有效期起期")
    private String authorizerValidBeginDate;
    @ApiModelProperty("授权办理人证件有效期止期")
    private String authorizerValidEndDate;
    @ApiModelProperty("出生日期")
    private String birthday;
    @ApiModelProperty("版权办理人姓名")
    private String copyrightManagerName;
    @ApiModelProperty("版权办理人证件类型")
    private String copyrightCertificateType;
    @ApiModelProperty("版权办理人证件号")
    private String copyrightCertificateNo;
    @ApiModelProperty("版权办理人证件有效期限起始日")
    private String copyrightBeginDate;
    @ApiModelProperty("版权办理人证件有效期限结束日")
    private String copyrightEndDate;
    @ApiModelProperty("邮箱")
    private String email;
    @ApiModelProperty("邮编")
    private String postcode;
    @ApiModelProperty("是否三证合一")
    private String isThreeInOne;
    @ApiModelProperty("控股股东/实际控制人姓名期限是否长期有效")
    private String isCtrlCreditLongTerm;
    @ApiModelProperty("年收入,单位：万元")
    private BigDecimal annualIncome;
    @ApiModelProperty("工作单位")
    private String workCompany;
    @ApiModelProperty("受益人标识 01法定受益人；02指定受益人")
    private String beneficiaryType;
    @ApiModelProperty("投保人与被保险人关系,对应clm_common_parameter表中collection_code=TBGX-GR（个人）/TBGX-DW（单位）")
    private String insureInsuredRelation;
    @ApiModelProperty("行业类型:金融,贸易等,对应clm_common_parameter表中collection_code=HYLX")
    private String trade;
    @ApiModelProperty("国有属性,对应clm_common_parameter表中collection_code=GYSX")
    private String stateAttribute;
    @ApiModelProperty("注册资本金")
    private BigDecimal registAmount;
    @ApiModelProperty("注册资本金币种,对应clm_common_parameter表中collection_code=HBBZ")
    private String registeredCapitalCurrency;
    @ApiModelProperty("")
    private List<PaymentClientInfoExVO> paymentClientInfoExList;

    public String getInfoAttribute() {
        return infoAttribute;
    }

    public void setInfoAttribute(String infoAttribute) {
        this.infoAttribute = infoAttribute;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getCarrer() {
        return carrer;
    }

    public void setCarrer(String carrer) {
        this.carrer = carrer;
    }

    public String getClientCertificateNo() {
        return clientCertificateNo;
    }

    public void setClientCertificateNo(String clientCertificateNo) {
        this.clientCertificateNo = clientCertificateNo;
    }

    public String getClientCertificateType() {
        return clientCertificateType;
    }

    public void setClientCertificateType(String clientCertificateType) {
        this.clientCertificateType = clientCertificateType;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getValidBeginDate() {
        return validBeginDate;
    }

    public void setValidBeginDate(String validBeginDate) {
        this.validBeginDate = validBeginDate;
    }

    public String getValidEndDate() {
        return validEndDate;
    }

    public void setValidEndDate(String validEndDate) {
        this.validEndDate = validEndDate;
    }

    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public String getBusinessLicenceNo() {
        return businessLicenceNo;
    }

    public void setBusinessLicenceNo(String businessLicenceNo) {
        this.businessLicenceNo = businessLicenceNo;
    }

    public String getTaxCertificateNo() {
        return taxCertificateNo;
    }

    public void setTaxCertificateNo(String taxCertificateNo) {
        this.taxCertificateNo = taxCertificateNo;
    }

    public String getOrgCodeCertificateNo() {
        return orgCodeCertificateNo;
    }

    public void setOrgCodeCertificateNo(String orgCodeCertificateNo) {
        this.orgCodeCertificateNo = orgCodeCertificateNo;
    }

    public String getArtificialName() {
        return artificialName;
    }

    public void setArtificialName(String artificialName) {
        this.artificialName = artificialName;
    }

    public String getArtificialCertificateType() {
        return artificialCertificateType;
    }

    public void setArtificialCertificateType(String artificialCertificateType) {
        this.artificialCertificateType = artificialCertificateType;
    }

    public String getArtificialCertificateNo() {
        return artificialCertificateNo;
    }

    public void setArtificialCertificateNo(String artificialCertificateNo) {
        this.artificialCertificateNo = artificialCertificateNo;
    }

    public String getBusinessLicenceBeginDate() {
        return businessLicenceBeginDate;
    }

    public void setBusinessLicenceBeginDate(String businessLicenceBeginDate) {
        this.businessLicenceBeginDate = businessLicenceBeginDate;
    }

    public String getBusinessLicenceEndDate() {
        return businessLicenceEndDate;
    }

    public void setBusinessLicenceEndDate(String businessLicenceEndDate) {
        this.businessLicenceEndDate = businessLicenceEndDate;
    }

    public String getTaxValidBeginDate() {
        return taxValidBeginDate;
    }

    public void setTaxValidBeginDate(String taxValidBeginDate) {
        this.taxValidBeginDate = taxValidBeginDate;
    }

    public String getTaxValidEndDate() {
        return taxValidEndDate;
    }

    public void setTaxValidEndDate(String taxValidEndDate) {
        this.taxValidEndDate = taxValidEndDate;
    }

    public String getOrgValidBeginDate() {
        return orgValidBeginDate;
    }

    public void setOrgValidBeginDate(String orgValidBeginDate) {
        this.orgValidBeginDate = orgValidBeginDate;
    }

    public String getOrgValidEndDate() {
        return orgValidEndDate;
    }

    public void setOrgValidEndDate(String orgValidEndDate) {
        this.orgValidEndDate = orgValidEndDate;
    }

    public String getArtificialBeginDate() {
        return artificialBeginDate;
    }

    public void setArtificialBeginDate(String artificialBeginDate) {
        this.artificialBeginDate = artificialBeginDate;
    }

    public String getArtificialEndDate() {
        return artificialEndDate;
    }

    public void setArtificialEndDate(String artificialEndDate) {
        this.artificialEndDate = artificialEndDate;
    }

    public String getIsArtificialLongTerm() {
        return isArtificialLongTerm;
    }

    public void setIsArtificialLongTerm(String isArtificialLongTerm) {
        this.isArtificialLongTerm = isArtificialLongTerm;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getUnifiedSocialCreditCode() {
        return unifiedSocialCreditCode;
    }

    public void setUnifiedSocialCreditCode(String unifiedSocialCreditCode) {
        this.unifiedSocialCreditCode = unifiedSocialCreditCode;
    }

    public String getSocialCreditValidBeginDate() {
        return socialCreditValidBeginDate;
    }

    public void setSocialCreditValidBeginDate(String socialCreditValidBeginDate) {
        this.socialCreditValidBeginDate = socialCreditValidBeginDate;
    }

    public String getSocialCreditValidEndDate() {
        return socialCreditValidEndDate;
    }

    public void setSocialCreditValidEndDate(String socialCreditValidEndDate) {
        this.socialCreditValidEndDate = socialCreditValidEndDate;
    }

    public String getIsSocialCreditLongTerm() {
        return isSocialCreditLongTerm;
    }

    public void setIsSocialCreditLongTerm(String isSocialCreditLongTerm) {
        this.isSocialCreditLongTerm = isSocialCreditLongTerm;
    }

    public String getIsOrgLongTerm() {
        return isOrgLongTerm;
    }

    public void setIsOrgLongTerm(String isOrgLongTerm) {
        this.isOrgLongTerm = isOrgLongTerm;
    }

    public String getIsBusinessLicenceLongTerm() {
        return isBusinessLicenceLongTerm;
    }

    public void setIsBusinessLicenceLongTerm(String isBusinessLicenceLongTerm) {
        this.isBusinessLicenceLongTerm = isBusinessLicenceLongTerm;
    }

    public String getIsTaxLongTerm() {
        return isTaxLongTerm;
    }

    public void setIsTaxLongTerm(String isTaxLongTerm) {
        this.isTaxLongTerm = isTaxLongTerm;
    }

    public String getCtrlShareholders() {
        return ctrlShareholders;
    }

    public void setCtrlShareholders(String ctrlShareholders) {
        this.ctrlShareholders = ctrlShareholders;
    }

    public String getCtrlCreditType() {
        return ctrlCreditType;
    }

    public void setCtrlCreditType(String ctrlCreditType) {
        this.ctrlCreditType = ctrlCreditType;
    }

    public String getCtrlCreditCode() {
        return ctrlCreditCode;
    }

    public void setCtrlCreditCode(String ctrlCreditCode) {
        this.ctrlCreditCode = ctrlCreditCode;
    }

    public String getCtrlCreditValidBeginDate() {
        return ctrlCreditValidBeginDate;
    }

    public void setCtrlCreditValidBeginDate(String ctrlCreditValidBeginDate) {
        this.ctrlCreditValidBeginDate = ctrlCreditValidBeginDate;
    }

    public String getCtrlCreditValidEndDate() {
        return ctrlCreditValidEndDate;
    }

    public void setCtrlCreditValidEndDate(String ctrlCreditValidEndDate) {
        this.ctrlCreditValidEndDate = ctrlCreditValidEndDate;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getIsValidLongTerm() {
        return isValidLongTerm;
    }

    public void setIsValidLongTerm(String isValidLongTerm) {
        this.isValidLongTerm = isValidLongTerm;
    }

    public String getAuthorizer() {
        return authorizer;
    }

    public void setAuthorizer(String authorizer) {
        this.authorizer = authorizer;
    }

    public String getAuthorizerCertificateType() {
        return authorizerCertificateType;
    }

    public void setAuthorizerCertificateType(String authorizerCertificateType) {
        this.authorizerCertificateType = authorizerCertificateType;
    }

    public String getAuthorizerCertificateNo() {
        return authorizerCertificateNo;
    }

    public void setAuthorizerCertificateNo(String authorizerCertificateNo) {
        this.authorizerCertificateNo = authorizerCertificateNo;
    }

    public String getIsAuthorizerLongTerm() {
        return isAuthorizerLongTerm;
    }

    public void setIsAuthorizerLongTerm(String isAuthorizerLongTerm) {
        this.isAuthorizerLongTerm = isAuthorizerLongTerm;
    }

    public String getAuthorizerValidBeginDate() {
        return authorizerValidBeginDate;
    }

    public void setAuthorizerValidBeginDate(String authorizerValidBeginDate) {
        this.authorizerValidBeginDate = authorizerValidBeginDate;
    }

    public String getAuthorizerValidEndDate() {
        return authorizerValidEndDate;
    }

    public void setAuthorizerValidEndDate(String authorizerValidEndDate) {
        this.authorizerValidEndDate = authorizerValidEndDate;
    }

    public List<PaymentClientInfoExVO> getPaymentClientInfoExList() {
        return paymentClientInfoExList;
    }

    public void setPaymentClientInfoExList(List<PaymentClientInfoExVO> paymentClientInfoExList) {
        this.paymentClientInfoExList = paymentClientInfoExList;
    }

    public String getAuthorizerContact() {
        return authorizerContact;
    }

    public void setAuthorizerContact(String authorizerContact) {
        this.authorizerContact = authorizerContact;
    }

    public String getCopyrightManagerName() {
        return copyrightManagerName;
    }

    public void setCopyrightManagerName(String copyrightManagerName) {
        this.copyrightManagerName = copyrightManagerName;
    }

    public String getCopyrightCertificateType() {
        return copyrightCertificateType;
    }

    public void setCopyrightCertificateType(String copyrightCertificateType) {
        this.copyrightCertificateType = copyrightCertificateType;
    }

    public String getCopyrightCertificateNo() {
        return copyrightCertificateNo;
    }

    public void setCopyrightCertificateNo(String copyrightCertificateNo) {
        this.copyrightCertificateNo = copyrightCertificateNo;
    }

    public String getCopyrightBeginDate() {
        return copyrightBeginDate;
    }

    public void setCopyrightBeginDate(String copyrightBeginDate) {
        this.copyrightBeginDate = copyrightBeginDate;
    }

    public String getCopyrightEndDate() {
        return copyrightEndDate;
    }

    public void setCopyrightEndDate(String copyrightEndDate) {
        this.copyrightEndDate = copyrightEndDate;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    public String getIsThreeInOne() {
        return isThreeInOne;
    }

    public void setIsThreeInOne(String isThreeInOne) {
        this.isThreeInOne = isThreeInOne;
    }

    public String getIsCtrlCreditLongTerm() {
        return isCtrlCreditLongTerm;
    }

    public void setIsCtrlCreditLongTerm(String isCtrlCreditLongTerm) {
        this.isCtrlCreditLongTerm = isCtrlCreditLongTerm;
    }

    public BigDecimal getAnnualIncome() {
        return annualIncome;
    }

    public void setAnnualIncome(BigDecimal annualIncome) {
        this.annualIncome = annualIncome;
    }

    public String getWorkCompany() {
        return workCompany;
    }

    public void setWorkCompany(String workCompany) {
        this.workCompany = workCompany;
    }

    public String getBeneficiaryType() {
        return beneficiaryType;
    }

    public void setBeneficiaryType(String beneficiaryType) {
        this.beneficiaryType = beneficiaryType;
    }

    public String getInsureInsuredRelation() {
        return insureInsuredRelation;
    }

    public void setInsureInsuredRelation(String insureInsuredRelation) {
        this.insureInsuredRelation = insureInsuredRelation;
    }

    public String getTrade() {
        return trade;
    }

    public void setTrade(String trade) {
        this.trade = trade;
    }

    public String getStateAttribute() {
        return stateAttribute;
    }

    public void setStateAttribute(String stateAttribute) {
        this.stateAttribute = stateAttribute;
    }

    public BigDecimal getRegistAmount() {
        return registAmount;
    }

    public void setRegistAmount(BigDecimal registAmount) {
        this.registAmount = registAmount;
    }

    public String getRegisteredCapitalCurrency() {
        return registeredCapitalCurrency;
    }

    public void setRegisteredCapitalCurrency(String registeredCapitalCurrency) {
        this.registeredCapitalCurrency = registeredCapitalCurrency;
    }

}
