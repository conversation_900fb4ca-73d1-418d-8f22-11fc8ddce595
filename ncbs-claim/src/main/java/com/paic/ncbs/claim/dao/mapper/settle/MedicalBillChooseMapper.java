package com.paic.ncbs.claim.dao.mapper.settle;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillChooseDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface MedicalBillChooseMapper extends BaseDao<MedicalBillChooseDTO> {

	String getMedicalBillChoose(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes);

}