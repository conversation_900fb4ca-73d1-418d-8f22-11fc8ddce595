package com.paic.ncbs.claim.model.vo.doc;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

//y
@ApiModel("打印账单信息")
public class PrintBillInfoVO {

	private String billNo;

	private String therapyType;

	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date startDate;

	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date endDate;
	

	private String hospitalName;
	

	private BigDecimal billAmount;
	

	private BigDecimal prepaidAmount;
	

	private BigDecimal deductibleAmount;
	

	private BigDecimal immoderateAmount;
	

	private BigDecimal partialDeductible;
	

	private BigDecimal discountAmt;
	

	private BigDecimal reasonableAmount;
	

	private String costComment;

	@ApiModelProperty("报案号")
	private String reportNo;
	

	private Integer caseTimes;
	

	private String startDateStr;
	

	private String therapyTypeName;
	

	private String BillIndex;
	

	private String idAhcsBillInfo;

	public String getBillNo() {
		return billNo;
	}

	public void setBillNo(String billNo) {
		this.billNo = billNo;
	}

	public String getTherapyType() {
		return therapyType;
	}

	public void setTherapyType(String therapyType) {
		this.therapyType = therapyType;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public String getHospitalName() {
		return hospitalName;
	}

	public void setHospitalName(String hospitalName) {
		this.hospitalName = hospitalName;
	}

	public BigDecimal getBillAmount() {
		return billAmount;
	}

	public void setBillAmount(BigDecimal billAmount) {
		this.billAmount = billAmount;
	}

	public BigDecimal getPrepaidAmount() {
		return prepaidAmount;
	}

	public void setPrepaidAmount(BigDecimal prepaidAmount) {
		this.prepaidAmount = prepaidAmount;
	}

	public BigDecimal getReasonableAmount() {
		return reasonableAmount;
	}

	public void setReasonableAmount(BigDecimal reasonableAmount) {
		this.reasonableAmount = reasonableAmount;
	}

	public BigDecimal getDiscountAmt() {
		return discountAmt;
	}

	public void setDiscountAmt(BigDecimal discountAmt) {
		this.discountAmt = discountAmt;
	}

	public String getCostComment() {
		return costComment;
	}

	public void setCostComment(String costComment) {
		this.costComment = costComment;
	}

	public String getReportNo() {
		return reportNo;
	}

	public void setReportNo(String reportNo) {
		this.reportNo = reportNo;
	}

	public Integer getCaseTimes() {
		return caseTimes;
	}

	public void setCaseTimes(Integer caseTimes) {
		this.caseTimes = caseTimes;
	}

	public String getStartDateStr() {
		return startDateStr;
	}

	public void setStartDateStr(String startDateStr) {
		this.startDateStr = startDateStr;
	}

	public String getTherapyTypeName() {
		return therapyTypeName;
	}

	public void setTherapyTypeName(String therapyTypeName) {
		this.therapyTypeName = therapyTypeName;
	}

	public String getBillIndex() {
		return BillIndex;
	}

	public void setBillIndex(String billIndex) {
		BillIndex = billIndex;
	}

	public BigDecimal getDeductibleAmount() {
		return deductibleAmount;
	}

	public void setDeductibleAmount(BigDecimal deductibleAmount) {
		this.deductibleAmount = deductibleAmount;
	}

	public BigDecimal getImmoderateAmount() {
		return immoderateAmount;
	}

	public void setImmoderateAmount(BigDecimal immoderateAmount) {
		this.immoderateAmount = immoderateAmount;
	}

	public BigDecimal getPartialDeductible() {
		return partialDeductible;
	}

	public void setPartialDeductible(BigDecimal partialDeductible) {
		this.partialDeductible = partialDeductible;
	}

	public String getIdAhcsBillInfo() {
		return idAhcsBillInfo;
	}

	public void setIdAhcsBillInfo(String idAhcsBillInfo) {
		this.idAhcsBillInfo = idAhcsBillInfo;
	}
	
}
