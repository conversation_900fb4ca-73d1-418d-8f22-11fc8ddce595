package com.paic.ncbs.claim.dao.mapper.ahcs;

import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyPlanDataEntity;
import com.paic.ncbs.claim.dao.entity.ahcs.PlanTermContentEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AhcsPolicyPlanDataMapper {

    int insert(AhcsPolicyPlanDataEntity record);

    int insertSelective(AhcsPolicyPlanDataEntity record);

    int insertList(@Param("policyPlanDataEntities") List<AhcsPolicyPlanDataEntity> policyPlanDataEntities);

    List<AhcsPolicyPlanDataEntity> selectPolicyInfo(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo, @Param("selfCardNo") String selfCardNo, @Param("planCode") String planCode);

    void deleteByReportNoAndPolicyNo(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo);

    /**
     * 批量保存小条款信息
     * @param planTermContentEntities
     * @return
     */
    int insertTermList(@Param("planTermContentEntities") List<PlanTermContentEntity> planTermContentEntities);

    /**
     * 根据报案号 赔付次数查询小条款
     * @param reportNo
     * @return
     */
    List<PlanTermContentEntity> getPlanTermContentInfo(@Param("reportNo") String reportNo);
}