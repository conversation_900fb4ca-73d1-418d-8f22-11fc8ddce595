package com.paic.ncbs.claim.model.vo.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.dao.entity.report.LinkManEntity;
import com.paic.ncbs.claim.common.constant.ReportConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(description = "电话报案信息")
public class TelReportVO {

    private String reportAcceptUm;

    /**
     * 出险者现状 对应枚举 InsuredApplyStatusEnum
     */
    @ApiModelProperty(value = "出险者现状")
    private String insuredApplyStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @ApiModelProperty(value = "出险时间")
    private Date accidentDate;

    /**
     * 事故类型 对应枚举 AccidentTypeEnum
     */
    @ApiModelProperty(value = "事故类型（疾病、意外）")
    private String accidentType;

    @ApiModelProperty(value = "是否大灾（Y-是 N-否）")
    private String isHugeAccident;

    /**
     * 出险类型 对应枚举 InsuredApplyTypeEnum
     */
    @ApiModelProperty(value = "出险类型")
    private String insuredApplyType;

    @ApiModelProperty(value = "事故地点(0：境内/1：境外)")
    private String whetherOutSideAccident;

    @ApiModelProperty(value = "出险省份")
    private String accidentProvince;

    @ApiModelProperty(value = "出险城市")
    private String accidentCity;

    @ApiModelProperty(value = "出险县")
    private String accidentCounty;

    @ApiModelProperty(value = "出险区域")
    private String accidentArea;

    @ApiModelProperty(value = "出险国家")
    private String accidentNation;

    @ApiModelProperty(value = "出险区域")
    private String accidentPlace;

    @ApiModelProperty(value = "保单号列表")
    private List<String> policyNos;

    @ApiModelProperty(value = "是否特殊报案")
    private String isSpecialReport = "N";

    @ApiModelProperty(value = "客户名")
    private String clientName;

    @ApiModelProperty(value = "出生日期")
    private String birthday;

    @ApiModelProperty(value = "证件类型")
    private String certificateType;

    @ApiModelProperty(value = "身份证号")
    private String certificateNo;

    @ApiModelProperty(value = "案件类别 1.人伤 2.非人伤 损失类别 6-其他")
    private String caseClass = "6";

    @ApiModelProperty(value = "是否单证齐全（Y/N）")
    private String isSuffice;

    @ApiModelProperty(value = "报案说明")
    private String remark;

    @ApiModelProperty(value = "联系人信息")
    private List<LinkManEntity> linkManList = new ArrayList<>();

    /**
     *报案来源(参考clm_common_parameter.collection_code = REPORT_FROM_TYPE)
     */
    @ApiModelProperty(value = "报案来源")
    private String reportMode = ReportConstant.REPORTMODE_CALL;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @ApiModelProperty(value = "报案时间")
    private Date reportDate = new Date();

    private String reportNo;


}
