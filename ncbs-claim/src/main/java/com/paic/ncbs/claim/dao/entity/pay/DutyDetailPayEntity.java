package com.paic.ncbs.claim.dao.entity.pay;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.common.constant.ModelConsts;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * 责任明细赔付信息
 */
@Getter
@Setter
public class DutyDetailPayEntity extends EntityDTO {
    private static final long serialVersionUID = 4250553689321162055L;
    /** 主键 */
    private String idAhcsDutyDetailPay;
    /** CLMS_DUTY_PAY主键 */
    private String idAhcsDutyPay;
    /** 保单号 */
    private String policyNo;
    /** 赔案号 */
    private String caseNo;
    /** 赔付次数 */
    private Integer caseTimes;
    /** 赔付批次号 */
    private String idAhcsBatch;
    /** 预陪/垫付/追偿 次数 */
    private Integer subTimes;
    /** 险种编码 */
    private String planCode;
    /** 责任编码 */
    private String dutyCode;
    /** 责任明细基本保额 */
    private BigDecimal baseAmountPay;
    /** 赔付类型名称 */
    private String claimType = ModelConsts.CLAIM_TYPE_PAY;
    /** 责任明细代码 */
    private String dutyDetailCode;
    /** 责任明细名称 */
    private String dutyDetailName;
    /** 责任明细类型编码 */
    private String dutyDetailType;
    /** 手动理算金额 */
    private BigDecimal settleAmount;
    /** 自动理算金额 */
    private BigDecimal autoSettleAmount;
    /** 免赔额 */
    private BigDecimal remitAmount;
    /** 赔付比例 */
    private BigDecimal payProportion;
    /** 合理费用(取当前费用类型下的金额,公式：账单金额-自费金额-部分自费-不合理金额-第三方支付金额) */
    private BigDecimal reasonableAmount;
    /** 合理费用类型(1住院，2门诊，3总费用) */
    private String reasonableAmountType;
    /** 治疗天数 -- 新加应该未使用 */
    private BigDecimal treatmentDays;
    /** 免赔天数 */
    private BigDecimal remitDays;
    /** 津贴日额 */
    private BigDecimal allowanceAmount;
    /** 津贴天数 */
    private BigDecimal allowanceDays = BigDecimal.ZERO;
    /** 通融赔付金额 */
    private BigDecimal accommodationAmount;
    /** 协议赔付金额 */
    private BigDecimal protocolAmount;
    /** 自动理算金额记录值,仅用于统计自动理算偏差率 */
    private BigDecimal autoAmountRecord;
    /** 归档时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date archiveTime;
    /** 账单总金额 -- 新加应该未使用 */
    private BigDecimal billAmount;
    /** 自费金额 -- 新加应该未使用 */
    private BigDecimal deductibleAmount;
   /** 不合理费用 -- 新加应该未使用 */
    private BigDecimal immoderateAmount;
    /** 先期给付金额 -- 新加应该未使用 */
    private BigDecimal prepaidAmount;
    /** 损失对象号 -- 新加应该未使用 */
    private String lossObjectNo;
    /** 是否有效(Y：是/N：否) -- 新加应该未使用 */
    private String isEffective;
    /** 伤残比例 */
    private BigDecimal disabilityRate;
    /** 伤残等级 */
    private String disabilityGrade;
    /** 限额比例 */
    private BigDecimal detailLimitAmount;
    /** 赔付方式（1-赔付 4-拒赔 15-合议） */
    private String indemnityMode;
    /** 固定限额 */
    private BigDecimal fixedLimit;
    /** 标的id */
    private String idPlyRiskProperty;

    public void setIndemnityMode(String indemnityMode) {
        this.indemnityMode = indemnityMode;
        if (StringUtils.isNotBlank(indemnityMode)){
            if (indemnityMode.equals(ModelConsts.INDEMNITY_MODE_PROTOCOL)) {
                this.protocolAmount = BigDecimalUtils.isNullBigDecimal(this.settleAmount) ? this.autoSettleAmount : this.settleAmount;
                this.accommodationAmount = BigDecimal.ZERO;
            } else if (indemnityMode.equals(ModelConsts.INDEMNITY_MODE_ACCOMMODATE)) {
                this.accommodationAmount = BigDecimalUtils.isNullBigDecimal(this.settleAmount) ? this.autoSettleAmount : this.settleAmount;
                this.protocolAmount = BigDecimal.ZERO;
            } else if(indemnityMode.equals(ModelConsts.INDEMNITY_MODE_PAY)){
                this.accommodationAmount = BigDecimal.ZERO;
                this.protocolAmount = BigDecimal.ZERO;
            }
        }
    }

    public void setSettleAmount(BigDecimal settleAmount){
        if (settleAmount==null){
            this.settleAmount = null;
        } else {
            this.settleAmount = settleAmount.setScale(2, RoundingMode.HALF_UP);
        }
    }

    public void setAutoSettleAmount(BigDecimal autoSettleAmount){
        if (autoSettleAmount==null){
            this.autoSettleAmount = BigDecimal.ZERO;
        } else {
            this.autoSettleAmount = autoSettleAmount.setScale(2, RoundingMode.HALF_UP);
        }
    }

}
