package com.paic.ncbs.claim.dao.mapper.duty;


import com.paic.ncbs.claim.model.dto.duty.OperationDefineDTO;
import com.paic.ncbs.claim.model.vo.duty.TherapyOperationVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface OperationDefineMapper {


	List<OperationDefineDTO> getOperationDefine();

	List<TherapyOperationVO> getTherapyOperation(TherapyOperationVO therapyOperation);

	String getTherapyOperationByCode(@Param("operationCode") String operationCode,@Param("orgType") String orgType);

}
