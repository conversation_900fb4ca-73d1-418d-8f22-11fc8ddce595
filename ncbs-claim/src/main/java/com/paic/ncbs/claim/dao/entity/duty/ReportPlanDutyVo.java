package com.paic.ncbs.claim.dao.entity.duty;

import com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ReportPlanDutyVo {
    private String idAhcsPolicyPlan;

    private String idAhcsPolicyInfo;

    private String planName;

    private String planCode;

    private String rescueCompany;

    private String isMain;

    private String groupCode;

    private String orgPlanCode;

    private String orgPlanName;

    private BigDecimal applyNum;

    private BigDecimal taxRate;

    private String idPlyRiskGroup;

    private String riskGroupNo;

    private String riskGroupName;

    private String reportNo;

    private String policyNo;

    private List<ReportDutyVo> reportDutyVoList;

}
