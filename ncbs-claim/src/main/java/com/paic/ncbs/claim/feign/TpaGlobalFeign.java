package com.paic.ncbs.claim.feign;

import com.paic.ncbs.claim.feign.fallback.TpaGlobalFallback;
import com.paic.ncbs.claim.model.dto.investigate.TpaGlobalAgentDTO;
import com.paic.ncbs.claim.model.vo.investigate.TpaInvestigateVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Tpa-Global
 */
@Component
@FeignClient(name ="tpa-global", url = "${tpa.globalUrl}" ,fallbackFactory = TpaGlobalFallback.class)
public interface TpaGlobalFeign {
    @PostMapping("/coreProxy/apiEntrance")
    String getExternalDepartmentList(@RequestBody TpaGlobalAgentDTO tpaGlobalAgentDTO);

    @PostMapping("/public/investigate/fainshTask")
    String fainshTask(@RequestBody TpaInvestigateVO tpaInvestigateVO);
}
