package com.paic.ncbs.claim.dao.mapper.accident;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.vo.accident.HugeAccidentMeasureVO;
import com.paic.ncbs.claim.model.vo.accident.MeasureVO;
import com.paic.ncbs.claim.model.dto.accident.HugeAccidentMeasureDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface HugeAccidentMeasureMapper extends BaseDao<HugeAccidentMeasureDTO> {

    List<HugeAccidentMeasureVO> getHugeAccidentMeasureVOsByHugeId(@Param("idAhcsHugeAccidentInfo") String idAhcsHugeAccidentInfo);

    List<MeasureVO> getMeasureMapAll();
}