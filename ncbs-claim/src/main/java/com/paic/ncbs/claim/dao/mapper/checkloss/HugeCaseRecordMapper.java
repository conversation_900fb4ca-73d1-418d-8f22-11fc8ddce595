package com.paic.ncbs.claim.dao.mapper.checkloss;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.checkloss.HugeCaseRecordDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface HugeCaseRecordMapper extends BaseDao<HugeCaseRecordDTO> {

    public int addHugeCaseRecord(HugeCaseRecordDTO hugeCaseRecord);

    public int addHugeCaseRecordList(@Param("list") List<HugeCaseRecordDTO> hugeCaseRecordList);

    public int modifyHugeCaseRecord(HugeCaseRecordDTO hugeCaseRecord);

    public int removeHugeCaseRecordById(@Param("idAhcsHugeCaseRecord") String idAhcsHugeCaseRecord);

//    public int removeHugeCaseRecord(HugeCaseRecordDTO hugeCaseRecord);

    public HugeCaseRecordDTO getHugeCaseRecordById(@Param("idAhcsHugeCaseRecord") String idAhcsHugeCaseRecord);

    public List<HugeCaseRecordDTO> getHugeCaseRecord(HugeCaseRecordDTO hugeCaseRecord);

//	public List<HugeCaseRecordVOForSearch> getHugeCaseRecordInPageByParma(SearchPamarVo pamar);

//	public HugeCaseRecordVO getHugeCaseRecordByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    public int getHugeCaseRecordCount(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

//	public List<String> getReportNoListByParma(SearchPamarVo pamar);

    public int getAcceptInsuranceFlagByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

//	public List<PolicyInfoVOForHugeCase> getPolicyInfoForMail(@Param("reportNo") String reportNo);

}