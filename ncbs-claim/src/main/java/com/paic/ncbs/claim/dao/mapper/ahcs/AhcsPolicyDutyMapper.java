package com.paic.ncbs.claim.dao.mapper.ahcs;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyDetailEntity;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyEntity;
import com.paic.ncbs.claim.dao.entity.duty.ReportDutyVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AhcsPolicyDutyMapper extends BaseDao<AhcsPolicyDutyEntity> {

    int deleteByPrimaryKey(String idAhcsPolicyDuty);

    int insert(AhcsPolicyDutyEntity record);

    int insertSelective(AhcsPolicyDutyEntity record);

    AhcsPolicyDutyEntity selectByPrimaryKey(String idAhcsPolicyDuty);

    int updateByPrimaryKeySelective(AhcsPolicyDutyEntity record);

    int updateByPrimaryKey(AhcsPolicyDutyEntity record);

    int deleteByIdAhcsPolicyPlan(String idAhcsPolicyPlan);

    List<AhcsPolicyDutyEntity> selectByIdAhcsPolicyPlan(String idAhcsPolicyPlan);

    void insertList(@Param("list") List<AhcsPolicyDutyEntity> policyDutyEntities);

    AhcsPolicyDutyEntity getInfoByReportInfo(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo, @Param("planCode") String planCode, @Param("dutyCode") String dutyCode);

    List<AhcsPolicyDutyEntity> getPolicyDutyInfo(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo, @Param("selfCardNo") String selfCardNo, @Param("planCode") String planCode, @Param("dutyCode") String dutyCode);

    void deleteByReportNoAndPolicyNo(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo);

    String getPlanCodeByDuty(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo, @Param("dutyCode") String dutyCode);

    List<ReportDutyVo> getDutyList(@Param("idAhcsPolicyPlan") String idAhcsPolicyPlan);

    List<AhcsPolicyDutyDetailEntity> getDutyDetailList(@Param("idAhcsPolicyDuty") String idAhcsPolicyDuty);
}