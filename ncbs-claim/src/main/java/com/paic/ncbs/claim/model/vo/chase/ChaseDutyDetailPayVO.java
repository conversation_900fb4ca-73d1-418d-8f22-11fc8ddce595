package com.paic.ncbs.claim.model.vo.chase;

import java.math.BigDecimal;

public class ChaseDutyDetailPayVO extends DutyDetailBaseVO{

	private BigDecimal baseAmount;
	

	private BigDecimal historyPay;
	

	private BigDecimal historyChasePay;
	

	private BigDecimal chasePay;

	public BigDecimal getBaseAmount() {
		return baseAmount;
	}

	public void setBaseAmount(BigDecimal baseAmount) {
		this.baseAmount = baseAmount;
	}

	public BigDecimal getHistoryPay() {
		return historyPay;
	}

	public void setHistoryPay(BigDecimal historyPay) {
		this.historyPay = historyPay;
	}

	public BigDecimal getHistoryChasePay() {
		return historyChasePay;
	}

	public void setHistoryChasePay(BigDecimal historyChasePay) {
		this.historyChasePay = historyChasePay;
	}

	public BigDecimal getChasePay() {
		return chasePay;
	}

	public void setChasePay(BigDecimal chasePay) {
		this.chasePay = chasePay;
	}
	
}
