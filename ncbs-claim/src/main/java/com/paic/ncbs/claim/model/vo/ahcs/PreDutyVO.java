package com.paic.ncbs.claim.model.vo.ahcs;

import cn.hutool.db.DaoTemplate;
import com.paic.ncbs.claim.model.dto.prepayinfo.ClmsPolicyPrepayDutyDetailDTO;
import com.paic.ncbs.claim.model.dto.settle.DutyAttributeDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
@Data
public class PreDutyVO {
    /**
     * 保单信息主键
     */
    private String idAhcsPolicyInfo;
    /**
     * 责任表主键
     */
    private String idPolicyDuty;
    private String dutyCode;
    private String dutyName;
    private BigDecimal dutyAmount;
    private BigDecimal dutyFinishPayAmount;
    private BigDecimal dutyMaxPayAmount;
    private BigDecimal dutyPreAmount;
    private Boolean shareAmount = false;
    /**
     * 是否责任共享保额
     */
    private Boolean dutyShareAmount = false;

    private String shareDutyGroup;

    private String planCode;
    private String planName;
    private String policyNo;
    private String caseNo;
    private String departmentCode;
    private String departmentName;
    private String coinsuranceMark;

    /**
     * 责任起始日期
     */
    private Date insuranceBeginDate;

    private String isDutySharedAmount;

    /**
     * 责任止期
     */
    private Date insuranceEndDate;
    /**
     * 责任属性列表
     */
    private List<DutyAttributeDTO> attributes;

    /**
     * 预配责任明细集合
     */
    private List<ClmsPolicyPrepayDutyDetailDTO> prepayDutyDetailDTOList;


}
