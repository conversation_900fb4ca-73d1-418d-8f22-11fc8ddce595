package com.paic.ncbs.claim.dao.entity.duty;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
    * 财产损失表
    */
@Getter
@Setter
@TableName(value = "clms_prop_loss")
public class PropLossEntity implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private String id;

    /**
     * 报案号
     */
    @TableField(value = "report_no")
    private String reportNo;

    /**
     * 赔付次数
     */
    @TableField(value = "case_times")
    private Integer caseTimes;

    /**
     * 应赔付金额
     */
    @TableField(value = "should_pay_amount")
    private BigDecimal shouldPayAmount;

    /**
     * 免赔额
     */
    @TableField(value = "deductible_amount")
    private BigDecimal deductibleAmount;

    /**
     * 免赔率
     */
    @TableField(value = "deductible_rate")
    private BigDecimal deductibleRate;

    /**
     * 理算金额
     */
    @TableField(value = "payable_amount")
    private BigDecimal payableAmount;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 创建人员
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_date")
    private Date createdDate;

    /**
     * 修改人员
     */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_date")
    private Date updatedDate;

    private static final long serialVersionUID = 1L;
}