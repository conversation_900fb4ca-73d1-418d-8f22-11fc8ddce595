package com.paic.ncbs.claim.dao.mapper.sync;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.sync.ClmFileSyncEntity;
import com.paic.ncbs.claim.model.dto.verify.VerifyDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface ClmFileSyncMapper extends BaseDao<ClmFileSyncEntity> {

    List<ClmFileSyncEntity> getCaseList(@Param("number") Integer number);

    void updateClmFileSync(ClmFileSyncEntity clmFileSyncEntity);


}
