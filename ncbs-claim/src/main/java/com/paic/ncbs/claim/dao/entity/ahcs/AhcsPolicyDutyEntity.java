package com.paic.ncbs.claim.dao.entity.ahcs;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.math.BigDecimal;
import java.util.Date;

public class AhcsPolicyDutyEntity extends EntityDTO {

    private static final long serialVersionUID = 5897340774101249328L;

    private String idAhcsPolicyDuty;

    private String idAhcsPolicyPlan;

    private String dutyName;

    private String dutyCode;

    private String dutyDetailtype;

    private BigDecimal dutyAmount;

    private String dutyDesc;

    private String orgDutyCode;

    private String orgDutyName;

    private String isDutySharedAmount;

    private String dutySharedAmountMerge;

    /**
     * 保单起期
     */
    private Date insuranceBeginDate;
    /***
     * 保单止期
     */
    private Date insuranceEndDate;

    public String getDutyDetailtype() {
        return dutyDetailtype;
    }

    public void setDutyDetailtype(String dutyDetailtype) {
        this.dutyDetailtype = dutyDetailtype;
    }

    public String getIdAhcsPolicyDuty() {
        return idAhcsPolicyDuty;
    }

    public void setIdAhcsPolicyDuty(String idAhcsPolicyDuty) {
        this.idAhcsPolicyDuty = idAhcsPolicyDuty == null ? null : idAhcsPolicyDuty.trim();
    }

    public String getIdAhcsPolicyPlan() {
        return idAhcsPolicyPlan;
    }

    public void setIdAhcsPolicyPlan(String idAhcsPolicyPlan) {
        this.idAhcsPolicyPlan = idAhcsPolicyPlan == null ? null : idAhcsPolicyPlan.trim();
    }

    public String getDutyName() {
        return dutyName;
    }

    public void setDutyName(String dutyName) {
        this.dutyName = dutyName == null ? null : dutyName.trim();
    }

    public String getDutyCode() {
        return dutyCode;
    }

    public void setDutyCode(String dutyCode) {
        this.dutyCode = dutyCode == null ? null : dutyCode.trim();
    }

    public BigDecimal getDutyAmount() {
        return dutyAmount;
    }

    public void setDutyAmount(BigDecimal dutyAmount) {
        this.dutyAmount = dutyAmount;
    }

    public String getDutyDesc() {
        return dutyDesc;
    }

    public void setDutyDesc(String dutyDesc) {
        this.dutyDesc = dutyDesc;
    }

    public String getOrgDutyCode() {
        return orgDutyCode;
    }

    public void setOrgDutyCode(String orgDutyCode) {
        this.orgDutyCode = orgDutyCode;
    }

    public String getOrgDutyName() {
        return orgDutyName;
    }

    public void setOrgDutyName(String orgDutyName) {
        this.orgDutyName = orgDutyName;
    }

    public String getIsDutySharedAmount() {
        return isDutySharedAmount;
    }

    public void setIsDutySharedAmount(String isDutySharedAmount) {
        this.isDutySharedAmount = isDutySharedAmount;
    }

    public String getDutySharedAmountMerge() {
        return dutySharedAmountMerge;
    }

    public void setDutySharedAmountMerge(String dutySharedAmountMerge) {
        this.dutySharedAmountMerge = dutySharedAmountMerge;
    }

    public Date getInsuranceBeginDate() {
        return insuranceBeginDate;
    }

    public void setInsuranceBeginDate(Date insuranceBeginDate) {
        this.insuranceBeginDate = insuranceBeginDate;
    }

    public Date getInsuranceEndDate() {
        return insuranceEndDate;
    }

    public void setInsuranceEndDate(Date insuranceEndDate) {
        this.insuranceEndDate = insuranceEndDate;
    }
}