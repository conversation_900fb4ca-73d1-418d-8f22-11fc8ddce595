package com.paic.ncbs.claim.dao.entity.other;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 公共参数信息表
 */
public class CommonParameterEntity extends EntityDTO {

    private static final long serialVersionUID = -4995355544709297366L;

    private String idClmCommonParameter;

    private String departmentCode;

    private String collectionCode;

    private String collectionName;

    private String valueCode;

    private String valueChineseName;

    private String valueChineseAbbrName;

    private String valueEnglishName;

    private String valueEnglishAbbrName;

    private Date effectiveDate;

    private Date invalidateDate;

    private BigDecimal displayNo;

    private String remark;

    public String getIdClmCommonParameter() {
        return idClmCommonParameter;
    }

    public void setIdClmCommonParameter(String idClmCommonParameter) {
        this.idClmCommonParameter = idClmCommonParameter == null ? null : idClmCommonParameter.trim();
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode == null ? null : departmentCode.trim();
    }

    public String getCollectionCode() {
        return collectionCode;
    }

    public void setCollectionCode(String collectionCode) {
        this.collectionCode = collectionCode == null ? null : collectionCode.trim();
    }

    public String getCollectionName() {
        return collectionName;
    }

    public void setCollectionName(String collectionName) {
        this.collectionName = collectionName == null ? null : collectionName.trim();
    }

    public String getValueCode() {
        return valueCode;
    }

    public void setValueCode(String valueCode) {
        this.valueCode = valueCode == null ? null : valueCode.trim();
    }

    public String getValueChineseName() {
        return valueChineseName;
    }

    public void setValueChineseName(String valueChineseName) {
        this.valueChineseName = valueChineseName == null ? null : valueChineseName.trim();
    }

    public String getValueChineseAbbrName() {
        return valueChineseAbbrName;
    }

    public void setValueChineseAbbrName(String valueChineseAbbrName) {
        this.valueChineseAbbrName = valueChineseAbbrName == null ? null : valueChineseAbbrName.trim();
    }

    public String getValueEnglishName() {
        return valueEnglishName;
    }

    public void setValueEnglishName(String valueEnglishName) {
        this.valueEnglishName = valueEnglishName == null ? null : valueEnglishName.trim();
    }

    public String getValueEnglishAbbrName() {
        return valueEnglishAbbrName;
    }

    public void setValueEnglishAbbrName(String valueEnglishAbbrName) {
        this.valueEnglishAbbrName = valueEnglishAbbrName == null ? null : valueEnglishAbbrName.trim();
    }

    public Date getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(Date effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public Date getInvalidateDate() {
        return invalidateDate;
    }

    public void setInvalidateDate(Date invalidateDate) {
        this.invalidateDate = invalidateDate;
    }

    public BigDecimal getDisplayNo() {
        return displayNo;
    }

    public void setDisplayNo(BigDecimal displayNo) {
        this.displayNo = displayNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }
}