package com.paic.ncbs.claim.dao.entity.clms;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 理赔二核保单层核保结论表(ClmsSeconduwPolicyConclusionEntity)实体类
 *
 * <AUTHOR>
 * @since 2023-09-14 14:07:13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("ClmsSeconduwPolicyConclusion实体类")
public class ClmsSeconduwPolicyConclusionEntity implements Serializable {
    private static final long serialVersionUID = 416325142462152420L;
    /**
     *  主键
     */    
    @ApiModelProperty(" 主键")
    private String id;
    /**
     * 理赔二核申请表主键
     */    
    @ApiModelProperty("理赔二核申请表主键")
    private String idClmsSecondUnderwriting;
    /**
     *  报案号
     */    
    @ApiModelProperty(" 报案号")
    private String reportNo;
    /**
     *  赔付次数
     */    
    @ApiModelProperty(" 赔付次数")
    private Integer caseTimes;
    /**
     * 保单号
     */    
    @ApiModelProperty("保单号")
    private String policyNo;
    /**
     * 投保人
     */    
    @ApiModelProperty("投保人")
    private String applyName;
    /**
     * 被保人
     */    
    @ApiModelProperty("被保人")
    private String insuredName;
    /**
     * 产品名称
     */    
    @ApiModelProperty("产品名称")
    private String productName;
    /**
     * 方案名称
     */    
    @ApiModelProperty("方案名称")
    private String schemeName;
    /**
     * 保单起期
     */    
    @ApiModelProperty("保单起期")
    private Date insuranceBeginDate;
    /**
     * 保单止期
     */    
    @ApiModelProperty("保单止期")
    private Date insuranceEndDate;
    /**
     * 保单状态：有效、中止、终止
     */    
    @ApiModelProperty("保单状态：有效、中止、终止")
    private String policyStatus;
    /**
     * 核保决定=核保结论 保单层级：除外、标体，拒保
     */    
    @ApiModelProperty("核保决定=核保结论 保单层级：除外、标体，拒保")
    private String uwConclusion;
    /**
     * 除外事项:核保说明 核保结论为“除外”或者“拒保”时才有核保说明
     */    
    @ApiModelProperty("除外事项:核保说明 核保结论为“除外”或者“拒保”时才有核保说明")
    private String uwExceptions;
    /**
     *  创建人员
     */    
    @ApiModelProperty(" 创建人员")
    private String createdBy;
    /**
     *  创建时间
     */    
    @ApiModelProperty(" 创建时间")
    private Date createdDate;
    /**
     *  修改人员
     */    
    @ApiModelProperty(" 修改人员")
    private String updatedBy;
    /**
     * 修改时间
     */    
    @ApiModelProperty("修改时间")
    private Date updatedDate;


}

