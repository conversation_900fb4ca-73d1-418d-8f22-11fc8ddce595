package com.paic.ncbs.claim.dao.entity.wzemployer;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 微众雇责理赔清单上传记录表
 */
@Data
@TableName(value = "clms_wz_list_Upload_record")
public class WZListUploadRecordEntity implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 序号
     */
    @TableField(value = "serial_no")
    private Integer serialNo;

    /**
     * 上传时间
     */
    @TableField(value = "upload_date")
    private String uploadDate;

    /**
     * 上传文件名称
     */
    @TableField(value = "upload_file_name")
    private String uploadFileName;

    /**
     * 上传文件地址
     */
    @TableField(value = "upload_file_url")
    private String uploadFileUrl;

    /**
     * 上传文件大小
     */
    @TableField(value = "upload_file_size")
    private BigDecimal uploadFileSize;

    /**
     * 创建人员
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "sys_ctime")
    private Date sysCtime;

    /**
     * 修改人员
     */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(value = "sys_utime")
    private Date sysUtime;

    private static final long serialVersionUID = 1L;
}