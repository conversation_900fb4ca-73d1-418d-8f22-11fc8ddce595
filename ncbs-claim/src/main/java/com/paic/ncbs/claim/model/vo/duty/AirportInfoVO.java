package com.paic.ncbs.claim.model.vo.duty;

public class AirportInfoVO{

	private String idAhcsAirportInfo;

	/**
	 * 机场三字码
	 */
	private String airportCode;

	/**
	 * 机场名称
	 */
	private String airportName;

	/**
	 * 城市代码
	 */
	private String cityCode;

	/**
	 * 城市名称
	 */
	private String cityName;

	/**
	 * 省会代码
	 */
	private String provinceCode;

	/**
	 * 省会名称
	 */
	private String provinceName;

	/**
	 * 机场类型(0-国内机场,1国际机场)
	 */
	private String airportType;

	/**
	 * 查询的参数
	 */
	private String queryParam;

	public String getIdAhcsAirportInfo() {
		return idAhcsAirportInfo;
	}

	public void setIdAhcsAirportInfo(String idAhcsAirportInfo) {
		this.idAhcsAirportInfo = idAhcsAirportInfo;
	}

	public String getAirportCode() {
		return airportCode;
	}

	public void setAirportCode(String airportCode) {
		this.airportCode = airportCode;
	}

	public String getAirportName() {
		return airportName;
	}

	public void setAirportName(String airportName) {
		this.airportName = airportName;
	}

	public String getCityCode() {
		return cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}

	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

	public String getProvinceCode() {
		return provinceCode;
	}

	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}

	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}

	public String getAirportType() {
		return airportType;
	}

	public void setAirportType(String airportType) {
		this.airportType = airportType;
	}

	public String getQueryParam() {
		return queryParam;
	}

	public void setQueryParam(String queryParam) {
		this.queryParam = queryParam;
	}

}
