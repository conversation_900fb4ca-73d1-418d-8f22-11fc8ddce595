package com.paic.ncbs.claim.model.vo.ahcs;

import com.paic.ncbs.claim.model.dto.fee.FeePayDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("用于直接理赔费用页面点击保存后页面传入的数据")
public class FeeBigVO {

	@ApiModelProperty("预赔费用信息")
	private List<FeePayDTO> prepayFee;

	@ApiModelProperty("正常费用信息")
	private List<FeePayDTO> normalFee;

	@ApiModelProperty("报案号")
	private String reportNo;

	@ApiModelProperty("赔付次数")
	private Integer caseTimes;

	@ApiModelProperty("PaymentItem表主键")
	private String idClmPaymentItem;

}
