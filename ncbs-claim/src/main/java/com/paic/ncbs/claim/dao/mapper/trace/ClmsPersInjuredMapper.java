package com.paic.ncbs.claim.dao.mapper.trace;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import com.paic.ncbs.claim.model.dto.trace.ClmsPersInjuredDTO;
import com.paic.ncbs.claim.model.dto.trace.ClmsPersInjuredPartDTO;
import com.paic.ncbs.claim.model.vo.trace.ClmsPersInjuredVO;
import com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 *
 * 表clms_pers_injured对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@MapperScan
public interface ClmsPersInjuredMapper extends BaseDao<ClmsPersInjuredDTO> {
    /**
     * 查询人伤跟踪详情信息数据
     * @param personTranceRequestVo
     * @return
     */
    public List<ClmsPersInjuredVO> selectClmsPersInjured(PersonTranceRequestVo personTranceRequestVo);

    /**
     * 修改
     */
    public void updateSelectiveByPrimaryKey(ClmsPersInjuredDTO clmsPersInjuredDTO);

    /**
     * 数据copy插入数据
     */
    public void  copyForCaseReopen(CaseReopenCopyDTO caseReopenCopyDTO);

    /**
     * 删除
     * @param personTranceRequestVo
     */
    public void deleteClmsPersInjured(PersonTranceRequestVo personTranceRequestVo);

}