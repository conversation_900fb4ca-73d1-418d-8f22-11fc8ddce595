package com.paic.ncbs.claim.model.vo.nbs;

import lombok.Data;

import java.util.List;

@Data
public class PageQueryMiniOrderVO {

    /**
     * 总记录数
     */
    private Integer total;
    /**
     * 每页显示条数
     */
    private Integer size;
    /**
     * 当前页码
     */
    private Integer current;
    /**
     * 总页数
     */
    private Integer pages;

    /**
     * 是否执行了总记录数查询
     */
    private Boolean searchCount;

    /**
     * 是否优化了 count sql 查询
     */
    private Boolean optimizeCountSql;

    /**
     * 当前页数据列表
     */
    private List<MiniOrderInfo> records;
}
