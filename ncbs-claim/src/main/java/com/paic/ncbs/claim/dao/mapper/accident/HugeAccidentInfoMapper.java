package com.paic.ncbs.claim.dao.mapper.accident;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.accident.HugeAccidentInfoDTO;
import com.paic.ncbs.claim.model.vo.accident.HugeAccidentInfoVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface HugeAccidentInfoMapper extends BaseDao<HugeAccidentInfoDTO> {

    int addHugeAccidentInfo(HugeAccidentInfoDTO hugeAccidentInfo);

    int modifyHugeAccidentInfo(HugeAccidentInfoDTO hugeAccidentInfo);

    int removeHugeAccidentInfoById(@Param("idAhcsHugeAccidentInfo") String idAhcsHugeAccidentInfo);

    HugeAccidentInfoDTO queryOneByCondition(HugeAccidentInfoDTO hugeAccidentInfo);

    int getHugeAccidentInfoCount(HugeAccidentInfoDTO hugeAccidentInfo);

    List<HugeAccidentInfoVO> getHugeAccidentInfo(HugeAccidentInfoVO hugeAccidentInfo);
}