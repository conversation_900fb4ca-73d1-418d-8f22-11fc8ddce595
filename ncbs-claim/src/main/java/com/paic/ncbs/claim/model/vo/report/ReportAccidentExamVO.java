package com.paic.ncbs.claim.model.vo.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@ApiModel(description = "考试不通过信息")
public class ReportAccidentExamVO {

    @ApiModelProperty(value = "报案号")
    private String reportNo;

    @ApiModelProperty(value = "不通过类型")
    private String[] noPassType;

    @ApiModelProperty(value = "费用预估")
    private BigDecimal costEstimate;

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String[] getNoPassType() {
        return noPassType;
    }

    public void setNoPassType(String[] noPassType) {
        this.noPassType = noPassType;
    }

    public BigDecimal getCostEstimate() {
        return costEstimate;
    }

    public void setCostEstimate(BigDecimal costEstimate) {
        this.costEstimate = costEstimate;
    }
}
