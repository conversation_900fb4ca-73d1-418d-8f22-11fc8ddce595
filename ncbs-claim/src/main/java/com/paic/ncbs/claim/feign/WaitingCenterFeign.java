package com.paic.ncbs.claim.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: justinwu
 * @create 2025/5/21 10:45
 */
@Component
@FeignClient(name = "waitingCenter", url = "${waitingCenter.url:http://ss-waitingcenterservice.lb.sssit.com:40013}")
public interface WaitingCenterFeign {

    /**
     * 发起办事中心审批流-方式二业务+办事中心双向审批
     * @param json
     * @return
     */
    @PostMapping(path = "/waitingcenter/instance/createInstance", consumes = {MediaType.APPLICATION_JSON_VALUE})
    String createInstance(@RequestBody String json);
}
