package com.paic.ncbs.claim.dao.mapper.trace;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceMainDTO;
import com.paic.ncbs.claim.model.dto.trace.ClmsTraceRecordDTO;
import com.paic.ncbs.claim.model.vo.trace.ClmsTraceRecordVO;
import com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 *
 * 表clms_trace_record对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@MapperScan
public interface ClmsTraceRecordMapper extends BaseDao<ClmsTraceRecordDTO> {
    /**
     * 查询人伤记录表信息数据
     * @param personTranceRequestVo
     * @return
     */
    public List<ClmsTraceRecordVO> selectClmsTraceRecord(PersonTranceRequestVo personTranceRequestVo);


    /**
     * 查询跟踪人员信息，并进行拼接
     * @param reportNo
     * @return
     */
    public List<ClmsTraceRecordVO> selectTracePersons(@RequestParam("reportNo") String reportNo);

    /**
     * 查询跟踪人员与案件的处理人是否为同一人
     * @param reportNo
     * @param caseTimes
     * @return
     */
    public TaskInfoDTO selectTaskInfoList(@RequestParam("reportNo") String reportNo, @RequestParam("caseTimes") int caseTimes);

    public List<ClmsTraceRecordDTO> selectPage(ClmsTraceRecordDTO clmsTraceRecordDTO);

    /**
     * 修改
     */
    public void updateSelectiveByPrimaryKey(ClmsTraceRecordDTO clmsTraceRecordDTO);

    /**
     * 数据copy插入数据
     */
    public void  copyForCaseReopen(CaseReopenCopyDTO caseReopenCopyDTO);

    /**
     * 删除
     */
    public void  deleteClmsTraceRecord(PersonTranceRequestVo personTranceRequestVo);

}