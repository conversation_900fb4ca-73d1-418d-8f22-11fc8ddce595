package com.paic.ncbs.claim.model.vo.duty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@ApiModel("考试不通过表VO")
public class ExaminFailVO {

    @ApiModelProperty("")
    private String moduleCode;

    @ApiModelProperty("考试不通过表主键")
    private String idAhcsExaminFail;

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("通道号")
    private String idAhcsChannelProcess;

    @ApiModelProperty("考试不通过类型 : 驾考不通过 EXAMIN_01 , 其他考试不通过 EXAMIN_02, 补考 EXAMIN_03  重新报名 EXAMIN_04")
    private String failType;

    @ApiModelProperty("")
    private String[] failTypeArr;

    @ApiModelProperty("未通过科目")
    private String failSubject;

    @ApiModelProperty("出险类型")
    private String accidentType;

    @ApiModelProperty("定损金额")
    private BigDecimal lossAmount;

    @ApiModelProperty("环节号")
    private String taskCode;

    @ApiModelProperty("状态。1：发送，0：暂存")
    private String status;

    public String getIdAhcsExaminFail() {
        return idAhcsExaminFail;
    }

    public void setIdAhcsExaminFail(String idAhcsExaminFail) {
        this.idAhcsExaminFail = idAhcsExaminFail;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public Integer getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Integer caseTimes) {
        this.caseTimes = caseTimes;
    }

    public String getIdAhcsChannelProcess() {
        return idAhcsChannelProcess;
    }

    public void setIdAhcsChannelProcess(String idAhcsChannelProcess) {
        this.idAhcsChannelProcess = idAhcsChannelProcess;
    }

    public String getFailType() {
        return failType;
    }

    public void setFailType(String failType) {
        this.failType = failType;
    }

    public String getFailSubject() {
        return failSubject;
    }

    public void setFailSubject(String failSubject) {
        this.failSubject = failSubject;
    }

    public String getAccidentType() {
        return accidentType;
    }

    public void setAccidentType(String accidentType) {
        this.accidentType = accidentType;
    }

    public BigDecimal getLossAmount() {
        return lossAmount;
    }

    public void setLossAmount(BigDecimal lossAmount) {
        this.lossAmount = lossAmount;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public String[] getFailTypeArr() {
        return failTypeArr;
    }

    public void setFailTypeArr(String[] failTypeArr) {
        this.failTypeArr = failTypeArr;
    }
}
