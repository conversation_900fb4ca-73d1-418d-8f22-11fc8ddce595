package com.paic.ncbs.claim.model.vo.ahcs;

import java.math.BigDecimal;

public class PaymentClientInfoExVO {

    private String partnerName;

    private String partnerCertificateType;

    private String partnerCertificateNo;

    private String partnerBeginDate;

    private String partnerEndDate;

    private String isCtrlCreditLongTerm;

    private String partnerAddress;

    private String personalType;

    private String beneficiaryBy;

    private String certificateCategory;

    private BigDecimal stockRightsScale;

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getPartnerCertificateType() {
        return partnerCertificateType;
    }

    public void setPartnerCertificateType(String partnerCertificateType) {
        this.partnerCertificateType = partnerCertificateType;
    }

    public String getPartnerCertificateNo() {
        return partnerCertificateNo;
    }

    public void setPartnerCertificateNo(String partnerCertificateNo) {
        this.partnerCertificateNo = partnerCertificateNo;
    }

    public String getPartnerBeginDate() {
        return partnerBeginDate;
    }

    public void setPartnerBeginDate(String partnerBeginDate) {
        this.partnerBeginDate = partnerBeginDate;
    }

    public String getPartnerEndDate() {
        return partnerEndDate;
    }

    public void setPartnerEndDate(String partnerEndDate) {
        this.partnerEndDate = partnerEndDate;
    }

    public String getIsCtrlCreditLongTerm() {
        return isCtrlCreditLongTerm;
    }

    public void setIsCtrlCreditLongTerm(String isCtrlCreditLongTerm) {
        this.isCtrlCreditLongTerm = isCtrlCreditLongTerm;
    }

    public String getPartnerAddress() {
        return partnerAddress;
    }

    public void setPartnerAddress(String partnerAddress) {
        this.partnerAddress = partnerAddress;
    }

    public String getPersonalType() {
        return personalType;
    }

    public void setPersonalType(String personalType) {
        this.personalType = personalType;
    }

    public String getBeneficiaryBy() {
        return beneficiaryBy;
    }

    public void setBeneficiaryBy(String beneficiaryBy) {
        this.beneficiaryBy = beneficiaryBy;
    }

    public BigDecimal getStockRightsScale() {
        return stockRightsScale;
    }

    public void setStockRightsScale(BigDecimal stockRightsScale) {
        this.stockRightsScale = stockRightsScale;
    }

    public String getCertificateCategory() {
        return certificateCategory;
    }

    public void setCertificateCategory(String certificateCategory) {
        this.certificateCategory = certificateCategory;
    }

}
