package com.paic.ncbs.claim.mq.producer;

import com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity;
import com.paic.ncbs.claim.model.dto.mq.claim.ClaimEndCaseReqDto;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;

import java.util.List;

/**
 *      立案注销环节
 */
public interface MqProducerClaimCancelService {
    /**
     * 通过mq同步报案环节信息
     * @param reportNo
     */
    void syncProducerClaimCancelLink(String reportNo, Integer caseTimes);

    List<ClaimEndCaseReqDto> getClaimEndCaseReqDto(List<CaseBaseEntity> caseBaseInfoList, TaskInfoDTO taskInfoDTO, ReportInfoEntity reportInfo);
}
