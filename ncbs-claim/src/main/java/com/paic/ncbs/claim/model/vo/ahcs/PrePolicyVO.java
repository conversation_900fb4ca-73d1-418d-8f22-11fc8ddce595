package com.paic.ncbs.claim.model.vo.ahcs;

import com.paic.ncbs.claim.model.dto.ahcs.PlanTermContentDTO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureDTO;
import com.paic.ncbs.claim.model.dto.settle.SpecialPromiseDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
@Data
public class PrePolicyVO {
    /**
     * 保单信息主键
     */
    private String idAhcsPolicyInfo;
    private String policyNo;
    private String caseNo;
    private String departmentCode;
    private String departmentName;
    private List<PrePlanVO> prePlanList;
    private BigDecimal policyPayAmount;
    private BigDecimal policyFeeAmount;
    private BigDecimal policyTotalAmount;
    private String coinsuranceDesc;
    private String coinsuranceMark;
    private String isFullPay;
    /**
     * 小条款列表
     */
    private List<PlanTermContentDTO>  planTermContentDTOList;
    /**
     * 特别约定
     */
    List<SpecialPromiseDTO>  specialPromiseDTOList;

    /**
     * 共保信息
     */
    private List<CoinsureDTO> coinsuranceList;
}
