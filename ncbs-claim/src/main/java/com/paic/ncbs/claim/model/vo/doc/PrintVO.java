package com.paic.ncbs.claim.model.vo.doc;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

//y
public class PrintVO {


    private static final long serialVersionUID = 2224315132995569937L;
    //案件信息
    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("被保险人")
    private String insuredName;

    @ApiModelProperty("证件号")
    private String certificateNo;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("出险时间/事故日期")
    private Date accidentDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("结案时间")
    private Date endCaseDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("出险时间/事故日期具体")
    private Date accidentDateDetail;


    //理赔情况说明
    @ApiModelProperty("理赔情况说明")
    private String ClaimInfo;


    //理赔信息明细
    @ApiModelProperty("赔付通知书信息列表/理赔信息明细")
    private List<PrintDutyPayVO> endorsementRemarkList;


    @ApiModelProperty("保单号")
    private String policyNos;

    @ApiModelProperty("应付金额合计")
    private BigDecimal shouldPayAmt;
    @ApiModelProperty("预赔金额合计")
    private BigDecimal preparePayAmt;

    @ApiModelProperty("最终赔付金额(不包含费用及注销、拒赔,支付赔款)")
    private BigDecimal realityPayAmt;

    @ApiModelProperty("总账单金额")
    private BigDecimal tolBillAmount;

    @ApiModelProperty("折扣金额？")
    private BigDecimal tolDiscountAmt;

    @ApiModelProperty("总预付金额")
    private BigDecimal tolPrepaidAmount;

    @ApiModelProperty("总合理金额")
    private BigDecimal tolReasonableAmount;

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("承保机构编码")
    private String departmentCode;


    @ApiModelProperty("打印账单信息列表")
    private List<PrintBillInfoVO> feeDiscountList;

    @ApiModelProperty("付款信息列表")
    private List<PrintPayInfoVO> payList;

    @ApiModelProperty("责任赔付信息列表")
    private List<PrintDutyPayInfoVO> dutyPayList;


    @ApiModelProperty("审批说明")
    private String verifyRemark;

    @ApiModelProperty("初核说明")
    private String auditingCommont;

    @ApiModelProperty("批单信息json串")
    private String endorsementRemark;

    @ApiModelProperty("批单")
    private String chsEndorsement;
    @ApiModelProperty("文件类型")
    private String docType;

    @ApiModelProperty("打印次数")
    private Integer printCount;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("打印平台模板名称")
    private String xslFileName;

    public String getXslFileName() {
        return xslFileName;
    }

    public void setXslFileName(String xslFileName) {
        this.xslFileName = xslFileName;
    }

    public Date getAccidentDateDetail() {
        return accidentDateDetail;
    }

    public void setAccidentDateDetail(Date accidentDateDetail) {
        this.accidentDateDetail = accidentDateDetail;
    }

    public Integer getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Integer caseTimes) {
        this.caseTimes = caseTimes;
    }

    public Integer getPrintCount() {
        return printCount;
    }

    public void setPrintCount(Integer printCount) {
        this.printCount = printCount;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getInsuredName() {
        return insuredName;
    }

    public void setInsuredName(String insuredName) {
        this.insuredName = insuredName;
    }

    public String getCertificateNo() {
        return certificateNo;
    }

    public void setCertificateNo(String certificateNo) {
        this.certificateNo = certificateNo;
    }

    public Date getAccidentDate() {
        return accidentDate;
    }

    public void setAccidentDate(Date accidentDate) {
        this.accidentDate = accidentDate;
    }

    public Date getEndCaseDate() {
        return endCaseDate;
    }

    public void setEndCaseDate(Date endCaseDate) {
        this.endCaseDate = endCaseDate;
    }

    public BigDecimal getShouldPayAmt() {
        return shouldPayAmt;
    }

    public void setShouldPayAmt(BigDecimal shouldPayAmt) {
        this.shouldPayAmt = shouldPayAmt;
    }

    public BigDecimal getPreparePayAmt() {
        return preparePayAmt;
    }

    public void setPreparePayAmt(BigDecimal preparePayAmt) {
        this.preparePayAmt = preparePayAmt;
    }

    public BigDecimal getRealityPayAmt() {
        return realityPayAmt;
    }

    public void setRealityPayAmt(BigDecimal realityPayAmt) {
        this.realityPayAmt = realityPayAmt;
    }

    public BigDecimal getTolBillAmount() {
        return tolBillAmount;
    }

    public void setTolBillAmount(BigDecimal tolBillAmount) {
        this.tolBillAmount = tolBillAmount;
    }

    public BigDecimal getTolDiscountAmt() {
        return tolDiscountAmt;
    }

    public void setTolDiscountAmt(BigDecimal tolDiscountAmt) {
        this.tolDiscountAmt = tolDiscountAmt;
    }

    public BigDecimal getTolPrepaidAmount() {
        return tolPrepaidAmount;
    }

    public void setTolPrepaidAmount(BigDecimal tolPrepaidAmount) {
        this.tolPrepaidAmount = tolPrepaidAmount;
    }

    public BigDecimal getTolReasonableAmount() {
        return tolReasonableAmount;
    }

    public void setTolReasonableAmount(BigDecimal tolReasonableAmount) {
        this.tolReasonableAmount = tolReasonableAmount;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public List<PrintDutyPayVO> getEndorsementRemarkList() {
        return endorsementRemarkList;
    }

    public void setEndorsementRemarkList(List<PrintDutyPayVO> endorsementRemarkList) {
        this.endorsementRemarkList = endorsementRemarkList;
    }

    public List<PrintBillInfoVO> getFeeDiscountList() {
        return feeDiscountList;
    }

    public void setFeeDiscountList(List<PrintBillInfoVO> feeDiscountList) {
        this.feeDiscountList = feeDiscountList;
    }

    public List<PrintPayInfoVO> getPayList() {
        return payList;
    }

    public void setPayList(List<PrintPayInfoVO> payList) {
        this.payList = payList;
    }

    public List<PrintDutyPayInfoVO> getDutyPayList() {
        return dutyPayList;
    }

    public void setDutyPayList(List<PrintDutyPayInfoVO> dutyPayList) {
        this.dutyPayList = dutyPayList;
    }

    public String getPolicyNos() {
        return policyNos;
    }

    public void setPolicyNos(String policyNos) {
        this.policyNos = policyNos;
    }

    public String getVerifyRemark() {
        return verifyRemark;
    }

    public void setVerifyRemark(String verifyRemark) {
        this.verifyRemark = verifyRemark;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getEndorsementRemark() {
        return endorsementRemark;
    }

    public void setEndorsementRemark(String endorsementRemark) {
        this.endorsementRemark = endorsementRemark;
    }

    public String getChsEndorsement() {
        return chsEndorsement;
    }

    public void setChsEndorsement(String chsEndorsement) {
        this.chsEndorsement = chsEndorsement;
    }


    public String getDocType() {
        return docType;
    }

    public void setDocType(String docType) {
        this.docType = docType;
    }

    public String getClaimInfo() {
        return ClaimInfo;
    }

    public void setClaimInfo(String claimInfo) {
        ClaimInfo = claimInfo;
    }

    public String getAuditingCommont() {
        return auditingCommont;
    }

    public void setAuditingCommont(String auditingCommont) {
        this.auditingCommont = auditingCommont;
    }
}
