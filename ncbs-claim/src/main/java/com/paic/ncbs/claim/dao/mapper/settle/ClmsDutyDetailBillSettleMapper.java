package com.paic.ncbs.claim.dao.mapper.settle;

import com.paic.ncbs.claim.model.dto.settle.DutyDetailSettleRequest;
import com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.DutyDetailBillSettleDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.RemitAmountDTO;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.List;


@MapperScan
public interface ClmsDutyDetailBillSettleMapper {
    void batchSaveData(List<ClmsDutyDetailBillSettleDTO> list);

    void updateClmsDutyDetailBillSettle(ClmsDutyDetailBillSettleDTO clmsDutyDetailBillSettleDTO);

    List<ClmsDutyDetailBillSettleDTO> getClmsDutyDetailBillSettleList(ClmsDutyDetailBillSettleDTO clmsDutyDetailBillSettleDTO);

    List<ClmsDutyDetailBillSettleDTO> getClmsDutyDetailBillSettleListCase(ClmsDutyDetailBillSettleDTO clmsDutyDetailBillSettleDTO);

    List<ClmsDutyDetailBillSettleDTO> getClmsPayBillSettleList(ClmsDutyDetailBillSettleDTO clmsDutyDetailBillSettleDTO);

    /**
     * 删除
     * @param reportNo
     * @param caseTimes
     */
    void deleteByReportNo(String reportNo, Integer caseTimes);

    List<ClmsDutyDetailBillSettleDTO> getAllInfoByReportNo(ClmsDutyDetailBillSettleDTO dto);

    void updateListById( List<ClmsDutyDetailBillSettleDTO> list);

    List<ClmsDutyDetailBillSettleDTO>  getDutyUsedRemitAmount(RemitAmountDTO remitAmountDTO);

    List<ClmsDutyDetailBillSettleDTO>  getDutyUsedDayRemitAmount(RemitAmountDTO remitAmountDTO);

    List<ClmsDutyDetailBillSettleDTO> getBillSettleInfoByCondition(DutyDetailSettleRequest detail);
    void  updateOneById(ClmsDutyDetailBillSettleDTO dto);

    BigDecimal getClmsDutyDetailByDutyCode(RemitAmountDTO remitAmountDTO);

    List<ClmsDutyDetailBillSettleDTO> getCurrentPolicyDutyBillSettleInfo(String reportNo, Integer caseTimes);
}
