package com.paic.ncbs.claim.dao.entity.checkloss;

import java.math.BigDecimal;
import java.util.Date;

public class BaseRimsOpencoverTreatyEntity {

    private String idRimsOpencoverTreaty;

    private String createdBy;

    private Date createdDate;

    private String updatedBy;

    private Date updatedDate;

    private String treatyNo;

    private String treatyCode;

    private String treatyName;

    private String opencoverType;

    private Date treatyBeginDate;

    private Date treatyEndDate;

    private BigDecimal surplusLimit;

    private String currencyCode;

    private String busiSystemType;

    private String isForBatch;

    private String isUsed;

    private String remark;

    private String treatyStatus;

    private String isEffectiveBefore;

    private String treatyClass;

    private String groupId;

    private String ownType;

    private String ownCurrency;

    private BigDecimal ownAmount;

    private BigDecimal ownAmountLimit;

    private String parentTreatyNo;

    private BigDecimal cededProp;

    private BigDecimal commissionRate;

    private BigDecimal qsCededProp;

    public String getIdRimsOpencoverTreaty() {
        return idRimsOpencoverTreaty;
    }

    public void setIdRimsOpencoverTreaty(String idRimsOpencoverTreaty) {
        this.idRimsOpencoverTreaty = idRimsOpencoverTreaty == null ? null : idRimsOpencoverTreaty.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(Date updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getTreatyNo() {
        return treatyNo;
    }

    public void setTreatyNo(String treatyNo) {
        this.treatyNo = treatyNo == null ? null : treatyNo.trim();
    }

    public String getTreatyCode() {
        return treatyCode;
    }

    public void setTreatyCode(String treatyCode) {
        this.treatyCode = treatyCode == null ? null : treatyCode.trim();
    }

    public String getTreatyName() {
        return treatyName;
    }

    public void setTreatyName(String treatyName) {
        this.treatyName = treatyName == null ? null : treatyName.trim();
    }

    public String getOpencoverType() {
        return opencoverType;
    }

    public void setOpencoverType(String opencoverType) {
        this.opencoverType = opencoverType == null ? null : opencoverType.trim();
    }

    public Date getTreatyBeginDate() {
        return treatyBeginDate;
    }

    public void setTreatyBeginDate(Date treatyBeginDate) {
        this.treatyBeginDate = treatyBeginDate;
    }

    public Date getTreatyEndDate() {
        return treatyEndDate;
    }

    public void setTreatyEndDate(Date treatyEndDate) {
        this.treatyEndDate = treatyEndDate;
    }

    public BigDecimal getSurplusLimit() {
        return surplusLimit;
    }

    public void setSurplusLimit(BigDecimal surplusLimit) {
        this.surplusLimit = surplusLimit;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode == null ? null : currencyCode.trim();
    }

    public String getBusiSystemType() {
        return busiSystemType;
    }

    public void setBusiSystemType(String busiSystemType) {
        this.busiSystemType = busiSystemType == null ? null : busiSystemType.trim();
    }

    public String getIsForBatch() {
        return isForBatch;
    }

    public void setIsForBatch(String isForBatch) {
        this.isForBatch = isForBatch == null ? null : isForBatch.trim();
    }

    public String getIsUsed() {
        return isUsed;
    }

    public void setIsUsed(String isUsed) {
        this.isUsed = isUsed == null ? null : isUsed.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getTreatyStatus() {
        return treatyStatus;
    }

    public void setTreatyStatus(String treatyStatus) {
        this.treatyStatus = treatyStatus == null ? null : treatyStatus.trim();
    }

    public String getIsEffectiveBefore() {
        return isEffectiveBefore;
    }

    public void setIsEffectiveBefore(String isEffectiveBefore) {
        this.isEffectiveBefore = isEffectiveBefore == null ? null : isEffectiveBefore.trim();
    }

    public String getTreatyClass() {
        return treatyClass;
    }

    public void setTreatyClass(String treatyClass) {
        this.treatyClass = treatyClass == null ? null : treatyClass.trim();
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId == null ? null : groupId.trim();
    }

    public String getOwnType() {
        return ownType;
    }

    public void setOwnType(String ownType) {
        this.ownType = ownType == null ? null : ownType.trim();
    }

    public String getOwnCurrency() {
        return ownCurrency;
    }

    public void setOwnCurrency(String ownCurrency) {
        this.ownCurrency = ownCurrency == null ? null : ownCurrency.trim();
    }

    public BigDecimal getOwnAmount() {
        return ownAmount;
    }

    public void setOwnAmount(BigDecimal ownAmount) {
        this.ownAmount = ownAmount;
    }

    public BigDecimal getOwnAmountLimit() {
        return ownAmountLimit;
    }

    public void setOwnAmountLimit(BigDecimal ownAmountLimit) {
        this.ownAmountLimit = ownAmountLimit;
    }

    public String getParentTreatyNo() {
        return parentTreatyNo;
    }

    public void setParentTreatyNo(String parentTreatyNo) {
        this.parentTreatyNo = parentTreatyNo == null ? null : parentTreatyNo.trim();
    }

    public BigDecimal getCededProp() {
        return cededProp;
    }

    public void setCededProp(BigDecimal cededProp) {
        this.cededProp = cededProp;
    }

    public BigDecimal getCommissionRate() {
        return commissionRate;
    }

    public void setCommissionRate(BigDecimal commissionRate) {
        this.commissionRate = commissionRate;
    }

    public BigDecimal getQsCededProp() {
        return qsCededProp;
    }

    public void setQsCededProp(BigDecimal qsCededProp) {
        this.qsCededProp = qsCededProp;
    }
}