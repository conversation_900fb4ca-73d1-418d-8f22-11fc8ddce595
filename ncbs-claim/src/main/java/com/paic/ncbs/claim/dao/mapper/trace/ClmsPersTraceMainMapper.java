package com.paic.ncbs.claim.dao.mapper.trace;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceMainDTO;
import com.paic.ncbs.claim.model.vo.trace.ClmsPersTraceMainVO;
import com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 *
 * 表clms_pers_trace_main对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@MapperScan
public interface ClmsPersTraceMainMapper extends BaseDao<ClmsPersTraceMainDTO> {


    /**
     * 查询主表信息数据
      * @param personTranceRequestVo
     * @return
     */
  public ClmsPersTraceMainVO selectPersonTranceMain(PersonTranceRequestVo personTranceRequestVo);

    /**
     * 修改
     */
    public void updateSelectiveByPrimaryKey(ClmsPersTraceMainDTO clmsPersTraceMainDTO);

    /**
     * 数据copy插入数据
     */
    public void  copyForCaseReopen(CaseReopenCopyDTO caseReopenCopyDTO);

  /**
   * 删除
   */
  public void deletePersonTranceMain(PersonTranceRequestVo personTranceRequestVo);

  /**
   * 定时任务扫描人伤跟踪提醒任务
   */
  public List<ClmsPersTraceMainVO> selectNoticeTask();

  /**
   * 查询方案名称
   */
  ClmsPersTraceMainVO selectRiskGroupName(@Param("reportNo") String reportNo);
}