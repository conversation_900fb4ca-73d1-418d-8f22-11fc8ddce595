package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 意键险其他交通工具延误信息
 */
public class ReportAccidentTrafficEntity extends EntityDTO {

    private static final long serialVersionUID = 1209280916894817538L;

    private String idAhcsReportAccidentTra;

    private String reportNo;

    private String departurePlace;

    private String destination;

    private String isTrafficDelay;

    private String changedPort;

    private String steamerDelayCase;

    private Date originalDepartureDate;

    private Date actualDepartureDate;

    private Date originalArrivalDate;

    private Date actualArrivalDate;

    private BigDecimal costEstimate;

    private String transportation;

    private int delayTime;

    public String getIdAhcsReportAccidentTra() {
        return idAhcsReportAccidentTra;
    }

    public void setIdAhcsReportAccidentTra(String idAhcsReportAccidentTra) {
        this.idAhcsReportAccidentTra = idAhcsReportAccidentTra == null ? null : idAhcsReportAccidentTra.trim();
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String getTransportation() {
        return transportation;
    }

    public void setTransportation(String transportation) {
        this.transportation = transportation;
    }

    public String getDeparturePlace() {
        return departurePlace;
    }

    public void setDeparturePlace(String departurePlace) {
        this.departurePlace = departurePlace == null ? null : departurePlace.trim();
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination == null ? null : destination.trim();
    }

    public String getIsTrafficDelay() {
        return isTrafficDelay;
    }

    public void setIsTrafficDelay(String isTrafficDelay) {
        this.isTrafficDelay = isTrafficDelay == null ? null : isTrafficDelay.trim();
    }

    public String getChangedPort() {
        return changedPort;
    }

    public void setChangedPort(String changedPort) {
        this.changedPort = changedPort == null ? null : changedPort.trim();
    }

    public String getSteamerDelayCase() {
        return steamerDelayCase;
    }

    public void setSteamerDelayCase(String steamerDelayCase) {
        this.steamerDelayCase = steamerDelayCase == null ? null : steamerDelayCase.trim();
    }

    public Date getOriginalDepartureDate() {
        return originalDepartureDate;
    }

    public void setOriginalDepartureDate(Date originalDepartureDate) {
        this.originalDepartureDate = originalDepartureDate;
    }

    public Date getActualDepartureDate() {
        return actualDepartureDate;
    }

    public void setActualDepartureDate(Date actualDepartureDate) {
        this.actualDepartureDate = actualDepartureDate;
    }

    public Date getOriginalArrivalDate() {
        return originalArrivalDate;
    }

    public void setOriginalArrivalDate(Date originalArrivalDate) {
        this.originalArrivalDate = originalArrivalDate;
    }

    public Date getActualArrivalDate() {
        return actualArrivalDate;
    }

    public void setActualArrivalDate(Date actualArrivalDate) {
        this.actualArrivalDate = actualArrivalDate;
    }

    public BigDecimal getCostEstimate() {
        return costEstimate;
    }

    public void setCostEstimate(BigDecimal costEstimate) {
        this.costEstimate = costEstimate;
    }

    public int getDelayTime() {
        return delayTime;
    }

    public void setDelayTime(int delayTime) {
        this.delayTime = delayTime;
    }
}