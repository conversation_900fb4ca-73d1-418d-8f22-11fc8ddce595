package com.paic.ncbs.claim.feign;


import com.paic.ncbs.claim.model.dto.rule.RuleRequestDTO;
import com.paic.ncbs.claim.model.dto.rule.RuleResponseDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@Component
@FeignClient(value = "sam-rule-senter", url = "http://sam-rule-senter.lb.ss${WESURE_ENVIRONMENT:dev}.com:48044")
public interface RuleFeign {

    /** @deprecated */
    @Deprecated
    @PostMapping({"/rule-web/api/v1/rule/fireRule"})
    RuleResponseDTO fireRule(@RequestBody RuleRequestDTO requestDTO);

    @PostMapping({"/rule-web/api/v2/rule/fireRule"})
    RuleResponseDTO fireRules(@RequestParam String requestId, @RequestBody RuleRequestDTO requestDTO);

}
