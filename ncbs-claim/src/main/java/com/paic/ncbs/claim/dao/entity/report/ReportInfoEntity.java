package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 报案信息表
 */
public class ReportInfoEntity extends EntityDTO {

    private static final long serialVersionUID = 6011847388199052639L;

    private String idClmReportInfo;
    /**
     * 报案号
     */
    private String reportNo;
    /**
     *  报案来源(参考 ReportModeEnum)
     */
    private String reportMode;
    /**
     * 报案来源小类
     */
    private String reportSubMode;
    /**
     *  报案时间
     */
    private Date reportDate;
    /**
     *  驾驶员
     */
    private String driverName;
    /**
     *  驾驶员性别 M:男 F女
     */
    private String driveSex;
    /**
     *  驾驶员身份证号码
     */
    private String driveCardId;
    /**
     *  是否有物损(Y：是,N：否)
     */
    private String isCargoLoss;
    /**
     *  是否有车损(Y：是,N：否)
     */
    private String isCarLoss;
    /**
     *  是否有人伤(Y：是,N：否)
     */
    private String isInjured;
    /**
     *  是否现场报案(Y：是,N：否)
     */
    private String reportOnPort;
    /**
     *  是否三者保险公司代报案(Y：是,N：否)
     */
    private String isThirdAgentReport;
    /**
     *  是否代位案件(Y：是,N：否)
     */
    private String isAgentCase;
    /**
     *  报案人
     */
    private String reporterName;
    /**
     *  报案人来电号码
     */
    private String reporterCallNo;

    private String reporterRegisterTel;

    private String reportRegisterUm;

    private String acceptDepartmentCode;

    private String reportOption;

    private Date cancelReportDate;

    private String remark;

    private String migrateFrom;

    private String isWaitCall;

    private String flagSelfService;

    private String repaireFactoryId;

    private String repairFactoryName;

    private String bjAppCaseNumber;

    private BigDecimal reportedLossAmount;

    private String isAcceptDirectPay;

    private String isCommunitySurvey;
    /**
     * 正常报案:1,异常报案:2
     */
    private String reportType;

    /**
     * 是否有风险标识：Y-是，N-否:TPA调用立案接口会传入
     */
    private String riskFlag;
    /**
     * 风险描述备注：是否有风险标识 未Y时必填 TPA调用立案接口时传入
     */
    private String riskRemark;

    @ApiModelProperty("案件录入时长")
    private BigDecimal caseInputDuration;

    @ApiModelProperty("模型识别案件：是/否")
    private String isAIModel;

    public String getIsAIModel() {
        return isAIModel;
    }

    public void setIsAIModel(String isAIModel) {
        this.isAIModel = isAIModel;
    }

    public BigDecimal getCaseInputDuration() {
        return caseInputDuration;
    }

    public void setCaseInputDuration(BigDecimal caseInputDuration) {
        this.caseInputDuration = caseInputDuration;
    }

    public String getReportType() {
        return reportType;
    }

    public void setReportType(String reportType) {
        this.reportType = reportType;
    }

    public String getIdClmReportInfo() {
        return idClmReportInfo;
    }

    public void setIdClmReportInfo(String idClmReportInfo) {
        this.idClmReportInfo = idClmReportInfo == null ? null : idClmReportInfo.trim();
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String getReportMode() {
        return reportMode;
    }

    public void setReportMode(String reportMode) {
        this.reportMode = reportMode == null ? null : reportMode.trim();
    }

    public Date getReportDate() {
        return reportDate;
    }

    public void setReportDate(Date reportDate) {
        this.reportDate = reportDate;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName == null ? null : driverName.trim();
    }

    public String getIsCargoLoss() {
        return isCargoLoss;
    }

    public void setIsCargoLoss(String isCargoLoss) {
        this.isCargoLoss = isCargoLoss == null ? null : isCargoLoss.trim();
    }

    public String getIsCarLoss() {
        return isCarLoss;
    }

    public void setIsCarLoss(String isCarLoss) {
        this.isCarLoss = isCarLoss == null ? null : isCarLoss.trim();
    }

    public String getIsInjured() {
        return isInjured;
    }

    public void setIsInjured(String isInjured) {
        this.isInjured = isInjured == null ? null : isInjured.trim();
    }

    public String getReportOnPort() {
        return reportOnPort;
    }

    public void setReportOnPort(String reportOnPort) {
        this.reportOnPort = reportOnPort == null ? null : reportOnPort.trim();
    }

    public String getIsThirdAgentReport() {
        return isThirdAgentReport;
    }

    public void setIsThirdAgentReport(String isThirdAgentReport) {
        this.isThirdAgentReport = isThirdAgentReport == null ? null : isThirdAgentReport.trim();
    }

    public String getIsAgentCase() {
        return isAgentCase;
    }

    public void setIsAgentCase(String isAgentCase) {
        this.isAgentCase = isAgentCase == null ? null : isAgentCase.trim();
    }

    public String getReporterName() {
        return reporterName;
    }

    public void setReporterName(String reporterName) {
        this.reporterName = reporterName == null ? null : reporterName.trim();
    }

    public String getReporterCallNo() {
        return reporterCallNo;
    }

    public void setReporterCallNo(String reporterCallNo) {
        this.reporterCallNo = reporterCallNo == null ? null : reporterCallNo.trim();
    }

    public String getReporterRegisterTel() {
        return reporterRegisterTel;
    }

    public void setReporterRegisterTel(String reporterRegisterTel) {
        this.reporterRegisterTel = reporterRegisterTel == null ? null : reporterRegisterTel.trim();
    }

    public String getReportRegisterUm() {
        return reportRegisterUm;
    }

    public void setReportRegisterUm(String reportRegisterUm) {
        this.reportRegisterUm = reportRegisterUm == null ? null : reportRegisterUm.trim();
    }

    public String getAcceptDepartmentCode() {
        return acceptDepartmentCode;
    }

    public void setAcceptDepartmentCode(String acceptDepartmentCode) {
        this.acceptDepartmentCode = acceptDepartmentCode == null ? null : acceptDepartmentCode.trim();
    }

    public String getReportOption() {
        return reportOption;
    }

    public void setReportOption(String reportOption) {
        this.reportOption = reportOption == null ? null : reportOption.trim();
    }

    public Date getCancelReportDate() {
        return cancelReportDate;
    }

    public void setCancelReportDate(Date cancelReportDate) {
        this.cancelReportDate = cancelReportDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getMigrateFrom() {
        return migrateFrom;
    }

    public void setMigrateFrom(String migrateFrom) {
        this.migrateFrom = migrateFrom == null ? null : migrateFrom.trim();
    }

    public String getIsWaitCall() {
        return isWaitCall;
    }

    public void setIsWaitCall(String isWaitCall) {
        this.isWaitCall = isWaitCall == null ? null : isWaitCall.trim();
    }

    public String getFlagSelfService() {
        return flagSelfService;
    }

    public void setFlagSelfService(String flagSelfService) {
        this.flagSelfService = flagSelfService == null ? null : flagSelfService.trim();
    }

    public String getRepaireFactoryId() {
        return repaireFactoryId;
    }

    public void setRepaireFactoryId(String repaireFactoryId) {
        this.repaireFactoryId = repaireFactoryId == null ? null : repaireFactoryId.trim();
    }

    public String getRepairFactoryName() {
        return repairFactoryName;
    }

    public void setRepairFactoryName(String repairFactoryName) {
        this.repairFactoryName = repairFactoryName == null ? null : repairFactoryName.trim();
    }

    public String getBjAppCaseNumber() {
        return bjAppCaseNumber;
    }

    public void setBjAppCaseNumber(String bjAppCaseNumber) {
        this.bjAppCaseNumber = bjAppCaseNumber == null ? null : bjAppCaseNumber.trim();
    }

    public BigDecimal getReportedLossAmount() {
        return reportedLossAmount;
    }

    public void setReportedLossAmount(BigDecimal reportedLossAmount) {
        this.reportedLossAmount = reportedLossAmount;
    }

    public String getIsAcceptDirectPay() {
        return isAcceptDirectPay;
    }

    public void setIsAcceptDirectPay(String isAcceptDirectPay) {
        this.isAcceptDirectPay = isAcceptDirectPay == null ? null : isAcceptDirectPay.trim();
    }

    public String getIsCommunitySurvey() {
        return isCommunitySurvey;
    }

    public void setIsCommunitySurvey(String isCommunitySurvey) {
        this.isCommunitySurvey = isCommunitySurvey == null ? null : isCommunitySurvey.trim();
    }

    public String getDriveSex() {
        return driveSex;
    }

    public void setDriveSex(String driveSex) {
        this.driveSex = driveSex;
    }

    public String getDriveCardId() {
        return driveCardId;
    }

    public void setDriveCardId(String driveCardId) {
        this.driveCardId = driveCardId;
    }

    public String getRiskFlag() {
        return riskFlag;
    }

    public void setRiskFlag(String riskFlag) {
        this.riskFlag = riskFlag;
    }

    public String getRiskRemark() {
        return riskRemark;
    }

    public void setRiskRemark(String riskRemark) {
        this.riskRemark = riskRemark;
    }

    public String getReportSubMode() {
        return reportSubMode;
    }

    public void setReportSubMode(String reportSubMode) {
        this.reportSubMode = reportSubMode;
    }
}