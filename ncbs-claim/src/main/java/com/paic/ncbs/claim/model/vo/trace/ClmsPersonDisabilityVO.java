package com.paic.ncbs.claim.model.vo.trace;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel("伤残信息")
@Data
public class ClmsPersonDisabilityVO {

    private static final long serialVersionUID = -6501308037984660087L;
    @ApiModelProperty("主键")
    private String idAhcsPersonDisability;

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private int caseTimes;

    @ApiModelProperty("通道号(AHCS_CHANNEL_PROCESS表主键)")
    private String idAhcsChannelProcess;

    @ApiModelProperty("序号")
    private String disabilityNo;

    @ApiModelProperty("残疾代码")
    private String disabilityCode;

    @ApiModelProperty("残疾名称")
    private String disabilityName;

    @ApiModelProperty("伤残条款")
    private String disabilityClause;
    @ApiModelProperty("评残等级")
    private String disabilityGrade;
    @ApiModelProperty("评残时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date assessmentDate;
    @ApiModelProperty("鉴定情况")
    private String appraisalSituation;
    @ApiModelProperty("鉴定机构")
    private String appraisalDepartment;
    @ApiModelProperty("鉴定人1")
    private String appraiserName;
    @ApiModelProperty("鉴定人2")
    private String appraiserNameSecond;
    @ApiModelProperty("是否合作")
    private String isCooperate;
    @ApiModelProperty("环节号")
    private String taskId;

    @ApiModelProperty("状态。1：发送，0：暂存")
    private String status;

    @ApiModelProperty("归档时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date archiveTime;

    @ApiModelProperty("是否鉴定")
    private String isAppraisal;

    @ApiModelProperty("标准省")
    private String provice;

    @ApiModelProperty("标准市")
    private String city;

    @ApiModelProperty("赔付比例")
    private BigDecimal payRate;

    @ApiModelProperty("是否异地鉴定")
    private String isnoPlaceAppraisal;

    @ApiModelProperty("多处伤残")
    private String disAbilities;

    @ApiModelProperty("鉴定机构证件类型")
    private String agencyIdType;

    @ApiModelProperty("鉴定机构证件号码")
    private String agencyIdNo;

    @ApiModelProperty("鉴定师资格证号码")
    private String agencyCertifyNo;
}
