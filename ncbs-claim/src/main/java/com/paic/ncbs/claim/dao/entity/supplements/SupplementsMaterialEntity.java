package com.paic.ncbs.claim.dao.entity.supplements;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 补材任务记录实体
 */
@Data
public class SupplementsMaterialEntity extends EntityDTO {
    private static final long serialVersionUID = 8819979536678681439L;

    /**
     * 主键
     */
    private String id;
    /**
     *  报案号
     */
    private  String reportNo;
    /**
     *  赔付次数
     */
    private Integer caseTimes;

    /**
     *  补材次数
     */
    private Integer supplementCount;
    /**
     * 业务节点：
     * OC_REPORT_TRACK:报案跟踪
     * OC_CHECK_DUTY：收单
     * OC_MANUAL_SETTLE：理算
     */
    private String taskDefinitionBpmKey;

    /**
     * 补充内容:
     * 补充的材料类型多个用逗号隔开
     * 如：出生证，结婚证，授权委托证明，检验报告，病理报告'
     */
    private String supplementsContent;

    /**
     * 补材任务状态：
     * 补材任务状态：00-补材中，01-已完成',
     */
    private String supplementsState;

    /**
     * 补材说明
     */
    private String supplementsDesc;

    /**
     * 回销说明
     */
    private String remark;
}
