package com.paic.ncbs.claim.model.vo.report;

import java.util.Date;
import java.util.List;

import com.paic.ncbs.claim.common.enums.CertificateTypeEnum;
import com.paic.ncbs.claim.common.enums.InsuredTypeEnum;
import com.paic.ncbs.claim.common.page.Pager;
import com.paic.ncbs.claim.exception.GlobalBusinessException;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;

@Data
public class PolicyQueryVO {
	
	// 被保险人客户类型
	private InsuredTypeEnum insuredType;
	
	private List<String> insuredCertificateTypes;	
	// 被保险人证件类型
    private String certificateType;
    // 被保险人证件号码
    private String certificateNo;
    // 被保险人名称 
    private String insuredName;
    // 保单号
    private String policyNo;
    // 报案号
    private String reportNo;
    // 保单号集合
    private List<String> policyNoList;
    // 投保人名称
    private String applicantName;
    // 事故日期
    private Date accidentDate;
    // 雇员证件类型
    private String staffCertificateType;
    // 雇员证件号码
    private String staffCertificateNo;    
    
    private List<String> departmentCodes;
    
    // 标的姓名
    private String riskSubPropName;  
    
    private boolean isPerson;    

    // 分页
    private Pager pager;    
    
    /**
     * 创建保单信息查询参数
     * 
     */
    public PolicyQueryVO buildPolicyQueryParam() { 
    	if(InsuredTypeEnum.E == this.getInsuredType()) {
            if (StrUtil.isEmpty(this.getInsuredName()) && StrUtil.isEmpty(this.getPolicyNo()) &&StrUtil.isEmpty(this.getStaffCertificateNo())) {
                throw new GlobalBusinessException("被保人名称、保单号、雇员证件号码至少必填一项");
            } 
            if (this.getAccidentDate() == null) {
                throw new GlobalBusinessException("事故日期必填");
            }
            this.setPerson(false);
            this.setInsuredCertificateTypes(CertificateTypeEnum.getEnterpriseTypeList());
     	}else {
            if (StrUtil.isEmpty(this.getCertificateNo()) && StrUtil.isEmpty(this.getPolicyNo())) {
                throw new GlobalBusinessException("被保人证件号码、保单号至少必填一项");
            }
        	this.setInsuredType(InsuredTypeEnum.P);    
        	this.setPerson(true);
        	this.setInsuredCertificateTypes(CertificateTypeEnum.getPersonalTypeList());
//            if (!org.apache.commons.lang3.StringUtils.isEmpty(customerQueryVo.getInsuredName()) && org.apache.commons.lang3.StringUtils.isEmpty(customerQueryVo.getPolicyNo())) {
//                throw new GlobalBusinessException("参数被保险人姓名需和保单号一起使用");
//            }
//            if (!org.apache.commons.lang3.StringUtils.isEmpty(customerQueryVo.getApplicantName()) && org.apache.commons.lang3.StringUtils.isEmpty(customerQueryVo.getPolicyNo())) {
//                throw new GlobalBusinessException("参数投保人姓名需和保单号一起使用");
//            }
    	}  
    	return this;
    } 
    
    
    /**
     * 创建保单被保人信息查询参数
     * 
     * @param reqVO
     */
    public PolicyQueryVO buildPolicyInsuredQueryParam() {
    	if(CollectionUtil.isEmpty(this.getPolicyNoList()))
    		 throw new GlobalBusinessException("保单至少必填一项");    	
    	if(InsuredTypeEnum.E == this.getInsuredType()) {
            if(this.getPolicyNoList().size() > 1)
            	throw new GlobalBusinessException("仅支持单个保单");     
            this.setInsuredCertificateTypes(CertificateTypeEnum.getEnterpriseTypeList());
            this.setPerson(false);
    	}else {
    		if(this.getPolicyNoList().size() > 1) {
    			if (StrUtil.isEmpty(this.getCertificateNo()) && StrUtil.isEmpty(this.getPolicyNo())) {
                    throw new GlobalBusinessException("请输入被保人证件号码查询多保单下被保人");
                }
    		}            
        	this.setInsuredType(InsuredTypeEnum.P); 
        	this.setPerson(true);
        	this.setInsuredCertificateTypes(CertificateTypeEnum.getPersonalTypeList());
    	}  
    	return this;
    } 
    
    
    public PolicyQueryVO buildPolicyRiskSubPropQueryParam() {    	
        if (StrUtil.isEmpty(this.getPolicyNo())) {
            throw new GlobalBusinessException("保单号必填");
        } 
    	return this;
    } 
        
}
