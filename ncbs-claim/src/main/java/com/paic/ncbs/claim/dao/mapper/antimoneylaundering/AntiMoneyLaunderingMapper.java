package com.paic.ncbs.claim.dao.mapper.antimoneylaundering;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsCoinsureEntity;
import com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsAntiMoneyLaunderingInfoDto;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import org.mybatis.spring.annotation.MapperScan;

/**
 * @author:QIANKEGONG513
 * date:2023/5/18
 * version:v0.0.1
 */
@MapperScan
public interface AntiMoneyLaunderingMapper extends BaseDao<AhcsCoinsureEntity> {

    /**
     * 查询反洗钱信息服务接口
     * @param clmsAntiMoneyLaunderingInfoDto
     */
    ClmsAntiMoneyLaunderingInfoDto getClmsAntiMoneyLaunderingInfo(ClmsAntiMoneyLaunderingInfoDto clmsAntiMoneyLaunderingInfoDto);

    /**
     * 增加反洗钱信息服务接口
     * @param clmsAntiMoneyLaunderingInfoDto
     */
    void addClmsAntiMoneyLaunderingInfo(ClmsAntiMoneyLaunderingInfoDto clmsAntiMoneyLaunderingInfoDto);

    /**
     * 删除反洗钱信息服务接口
     * @param clmsAntiMoneyLaunderingInfoDto
     */
    void deleteClmsAntiMoneyLaunderingInfo(ClmsAntiMoneyLaunderingInfoDto clmsAntiMoneyLaunderingInfoDto);

    /**
     * 修改反洗钱信息服务接口
     * @param clmsAntiMoneyLaunderingInfoDto
     */
    void updateClmsAntiMoneyLaunderingInfo(ClmsAntiMoneyLaunderingInfoDto clmsAntiMoneyLaunderingInfoDto);

    /**
     * 历史数据置为失效
     * @param clmsAntiMoneyLaunderingInfoDto
     */
    void invalidClmsAntiMoney(ClmsAntiMoneyLaunderingInfoDto clmsAntiMoneyLaunderingInfoDto);

    /**
     * 根据主键更新反洗钱信息根据
     * @param clmsAntiMoneyLaunderingInfoDto
     */
    void updateClmsAntiMoneyLaunderingInfoById(ClmsAntiMoneyLaunderingInfoDto clmsAntiMoneyLaunderingInfoDto);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);

    /**
     * 根据报案号，赔付次数，客户号删除个人反洗钱数据
     * @param dto
     */
    void deleteClmsAmlInfoByCustomerNo(ClmsAntiMoneyLaunderingInfoDto dto);

    /**
     * 根据主键查询反洗钱信息
     * @param idClmsAntiMoneyLaunderingInfo
     * @return
     */
    ClmsAntiMoneyLaunderingInfoDto getClmsAntiMoneyLaunderingInfoById(String idClmsAntiMoneyLaunderingInfo);

    /**
     * 根据姓名，证件号，证件类型查询反洗钱信息
     * @param parmsDto
     * @return
     */
    ClmsAntiMoneyLaunderingInfoDto getAmlByNameAndCardTypeAndCardNo(ClmsAntiMoneyLaunderingInfoDto parmsDto);

    /**
     * 根据客户号查询反洗钱信息
     * @param parmsDto
     * @return
     */
    ClmsAntiMoneyLaunderingInfoDto getAmlByCustomerNo(ClmsAntiMoneyLaunderingInfoDto parmsDto);
}
