package com.paic.ncbs.claim.dao.mapper.prepay;

import com.paic.ncbs.claim.dao.entity.prepay.ClmsPolicyPrepayDutyDetailEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 预赔责任明细表(ClmsPolicyPrepayDutyDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-12-15 11:01:24
 */
public interface ClmsPolicyPrepayDutyDetailMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ClmsPolicyPrepayDutyDetailEntity queryById(String id);



    /**
     * 新增数据
     *
     * @param clmsPolicyPrepayDutyDetail 实例对象
     * @return 影响行数
     */
    int insert(ClmsPolicyPrepayDutyDetailEntity clmsPolicyPrepayDutyDetail);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<ClmsPolicyPrepayDutyDetail> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(List<ClmsPolicyPrepayDutyDetailEntity> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<ClmsPolicyPrepayDutyDetail> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(List<ClmsPolicyPrepayDutyDetailEntity> entities);

    /**
     * 修改数据
     *
     * @param clmsPolicyPrepayDutyDetail 实例对象
     * @return 影响行数
     */
    int update(ClmsPolicyPrepayDutyDetailEntity clmsPolicyPrepayDutyDetail);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

    /**
     * 过根据报案号，赔付次数，预赔次数查询
     * @param reportNo
     * @param caseTimes
     * @param subTimes
     */
    List<ClmsPolicyPrepayDutyDetailEntity> getDutyDetailInfo(String reportNo, Integer caseTimes, Integer subTimes);

    Integer checkPrePayAmount(@Param("reportNo") String reportNo,@Param("caseTimes") Integer caseTimes);

    List<ClmsPolicyPrepayDutyDetailEntity> selectPrePayAmountAndCode(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);
}

