package com.paic.ncbs.claim.model.vo.chase;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChasePolicyFeeVO extends PolicyBaseVO{
	

	private BigDecimal policyChaseFee = BigDecimal.ZERO;

	private BigDecimal policyAwardFee = BigDecimal.ZERO;

	private List<ChasePlanFeeVO> chasePlanFees;

}
