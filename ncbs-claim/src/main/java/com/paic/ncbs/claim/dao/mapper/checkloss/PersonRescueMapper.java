package com.paic.ncbs.claim.dao.mapper.checkloss;

import com.paic.ncbs.claim.model.dto.duty.PersonRescueDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface PersonRescueMapper {

    public void addPersonRescue(@Param("personRescueList") List<PersonRescueDTO> personRescueList);

    public void removePersonRescue(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("taskId") String taskId);

    public List<PersonRescueDTO> getPersonRescue(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("status") String status, @Param("taskId") String taskId);

    public void addPersonRescueList(@Param("personRescueList") List<PersonRescueDTO> personRescueList,
                                    @Param("caseTimes") int caseTimes, @Param("userId") String userId);

}
