package com.paic.ncbs.claim.model.vo.ahcs;

import com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO;
import com.paic.ncbs.claim.model.dto.prepayinfo.ClmsPolicyPrepayDutyDetailDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PrePayCaseVO {
    private String taskNode;
    private String reportNo;
    private Integer caseTimes;
    private BigDecimal prePayAmount;
    private BigDecimal preFeeAmount;
    private BigDecimal preTotalAmount;
    private List<PrePolicyVO> prePolicyList;
    private List<PrePaymentVO> prePaymentList;
    private List<PrePaymentVO> prePaymentFeeList;


    private String prePayCause;
    private String applyRemark;
    private String idAhcsPrepayInfo;
    private Integer subTimes;

    private List<PaymentInfoDTO> paymentInfoList;
    private List<PaymentInfoDTO> paymentFeeList;

    /**
     * 预配责任明细集合:给理算详情页面返回用
     */
    private List<ClmsPolicyPrepayDutyDetailDTO> settlePrepayDutyDetailDTOList;

    @ApiModelProperty(value = "预赔审批人")
    private String prePayApproved;


}
