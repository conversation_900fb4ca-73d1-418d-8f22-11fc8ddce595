package com.paic.ncbs.claim.dao.entity.clms;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 救援信息表(ClmsRescueInfo)实体类
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:49
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ClmsRescueInfo implements Serializable {
    private static final long serialVersionUID = -83273791140866954L;
    /**
     * 主键
     */
    private String id;
    /**
     * 报案号
     */
    private String reportNo;
    /**
     * 赔付次数
     */
    private Integer caseTimes;
    /**
     * 救援类型
     * 1 紧急医疗运送
     * 2 紧急医疗送返
     * 3 身故遗体送返
     * 4 未成年子女送返
     * 5 旅伴送返
     */
    private String rescueType;
    /**
     * 救援日期
     */
    private Date rescueDate;
    /**
     * 救援机构名称
     */
    private String rescueName;
    /**
     * 救援费用
     */
    private BigDecimal rescueAmount;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人员
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDate;
    /**
     * 修改人员
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDate;

    /**
     * 救援地址
     */
    private String rescueAddress;

}

