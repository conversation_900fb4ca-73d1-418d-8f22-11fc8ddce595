package com.paic.ncbs.claim.dao.mapper.taskdeal;

import com.paic.ncbs.claim.model.dto.taskdeal.ThirdCarDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface ThirdCarMapper {

    public void removeThirdCarList(@Param("idAhcsPersonAccident") String idAhcsPersonAccident);

    public List<ThirdCarDTO> getThirdCarList(@Param("idAhcsPersonAccident") String idAhcsPersonAccident);

    public void addThirdCarList(@Param("thirdCarList") List<ThirdCarDTO> thirdCarList,
                                @Param("userId") String userId, @Param("idAhcsPersonAccident") String idAhcsPersonAccident);

}
