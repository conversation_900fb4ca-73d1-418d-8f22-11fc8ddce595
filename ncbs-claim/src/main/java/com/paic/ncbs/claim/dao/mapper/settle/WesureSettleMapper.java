package com.paic.ncbs.claim.dao.mapper.settle;

import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.settle.*;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.stereotype.Repository;

import java.util.List;

@MapperScan
@Repository
public interface WesureSettleMapper {

    List<WesureBenefitDTO> getClaimedAmount(WesureClaimedDTO wesureClaimedDTO);

    List<WesureReceiptClaimDTO> getReceiptStatus(@Param("receiptClaimList") List<WesureReceiptClaimDTO> receiptClaimList);

    List<WesureReceiptClaimDTO> getWesureSyncReceiptStatus(@Param("receiptClaimList") List<WesureReceiptClaimDTO> receiptClaimList);

    void saveWesureSettle(WesureSettleDTO wesureSettleDTO);

    void saveWesurePolicy(@Param("wesurePolicyList") List<WesurePolicyDTO> wesurePolicyList);

    void saveWesureBenefit(@Param("wesureBenefitList") List<WesureBenefitDTO> wesureBenefitList);

    void saveWesureReceipt(@Param("wesureReceiptList") List<WesureReceiptDetailDTO> wesureReceiptList);

    void removeWesureSettle(WesureSettleDTO wesureSettleDTO);

    WesureSettleDTO getWesureSettle(WesureSettleDTO wesureSettleDTO);

    List<WesurePolicyDTO> getWesurePolicyById(@Param("idClmsWesureSettle") String idClmsWesureSettle);

    List<WesureBenefitDTO> getWesureBenefitById(@Param("idClmsWesurePolicy") String idClmsWesurePolicy);

    List<WesureReceiptDetailDTO> getWesureReceiptById(@Param("idClmsWesureBenefit")String idClmsWesureBenefit);

    List<WesureMedicalDTO> getWesureMedicalList(WesureMedicalDTO wesureMedicalDTO);

    List<WesureMedicalReceiptDTO> getWesureMedicalReceiptById(@Param("idClmsWesureMedical")String idClmsWesureMedical);

    void saveWesureMedical(@Param("wesureMedicalList") List<WesureMedicalDTO> wesureMedicalList);

    void saveWesureMedicalReceipt(@Param("wesureMedicalReceiptList") List<WesureMedicalReceiptDTO> wesureMedicalReceiptList);

    void saveWesureMedicalDisease(@Param("wesureMedicalDiseaseList") List<WesureMedicalDiseaseDTO> wesureMedicalDiseaseList);

    void removeWesureMedical(WesureMedicalDTO wesureMedicalDTO);

    void removeWesureMedicalReceipt(WesureMedicalDTO wesureMedicalDTO);

    void removeWesureMedicalDisease(WesureMedicalDTO wesureMedicalDTO);

    String getWesureAutoClaim(@Param("reportNo") String reportNo);

    List<DutyDetailPayDTO> getDutyDetailPayList(WholeCaseBaseDTO wholeCaseBaseDTO);

    List<DutyDetailPayDTO> getPolicyDutyDetailPayList(WholeCaseBaseDTO wholeCaseBaseDTO);

    String getRefusePayDesc(WholeCaseBaseDTO wholeCaseBaseDTO);

    String getZeroCancelDesc(WholeCaseBaseDTO wholeCaseBaseDTO);

    List<MedicalBillInfoDTO> getBillAmounts(WesureMedicalDTO wesureMedicalDTO);

    List<WesureMedicalDiseaseDTO> getWesureMedicalDisease(WesureMedicalDiseaseDTO diseaseDTO);

}
