package com.paic.ncbs.claim.model.vo.duty;


import com.paic.ncbs.claim.model.dto.duty.PersonDiagnoseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
@ApiModel("诊断信息VO")
public class PersonDiagnoseVO {
	@ApiModelProperty("诊断信息DTO")
	private List<PersonDiagnoseDTO> diagnoseDTOs;

	@ApiModelProperty("")
	private String showFertility;

	@ApiModelProperty("")
	private String moduleCode;
	
	public List<PersonDiagnoseDTO> getDiagnoseDTOs() {
		return diagnoseDTOs;
	}
	
	public void setDiagnoseDTOs(List<PersonDiagnoseDTO> diagnoseDTOs) {
		this.diagnoseDTOs = diagnoseDTOs;
	}
	
	public String getModuleCode() {
		return moduleCode;
	}
	
	public void setModuleCode(String moduleCode) {
		this.moduleCode = moduleCode;
	}

	public String getShowFertility() {
		return showFertility;
	}

	public void setShowFertility(String showFertility) {
		this.showFertility = showFertility;
	}
	
	
}
