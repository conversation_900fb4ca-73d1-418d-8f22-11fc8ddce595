package com.paic.ncbs.claim.model.vo.report;

import com.paic.ncbs.claim.model.dto.ahcs.AhcsPolicyDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel
public class CustomerPolicyVO {

    @ApiModelProperty(value = "保单概要列表")
    private List<AhcsPolicyDetailDTO> ahcsPolicyDetailDTOList;

    @ApiModelProperty(value = "报案客户信息")
    private ReportCustomerInfoVO customerInfo;

    public List<AhcsPolicyDetailDTO> getAhcsPolicyDetailDTOList() {
        return ahcsPolicyDetailDTOList;
    }

    public void setAhcsPolicyDetailDTOList(List<AhcsPolicyDetailDTO> ahcsPolicyDetailDTOList) {
        this.ahcsPolicyDetailDTOList = ahcsPolicyDetailDTOList;
    }

    public ReportCustomerInfoVO getCustomerInfo() {
        return customerInfo;
    }

    public void setCustomerInfo(ReportCustomerInfoVO customerInfo) {
        this.customerInfo = customerInfo;
    }

    @Override
    public String toString() {
        return "CustomerPolicyVO [ahcsPolicyDetailDTOList=" + ahcsPolicyDetailDTOList + ", customerInfo=" + customerInfo
                + "]";
    }
}
