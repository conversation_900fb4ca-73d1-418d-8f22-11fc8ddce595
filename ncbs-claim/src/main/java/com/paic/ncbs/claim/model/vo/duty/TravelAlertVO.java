package com.paic.ncbs.claim.model.vo.duty;


import com.paic.ncbs.claim.model.dto.taskdeal.TravelAlertDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("旅行变更表VO")
public class TravelAlertVO {

    @ApiModelProperty("")

    private String moduleCode;

    @ApiModelProperty("通道号(AHCS_CHANNEL_PROCESS表主键)")
    private String idAhcsChannelProcess;

    @ApiModelProperty("旅行变更表主键")
    private String idAhcsTravelAlert;

    @ApiModelProperty("境内外：0：境内，1：境外")
    private String accidentOverseas;

    @ApiModelProperty("省编码")
    private String provinceCode;

    @ApiModelProperty("出险大洲")
    private String accidentContinentCode;

    @ApiModelProperty("出险城市")
    private String accidentCityCode;

    @ApiModelProperty("出险县")
    private String accidentCountyCode;

    @ApiModelProperty("出险地点")
    private String accidentPlace;

    @ApiModelProperty("状态。1：发送，0：暂存")
    private String status;

    @ApiModelProperty("出险类型列表")
    private List<String> accidentTypeList;

    @ApiModelProperty("旅行变更表DTO")
    private List<TravelAlertDTO> travelAlertlist;

    @ApiModelProperty("出险类型")
    private String accidentType;

    @ApiModelProperty("出发地境内外：0：境内，1：境外")
    private String dAccidentOverseas;

    @ApiModelProperty("出发地省代码")
    private String departPlaceProvinceCode;

    @ApiModelProperty("出发地城市代码")
    private String departPlaceCityCode;

    @ApiModelProperty("出发地区县代码")
    private String departPlaceCountyCode;

    @ApiModelProperty("出发地出险地点")
    private String departPlacePlace;

    @ApiModelProperty("出发地的出险大洲")
    private String dAccidentContinentCode;

    @ApiModelProperty("出发地的出险大洲地点")
    private String dAccidentContinentPlace;

    @ApiModelProperty("目的地境内外：0：境内，1：境外")
    private String aAccidentOverseas;

    @ApiModelProperty("目的地省代码")
    private String arrivalPlaceProvinceCode;

    @ApiModelProperty("目的地城市代码")
    private String arrivalPlaceCityCode;

    @ApiModelProperty("目的地区县代码")
    private String arrivalPlaceCountyCode;

    @ApiModelProperty("目的地出现地点")
    private String arrivalPlacePlace;

    @ApiModelProperty("目的地出险大洲")
    private String aAccidentContinentCode;

    @ApiModelProperty("目的地出险大洲地点")
    private String aAccidentContinentPlace;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("旅行变更发票信息表VO")
    private List<TravelAlertInvoiceVO> travelAlertInvoiceList;

    @ApiModelProperty("出险原因")
    private String accidentReason;

    public List<String> getAccidentTypeList() {
        return accidentTypeList;
    }

    public void setAccidentTypeList(List<String> accidentTypeList) {
        this.accidentTypeList = accidentTypeList;
    }

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public String getIdAhcsChannelProcess() {
        return idAhcsChannelProcess;
    }

    public void setIdAhcsChannelProcess(String idAhcsChannelProcess) {
        this.idAhcsChannelProcess = idAhcsChannelProcess;
    }

    public String getIdAhcsTravelAlert() {
        return idAhcsTravelAlert;
    }

    public void setIdAhcsTravelAlert(String idAhcsTravelAlert) {
        this.idAhcsTravelAlert = idAhcsTravelAlert;
    }

    public String getAccidentOverseas() {
        return accidentOverseas;
    }

    public void setAccidentOverseas(String accidentOverseas) {
        this.accidentOverseas = accidentOverseas;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getAccidentContinentCode() {
        return accidentContinentCode;
    }

    public void setAccidentContinentCode(String accidentContinentCode) {
        this.accidentContinentCode = accidentContinentCode;
    }

    public String getAccidentCityCode() {
        return accidentCityCode;
    }

    public void setAccidentCityCode(String accidentCityCode) {
        this.accidentCityCode = accidentCityCode;
    }

    public String getAccidentCountyCode() {
        return accidentCountyCode;
    }

    public void setAccidentCountyCode(String accidentCountyCode) {
        this.accidentCountyCode = accidentCountyCode;
    }

    public String getAccidentPlace() {
        return accidentPlace;
    }

    public void setAccidentPlace(String accidentPlace) {
        this.accidentPlace = accidentPlace;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<TravelAlertDTO> getTravelAlertlist() {
        return travelAlertlist;
    }

    public void setTravelAlertlist(List<TravelAlertDTO> travelAlertlist) {
        this.travelAlertlist = travelAlertlist;
    }

    public String getAccidentType() {
        return accidentType;
    }

    public void setAccidentType(String accidentType) {
        this.accidentType = accidentType;
    }

    public String getDepartPlaceProvinceCode() {
        return departPlaceProvinceCode;
    }

    public void setDepartPlaceProvinceCode(String departPlaceProvinceCode) {
        this.departPlaceProvinceCode = departPlaceProvinceCode;
    }

    public String getDepartPlaceCityCode() {
        return departPlaceCityCode;
    }

    public void setDepartPlaceCityCode(String departPlaceCityCode) {
        this.departPlaceCityCode = departPlaceCityCode;
    }

    public String getDepartPlaceCountyCode() {
        return departPlaceCountyCode;
    }

    public void setDepartPlaceCountyCode(String departPlaceCountyCode) {
        this.departPlaceCountyCode = departPlaceCountyCode;
    }

    public String getDepartPlacePlace() {
        return departPlacePlace;
    }

    public void setDepartPlacePlace(String departPlacePlace) {
        this.departPlacePlace = departPlacePlace;
    }

    public String getArrivalPlaceProvinceCode() {
        return arrivalPlaceProvinceCode;
    }

    public void setArrivalPlaceProvinceCode(String arrivalPlaceProvinceCode) {
        this.arrivalPlaceProvinceCode = arrivalPlaceProvinceCode;
    }

    public String getArrivalPlaceCityCode() {
        return arrivalPlaceCityCode;
    }

    public void setArrivalPlaceCityCode(String arrivalPlaceCityCode) {
        this.arrivalPlaceCityCode = arrivalPlaceCityCode;
    }

    public String getArrivalPlaceCountyCode() {
        return arrivalPlaceCountyCode;
    }

    public void setArrivalPlaceCountyCode(String arrivalPlaceCountyCode) {
        this.arrivalPlaceCountyCode = arrivalPlaceCountyCode;
    }

    public String getArrivalPlacePlace() {
        return arrivalPlacePlace;
    }

    public void setArrivalPlacePlace(String arrivalPlacePlace) {
        this.arrivalPlacePlace = arrivalPlacePlace;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public List<TravelAlertInvoiceVO> getTravelAlertInvoiceList() {
        return travelAlertInvoiceList;
    }

    public void setTravelAlertInvoiceList(List<TravelAlertInvoiceVO> travelAlertInvoiceList) {
        this.travelAlertInvoiceList = travelAlertInvoiceList;
    }

    public String getAccidentReason() {
        return accidentReason;
    }

    public void setAccidentReason(String accidentReason) {
        this.accidentReason = accidentReason;
    }

    public String getdAccidentOverseas() {
        return dAccidentOverseas;
    }

    public void setdAccidentOverseas(String dAccidentOverseas) {
        this.dAccidentOverseas = dAccidentOverseas;
    }

    public String getdAccidentContinentCode() {
        return dAccidentContinentCode;
    }

    public void setdAccidentContinentCode(String dAccidentContinentCode) {
        this.dAccidentContinentCode = dAccidentContinentCode;
    }

    public String getdAccidentContinentPlace() {
        return dAccidentContinentPlace;
    }

    public void setdAccidentContinentPlace(String dAccidentContinentPlace) {
        this.dAccidentContinentPlace = dAccidentContinentPlace;
    }

    public String getaAccidentOverseas() {
        return aAccidentOverseas;
    }

    public void setaAccidentOverseas(String aAccidentOverseas) {
        this.aAccidentOverseas = aAccidentOverseas;
    }

    public String getaAccidentContinentCode() {
        return aAccidentContinentCode;
    }

    public void setaAccidentContinentCode(String aAccidentContinentCode) {
        this.aAccidentContinentCode = aAccidentContinentCode;
    }

    public String getaAccidentContinentPlace() {
        return aAccidentContinentPlace;
    }

    public void setaAccidentContinentPlace(String aAccidentContinentPlace) {
        this.aAccidentContinentPlace = aAccidentContinentPlace;
    }

}
