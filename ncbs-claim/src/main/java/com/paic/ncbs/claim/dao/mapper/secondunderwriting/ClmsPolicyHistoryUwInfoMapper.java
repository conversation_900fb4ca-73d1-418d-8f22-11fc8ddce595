package com.paic.ncbs.claim.dao.mapper.secondunderwriting;

import com.paic.ncbs.claim.dao.entity.clms.ClmsPolicyHistoryUwInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 理赔保单历史保结信息(ClmsPolicyHistoryUwInfoEntity)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-11-28 15:28:08
 */
@Mapper
public interface ClmsPolicyHistoryUwInfoMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ClmsPolicyHistoryUwInfoEntity queryById(String id);

    /**
     * 新增数据
     *
     * @param clmsPolicyHistoryUwInfoEntity 实例对象
     * @return 影响行数
     */
    int insert(ClmsPolicyHistoryUwInfoEntity clmsPolicyHistoryUwInfoEntity);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<ClmsPolicyHistoryUwInfoEntity> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ClmsPolicyHistoryUwInfoEntity> entities);

    /**
     * 修改数据
     *
     * @param clmsPolicyHistoryUwInfoEntity 实例对象
     * @return 影响行数
     */
    int update(ClmsPolicyHistoryUwInfoEntity clmsPolicyHistoryUwInfoEntity);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

    /**
     * 查询保单历史核保信息
     * @param reportNo
     * @return
     */
    List<ClmsPolicyHistoryUwInfoEntity> getClmsPolicyHistoryUwInfoList(String reportNo);

    /**
     * 根据报案好查询保单险种核保信息
     * @param reportNo
     * @return
     */
    List<ClmsPolicyHistoryUwInfoEntity> getPlanUwPlanConclusionInfo(List<String> list);

    /**
     * 根据保单、方案查询核保信息
     * @param policyNo
     * @param schemeName
     * @return
     */
    List<ClmsPolicyHistoryUwInfoEntity> getPolicyNoUwConclusionInfo(String policyNo, String schemeName);

}

