package com.paic.ncbs.claim.feign.fallback;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.feign.UwFeign;
import com.paic.ncbs.claim.model.vo.senconduw.UwsLetterSellBackDTO;
import com.paic.ncbs.claim.model.vo.senconduw.UwTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 调用UWS降级类
 * <AUTHOR>
 */
@Component
@Slf4j
public class UwFeignFallback implements FallbackFactory<UwFeign> {

     @Override
    public UwFeign create(Throwable cause) {
        return new UwFeign() {

            @Override
            public String createTask(UwTaskVO param) {
                log.error("UwFeign.createTask error, param={}", JSON.toJSONString(param), cause);
                return null;
            }
            @Override
            public String letterSellBack(UwsLetterSellBackDTO uwsLetterSellBackDTO) {
                log.error("UwFeign.letterSellBack error, param={}", JSON.toJSONString(uwsLetterSellBackDTO), cause);
                return null;
            }
        };
    }
}
