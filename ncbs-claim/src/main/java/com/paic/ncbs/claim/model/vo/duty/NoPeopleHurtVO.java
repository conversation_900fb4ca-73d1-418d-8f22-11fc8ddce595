package com.paic.ncbs.claim.model.vo.duty;

import com.paic.ncbs.claim.model.dto.duty.*;
import com.paic.ncbs.claim.model.vo.pet.PetInjureVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("非人伤VO")
public class NoPeopleHurtVO {

    @ApiModelProperty("通道任务类型")
    private String channelType;

    @ApiModelProperty("通道号(AHCS_CHANNEL_PROCESS表主键)")
    private String idAhcsChannelProcess;

    @ApiModelProperty("任务实例ID")
    private String taskId;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("航班延误VO")
    private FlightDelayVO flightDelayVO;

    @ApiModelProperty("行李延误VO")
    private BaggageDelayVO baggageDelayVO;

    @ApiModelProperty("其他公交延误VO")
    private VehiclDelayOtherVO vehiclDelayOtherVO;

    @ApiModelProperty("考试不通过表VO")
    private ExaminFailVO examinFailVO;

    @ApiModelProperty("旅行变更表VO")
    private TravelAlertVO travelAlertVO;

    @ApiModelProperty("财产损失表VO")
    private PropertyLossVO propertyLossVO;

    @ApiModelProperty("其他损失VO")
    private List<OtherLossVO> otherLossVO;

    @ApiModelProperty("个财非人伤表VO列表")
    private List<OtherLossPptDTO> otherLossPptDTOs;

    @ApiModelProperty("宠物损伤VO")
    private PetInjureVO petInjureVO;

    @ApiModelProperty("给付津贴")
    private List<ClmsAllowanceInfoDTO> clmsAllowanceInfoDTO;

    @ApiModelProperty("救援信息")
    private List<ClmsRescueInfoDTO> clmsRescueInfoDTO;

    @ApiModelProperty("个人财产损失信息")
    private List<ClmsSubstanceLossInfoDTO> substanceLossInfoDTOList;

    //现金损失信息
    @ApiModelProperty("现金损失信息")
    private List<ClmsCashLossInfoDTO> clmsCashLossInfoDTO;

    @ApiModelProperty("延误信息")
    private ClmsTravelDelayInfoDTO clmsTravelDelayInfoDTO;

    @ApiModelProperty("人身伤亡信息")
    private ClmsPersonalInjuryDeathInfoDTO clmsPersonalInjuryDeathInfoDTO;

    @ApiModelProperty("第三者财产损失信息")
    private List<ClmsPropertyLossInfoDTO> clmsPropertyLossInfoDTO;

    @ApiModelProperty("法律责任及其他信息")
    private List<ClmsLegalLiabilityClassInfoDTO> clmsLegalLiabilityClassInfoDTO;

    @ApiModelProperty("企业财产损失信息")
    private PropLossDTO propLossDTO;

    @ApiModelProperty("损失标的信息")
    private List<ClmsItemLoss> clmsItemLossVO;

    @ApiModelProperty("货运险信息")
    private ClmsCargoVO clmsCargoVO;

}
