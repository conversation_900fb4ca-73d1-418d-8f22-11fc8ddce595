package com.paic.ncbs.claim.model.vo.duty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@ApiModel("其他损失VO")
public class OtherLossVO {

    @ApiModelProperty("")
    private String moduleCode;

    @ApiModelProperty("其他损失主键")
    private String idAhcsOtherLoss;

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("通道号")
    private String idAhcsChannelProcess;

    @ApiModelProperty("境内外：0：境内，1：境外")
    private String accidentOverseas;

    @ApiModelProperty("出险省份")
    private String provinceCode;

    @ApiModelProperty("出险大洲")
    private String accidentContinentCode;

    @ApiModelProperty("出险城市CODE")
    private String accidentCityCode;

    @ApiModelProperty("出险县")
    private String accidentCountyCode;

    @ApiModelProperty("出险地点")
    private String accidentPlace;

    @ApiModelProperty("出险原因")
    private String accidentReason;

    @ApiModelProperty("损失标的")
    private String lossObj;

    @ApiModelProperty("定损金额")
    private BigDecimal lossAmount;

    @ApiModelProperty("环节号")
    private String taskCode;

    @ApiModelProperty("状态。1：发送，0：暂存")
    private String status;

    @ApiModelProperty("损失描述")
    private String lossDescribe;

    @ApiModelProperty("报损金额")
    private BigDecimal reportLossesAmount;

    @ApiModelProperty("残值")
    private BigDecimal salvageValue;

    @ApiModelProperty("投保比例(百分比)")
    private BigDecimal insuredProportion;

    @ApiModelProperty("备注")
    private String remark;

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public String getIdAhcsOtherLoss() {
        return idAhcsOtherLoss;
    }

    public void setIdAhcsOtherLoss(String idAhcsOtherLoss) {
        this.idAhcsOtherLoss = idAhcsOtherLoss;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getIdAhcsChannelProcess() {
        return idAhcsChannelProcess;
    }

    public void setIdAhcsChannelProcess(String idAhcsChannelProcess) {
        this.idAhcsChannelProcess = idAhcsChannelProcess;
    }

    public Integer getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Integer caseTimes) {
        this.caseTimes = caseTimes;
    }

    public String getAccidentOverseas() {
        return accidentOverseas;
    }

    public void setAccidentOverseas(String accidentOverseas) {
        this.accidentOverseas = accidentOverseas;
    }


    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getAccidentContinentCode() {
        return accidentContinentCode;
    }

    public void setAccidentContinentCode(String accidentContinentCode) {
        this.accidentContinentCode = accidentContinentCode;
    }

    public String getAccidentCityCode() {
        return accidentCityCode;
    }

    public void setAccidentCityCode(String accidentCityCode) {
        this.accidentCityCode = accidentCityCode;
    }

    public String getAccidentCountyCode() {
        return accidentCountyCode;
    }

    public void setAccidentCountyCode(String accidentCountyCode) {
        this.accidentCountyCode = accidentCountyCode;
    }

    public String getAccidentPlace() {
        return accidentPlace;
    }

    public void setAccidentPlace(String accidentPlace) {
        this.accidentPlace = accidentPlace;
    }

    public String getAccidentReason() {
        return accidentReason;
    }

    public void setAccidentReason(String accidentReason) {
        this.accidentReason = accidentReason;
    }

    public String getLossObj() {
        return lossObj;
    }

    public void setLossObj(String lossObj) {
        this.lossObj = lossObj;
    }

    public BigDecimal getLossAmount() {
        return lossAmount;
    }

    public void setLossAmount(BigDecimal lossAmount) {
        this.lossAmount = lossAmount;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getLossDescribe() {
        return lossDescribe;
    }

    public void setLossDescribe(String lossDescribe) {
        this.lossDescribe = lossDescribe;
    }

    public BigDecimal getReportLossesAmount() {
        return reportLossesAmount;
    }

    public void setReportLossesAmount(BigDecimal reportLossesAmount) {
        this.reportLossesAmount = reportLossesAmount;
    }

    public BigDecimal getSalvageValue() {
        return salvageValue;
    }

    public void setSalvageValue(BigDecimal salvageValue) {
        this.salvageValue = salvageValue;
    }

    public BigDecimal getInsuredProportion() {
        return insuredProportion;
    }

    public void setInsuredProportion(BigDecimal insuredProportion) {
        this.insuredProportion = insuredProportion;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
