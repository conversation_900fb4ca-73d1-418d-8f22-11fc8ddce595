package com.paic.ncbs.claim.model.vo.pet;

import com.paic.ncbs.claim.model.dto.pet.PetHospitalDTO;

public class PetHospitalVO {
    private Integer idClmsPetHospital;
    private String hospitalCode;
    private String hospitalName;
    private String provinceCode;
    private String cityCode;
    private String countryCode;
    private String hospitalAccount;
    private String hospitalAddress;
    private String telephone;

    public PetHospitalVO() {
    }

    public PetHospitalVO(PetHospitalDTO dto) {
        this.idClmsPetHospital = dto.getIdClmsPetHospital();
        this.hospitalCode = dto.getHospitalCode();
        this.hospitalName = dto.getHospitalName();
        this.provinceCode = dto.getProvinceCode();
        this.cityCode = dto.getCityCode();
        this.countryCode = dto.getCountryCode();
        this.hospitalAccount = dto.getHospitalAccount();
        this.hospitalAddress = dto.getHospitalAddress();
        this.telephone = dto.getTelephone();
    }

    public Integer getIdClmsPetHospital() {
        return idClmsPetHospital;
    }

    public void setIdClmsPetHospital(Integer idClmsPetHospital) {
        this.idClmsPetHospital = idClmsPetHospital;
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public String getHospitalName() {
        return hospitalName;
    }

    public void setHospitalName(String hospitalName) {
        this.hospitalName = hospitalName;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getHospitalAccount() {
        return hospitalAccount;
    }

    public void setHospitalAccount(String hospitalAccount) {
        this.hospitalAccount = hospitalAccount;
    }

    public String getHospitalAddress() {
        return hospitalAddress;
    }

    public void setHospitalAddress(String hospitalAddress) {
        this.hospitalAddress = hospitalAddress;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }
}
