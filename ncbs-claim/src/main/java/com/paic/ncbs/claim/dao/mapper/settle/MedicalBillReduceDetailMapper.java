package com.paic.ncbs.claim.dao.mapper.settle;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.restartcase.BillCopyDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillReduceDetailDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface MedicalBillReduceDetailMapper extends BaseDao<MedicalBillReduceDetailDTO> {

     void addBillReduceDetailList(List<MedicalBillReduceDetailDTO> billReduceDetailList);

     void addBillReduceDetail(MedicalBillReduceDetailDTO billReduceDetail);
     void addBillReduceDetailAmend(MedicalBillReduceDetailDTO billReduceDetail);

     void modifyBillReduceDetail(MedicalBillReduceDetailDTO billReduceDetail);

     List<MedicalBillReduceDetailDTO> getBillReduceDetailList(@Param("idAhcsBillInfo") String idAhcsBillInfo);

     void removeBillReduceDetailList(@Param("idAhcsBillInfoList") List<String> idAhcsBillInfoList, @Param("userUM") String userUM);

     /**
      * 案件重开，数据拷贝
      * @param paramList
      */
     void copyForCaseReopen(List<BillCopyDTO> paramList);
}
