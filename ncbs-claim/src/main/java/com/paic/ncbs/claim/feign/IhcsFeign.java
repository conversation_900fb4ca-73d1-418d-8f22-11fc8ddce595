package com.paic.ncbs.claim.feign;

import com.paic.ncbs.claim.model.dto.report.ThirdPartyReportDetailQueryReqDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(name = "${ncbs.ihcs.name:ncbs-ihcs}", path = "/ihcs")
public interface IhcsFeign {

    /**
     * 调用退运险小保单查询当日小保单
     */
    @PostMapping("/claim/queryPolicyReportDetailDataList")
    String queryPolicyReportDetailDataList(@RequestBody ThirdPartyReportDetailQueryReqDTO thirdPartyReportDetailQueryReqDTO);


}
