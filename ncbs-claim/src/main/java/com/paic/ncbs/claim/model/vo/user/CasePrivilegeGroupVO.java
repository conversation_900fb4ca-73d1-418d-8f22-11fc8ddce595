package com.paic.ncbs.claim.model.vo.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("CasePrivilegeGroupVO-权限组VO")
public class CasePrivilegeGroupVO {
    @ApiModelProperty("案件类型(02-快赔案件 04-复杂案件 05-自助案件 06-意健险批量案件 07-意健险小额免审件)")
    private String caseType;

    @ApiModelProperty(value = "权限资源细值表VO")
    private List<ResourceDetailValueVO> resDetails;

    @ApiModelProperty(value = "")
    private String resourceCode;

    @ApiModelProperty(value = "")
    private List<String> insuredApplyStatusList;

    @ApiModelProperty(value = "资源明细编码")
    private String resourceDetailCode;

    @ApiModelProperty(value = "")
    private List<String> assignees;

    @ApiModelProperty(value = "")
    private BigDecimal policyPayTotal;

    @ApiModelProperty(value = "承保机构编码")
    private String departmentCode;

    @ApiModelProperty(value = "权限组主键列表")
    private List<String> idPrivilegeGroupList;

    @ApiModelProperty("用户id")
    private String userId;

}
