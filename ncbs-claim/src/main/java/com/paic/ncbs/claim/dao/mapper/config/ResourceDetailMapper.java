package com.paic.ncbs.claim.dao.mapper.config;


import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.config.ResourceDetailDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;


@MapperScan
public interface ResourceDetailMapper extends BaseDao<ResourceDetailDTO> {
	

	//int addResourceDetail(ResourceDetailDTO resourceDetail);
   	

   	int addResourceDetailList(@Param("list") List<ResourceDetailDTO> resourceDetailList);
   	

	//int modifyResourceDetail(ResourceDetailDTO resourceDetail);
	

	int removeResourceDetailById(String idAhcsResourceDetail);


	//int removeResourceDetail(ResourceDetailDTO resourceDetail);
	

	//ResourceDetailDTO getResourceDetailById(String idAhcsResourceDetail);
	

	List<ResourceDetailDTO> getByResourceCode(@Param("resourceCode")String resourceCode);
	
	

	//List<ResourceDetailDTO> getResourceDetail(ResourceDetailDTO resourceDetail);
	
}