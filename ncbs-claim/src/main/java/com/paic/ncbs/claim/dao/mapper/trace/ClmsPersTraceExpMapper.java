package com.paic.ncbs.claim.dao.mapper.trace;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceExpDTO;
import com.paic.ncbs.claim.model.dto.trace.ClmsPersTraceFeeDTO;
import com.paic.ncbs.claim.model.vo.trace.ClmsPersTraceExpVO;
import com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 *
 * 表clms_pers_trace_exp对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@MapperScan
public interface ClmsPersTraceExpMapper  extends BaseDao<ClmsPersTraceExpDTO> {
    /**
     * 查询人伤损失信息数据
     * @param personTranceRequestVo
     * @return
     */
    public List<ClmsPersTraceExpVO> selectClmsPersTraceExp(PersonTranceRequestVo personTranceRequestVo);

    /**
     * 修改
     */
    public void updateSelectiveByPrimaryKey(ClmsPersTraceExpDTO clmsPersTraceExpDTO);

    /**
     * 数据copy插入数据
     */
    public void  copyForCaseReopen(CaseReopenCopyDTO caseReopenCopyDTO);

    /**
     * 保存
     * @param clmsPersTraceExpDTO
     * @return
     */
    public int savePersTraceExp(ClmsPersTraceExpDTO clmsPersTraceExpDTO);

    /**
     * 删除
     * @param personTranceRequestVo
     */
    public void deleteClmsPersTraceExp(PersonTranceRequestVo personTranceRequestVo);
}