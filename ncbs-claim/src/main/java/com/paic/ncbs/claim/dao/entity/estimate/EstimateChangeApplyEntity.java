package com.paic.ncbs.claim.dao.entity.estimate;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 未决修正审批信息表
 */
@Data
@TableName(value = "clms_estimate_change_apply")
public class EstimateChangeApplyEntity implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private String id;

    /**
     * 报案号
     */
    @TableField(value = "report_no")
    private String reportNo;

    /**
     * 赔付次数
     */
    @TableField(value = "case_times")
    private Integer caseTimes;

    /**
     * 申请人UM
     */
    @TableField(value = "apply_um")
    private String applyUm;

    /**
     * 发起时间
     */
    @TableField(value = "apply_date")
    private Date applyDate;

    /**
     * 发起次数
     */
    @TableField(value = "apply_times")
    private Integer applyTimes;

    /**
     * 申请说明
     */
    @TableField(value = "apply_remark")
    private String applyRemark;

    /**
     * 审批人UM
     */
    @TableField(value = "audit_um")
    private String auditUm;

    /**
     * 审批意见, 1-同意，2-不同意, 3-失效
     */
    @TableField(value = "audit_opinion")
    private String auditOpinion;

    /**
     * 审批时间
     */
    @TableField(value = "audit_date")
    private Date auditDate;

    /**
     * 审批状态,1-审批中,2-审批结束
     */
    @TableField(value = "audit_status")
    private String auditStatus;

    /**
     * 审批说明
     */
    @TableField(value = "audit_remark")
    private String auditRemark;

    /**
     * 归档时间
     */
    @TableField(value = "archive_time")
    private Date archiveTime;

    /**
     * 申请金额
     */
    @TableField(value = "apply_amount")
    private BigDecimal applyAmount;

    /**
     * 申请参数
     */
    @TableField(value = "apply_param")
    private String applyParam;

    /**
     * 创建人员
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_date")
    private Date createdDate;

    /**
     * 修改人员
     */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_date")
    private Date updatedDate;

    private static final long serialVersionUID = 1L;
}