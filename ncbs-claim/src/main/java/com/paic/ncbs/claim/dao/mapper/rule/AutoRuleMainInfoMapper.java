package com.paic.ncbs.claim.dao.mapper.rule;

import com.paic.ncbs.claim.dao.entity.rule.AutoRuleMainInfoEntity;

import java.util.List;

public interface AutoRuleMainInfoMapper {
    int deleteByPrimaryKey(String idAutoRuleMain);

    int insert(AutoRuleMainInfoEntity record);

    int insertSelective(AutoRuleMainInfoEntity record);

    AutoRuleMainInfoEntity selectByPrimaryKey(String idAutoRuleMain);

    List<AutoRuleMainInfoEntity> selectByCondition(AutoRuleMainInfoEntity autoRuleMainInfo);

    int updateByPrimaryKeySelective(AutoRuleMainInfoEntity record);

    int updateByPrimaryKey(AutoRuleMainInfoEntity record);
}