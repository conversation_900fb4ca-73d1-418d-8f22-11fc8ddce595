package com.paic.ncbs.claim.dao.mapper.other;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.duty.SurveyDTO;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import com.paic.ncbs.claim.model.vo.duty.SurveyVO;
import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface SurveyMapper extends BaseDao<SurveyDTO> {
	public void addSurvey(SurveyDTO surveyDTO);

	public void modifySurvey(SurveyDTO surveyDTO);

	public SurveyDTO getSurvey(SurveyDTO surveyDTO);

	/**
	 * 案件重开，数据拷贝
	 * @param dto
	 */
	void copyForCaseReopen(CaseReopenCopyDTO dto);

	SurveyVO getsureyDeatalInfo(String reportNo, String taskId, String status);
}
