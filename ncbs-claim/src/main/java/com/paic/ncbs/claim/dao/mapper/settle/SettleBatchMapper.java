package com.paic.ncbs.claim.dao.mapper.settle;

import com.paic.ncbs.claim.model.dto.settle.SettleBatchInfoDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface SettleBatchMapper {
     
	SettleBatchInfoDTO getSettleAmountsSum(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes);

	SettleBatchInfoDTO getAcmAndProAmountSum(@Param("reportNo")String reportNo,@Param("caseTimes")Integer caseTimes);

	SettleBatchInfoDTO getPartSettleSum(@Param("reportNo")String reportNo,@Param("caseTimes")Integer caseTimes,@Param("caseNo")String caseNo);

	SettleBatchInfoDTO getPartAcmAndProSum(@Param("reportNo")String reportNo,@Param("caseTimes")Integer caseTimes,@Param("caseNo")String caseNo);

	String getSettleUser(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes);

	String getSettleReviewRemark(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes);
}
