package com.paic.ncbs.claim.dao.mapper.casezero;


import com.paic.ncbs.claim.model.vo.endcase.ZeroRemitAmountDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;


@MapperScan
public interface ZeroRemitAmountMapper {
	



	public List<ZeroRemitAmountDTO> getZeroRemitAmountByIdZeroCancel(@Param("idAhcsZeroCancelApply") String idAhcsZeroCancelApply);
	

	public void removeRemitAmountByIdZeroCancel(@Param("idAhcsZeroCancelApply") String idAhcsZeroCancelApply);


	public List<String> getCaseNoByPolicyNo(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo);

	public void addZeroRemitAmount(ZeroRemitAmountDTO zeroRemitAmountDTO);
}
