package com.paic.ncbs.claim.model.vo.batch;

import com.paic.ncbs.claim.model.dto.ahcs.AhcsDomainDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "退运险线上批量报案对象")
public class OnlineBatchAutoReportDTO {

    @ApiModelProperty(value = "批量结案主对象")
    private OnlineBatchAutoClose onlineBatchAutoClose;

    @ApiModelProperty(value = "抄单信息")
    private AhcsDomainDTO ahcsDomainDTO;

}
