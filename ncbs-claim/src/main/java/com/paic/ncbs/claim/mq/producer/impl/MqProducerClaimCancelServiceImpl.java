package com.paic.ncbs.claim.mq.producer.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.enums.ReportModeEnum;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.report.*;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoExMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyPayMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyFormDTO;
import com.paic.ncbs.claim.model.dto.mq.ClaimCancelDto;
import com.paic.ncbs.claim.model.dto.mq.claim.ClaimEndCaseReqDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLClaimDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLClaimLossDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLClaimLossTraceDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLLTextDto;
import com.paic.ncbs.claim.model.dto.mq.swf.SwfFlowMainDto;
import com.paic.ncbs.claim.model.dto.mq.swf.SwfLogStoreDto;
import com.paic.ncbs.claim.model.dto.report.ReportBaseInfoResData;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.duty.SurveyVO;
import com.paic.ncbs.claim.mq.producer.MqProducerClaimCancelService;
import com.paic.ncbs.claim.mq.producer.MqProducerClaimService;
import com.paic.ncbs.claim.service.duty.DutySurveyService;
import com.paic.ncbs.claim.service.endcase.CaseBaseService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.endcase.WholeCaseService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.report.ReportAccidentExService;
import com.paic.ncbs.claim.service.report.ReportAccidentService;
import com.paic.ncbs.claim.service.report.ReportCustomerInfoService;
import com.paic.ncbs.claim.service.report.ReportInfoService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

//import org.springframework.amqp.rabbit.core.RabbitTemplate;

@Service
@Slf4j
public class MqProducerClaimCancelServiceImpl implements MqProducerClaimCancelService {
    @Value("${mq.rabbit.exchange.ncbs.claim.root:ncbsClaimExchange}")
    private String ncbsClaimExchange;
    //立案注销环节topic
    @Value("${mq.claimCancel.topic}")
    private String claimCancelTopic;
    @Autowired
    private ReportCustomerInfoService reportCustomerInfoService;
    @Autowired
    private ReportAccidentService reportAccidentService;
    @Autowired
    private ReportAccidentExService reportAccidentExService;
    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;
    @Autowired
    private ReportInfoService reportInfoService;
    @Autowired
    private ReportInfoExMapper reportInfoExMapper;
    @Autowired
    private PolicyInfoMapper policyInfoMapper;
    /*@Autowired
    private RabbitTemplate rabbitTemplate;*/
    @Autowired
    private OcasMapper ocasMapper;
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private DepartmentDefineMapper departmentDefineMapper;
    @Autowired
    private DutySurveyService dutySurveyService;

    @Autowired
    private MqProducerClaimService mqProducerClaimService;

    @Autowired
    private WholeCaseService wholeCaseService;

    @Autowired
    private CaseBaseService caseBaseService;

    @Autowired
    private EstimateService estimateService;

    @Autowired
    private PolicyPayMapper policyPayDao;

    @Override
    @Async("asyncPool")
    public void syncProducerClaimCancelLink(String reportNo, Integer caseTimes) {
        // 数据组装
        ClaimCancelDto claimCancelDto = initProducerClaimCancelDto(reportNo, caseTimes);
        //发送MQ消息
        JSONObject jsonObj = (JSONObject) JSON.toJSON(claimCancelDto);
        log.info("syncProducerClaimCancelLink===sendMessage: {}", jsonObj);
      //   rabbitTemplate.convertAndSend(ncbsClaimExchange, claimCancelTopic, claimCancelDto);
    }

    @SneakyThrows
    private ClaimCancelDto initProducerClaimCancelDto(String reportNo, Integer caseTimes) {
        ReportInfoEntity reportInfo = reportInfoService.getReportInfo(reportNo);
        //报案方式
        reportInfo.setReportMode(ReportModeEnum.getName(reportInfo.getReportMode() == null ? "2" : reportInfo.getReportMode()));
        //查询 报案客户 被保险人 信息表
        ReportCustomerInfoEntity customerInfo = reportCustomerInfoService.getReportCustomerInfoByReportNo(reportNo);
        //查询 意键险报案信息扩展表
        ReportAccidentExEntity reportAccidentEx = reportAccidentExService.getReportAccidentEx(reportNo);
        //查询 事故信息表
        ReportAccidentEntity reportAccident = reportAccidentService.getReportAccident(reportNo);

        //责任明细
        List<PolicyPayDTO> policys = policyPayDao.selectFromPolicyCopy(reportNo, caseTimes);
        SettleHelper.setPolicyPays(policys,null);
        LogUtil.info("initClaimDto-policys：{}",JSON.toJSONString(policys));
        EstimatePolicyFormDTO result  = estimateService.getEstimateDataByTache(reportNo, caseTimes, EstimateUtil.ESTIMATE_TYPE_REGISTRATION, null);
        List<EstimatePolicyDTO> policyList = result.getEstimatePolicyList();
        LogUtil.info("initClaimDto-policyList：{}",policyList);

        WholeCaseBaseDTO wholeCaseBaseEntity = wholeCaseBaseService.getWholeCaseBase(reportNo,caseTimes);
        List<ReportInfoExEntity> reportInfoExEntityList = reportInfoExMapper.getReportInfoEx(reportNo);

        //查询保单信息表
        List<PolicyInfoDTO> policyInfoDTOList = policyInfoMapper.getPolicyInfoListByReportNo(reportNo);
        PolicyInfoDTO policyInfoDTO = policyInfoDTOList.get(0);
        ReportInfoExEntity reportInfoExEntity = reportInfoExEntityList.get(0);
        List<CaseBaseEntity> caseBaseInfoList = caseBaseService.getCaseBaseInfoByReportNoAndCasetimes(reportNo, String.valueOf(caseTimes));

        ClaimCancelDto claimCancelDto = new ClaimCancelDto();
        ReportBaseInfoResData reportBaseInfo = wholeCaseService.getReportBaseInfo(reportNo, caseTimes);
        TaskInfoDTO taskInfoDTO = taskInfoMapper.findLatestByReportNoAndBpmKey(reportNo, caseTimes, BpmConstants.OC_REJECT_REVIEW);
        if (taskInfoDTO == null){
            taskInfoDTO = taskInfoMapper.findLatestByReportNoAndBpmKey(reportNo, caseTimes, BpmConstants.OC_ZERO_CANCEL_DEPT_AUDIT);
        }
        // 立案基本信息表
        List<PrpLClaimDto> prpLClaimDtos = mqProducerClaimService.initPrpLClaimDto("claimCancel", reportInfo, customerInfo, reportAccident, policyInfoDTOList, wholeCaseBaseEntity, reportInfoExEntity, taskInfoDTO, reportAccidentEx, reportBaseInfo);
        claimCancelDto.setPrpLClaimDtoList(prpLClaimDtos);
        //立案文字表
        List<PrpLLTextDto> prpLLTextDtoList = initPrpLLTextDto(wholeCaseBaseEntity,caseBaseInfoList);
        claimCancelDto.setPrpLLTextDtoList(prpLLTextDtoList);
        // 理赔工作流转储表
        List<SwfLogStoreDto> swfLogStoreDtoList=new ArrayList<>();
        for(CaseBaseEntity caseBaseEntity:caseBaseInfoList){
            SwfLogStoreDto swfLogStoreDto=new SwfLogStoreDto();
            swfLogStoreDto.setLogNo(2);
            swfLogStoreDto.setModelNo(12);
            swfLogStoreDto.setNodeName("注销/拒赔");
            swfLogStoreDto.setBusinessNo(caseBaseEntity.getRegistNo());
            swfLogStoreDto.setHandleDept(taskInfoDTO.getDepartmentCode());
            swfLogStoreDto.setHandlerCode(taskInfoDTO.getAssigner());
            swfLogStoreDto.setHandlerName(taskInfoDTO.getAssigneeName());
            swfLogStoreDto.setFlowInTime(DateUtils.parseToFormatString(new Date(),DateUtils.FULL_DATE_STR));
            swfLogStoreDto.setTimeLimit(0);
            swfLogStoreDto.setHandleTime(DateUtils.parseToFormatString(new Date(),DateUtils.FULL_DATE_STR));
            swfLogStoreDto.setSubmitTime(DateUtils.parseToFormatString(new Date(),DateUtils.FULL_DATE_STR));
            swfLogStoreDto.setPackageID("0");
            swfLogStoreDto.setTaskNo(0);
            swfLogStoreDto.setNodeType("cance");
            swfLogStoreDto.setTitleStr("");
            swfLogStoreDto.setBusinessType("1");
            Map<String, String> productMap = ocasMapper.getPlyBaseInfo(caseBaseEntity.getPolicyNo());
            String productCode = MapUtils.getString(productMap,"productCode");
            swfLogStoreDto.setRiskCode(CodeUtil.subStringRiskCode(productCode));
            swfLogStoreDto.setKeyIn(reportNo);
            swfLogStoreDto.setKeyOut(reportNo);
            swfLogStoreDto.setDeptName(departmentDefineMapper.queryDepartmentNameByDeptCode(reportInfo.getAcceptDepartmentCode()));
            swfLogStoreDto.setMainFlowID("0");
            swfLogStoreDto.setSubFlowID("0");
            swfLogStoreDto.setPosX(0);
            swfLogStoreDto.setPosY(0);
            swfLogStoreDto.setEndFlag("1");
            swfLogStoreDto.setBeforeHandlerCode("");
            swfLogStoreDto.setBeforeHandlerName("");
            swfLogStoreDto.setPolicyNo(caseBaseEntity.getPolicyNo());
            swfLogStoreDto.setComCode(reportInfo.getAcceptDepartmentCode());
            swfLogStoreDto.setRegistNo(reportNo);
            swfLogStoreDto.setInsuredName(customerInfo.getName());
            swfLogStoreDto.setEntrustFlag("1");
            swfLogStoreDto.setEntrustNodeStatus("1");
            swfLogStoreDto.setDamageEndDate(new Date());
            swfLogStoreDto.setDamageStartDate(new Date());
            swfLogStoreDto.setUpdateTime(new Date());

            swfLogStoreDto.setFlowID("0");
            swfLogStoreDto.setNodeStatus("4");
            swfLogStoreDto.setNodeNo(7);
            swfLogStoreDto.setFlowStatus("0");
            swfLogStoreDtoList.add(swfLogStoreDto);
        }
        claimCancelDto.setSwfLogStoreDtoList(swfLogStoreDtoList);
        // 立案险别估损金额表
        List<PrpLClaimLossDto> prpLClaimLossDtoList = mqProducerClaimService.initPrpLClaimLossDto(reportInfo, wholeCaseBaseEntity, policyInfoDTOList,customerInfo,policys,  policyList);
        claimCancelDto.setPrpLClaimLossDtoList(prpLClaimLossDtoList);
        // 立案险别估损金额轨迹信息表
        List<PrpLClaimLossTraceDto> prpLClaimLossTraceDtoList = mqProducerClaimService.initPrpLClaimLossTraceDto(policyInfoDTOList, reportInfo,wholeCaseBaseEntity,customerInfo,policys,  policyList);
        claimCancelDto.setPrpLClaimLossTraceDtoList(prpLClaimLossTraceDtoList);
        List<ClaimEndCaseReqDto> claimEndCaseReqDtos = getClaimEndCaseReqDto(caseBaseInfoList,taskInfoDTO,reportInfo);
        claimCancelDto.setClaimEndCaseReqDtoList(claimEndCaseReqDtos);

        SwfFlowMainDto swfFlowMainDto = getSwfFlowMainDto(reportNo, reportInfo, policyInfoDTO);
        claimCancelDto.setSwfFlowMainDto(swfFlowMainDto);
        return claimCancelDto;
    }

    /**
     * 类型：0-结案、注销、拒赔;1-重开赔案
     * @param caseBaseInfoList
     * @return
     */
    @Override
    public List<ClaimEndCaseReqDto> getClaimEndCaseReqDto(List<CaseBaseEntity> caseBaseInfoList, TaskInfoDTO taskInfoDTO,ReportInfoEntity reportInfo) {
        SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy-MM-dd");
        List<ClaimEndCaseReqDto> claimEndCaseReqDtoList = new ArrayList<>();
        for (int i = 0 ; i< caseBaseInfoList.size() ;i++){
            CaseBaseEntity caseBaseEntity = caseBaseInfoList.get(i);
            ClaimEndCaseReqDto claimEndCaseReqDto = new ClaimEndCaseReqDto();
            claimEndCaseReqDto.setClaimNo(caseBaseEntity.getRegistNo());
            claimEndCaseReqDto.setBusinessType("0");

            claimEndCaseReqDto.setOperateDate(formatter1.format(new Date()));
            if (taskInfoDTO !=null){
                claimEndCaseReqDto.setOperateComCode(taskInfoDTO.getDepartmentCode());
                claimEndCaseReqDto.setOperaterCode(taskInfoDTO.getAssigner());
            } else {
                claimEndCaseReqDto.setOperateComCode(reportInfo.getAcceptDepartmentCode());
                claimEndCaseReqDto.setOperaterCode(reportInfo.getReportRegisterUm());
            }
            claimEndCaseReqDtoList.add(claimEndCaseReqDto);
        }

        return claimEndCaseReqDtoList;
    }


    private List<PrpLLTextDto> initPrpLLTextDto(WholeCaseBaseDTO wholeCaseBaseEntity,List<CaseBaseEntity> caseBaseInfoList) {
        SurveyVO surveyVOByCaseType = dutySurveyService.getSurveyVOByCaseType(wholeCaseBaseEntity.getReportNo(), wholeCaseBaseEntity.getCaseTimes(), 1);
        List<PrpLLTextDto> initPrpLLTextDto = new ArrayList<>() ;
        for (int i = 0 ; i< caseBaseInfoList.size() ;i++){
            PrpLLTextDto prpLLTextDto=new PrpLLTextDto();
//        立案号码	CLAIMNO	Y
            prpLLTextDto.setClaimNo(caseBaseInfoList.get(i).getRegistNo());
//        文字说明类型 	TEXTTYPE	Y	出险摘要/查勘报告/结案报告"
            prpLLTextDto.setTextType("10");
//        行号	LINENO	Y	按顺序递加
            prpLLTextDto.setLineNo((i+1));
//        文字说明	CONTEXT	Y
            if (surveyVOByCaseType!=null&& StringUtils.isNotEmpty(surveyVOByCaseType.getPhoneSurveyDetail())){
                prpLLTextDto.setContext(surveyVOByCaseType.getPhoneSurveyDetail());
            } else {
                prpLLTextDto.setContext("立案注销");
            }
//        更新时间	UPDATETIME           	CY	只要对表操作就修改updatetime
            prpLLTextDto.setUpdateTime(new Date());
            initPrpLLTextDto.add(prpLLTextDto) ;
        }


        return initPrpLLTextDto;
    }

    private SwfFlowMainDto getSwfFlowMainDto(String reportNo, ReportInfoEntity reportInfo, PolicyInfoDTO policyInfoDTO) {
        SwfFlowMainDto swfFlowMainDto = new SwfFlowMainDto();
        swfFlowMainDto.setFlowID("");
        swfFlowMainDto.setFlowName(reportNo);
        swfFlowMainDto.setFlowStatus("0");
        swfFlowMainDto.setPolicyNo(policyInfoDTO.getPolicyNo());
        swfFlowMainDto.setCreatDate(reportInfo.getReportDate());
        swfFlowMainDto.setCloseDate(new Date());
        swfFlowMainDto.setModelNo(12);
        swfFlowMainDto.setStoreFlag("2");
        swfFlowMainDto.setUpdateTime(new Date());
        return swfFlowMainDto;
    }


}
