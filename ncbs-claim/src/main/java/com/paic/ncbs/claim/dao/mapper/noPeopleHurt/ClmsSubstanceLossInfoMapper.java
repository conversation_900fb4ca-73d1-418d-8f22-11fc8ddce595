package com.paic.ncbs.claim.dao.mapper.noPeopleHurt;

import com.paic.ncbs.claim.dao.entity.clms.ClmsSubstanceLossInfo;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 物质损失信息表(ClmsSubstanceLossInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:51
 */
public interface ClmsSubstanceLossInfoMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ClmsSubstanceLossInfo queryById(String id);

    /**
     * 通过报案号查询数据
     *
     * @return 实例对象
     */
    List<ClmsSubstanceLossInfo> queryByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes);

    /**
     * 新增数据
     *
     * @param clmsSubstanceLossInfo 实例对象
     * @return 影响行数
     */
    int insert(ClmsSubstanceLossInfo clmsSubstanceLossInfo);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<ClmsSubstanceLossInfo> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ClmsSubstanceLossInfo> entities);

    /**
     * 修改数据
     *
     * @param clmsSubstanceLossInfo 实例对象
     * @return 影响行数
     */
    int update(ClmsSubstanceLossInfo clmsSubstanceLossInfo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);

    void deleteByCondition(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes);

    /**
     * 获取理算金额
     *
     * @param reportNo  报案号
     * @param caseTimes 赔付次数
     * @return 理算金额
     */
    BigDecimal getSettleAmout(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes);
}

