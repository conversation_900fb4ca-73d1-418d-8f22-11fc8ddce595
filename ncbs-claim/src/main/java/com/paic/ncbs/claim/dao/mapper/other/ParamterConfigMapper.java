package com.paic.ncbs.claim.dao.mapper.other;

import com.paic.ncbs.claim.model.dto.other.ParamterConfigDTO;
import com.paic.ncbs.claim.model.vo.other.ResultAmountVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface ParamterConfigMapper {

    public void saveParamterConfig(ParamterConfigDTO paramterConfigDTO);

    public ParamterConfigDTO getParamterConfigByCode(@Param("configCode") String configCode);

    public List<ParamterConfigDTO> getParamterConfigByCodes(@Param("configCodes") List<String> configCodes);

    public String getParamterConfigValueByCode(@Param("configCode") String configCode);

    public ResultAmountVO getcheckInputBillEstimateAmountByCode(@Param("configCode") String configCode);

    String getReportNoWhenMigrateFromEqualso(@Param("reportNo") String reportNo);

}
