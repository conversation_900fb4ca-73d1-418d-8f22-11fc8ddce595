package com.paic.ncbs.claim.dao.mapper.settle;

import com.paic.ncbs.claim.model.dto.settle.SubProfessionDefineDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface ProfessionDefineMapper {

    public List<SubProfessionDefineDTO> getProfessionDefines();

    public List<SubProfessionDefineDTO> getOldProfessionByOther(@Param("professionCode") String professionCode);

    public List<SubProfessionDefineDTO> getSubProfessionDefines(@Param("parentCode") String parentCode);

    public List<String> getSubProfessionDefinesNameAll();

    public String getProfessionLevel(@Param("professionCode") String professionCode,
                                     @Param("parentCode") String parentCode);

    public List<SubProfessionDefineDTO> getPolicyProfession(@Param("reportNo") String reportNo);

    public List<SubProfessionDefineDTO> getAllSubProfessionDefines();

    public SubProfessionDefineDTO getParentSub(@Param("professionCode" ) String professionCode);

    List<SubProfessionDefineDTO>  getProfessiondefine(@Param("professionCode") String professionCode);
}
