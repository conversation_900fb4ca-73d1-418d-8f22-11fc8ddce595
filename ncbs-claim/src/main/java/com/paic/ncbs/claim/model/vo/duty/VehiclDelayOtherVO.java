package com.paic.ncbs.claim.model.vo.duty;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel("其他公交延误VO")
public class VehiclDelayOtherVO {

    @ApiModelProperty("")
    private String moduleCode;

    @ApiModelProperty("其他公交延误主键")
    private String idAhcsVehiclDelayOther;

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("通道号")
    private String idAhcsChannelProcess;

    @ApiModelProperty("公共交通工具类型")
    private String vehiclType;

    @ApiModelProperty("计划出发时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date originalDepartTime;

    @ApiModelProperty("计划到达时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date originalArrivalTime;

    @ApiModelProperty("班次号")
    private String classNo;

    @ApiModelProperty("实际出发时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date realDepartTime;

    @ApiModelProperty("实际到达时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date realArrivalTime;

    @ApiModelProperty("出发地")
    private String departPlace;

    @ApiModelProperty("目的地")
    private String arrivalPlace;

    @ApiModelProperty("延误时长")
    private BigDecimal delayDuration;

    @ApiModelProperty("延误时长单位")
    private String delayDurationUnit;

    @ApiModelProperty("港口信息")
    private String portInfo;

    @ApiModelProperty("取消港口个数")
    private Integer cancelPortCount;

    @ApiModelProperty("邮轮延误情况")
    private String cruisesDelayDesc;

    @ApiModelProperty("")
    private String[] cruisesDelayDescArr;

    @ApiModelProperty("变更时长")
    private BigDecimal alertDuration;

    @ApiModelProperty("变更时长单位")
    private String alertDurationUnit;

    @ApiModelProperty("环节号")
    private String taskCode;

    @ApiModelProperty("状态。1：发送，0：暂存")
    private String status;

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public String getIdAhcsVehiclDelayOther() {
        return idAhcsVehiclDelayOther;
    }

    public void setIdAhcsVehiclDelayOther(String idAhcsVehiclDelayOther) {
        this.idAhcsVehiclDelayOther = idAhcsVehiclDelayOther;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public Integer getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Integer caseTimes) {
        this.caseTimes = caseTimes;
    }

    public String getIdAhcsChannelProcess() {
        return idAhcsChannelProcess;
    }

    public void setIdAhcsChannelProcess(String idAhcsChannelProcess) {
        this.idAhcsChannelProcess = idAhcsChannelProcess;
    }

    public String getVehiclType() {
        return vehiclType;
    }

    public void setVehiclType(String vehiclType) {
        this.vehiclType = vehiclType;
    }

    public Date getOriginalDepartTime() {
        return originalDepartTime;
    }

    public void setOriginalDepartTime(Date originalDepartTime) {
        this.originalDepartTime = originalDepartTime;
    }

    public Date getOriginalArrivalTime() {
        return originalArrivalTime;
    }

    public void setOriginalArrivalTime(Date originalArrivalTime) {
        this.originalArrivalTime = originalArrivalTime;
    }

    public String getClassNo() {
        return classNo;
    }

    public void setClassNo(String classNo) {
        this.classNo = classNo;
    }

    public Date getRealDepartTime() {
        return realDepartTime;
    }

    public void setRealDepartTime(Date realDepartTime) {
        this.realDepartTime = realDepartTime;
    }

    public Date getRealArrivalTime() {
        return realArrivalTime;
    }

    public void setRealArrivalTime(Date realArrivalTime) {
        this.realArrivalTime = realArrivalTime;
    }

    public String getDepartPlace() {
        return departPlace;
    }

    public void setDepartPlace(String departPlace) {
        this.departPlace = departPlace;
    }

    public String getArrivalPlace() {
        return arrivalPlace;
    }

    public void setArrivalPlace(String arrivalPlace) {
        this.arrivalPlace = arrivalPlace;
    }

    public BigDecimal getDelayDuration() {
        return delayDuration;
    }

    public void setDelayDuration(BigDecimal delayDuration) {
        this.delayDuration = delayDuration;
    }

    public String getDelayDurationUnit() {
        return delayDurationUnit;
    }

    public void setDelayDurationUnit(String delayDurationUnit) {
        this.delayDurationUnit = delayDurationUnit;
    }

    public String getPortInfo() {
        return portInfo;
    }

    public void setPortInfo(String portInfo) {
        this.portInfo = portInfo;
    }

    public Integer getCancelPortCount() {
        return cancelPortCount;
    }

    public void setCancelPortCount(Integer cancelPortCount) {
        this.cancelPortCount = cancelPortCount;
    }

    public String getCruisesDelayDesc() {
        return cruisesDelayDesc;
    }

    public void setCruisesDelayDesc(String cruisesDelayDesc) {
        this.cruisesDelayDesc = cruisesDelayDesc;
    }

    public BigDecimal getAlertDuration() {
        return alertDuration;
    }

    public void setAlertDuration(BigDecimal alertDuration) {
        this.alertDuration = alertDuration;
    }

    public String getAlertDurationUnit() {
        return alertDurationUnit;
    }

    public void setAlertDurationUnit(String alertDurationUnit) {
        this.alertDurationUnit = alertDurationUnit;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String[] getCruisesDelayDescArr() {
        return cruisesDelayDescArr;
    }

    public void setCruisesDelayDescArr(String[] cruisesDelayDescArr) {
        this.cruisesDelayDescArr = cruisesDelayDescArr;
    }
}
