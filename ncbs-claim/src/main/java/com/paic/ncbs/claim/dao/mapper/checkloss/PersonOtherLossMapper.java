package com.paic.ncbs.claim.dao.mapper.checkloss;

import com.paic.ncbs.claim.model.dto.duty.PersonOtherLossDTO;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface PersonOtherLossMapper {

    public void addPersonOtherLoss(@Param("personOtherLossList") List<PersonOtherLossDTO> personOtherLossList);

    public void removePersonOtherLoss(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("taskId") String taskId, @Param("idAhcsChannelProcess") String idAhcsChannelProcess);

    public List<PersonOtherLossDTO> getPersonOtherLoss(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("status") String status, @Param("taskId") String taskId, @Param("channelProcessId") String channelProcessId);

    public List<PersonOtherLossDTO> getPersonOtherLossList(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId);

    public void addPersonOtherLossList(@Param("personOtherLossList") List<PersonOtherLossDTO> personOtherLossList,
                                       @Param("caseTimes") int caseTimes, @Param("userId") String userId, @Param("channelProcessId") String channelProcessId);

    public void updatePersonOtherLoss(PersonOtherLossDTO personOtherLossDTO);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);
}
