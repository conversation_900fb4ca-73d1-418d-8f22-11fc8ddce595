package com.paic.ncbs.claim.dao.mapper.checkloss;

import com.paic.ncbs.claim.model.dto.checkloss.DiagnoseDefineDTO;
import com.paic.ncbs.claim.model.vo.checkloss.DiagnoseDefineVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface DiagnoseDefineMapper {

    List<DiagnoseDefineDTO> getDiagnoseDefines(@Param("searchStr") String searchStr,@Param("orgType")String orgType);

    String getDiagnoseCode(@Param("diagnoseCode") String diagnoseCode);

    String getDiagnoseName(@Param("diagnoseCode") String diagnoseCode);

    List<DiagnoseDefineDTO> getDiagnoseDefineList(DiagnoseDefineDTO diagnoseDefineDTO);

    /**
     * 查询国际ICD编码以S开头的数据
     * @param searchStr
     * @param reportNo
     * @return
     */
    public List<DiagnoseDefineDTO> getDiagnoseDefineDTOs(@Param("searchStr") String searchStr,@Param("orgType")String orgType,@Param("diagnoseCode")String diagnoseCode);

}
