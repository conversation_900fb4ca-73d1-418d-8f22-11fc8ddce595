package com.paic.ncbs.claim.model.vo.trace;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 通过ins-framework-mybatis工具自动生成，请勿手工修改。表clms_pers_trace_fee的PO对象<br/>
 * 对应表名：clms_pers_trace_fee,备注：人伤跟踪损失费用明细表
 *
 */
@ApiModel("人伤跟踪损失信息详情")
@Data
public class ClmsPersTraceFeeVO  implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：id,备注：主键 */
	@TableField(value="id")
	private Integer id;
	/** 对应字段：report_no,备注：报案号 */
	@TableField(value="report_no")
	private String reportNo;
	/** 对应字段：case_times,备注：赔付次数 */
	@TableField(value="case_times")
	private Integer caseTimes;
	/** 对应字段：injured_id,备注：跟踪人员表id */
	@TableField(value="injured_id")
	private Integer injuredId;
	/** 对应字段：trace_exp_id,备注：人伤跟踪损失表id */
	@TableField(value="trace_exp_id")
	private Integer traceExpId;
	/** 对应字段：risk_code,备注：险种 */
	@TableField(value="risk_code")
	private String riskCode;
	/** 对应字段：kind_code,备注：险别代码 */
	@TableField(value="kind_code")
	private String kindCode;
	/** 对应字段：kind_name,备注：险别名称 */
	@TableField(value="kind_name")
	private String kindName;
	/** 对应字段：loss_item_no,备注：损失类别代码 */
	@TableField(value="loss_item_no")
	private String lossItemNo;
	/** 对应字段：loss_item_name,备注：损失类别名称 */
	@TableField(value="loss_item_name")
	private String lossItemName;
	/** 对应字段：family_no,备注：标的-方案（序号） */
	@TableField(value="family_no")
	private String familyNo;
	/** 对应字段：item_no,备注：标的代码 */
	@TableField(value="item_no")
	private String itemNo;
	/** 对应字段：item_name,备注：标的名称 */
	@TableField(value="item_name")
	private String itemName;
	/** 对应字段：amount,备注：保额/责任限额 */
	@TableField(value="amount")
	private BigDecimal amount;
	/** 对应字段：currency,备注：理赔币别 默认cny */
	@TableField(value="currency")
	private String currency;
	/** 对应字段：exch_rate,备注：人民币兑换率 默认1 */
	@TableField(value="exch_rate")
	private BigDecimal exchRate;
	/** 对应字段：fee_type_code,备注：费用代码 */
	@TableField(value="fee_type_code")
	private String feeTypeCode;
	/** 对应字段：fee_type_name,备注：费用名称 */
	@TableField(value="fee_type_name")
	private String feeTypeName;
	/** 对应字段：unit_amount,备注：单位金额 */
	@TableField(value="unit_amount")
	private BigDecimal unitAmount;
	/** 对应字段：quantity,备注：数量 */
	@TableField(value="quantity")
	private Integer quantity;
	/** 对应字段：real_fee,备注：索赔金额 */
	@TableField(value="real_fee")
	private BigDecimal realFee;
	/** 对应字段：deduction_fee,备注：减损金额 */
	@TableField(value="deduction_fee")
	private BigDecimal deductionFee;
	/** 对应字段：sum_def_loss,备注：定损金额 */
	@TableField(value="sum_def_loss")
	private BigDecimal sumDefLoss;
	/** 对应字段：own_expense,备注：自费金额 */
	@TableField(value="own_expense")
	private BigDecimal ownExpense;
	/** 对应字段：own_pay,备注：分类自付金额 */
	@TableField(value="own_pay")
	private BigDecimal ownPay;
	/** 对应字段：third_party,备注：社保/第三方支付金额 */
	@TableField(value="third_party")
	private BigDecimal thirdParty;
	/** 对应字段：unreasonable,备注：不合理费用金额 */
	@TableField(value="unreasonable")
	private BigDecimal unreasonable;
	/** 对应字段：remark,备注：费用说明 */
	@TableField(value="remark")
	private String remark;
	/** 对应字段：valid_flag,备注：有效标志 */
	@TableField(value="valid_flag")
	private String validFlag;
	/** 对应字段：flag,备注：标志字段 */
	@TableField(value="flag")
	private String flag;
	/** 对应字段：estimate_loss,备注：预估损失 */
	@TableField(value="estimate_loss")
	private BigDecimal estimateLoss;
	/** 对应字段：disabled_grade,备注：伤残等级 */
	@TableField(value="disabled_grade")
	private String disabledGrade;
	/** 对应字段：disabled_pay_rate,备注：伤残比例 */
	@TableField(value="disabled_pay_rate")
	private BigDecimal disabledPayRate;
	/** 对应字段：create_flag,备注：创建标志 */
	@TableField(value="create_flag")
	private String createFlag;
	/** 对应字段：care_fee,备注：护理费 */
	@TableField(value="care_fee")
	private BigDecimal careFee;
	/** 对应字段：pay_pers_day,备注：支付天数 */
	@TableField(value="pay_pers_day")
	private BigDecimal payPersDay;
	/** 对应字段：in_hosiptal_day,备注：住院天数 */
	@TableField(value="in_hosiptal_day")
	private BigDecimal inHosiptalDay;
	/** 对应字段：outpay_day,备注：超额支付天数 */
	@TableField(value="outpay_day")
	private BigDecimal outpayDay;
	/** 对应字段：nopay_day,备注：免赔天数 */
	@TableField(value="nopay_day")
	private BigDecimal nopayDay;
	/** 对应字段：nopay_day,备注：免赔额 */
	@TableField(value="remit_amount")
	private BigDecimal remitAmount;
	/** 对应字段：other_pay_rate,备注：其他比例 */
	@TableField(value="other_pay_rate")
	private BigDecimal otherPayRate;
	/** 对应字段：deduction_content,备注：减损内容/数量 */
	@TableField(value="deduction_content")
	private String deductionContent;
	/** 对应字段：created_by,备注：创建人 */
	@TableField(value="created_by")
	private String createdBy;
	/** 对应字段：sys_ctime,备注：创建时间 */
	@TableField(value="sys_ctime")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date sysCtime;
	/** 对应字段：updated_by,备注：最新修改人员 */
	@TableField(value="updated_by")
	private String updatedBy;
	/** 对应字段：sys_utime,备注：最新修改时间 */
	@TableField(value="sys_utime")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date sysUtime;

}
