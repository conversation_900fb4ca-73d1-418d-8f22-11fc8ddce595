package com.paic.ncbs.claim.dao.mapper.casezero;

import com.paic.ncbs.claim.model.vo.casezero.ZeroCancelAuditInfoVO;
import com.paic.ncbs.claim.model.dto.endcase.CaseZeroCancelDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface CaseZeroCancelMapper {


    void addCaseZeroCancelApply(CaseZeroCancelDTO zeroCancelDTO);

    int getIsContinueByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    int getCaseZeroCancelApplyCount(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("status") String status);


    List<CaseZeroCancelDTO> getCaseZeroCancelApplyList(@Param("reportNo") String reportNo,
                                                       @Param("caseTimes") Integer caseTimes,
                                                       @Param("status") String status);

    void saveCaseZeroCancelAudit(CaseZeroCancelDTO caseZeroCancelDTO);

    List<ZeroCancelAuditInfoVO> getAuditInfoList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    int getMaxApplyTimes(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    CaseZeroCancelDTO getTempApplyInfo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("applyTimes") Integer applyTimes);

    List<CaseZeroCancelDTO> getZeroCancelInfoList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    String getIdZeroCancelApplyByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("status") String status);

    void modifyCaseZeroCancelApply(CaseZeroCancelDTO zeroCancelDTO);

    List<CaseZeroCancelDTO> getCaseZeroCancelApplyAuditList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    void modifyZeroApplyType(CaseZeroCancelDTO zeroCancelDTO);

    Integer getZeroCancelApplyCount(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    CaseZeroCancelDTO getZeroCancelApplyTime(String reportNo, int caseTimes);
}
