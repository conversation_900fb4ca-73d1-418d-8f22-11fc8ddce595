package com.paic.ncbs.claim.dao.mapper.settle;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.settle.AutoSettleDetailDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface AutoSettleDetailMapper extends BaseDao<AutoSettleDetailDTO> {

    List<AutoSettleDetailDTO> getAutoSettleDetails(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    void deleteAutoSettleDetail(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    void removeAutoSettleDetail(DutyDetailPayDTO dutyDetail);

    List<AutoSettleDetailDTO> getAutoSettleResult(@Param("caseNo") String caseNo,
                                                  @Param("policyNo") String policyNo,
                                                  @Param("caseTimes") Integer caseTimes,
                                                  @Param("planCode") String planCode,
                                                  @Param("dutyCode") String dutyCode);

    List<AutoSettleDetailDTO> selectByParam(AutoSettleDetailDTO autoSettleDetailDTO);

    void batchInsert(List<AutoSettleDetailDTO> addList);

    void updateById(AutoSettleDetailDTO autoSettleDetailDTO);
}
