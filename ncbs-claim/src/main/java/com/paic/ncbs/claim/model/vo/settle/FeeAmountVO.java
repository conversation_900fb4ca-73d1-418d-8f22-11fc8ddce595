package com.paic.ncbs.claim.model.vo.settle;

import java.math.BigDecimal;

public class FeeAmountVO {

    private BigDecimal feeAmountSum = BigDecimal.ZERO;

    private BigDecimal feeAmountArb = BigDecimal.ZERO;

    private BigDecimal feeAmountAwa = BigDecimal.ZERO;

    private BigDecimal feeAmountCom = BigDecimal.ZERO;

    private BigDecimal feeAmountExe = BigDecimal.ZERO;

    private BigDecimal feeAmountLaws = BigDecimal.ZERO;

    private BigDecimal feeAmountLawy = BigDecimal.ZERO;

    private BigDecimal feeAmountVer = BigDecimal.ZERO;


    private BigDecimal feeAmountInq = BigDecimal.ZERO;
    private BigDecimal feeAmountOth = BigDecimal.ZERO;
    private BigDecimal feeAmountSpe = BigDecimal.ZERO;

    public BigDecimal getFeeAmountInq() {
        return feeAmountInq;
    }

    public void setFeeAmountInq(BigDecimal feeAmountInq) {
        this.feeAmountInq = feeAmountInq;
    }

    public BigDecimal getFeeAmountOth() {
        return feeAmountOth;
    }

    public void setFeeAmountOth(BigDecimal feeAmountOth) {
        this.feeAmountOth = feeAmountOth;
    }

    public BigDecimal getFeeAmountSpe() {
        return feeAmountSpe;
    }

    public void setFeeAmountSpe(BigDecimal feeAmountSpe) {
        this.feeAmountSpe = feeAmountSpe;
    }

    public BigDecimal getFeeAmountSum() {
        return feeAmountSum;
    }

    public void setFeeAmountSum(BigDecimal feeAmountSum) {
        this.feeAmountSum = feeAmountSum;
    }

    public BigDecimal getFeeAmountArb() {
        return feeAmountArb;
    }

    public void setFeeAmountArb(BigDecimal feeAmountArb) {
        this.feeAmountArb = feeAmountArb;
    }

    public BigDecimal getFeeAmountAwa() {
        return feeAmountAwa;
    }

    public void setFeeAmountAwa(BigDecimal feeAmountAwa) {
        this.feeAmountAwa = feeAmountAwa;
    }

    public BigDecimal getFeeAmountCom() {
        return feeAmountCom;
    }

    public void setFeeAmountCom(BigDecimal feeAmountCom) {
        this.feeAmountCom = feeAmountCom;
    }

    public BigDecimal getFeeAmountExe() {
        return feeAmountExe;
    }

    public void setFeeAmountExe(BigDecimal feeAmountExe) {
        this.feeAmountExe = feeAmountExe;
    }

    public BigDecimal getFeeAmountLaws() {
        return feeAmountLaws;
    }

    public void setFeeAmountLaws(BigDecimal feeAmountLaws) {
        this.feeAmountLaws = feeAmountLaws;
    }

    public BigDecimal getFeeAmountLawy() {
        return feeAmountLawy;
    }

    public void setFeeAmountLawy(BigDecimal feeAmountLawy) {
        this.feeAmountLawy = feeAmountLawy;
    }

    public BigDecimal getFeeAmountVer() {
        return feeAmountVer;
    }

    public void setFeeAmountVer(BigDecimal feeAmountVer) {
        this.feeAmountVer = feeAmountVer;
    }

}
