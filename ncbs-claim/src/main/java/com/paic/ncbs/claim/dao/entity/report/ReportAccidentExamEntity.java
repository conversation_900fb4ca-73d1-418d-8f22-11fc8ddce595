package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.math.BigDecimal;

/**
 * 意键险考试不通过表
 */
public class ReportAccidentExamEntity extends EntityDTO {

    private static final long serialVersionUID = 5247737963215434034L;

    private String idAhcsReportAccidentExam;

    private String reportNo;

    private String noPassType;

    private BigDecimal costEstimate;

    public String getIdAhcsReportAccidentExam() {
        return idAhcsReportAccidentExam;
    }

    public void setIdAhcsReportAccidentExam(String idAhcsReportAccidentExam) {
        this.idAhcsReportAccidentExam = idAhcsReportAccidentExam == null ? null : idAhcsReportAccidentExam.trim();
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String getNoPassType() {
        return noPassType;
    }

    public void setNoPassType(String noPassType) {
        this.noPassType = noPassType == null ? null : noPassType.trim();
    }

    public BigDecimal getCostEstimate() {
        return costEstimate;
    }

    public void setCostEstimate(BigDecimal costEstimate) {
        this.costEstimate = costEstimate;
    }
}