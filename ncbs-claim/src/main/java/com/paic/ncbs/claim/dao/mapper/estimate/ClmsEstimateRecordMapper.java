package com.paic.ncbs.claim.dao.mapper.estimate;

import com.paic.ncbs.claim.model.dto.estimate.ClmsEstimateRecord;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface ClmsEstimateRecordMapper {

    int deleteByPrimaryKey(String idClmsEstimateRecord);

    int insert(ClmsEstimateRecord record);

    ClmsEstimateRecord selectByPrimaryKey(String idClmsEstimateRecord);

    List<ClmsEstimateRecord> selectByReportNoAndType(@Param("reportNo") String reportNo,
                                                     @Param("caseTimes") String caseTimes,
                                                     @Param("estimateType") String estimateType);

    int updateByPrimaryKeySelective(ClmsEstimateRecord record);

    int updateByPrimaryKey(ClmsEstimateRecord record);

    BigDecimal getEstimateRecordAmount(@Param("reportNo") String reportNo,
                                       @Param("caseTimes") Integer caseTimes);

    BigDecimal getLastEstimateRecordAmount(@Param("reportNo") String reportNo,
                                       @Param("caseTimes") Integer caseTimes);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);

    int updateEstimateRecordInfo(ClmsEstimateRecord record);
}