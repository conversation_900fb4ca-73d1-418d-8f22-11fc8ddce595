package com.paic.ncbs.claim.dao.mapper.ahcs;

import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyDataEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AhcsPolicyDutyDataMapper {

    int insert(AhcsPolicyDutyDataEntity record);

    int insertSelective(AhcsPolicyDutyDataEntity record);
    
	int insertList(@Param("policyDutyDataEntities") List<AhcsPolicyDutyDataEntity> policyDutyEntities);
	
	List<AhcsPolicyDutyDataEntity> getPolicyDutyInfo(@Param("reportNo")String reportNo, @Param("policyNo")String policyNo, @Param("selfCardNo")String selfCardNo, @Param("planCode")String planCode, @Param("dutyCode")String dutyCode);

	void deleteByReportNoAndPolicyNo(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo);
}