package com.paic.ncbs.claim.dao.entity.pay;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 险种责任赔付信息
 */
@Getter
@Setter
public class PlanDutyPayEntity extends EntityDTO {
    private static final long serialVersionUID = 1262397583511962490L;
    /** CLMS_DUTY_PAY主键 */
    private String idClmPlanDutyPay;
    /** 赔案号 */
    private String caseNo;
    /** 赔付次数 */
    private Integer caseTimes;
    /** 预陪/垫付/追偿 次数 */
    private Integer subTimes;
    /** 赔付批次号 */
    private String idAhcsBatch;
    /** 险种代码 */
    private String planCode;
    /** 责任编码 */
    private String dutyCode;
    /** 赔偿限额 -- 新加应该未使用 */
    private BigDecimal dutyPayLimit;
    /**  损失金额 -- 新加应该未使用 */
    private BigDecimal lossAmount;
    /** 本次理算该险别下分摊的施救费金额 -- 新加应该未使用 */
    private BigDecimal rescueFee;
    /** 单个赔偿限额 -- 新加应该未使用 */
    private BigDecimal perIndemnityLimit;
    /** 保单累计赔付金额 -- 新加应该未使用 */
    private BigDecimal historyPayAmount;
    /** 应负赔偿金额（责任险代步新增设备停驶险中有计算，在限额控制使用） -- 新加应该未使用 */
    private BigDecimal selfAffordLossAmount;
    /** 调整金额 -- 新加应该未使用 */
    private BigDecimal adjustAmount;
    /** 超限金额 -- 新加应该未使用 */
    private BigDecimal consultAmount;
    /** 冲减金额 -- 新加应该未使用 */
    private BigDecimal writeoffAmount;
    /** 责任系数 -- 新加应该未使用 */
    private BigDecimal dutyRate;
    /** 责任免赔率 -- 新加应该未使用 */
    private BigDecimal nopayRate;
    /** 绝对免赔率 -- 新加应该未使用 */
    private BigDecimal absnopayRate;
    /** 绝对免赔额 -- 新加应该未使用 */
    private BigDecimal absnopayAmount;
    /** 加扣免赔率 -- 新加应该未使用 */
    private BigDecimal deductNopayRate;
    /** 责任赔付/预陪/垫付 金额 -- 新加应该未使用 */
    private BigDecimal dutyPayAmount;
    /** 拒赔/注销金额 -- 新加应该未使用 */
    private BigDecimal refuseAmount;
    /** 仲裁费 */
    private BigDecimal arbitrateFee;
    /** 诉讼费 */
    private BigDecimal lawsuitFee;
   /** 律师费 */
    private BigDecimal lawyerFee;
    /** 检验及鉴定费 -- 新加应该未使用 */
    private BigDecimal checkFee;
    /** 执行费 */
    private BigDecimal executeFee;
    /** 公估费 -- 新加应该未使用 */
    private BigDecimal evaluationFee;
    /** 减损费用/奖励费（意健险理赔） -- 新加应该未使用 */
    private BigDecimal decreaseFee;
    /** 备注 */
    private String remark;
    /** 数量 -- 新加应该未使用 */
    private BigDecimal quantity;
    /** 数据来源，区分理赔各子领域以及其新老数据，默认n代表新车险理赔数据，c代码原车险理赔数据，np代表新财产险理赔数据，op代表原财产险理赔数据，a代表新农险数据 -- 新加应该未使用 */
    private String migrateFrom;
    /** 追偿费用支出 -- 新加应该未使用 */
    private BigDecimal chaseFeeOut;
    /** 查勘费 -- 新加应该未使用 */
    private BigDecimal surveyFee;
    /** 专家鉴定费 -- 新加应该未使用 */
    private BigDecimal mavinAppraisalFee;
    /** 检验费 -- 新加应该未使用 */
    private BigDecimal inspectFee;
    /** 调查取证费用 -- 新加应该未使用 */
    private BigDecimal inquiryEvidenceFee;
    /** 其他费用 */
    private BigDecimal otherFee;
    /** 咨询费 -- 新加应该未使用 */
    private BigDecimal consultFee;
    /** 住车费 -- 新加应该未使用 */
    private BigDecimal carRentalFee;
    /** 检测费 -- 新加应该未使用 */
    private BigDecimal detectFee;
    /** 差旅费 -- 新加应该未使用 */
    private BigDecimal travelFee;
    /** 代位追偿金额 -- 新加应该未使用 */
    private BigDecimal subrogationAmount;
    /** 币种(01-人民币 02-港币 03-美元) -- 新加应该未使用 */
    private String currencyCode;
    /** 罚息收入 -- 新加应该未使用 */
    private BigDecimal amercement;
    /** 追偿费用转回 -- 新加应该未使用 */
    private BigDecimal chaseFeeBack;
    /** 理算依据 */
    private String settleReason;
    /** 查勘补助费 -- 新加应该未使用 */
    private BigDecimal surveySubsidyFee;
    /** 公估费(外包) -- 新加应该未使用 */
    private BigDecimal evaluationOutFee;
    /** 咨询费(对公) -- 新加应该未使用 */
    private BigDecimal consultPubFee;
    /** 国外代理费 -- 新加应该未使用 */
    private BigDecimal agencyFee;
    /** 公证取证费 -- 新加应该未使用 */
    private BigDecimal notarialFee;
    /** 理算依据 -- 新加应该未使用 */
    private String settleaReason;
    /** 前置调查费 -- 新加应该未使用 */
    private BigDecimal preInvestigateFee;
    /** 归档时间 -- 新加应该未使用 */
    private Date archiveDate;
    /** 删除时间 -- 新加应该未使用 */
    private Date deletedTime;
    /** 责任层保单开始日期 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date insuranceBeginDate;
    /** 责任层保单止期 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date insuranceEndDate;
    /** 标的id */
    private String idPlyRiskProperty;

}
