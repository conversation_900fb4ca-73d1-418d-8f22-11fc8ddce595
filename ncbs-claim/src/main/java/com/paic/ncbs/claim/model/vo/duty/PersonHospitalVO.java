package com.paic.ncbs.claim.model.vo.duty;


import com.paic.ncbs.claim.model.dto.duty.PersonHospitalDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("医院信息VO")
public class PersonHospitalVO {

    @ApiModelProperty("人伤门诊住院信息表DTO")
    private List<PersonHospitalDTO> personHospitalList;

    @ApiModelProperty("")
    private String moduleCode;

    @ApiModelProperty("通道号(AHCS_CHANNEL_PROCESS表主键)")
    private String idAhcsChannelProcess;

    @ApiModelProperty("环节号")
    private String taskId;

    @ApiModelProperty("状态（0：未审核，1：已审核）")
    private String status;


    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public List<PersonHospitalDTO> getPersonHospitalList() {
        return personHospitalList;
    }

    public void setPersonHospitalList(List<PersonHospitalDTO> personHospitalList) {
        this.personHospitalList = personHospitalList;
    }

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public String getIdAhcsChannelProcess() {
        return idAhcsChannelProcess;
    }

    public void setIdAhcsChannelProcess(String idAhcsChannelProcess) {
        this.idAhcsChannelProcess = idAhcsChannelProcess;
    }

}
