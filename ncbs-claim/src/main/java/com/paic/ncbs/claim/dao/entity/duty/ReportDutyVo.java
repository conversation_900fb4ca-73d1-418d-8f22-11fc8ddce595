package com.paic.ncbs.claim.dao.entity.duty;

import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyDutyDetailEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class ReportDutyVo {
    private String idAhcsPolicyDuty;

    private String idAhcsPolicyPlan;

    private String dutyName;

    private String dutyCode;

    private String dutyDetailtype;

    private BigDecimal dutyAmount;

    private String dutyDesc;

    private String orgDutyCode;

    private String orgDutyName;

    private String isDutySharedAmount;

    private String dutySharedAmountMerge;

    /**
     * 保单起期
     */
    private Date insuranceBeginDate;
    /***
     * 保单止期
     */
    private Date insuranceEndDate;

    private Boolean shareAmount = false;
    /**
     * 是否责任共享保额
     */
    private Boolean dutyShareAmount = false;

    private String shareDutyGroup;

    private List<AhcsPolicyDutyDetailEntity> ahcsPolicyDutyDetail;

    //免赔额
    private BigDecimal deductible;
}
