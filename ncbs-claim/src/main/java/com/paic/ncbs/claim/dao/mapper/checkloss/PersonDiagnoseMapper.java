package com.paic.ncbs.claim.dao.mapper.checkloss;

import com.paic.ncbs.claim.model.dto.duty.PersonDiagnoseDTO;
import com.paic.ncbs.claim.model.dto.duty.PersonDisabilityDTO;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface PersonDiagnoseMapper {

    public void savePersonDiagnose(PersonDiagnoseDTO personDiagnoseDTO);

    public void removePersonDiagnose(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId);

    void updateEffective(PersonDiagnoseDTO personDiagnoseDTO);

    public List<PersonDiagnoseDTO> getPersonDiagnoseListById(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId);

    public List<PersonDiagnoseDTO> getPersonDiagnoseDTOList(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId, @Param("status") String status);

    public void addPersonDiagnoseList(@Param("personDiagnoseList") List<PersonDiagnoseDTO> personDiagnoseList,
                                      @Param("caseTimes") int caseTimes, @Param("userId") String userId, @Param("channelProcessId") String channelProcessId);

    public List<PersonDiagnoseDTO> getPersonDiagnoseListByCt(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId);

    public List<PersonDiagnoseDTO> getPersonDiagnoseListByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("taskId") String taskId);

    public List<PersonDiagnoseDTO> getSurgicalInfo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("tacheCodeList") List<String> tacheCodeList);

    public List<PersonDiagnoseDTO> getSurgicalByTaskCode(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("taskId") String taskId);

    public List<String> getDiseaseCodeList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

	List<PersonDisabilityDTO> getPersonDisabilityList(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId, @Param("status") String status);

	List<PersonDisabilityDTO> getList(@Param("personDisability") PersonDisabilityDTO personDisability);

    List<PersonDiagnoseDTO> getPersonDiagnoseInfo(@Param("reportNo") String reportNo);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);
}
