package com.paic.ncbs.claim.dao.entity.wzemployer;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 微众雇责理赔清单表
 */
@Data
@TableName(value = "clms_case_wz_employer_list")
public class CaseWZEmployerListEntity implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 保险公司
     */
    @TableField(value = "company")
    private String company;

    /**
     * 保单号
     */
    @TableField(value = "policy_no")
    private String policyNo;

    /**
     *  报案号
     */
    @TableField(value = "report_no")
    private String reportNo;

    /**
     * 立案号
     */
    @TableField(value = "claim_no")
    private String claimNo;

    /**
     * 批单号
     */
    @TableField(value = "accident_pesron_endorse_no")
    private String accidentPesronEndorseNo;

    /**
     * 被保企业
     */
    @TableField(value = "appnt_name")
    private String appntName;

    /**
     * 客户所属省
     */
    @TableField(value = "appnt_province_name")
    private String appntProvinceName;

    /**
     * 客户所属市
     */
    @TableField(value = "appnt_city_name")
    private String appntCityName;

    /**
     * 产品代码
     */
    @TableField(value = "risk_code")
    private String riskCode;

    /**
     * 产品名称
     */
    @TableField(value = "risk_name")
    private String riskName;

    /**
     * 主保单投保年月日
     */
    @TableField(value = "underwrite_end_time")
    private String underwriteEndTime;

    /**
     * 主保单生效日期年月日
     */
    @TableField(value = "underwrite_valid_time")
    private String underwriteValidTime;

    /**
     * 员工起保日期
     */
    @TableField(value = "accident_pesron_start_date")
    private String accidentPesronStartDate;

    /**
     * 出险日期
     */
    @TableField(value = "accident_date")
    private String accidentDate;

    /**
     * 报案日期
     */
    @TableField(value = "report_time")
    private String reportTime;

    /**
     * 立案日期
     */
    @TableField(value = "claim_time")
    private String claimTime;

    /**
     * 结案日期
     */
    @TableField(value = "end_case_date")
    private String endCaseDate;

    /**
     * 出险员工证件号
     */
    @TableField(value = "cert_no")
    private String certNo;

    /**
     * 出险员工工种类别
     */
    @TableField(value = "accident_pesron_job_risk_level_name")
    private String accidentPesronJobRiskLevelName;

    /**
     * 出险员工职业名称名称
     */
    @TableField(value = "accident_pesron_job_name")
    private String accidentPesronJobName;

    /**
     * 员工投保方案
     */
    @TableField(value = "item_name")
    private String itemName;

    /**
     * 损失项目
     */
    @TableField(value = "injury_type")
    private String injuryType;

    /**
     * 未决赔款
     */
    @TableField(value = "os_amount")
    private BigDecimal osAmount;

    /**
     * 已决赔款
     */
    @TableField(value = "indem_pay")
    private BigDecimal indemPay;

    /**
     * 未决金额
     */
    @TableField(value = "outstanding")
    private BigDecimal outstanding;

    /**
     * 已决金额
     */
    @TableField(value = "indem_amount")
    private BigDecimal indemAmount;

    /**
     * 案件状态
     */
    @TableField(value = "endcase_type_name")
    private String endcaseTypeName;

    /**
     * 案件详情
     */
    @TableField(value = "case_logs")
    private String caseLogs;

    /**
     * 理赔备注
     */
    @TableField(value = "claim_remarks")
    private String claimRemarks;

    /**
     * 创建人员
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "sys_ctime")
    private Date sysCtime;

    /**
     * 修改人员
     */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(value = "sys_utime")
    private Date sysUtime;

    private static final long serialVersionUID = 1L;
}