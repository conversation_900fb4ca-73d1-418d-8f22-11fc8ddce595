package com.paic.ncbs.claim.feign.mesh;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.base.util.MeshSendUtils;
import com.paic.ncbs.claim.common.util.HttpClientUtil;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.feign.TpaGlobalFeign;
import com.paic.ncbs.claim.model.dto.investigate.TpaGlobalAgentDTO;
import com.paic.ncbs.claim.model.vo.investigate.TpaInvestigateVO;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Slf4j
@RefreshScope
@Component
public class TpaGlobalRequest {
    /**
     * 调用mesh和feign开关, true:调用mesh
     */
    @Value("${switch.mesh}")
    private Boolean switchMesh;
    @Value("${tpa.globalUrl:http://tpa-global-agent.lb.ssdev.com:48001}")
    private String tpaUrl;

    @Autowired
    private TpaGlobalFeign tpaGlobalFeign;

    public String getExternalDepartmentList(TpaGlobalAgentDTO tpaGlobalAgentDTO){
        String result;
        LogUtil.audit("请求中台查询外部调查机构接口请求报文:"+JSON.toJSONString(tpaGlobalAgentDTO));
        LogUtil.audit("mesh开关："+switchMesh);
        if (switchMesh) {
            result = MeshSendUtils.post(tpaUrl+"/coreProxy/apiEntrance", JSON.toJSONString(tpaGlobalAgentDTO));
        } else {
            result = tpaGlobalFeign.getExternalDepartmentList(tpaGlobalAgentDTO);
        }
        LogUtil.audit("请求中台查询外部调查机构接口返回报文:"+result);
        return result;
    }

    public String fainshTask(TpaInvestigateVO tpaInvestigateVO){
        String result;
        if (switchMesh) {
            result = MeshSendUtils.post(tpaUrl+"/coreProxy/apiEntrance", JSON.toJSONString(tpaInvestigateVO));
        } else {
            result = tpaGlobalFeign.fainshTask(tpaInvestigateVO);
        }
        return result;
    }
}
