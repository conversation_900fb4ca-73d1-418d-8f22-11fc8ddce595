package com.paic.ncbs.claim.dao.entity.dynamic;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 动态字段结果表
 */
@Data
@TableName(value = "clm_dynamic_field_result")
public class DynamicFieldsResultEntity implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private String id;

    /**
     * 删除标记
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     * 报案号
     */
    @TableField(value = "report_no")
    private String reportNo;

    /**
     * 赔付次数
     */
    @TableField(value = "case_times")
    private Integer caseTimes;

    /**
     * 低码表单key
     */
    @TableField(value = "template_id")
    private String templateId;

    /**
     * 字段名
     */
    @TableField(value = "field_code")
    private String fieldCode;

    /**
     * 字段值
     */
    @TableField(value = "field_value")
    private String fieldValue;

    /**
     * 字段映射
     */
    @TableField(value = "value_mapper")
    private String valueMapper;

    /**
     * 创建人员
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "sys_ctime")
    private Date sysCtime;

    /**
     * 修改人员
     */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(value = "sys_utime")
    private Date sysUtime;

    private static final long serialVersionUID = 1L;
}