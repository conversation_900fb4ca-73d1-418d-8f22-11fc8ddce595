package com.paic.ncbs.claim.dao.mapper.estimate;

import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface EstimateDutyMapper {

    List<EstimateDutyDTO> getByPlanID(@Param("idAhcsEstimatePlan") String idAhcsEstimatePlan);

    List<EstimateDutyDTO> getDutyFormCopy(@Param("idAhcsEstimatePlan") String idAhcsEstimatePlan);

    void addBatchEstimateDuty(@Param("paramList") List<EstimateDutyDTO> estimateDutyListUpdate);

    void modifyBatchEstimateDuty(@Param("paramList") List<EstimateDutyDTO> estimateDutyList);

    public void modifyBatchEstimateType(@Param("caseNoList") List<String> caseNoList, @Param("caseTimes") Integer caseTimes);

    public List<EstimateDutyDTO> getEstimateDutyDTOList(@Param("caseNoList") List<String> caseNoList, @Param("caseTimes") Integer caseTimes);

    List<EstimateDutyDTO> getDutyListByIdPlanList(@Param("paramList") List<String> paramList);

    void delEstimateDutyDataByCaseNo(@Param("caseNo") String caseNo, @Param("caseTimes") Integer caseTimes);

    /**
     * 案件重开，数据拷贝
     * @param paramList
     */
    void copyForCaseReopen(List<EstimateDutyDTO> paramList);

    void delEstimateDutyByCaseNo(@Param("caseNoList") List<String> caseNoList,@Param("caseTimes") Integer caseTimes);
}
