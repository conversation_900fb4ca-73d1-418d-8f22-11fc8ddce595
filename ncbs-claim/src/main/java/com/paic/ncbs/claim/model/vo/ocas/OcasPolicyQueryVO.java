package com.paic.ncbs.claim.model.vo.ocas;

import com.paic.ncbs.claim.model.dto.ocas.OcasPolicyDTO;
import com.paic.ncbs.claim.common.page.Pager;

import java.util.List;

public class OcasPolicyQueryVO {
    private String certificateType;
    private String certificateNo;
    private String insuredName;
    private String personnelAttribute;
    private String policyNo;
    private List<OcasPolicyDTO> policyList;
    private List<String> departmentCodes;
    private Pager pager;
    private String idPlyRiskProperty;
    private List<String> idPlyRiskPropertyList;

    public List<String> getDepartmentCodes() {
        return departmentCodes;
    }

    public void setDepartmentCodes(List<String> departmentCodes) {
        this.departmentCodes = departmentCodes;
    }

    public String getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(String certificateType) {
        this.certificateType = certificateType;
    }

    public String getCertificateNo() {
        return certificateNo;
    }

    public void setCertificateNo(String certificateNo) {
        this.certificateNo = certificateNo;
    }

    public String getInsuredName() {
        return insuredName;
    }

    public void setInsuredName(String insuredName) {
        this.insuredName = insuredName;
    }

    public String getPersonnelAttribute() {
        return personnelAttribute;
    }

    public void setPersonnelAttribute(String personnelAttribute) {
        this.personnelAttribute = personnelAttribute;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public List<OcasPolicyDTO> getPolicyList() {
        return policyList;
    }

    public void setPolicyList(List<OcasPolicyDTO> policyList) {
        this.policyList = policyList;
    }

    public Pager getPager() {
        return pager;
    }

    public void setPager(Pager pager) {
        this.pager = pager;
    }

    public String getIdPlyRiskProperty() {
        return idPlyRiskProperty;
    }

    public void setIdPlyRiskProperty(String idPlyRiskProperty) {
        this.idPlyRiskProperty = idPlyRiskProperty;
    }

    public List<String> getIdPlyRiskPropertyList() {
        return idPlyRiskPropertyList;
    }

    public void setIdPlyRiskPropertyList(List<String> idPlyRiskPropertyList) {
        this.idPlyRiskPropertyList = idPlyRiskPropertyList;
    }
}
