package com.paic.ncbs.claim.dao.mapper.riskppt;

import com.paic.ncbs.claim.model.dto.riskppt.RiskPropertyPayDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface RiskPropertyPayMapper {

    List<RiskPropertyPayDTO> getRiskPropertyPayList(RiskPropertyPayDTO riskPropertyPayDTO);

    void saveRiskPropertyPayList(@Param("riskPropertyPayList") List<RiskPropertyPayDTO> riskPropertyPayList);

    void removeRiskPropertyPay(RiskPropertyPayDTO riskPropertyPayDTO);

    List<RiskPropertyPayDTO> getDutyDetailPayedList(@Param("reportNo") String reportNo,@Param("caseTimes")Integer caseTimes);

    List<RiskPropertyPayDTO> getRiskPropertyPlanPayList(RiskPropertyPayDTO riskPropertyPayDTO);

    List<RiskPropertyPayDTO> getRiskPropertyDutyPay(RiskPropertyPayDTO riskPropertyPayDTO);

}