package com.paic.ncbs.claim.dao.mapper.checkloss;


import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.duty.PersonDeathDTO;
import com.paic.ncbs.claim.model.dto.duty.PersonDisabilityDTO;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import com.paic.ncbs.claim.model.vo.duty.PersonDeathVO;
import com.paic.ncbs.claim.model.vo.settle.SettleDeathVO;
import com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;


@MapperScan
public interface PersonDeathMapper  extends BaseDao<PersonDeathDTO> {

	 
	public void savePersonDeath(PersonDeathDTO personDeathDTO);
	
	 
	public void removePersonDeath(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId);

	void updateEffective(PersonDeathDTO personDeathDTO);
	
	 
	public PersonDeathDTO getPersonDeath(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId, @Param("status") String status);
	
	 
	public SettleDeathVO getSettleDeath(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId, @Param("status") String status);
	
	 
	public PersonDeathDTO getPersonDeathDTO(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId);
	
	 
	public PersonDeathDTO getPersonDeathByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") String caseTimes, @Param("taskId") String taskId, @Param("status") String status);
	
	 
	public String getPersonDeathDateByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

	/**
	 * 案件重开，数据拷贝
	 * @param dto
	 */
	void copyForCaseReopen(CaseReopenCopyDTO dto);

	/**
	 * 查询死亡信息数据--根据报案号，任务号，赔付次数
	 * @param personTranceRequestVo
	 * @return
	 */
	public List<PersonDeathVO> selectPersonDeathDTO(PersonTranceRequestVo personTranceRequestVo);

	/**
	 * 修改
	 * @param personDisabilityDTO
	 * @return
	 */
	int updateByPrimaryKeySelective(PersonDeathDTO personDisabilityDTO);

	/**
	 * 删除
 	 * @param personTranceRequestVo
	 */
	void deletePersonDeath(PersonTranceRequestVo personTranceRequestVo);
}
