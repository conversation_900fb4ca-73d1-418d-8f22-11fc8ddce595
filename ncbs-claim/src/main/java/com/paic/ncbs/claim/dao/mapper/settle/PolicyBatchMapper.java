package com.paic.ncbs.claim.dao.mapper.settle;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.settle.PolicyBatchPayDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface PolicyBatchMapper extends BaseDao<PolicyBatchPayDTO> {

    void insertPolicyBatch(@Param("policyBatch") PolicyBatchPayDTO policyBatch);

    PolicyBatchPayDTO getPolicyBatch(@Param("policyNo") String policyNo, @Param("caseTimes") Integer caseTimes, @Param("idAhcsBatch") String idAhcsBatch);

    List<PolicyBatchPayDTO> listPolicyBatchs(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    void bathInsertPolicyBath(@Param("policyBathPayList") List<PolicyBatchPayDTO> policyBathPayList);

    void bathUpdatePolicyBath(PolicyBatchPayDTO policyBathPayList);

    List<PolicyBatchPayDTO> getPolicyBatchsByCliamType(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("claimType") String claimType);

    void deleteByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    void bathUpdatePolicy(@Param("list") List<PolicyBatchPayDTO> policyBatchPays);
}
