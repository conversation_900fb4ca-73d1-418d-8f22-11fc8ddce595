package com.paic.ncbs.claim.model.vo.policy;

import java.math.BigDecimal;

public class PolicyDutyDetailVO {
	 
	private String dutyDetailName;
	
	 
	private String dutyDetailCode;
	
	 
	private BigDecimal dutyAmount;
	
	 
	private BigDecimal detailMaxPay;
	
	 
	private String isMergePolicyDuty;

	public String getDutyDetailName() {
		return dutyDetailName;
	}

	public void setDutyDetailName(String dutyDetailName) {
		this.dutyDetailName = dutyDetailName;
	}

	public String getDutyDetailCode() {
		return dutyDetailCode;
	}

	public void setDutyDetailCode(String dutyDetailCode) {
		this.dutyDetailCode = dutyDetailCode;
	}

	public BigDecimal getDutyAmount() {
		return dutyAmount;
	}

	public void setDutyAmount(BigDecimal dutyAmount) {
		this.dutyAmount = dutyAmount;
	}

	public BigDecimal getDetailMaxPay() {
		return detailMaxPay;
	}

	public void setDetailMaxPay(BigDecimal detailMaxPay) {
		this.detailMaxPay = detailMaxPay;
	}

	public String getIsMergePolicyDuty() {
		return isMergePolicyDuty;
	}

	public void setIsMergePolicyDuty(String isMergePolicyDuty) {
		this.isMergePolicyDuty = isMergePolicyDuty;
	}
}
