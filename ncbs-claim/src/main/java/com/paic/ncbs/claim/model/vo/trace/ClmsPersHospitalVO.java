package com.paic.ncbs.claim.model.vo.trace;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * 通过ins-framework-mybatis工具自动生成，请勿手工修改。表clms_pers_hospital的PO对象<br/>
 * 对应表名：clms_pers_hospital,备注：人伤医院情况表
 *
 */
@ApiModel("医院信息表")
@Data
public class ClmsPersHospitalVO  implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：id,备注：主键 */
	@TableField(value="id")
	private Integer id;
	/** 对应字段：report_no,备注：报案号 */
	@TableField(value="report_no")
	private String reportNo;
	/** 对应字段：case_times,备注：赔付次数 */
	@TableField(value="case_times")
	private Integer caseTimes;
	/** 对应字段：injured_id,备注：伤亡人员id */
	@TableField(value="injured_id")
	private Integer injuredId;
	/** 对应字段：person_name,备注：人员名称 */
	@TableField(value="person_name")
	private String personName;
	/** 对应字段：risk_code,备注：险种代码 */
	@TableField(value="risk_code")
	private String riskCode;
	/** 对应字段：inhospital_days,备注：估计治疗/住院天数 */
	@TableField(value="inhospital_days")
	private Integer inhospitalDays;
	/** 对应字段：hospital_bed,备注：床位 */
	@TableField(value="hospital_bed")
	private String hospitalBed;
	/** 对应字段：in_hospital_date,备注：住院日期 */
	@TableField(value="in_hospital_date")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date inHospitalDate;
	/** 对应字段：out_hospital_date,备注：出院日期 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	@TableField(value="out_hospital_date")
	private Date outHospitalDate;
	/** 对应字段：hospital_province,备注：医院所在省份 */
	@TableField(value="hospital_province")
	private String hospitalProvince;
	/** 对应字段：hospital_city,备注：医院所在城市 */
	@TableField(value="hospital_city")
	private String hospitalCity;
	/** 对应字段：hospital_code,备注：医院代码 */
	@TableField(value="hospital_code")
	private String hospitalCode;
	/** 对应字段：hospital_name,备注：医院名称 */
	@TableField(value="hospital_name")
	private String hospitalName;
	/** 对应字段：hospital_grade,备注：医院等级 */
	@TableField(value="hospital_grade")
	private String hospitalGrade;
	/** 对应字段：agency_type,备注：服务机构类型 */
	@TableField(value="agency_type")
	private String agencyType;
	/** 对应字段：agency_attributes,备注：服务机构属性 */
	@TableField(value="agency_attributes")
	private String agencyAttributes;
	/** 对应字段：is_relative_hospital,备注：是否协议医院 */
	@TableField(value="is_relative_hospital")
	private String isRelativeHospital;
	/** 对应字段：main_physician,备注：主治医师 */
	@TableField(value="main_physician")
	private String mainPhysician;
	/** 对应字段：main_physician_phone,备注：主治医师联系方式 */
	@TableField(value="main_physician_phone")
	private String mainPhysicianPhone;
	/** 对应字段：admin_office,备注：科室 */
	@TableField(value="admin_office")
	private String adminOffice;
	/** 对应字段：is_transfers,备注：是否转院治疗 */
	@TableField(value="is_transfers")
	private String isTransfers;
	/** 对应字段：major_diagnosis,备注：主要诊断情况 */
	@TableField(value="major_diagnosis")
	private String majorDiagnosis;
	/** 对应字段：hospital_course,备注：住院情况 */
	@TableField(value="hospital_course")
	private String hospitalCourse;
	/** 对应字段：remark,备注：备注 */
	@TableField(value="remark")
	private String remark;
	/** 对应字段：valid_flag,备注：有效标志 */
	@TableField(value="valid_flag")
	private String validFlag;
	/** 对应字段：flag,备注：标志字段 */
	@TableField(value="flag")
	private String flag;
	/** 对应字段：created_by,备注：创建人 */
	@TableField(value="created_by")
	private String createdBy;
	/** 对应字段：sys_ctime,备注：创建时间 */
	@TableField(value="sys_ctime")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date sysCtime;
	/** 对应字段：updated_by,备注：最新修改人员 */
	@TableField(value="updated_by")
	private String updatedBy;
	/** 对应字段：sys_utime,备注：最新修改时间 */
	@TableField(value="sys_utime")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date sysUtime;

}
