package com.paic.ncbs.claim.model.vo.nbs;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class MiniOrderInfo {


    /**
     * 关联的保单号
     */
    private String policyNo;
    /**
     * 渠道传来的小订单号
     */
    private String miniOrderNo;
    /**
     * 保费金额
     */
    private BigDecimal premium;

    /**
     * 保险金额
     */
    private BigDecimal insuranceAmount;
    /**
     * 被保人姓名
     */
    private String insuranceName;
    /**
     * 被保人证件号码
     */
    private String certificateNo;
    /**
     * 被保人证件类型
     */
    private String certificateType;
    /**
     * 服务时长
     */
    private String serviceDuration;
    /**
     * 预约服务开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date bookStartTime;
    /**
     * 服务地址
     */
    private String serviceAddress;
    /**
     * 订单类目
     */
    private String dealGroupCategory;
    /**
     * 承保产品代码
     */
    private String productCode;
    /**
     * 责任code
     */
    private String dutyCode;
    /**
     * 承保方案代码
     */
    private String planCode;
    /**
     * 订单状态
     */
    private String orderStatus;
    /**
     * 小订单的保险起期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;
    /**
     * 小订单的保险止期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;
    /**
     * 商户ID
     */
    private String customerId;
    /**
     * 商家名称
     */
    private String customerName;
    /**
     * 门店名称
     */
    private String shopName;
    /**
     * document表的groupId
     */
    private String documentGroupId;

}
