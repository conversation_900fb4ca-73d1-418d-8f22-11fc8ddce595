package com.paic.ncbs.claim.model.vo.duty;

import com.paic.ncbs.claim.model.dto.duty.PersonBenefitDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("津贴信息表VO")
public class PersonBenefitVO {

    @ApiModelProperty("")
    private String moduleCode;

    @ApiModelProperty("津贴类型")
    private List<String> benefitTypes;

    @ApiModelProperty("津贴信息表DTO列表")
    private List<List<PersonBenefitDTO>> personBenefitList;

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public List<String> getBenefitTypes() {
        return benefitTypes;
    }

    public void setBenefitTypes(List<String> benefitTypes) {
        this.benefitTypes = benefitTypes;
    }

    public List<List<PersonBenefitDTO>> getPersonBenefitList() {
        return personBenefitList;
    }

    public void setPersonBenefitList(List<List<PersonBenefitDTO>> personBenefitList) {
        this.personBenefitList = personBenefitList;
    }
}
