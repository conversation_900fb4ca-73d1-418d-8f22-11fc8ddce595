package com.paic.ncbs.claim.dao.mapper.checkloss;

import com.paic.ncbs.claim.model.dto.duty.PersonObjectDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface PersonObjectMapper {

    void savePersonObject(PersonObjectDTO personObject);

    List<String> getPartyNo(@Param("reportNo") String reportNo);

    String getClientNoByPolicyNo(@Param("reportNo") String reportNo, @Param("idPolicyInfo") String idPolicyInfo);

    String getClientNoByReportNo(@Param("reportNo") String reportNo);

    int getCustomerNoByPolicyNo(@Param("reportNo") String reportNo, @Param("idPolicyInfo") String idPolicyInfo);

    String getCustomerNoByReportNo(@Param("reportNo") String reportNo, @Param("idPolicyInfo") String idPolicyInfo);

    String getClientNoByInsuredPerson(@Param("reportNo") String reportNo, @Param("idPolicyInfo") String idPolicyInfo);

    String getLossObjectNoByPartNo(@Param("reportNo") String reportNo, @Param("partyNo") String partyNo);

}
