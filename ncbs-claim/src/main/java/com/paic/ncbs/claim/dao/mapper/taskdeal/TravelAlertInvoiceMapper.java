package com.paic.ncbs.claim.dao.mapper.taskdeal;


import com.paic.ncbs.claim.model.dto.taskdeal.TravelAlertInvoiceDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface TravelAlertInvoiceMapper {

    void saveTravelAlertInvoiceList(@Param("travelAlertInvoiceList") List<TravelAlertInvoiceDTO> travelAlertInvoiceList);

    List<TravelAlertInvoiceDTO> getTravelAlertInvoiceList(@Param("reportNo") String reportNo);

    void updateTravelAlertInvoiceDTO(TravelAlertInvoiceDTO travelAlertInvoiceDTO);

    void deleteTravelAlertInvoice(@Param("idAhcsTravelAlertInvoice") String idAhcsTravelAlertInvoice);

    void deleTetravelAlertInvoiceForReportNo(@Param("reportNo") String reportNo, @Param("channelId") String channelId);

}