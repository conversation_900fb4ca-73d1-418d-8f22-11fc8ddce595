package com.paic.ncbs.claim.model.vo.duty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
@Data
public class LossEstimationVO {
    @ApiModelProperty("估损信息主键")
    private String idLossEstimation;
    @ApiModelProperty("报案号")
    private String reportNo;
    @ApiModelProperty("赔付次数")
    private Integer caseTimes;
    @ApiModelProperty("估损类型 3-人伤；4-健康；5-财产；6-其他")
    private String lossType;
    @ApiModelProperty("损失/人员名称")
    private String lossName;
    @ApiModelProperty("证件类型")
    private String certificateType;
    @ApiModelProperty("证件号码")
    private String certificateNo;
    @ApiModelProperty("医疗费")
    private BigDecimal medicalFee;
    @ApiModelProperty("误工费")
    private BigDecimal lossWorkingFee;
    @ApiModelProperty("损失金额")
    private BigDecimal damagesAmount;
    @ApiModelProperty("估损依据")
    private String lossBasis;
}
