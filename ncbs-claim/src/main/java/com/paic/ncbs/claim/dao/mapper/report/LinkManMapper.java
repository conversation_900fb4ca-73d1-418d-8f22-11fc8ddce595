package com.paic.ncbs.claim.dao.mapper.report;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.report.LinkManEntity;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import io.swagger.models.auth.In;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LinkManMapper extends BaseDao<LinkManEntity> {

    List<LinkManEntity> getLinkMans(@Param("reportNo") String reportNo,
                                    @Param("caseTimes") Integer caseTimes);

    void updateByReportNo(LinkManEntity entity);

    int isLinkManInfoChanged(LinkManEntity entity);

    void insertList(@Param("list") List<LinkManEntity> list);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);
}