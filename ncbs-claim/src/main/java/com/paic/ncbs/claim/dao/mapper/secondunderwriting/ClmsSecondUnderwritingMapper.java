package com.paic.ncbs.claim.dao.mapper.secondunderwriting;

import com.paic.ncbs.claim.dao.entity.clms.ClmsSecondUnderwritingEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 理赔二核申请记录表(ClmsSecondUnderwritingEntity)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-08 15:15:58
 */
public interface ClmsSecondUnderwritingMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ClmsSecondUnderwritingEntity queryById(String id);

    /**
     * 通过报案号查询单条数据
     *
     * @return 实例对象
     */
    List<ClmsSecondUnderwritingEntity> queryByReportNo(@Param("reportNo") String reportNo,
                                                       @Param("caseTimes") Integer caseTimes);

    /**
     * 新增数据
     *
     * @param clmsSecondUnderwritingEntity 实例对象
     * @return 影响行数
     */
    int insert(ClmsSecondUnderwritingEntity clmsSecondUnderwritingEntity);

    /**
     * 修改数据
     *
     * @param clmsSecondUnderwritingEntity 实例对象
     * @return 影响行数
     */
    int update(ClmsSecondUnderwritingEntity clmsSecondUnderwritingEntity);

    /**
     * 根据报案号赔付次数查询送核状态为01-送核审批中 的数据
     * 一个报案号只会存在 一条 状态为 01-送核审批中的数据
     **/
    ClmsSecondUnderwritingEntity getUWRecord(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);


    /**
     * 查询核保通过的函件信息
     *
     * @param reportNo
     * @param caseTimes
     * @return
     */
    Integer getUnderwritingPassRecord(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    /**
     * 根据报案号，赔付次数查询 已核保完成 的且按序号降序排序的数据
     */
    ClmsSecondUnderwritingEntity getUwInfoOrderBySerialNo(@Param("reportNo") String reportNo,
                                                          @Param("caseTimes") Integer caseTimes);

    String getUwsLetterCode(@Param("manualInfoId") String manualInfoId);

    /**
     * 查询案件对应保单与出险人历次所有的二核记录
     *
     * @param reportNo
     * @param caseTimes
     * @param underwritingStatus  记录状态
     * @return
     */
    List<ClmsSecondUnderwritingEntity> getSecondUWRecord(@Param("reportNo") String reportNo,
                                                         @Param("caseTimes") Integer caseTimes,
                                                         @Param("underwritingStatus") List<String> underwritingStatus);

    /**
     * 查询案件对应保单与出险人历次拒保二核记录
     *
     * @param clientNo
     * @return
     */
    List<ClmsSecondUnderwritingEntity> getRejectSecondUWByClientNo(@Param("clientNo") String clientNo);
}

