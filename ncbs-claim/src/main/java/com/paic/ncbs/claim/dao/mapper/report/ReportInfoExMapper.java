package com.paic.ncbs.claim.dao.mapper.report;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoExEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ReportInfoExMapper extends BaseDao<ReportInfoExEntity> {

    List<ReportInfoExEntity> getReportInfoEx(String reportNo);

    ReportInfoExEntity getAcceptanceNoByReportNo(String reportNo);

    int updataReportInfoExByReportNo(ReportInfoExEntity reportInfoExEntity);

    void insertList(@Param("list") List<ReportInfoExEntity> list);

    List<ReportInfoExEntity> getReportInfoExByAcceptanceNo(String acceptanceNo);

    void updateCompanyIdByReportNo(@Param("reportNo") String reportNo, @Param("companyId") String companyId);

    void updateCaseClassByReportNo(@Param("reportNo") String reportNo, @Param("caseClass") String caseClass, @Param("loginUm") String loginUm );
}