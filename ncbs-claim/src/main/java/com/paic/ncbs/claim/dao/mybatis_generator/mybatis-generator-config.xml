<!--<?xml version="1.0" encoding="UTF-8" ?>-->
<!--<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"-->
<!--        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd" >-->
<!--<generatorConfiguration>-->
<!--    &lt;!&ndash;加载数据库连接驱动包&ndash;&gt;-->
<!--    <classPathEntry-->
<!--            location="D:\maven\repository\mysql\mysql-connector-java\8.0.25\mysql-connector-java-8.0.25.jar"/>-->

<!--    <context id="context1" targetRuntime="MyBatis3">-->
<!--        <commentGenerator>-->
<!--            <property name="suppressDate" value="true"/>-->
<!--            <property name="suppressAllComments" value="true"/>-->
<!--        </commentGenerator>-->

<!--        <jdbcConnection driverClass="com.mysql.jdbc.Driver"-->
<!--                        connectionURL="************************************************************"-->
<!--                        userId="ncbstechdata" password="Drd1$5y7"/>-->

<!--        &lt;!&ndash; 生成模型的包名和位置&ndash;&gt;-->
<!--        <javaModelGenerator targetPackage="com.paic.ncbs.claim.dao.entity" targetProject="./src/main/java">-->

<!--        </javaModelGenerator>-->
<!--        &lt;!&ndash; 生成映射文件的包名和位置&ndash;&gt;-->
<!--        <sqlMapGenerator targetPackage="mapper" targetProject="./src/main/resources">-->

<!--        </sqlMapGenerator>-->
<!--        &lt;!&ndash; 生成DAO的包名和位置&ndash;&gt;-->
<!--        <javaClientGenerator type="XMLMAPPER" targetPackage="com.paic.ncbs.claim.dao.mapper" targetProject="./src/main/java">-->
<!--        </javaClientGenerator>-->

<!--        <table tableName="clms_estimate_record" domainObjectName="ClmsEstimateRecordEntity" mapperName="ClmsEstimateRecordMapper"-->
<!--               enableCountByExample="false" enableUpdateByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false"-->
<!--               selectByExampleQueryId="false">-->
<!--            <generatedKey column="id_clms_estimate_record" sqlStatement="JDBC"/>-->
<!--        </table>-->
<!--    </context>-->
<!--</generatorConfiguration>-->
