package com.paic.ncbs.claim.model.vo.risk;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("风险信息")
public class RiskInfoVO {

	@ApiModelProperty("风险来源")
	private String riskFrom;

	@ApiModelProperty("信息来源（1-内部;2-外部）")
	private String infoFrom;

	@ApiModelProperty("操作机构CODE")
	private String departmentCode;

	@ApiModelProperty("风险信息来源环节")
	private String tacheFrom;
	@ApiModelProperty("风险详细信息")
	private List<RiskDetailVO> riskDetailVOList;

	@ApiModelProperty("风险详细主要信息")
	private List<RiskDetailVO> riskDetailManList;

	@ApiModelProperty("风险详细分组信息")
	private List<RiskDetailVO> riskDetailGroupList;
	@ApiModelProperty("风险信息来源环节列表")
	private List<String> riskFromList;

	@ApiModelProperty("规则组执行结果")
	private List<RmGroupResultVO> rmGroupResultList;

	@ApiModelProperty("风险信息")
	private String riskPrompt;

	public List<RmGroupResultVO> getRmGroupResultList() {
		return rmGroupResultList;
	}

	public void setRmGroupResultList(List<RmGroupResultVO> rmGroupResultList) {
		this.rmGroupResultList = rmGroupResultList;
	}

	public String getRiskFrom() {
		return this.riskFrom;
	}

	public void setRiskFrom(String riskFrom) {
		this.riskFrom = riskFrom;
	}

	public String getInfoFrom() {
		return this.infoFrom;
	}

	public void setInfoFrom(String infoFrom) {
		this.infoFrom = infoFrom;
	}

	public String getDepartmentCode() {
		return this.departmentCode;
	}

	public void setDepartmentCode(String departmentCode) {
		this.departmentCode = departmentCode;
	}

	public String getTacheFrom() {
		return this.tacheFrom;
	}

	public void setTacheFrom(String tacheFrom) {
		this.tacheFrom = tacheFrom;
	}

	public List<RiskDetailVO> getRiskDetailVOList() {
		return riskDetailVOList;
	}

	public void setRiskDetailVOList(List<RiskDetailVO> riskDetailVOList) {
		this.riskDetailVOList = riskDetailVOList;
	}

	public List<RiskDetailVO> getRiskDetailManList() {
		return riskDetailManList;
	}

	public void setRiskDetailManList(List<RiskDetailVO> riskDetailManList) {
		this.riskDetailManList = riskDetailManList;
	}

	public List<RiskDetailVO> getRiskDetailGroupList() {
		return riskDetailGroupList;
	}

	public void setRiskDetailGroupList(List<RiskDetailVO> riskDetailGroupList) {
		this.riskDetailGroupList = riskDetailGroupList;
	}

	public List<String> getRiskFromList() {
		return riskFromList;
	}

	public void setRiskFromList(List<String> riskFromList) {
		this.riskFromList = riskFromList;
	}

	public String getRiskPrompt() {
		return riskPrompt;
	}

	public void setRiskPrompt(String riskPrompt) {
		this.riskPrompt = riskPrompt;
	}
	

}
