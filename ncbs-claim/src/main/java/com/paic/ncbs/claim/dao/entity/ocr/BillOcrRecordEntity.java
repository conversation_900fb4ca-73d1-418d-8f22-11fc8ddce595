package com.paic.ncbs.claim.dao.entity.ocr;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 账单OCR记录表
 */
@Data
@TableName(value = "clm_bill_ocr_record")
public class BillOcrRecordEntity implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 删除标记
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     * 付款人姓名
     */
    @TableField(value = "payer_name")
    private String payerName;

    /**
     * 报案号
     */
    @TableField(value = "report_no")
    private String reportNo;

    /**
     * 发票号
     */
    @TableField(value = "receipt_no")
    private String receiptNo;

    /**
     * 发票金额
     */
    @TableField(value = "receipt_amount")
    private BigDecimal receiptAmount;

    /**
     * 就诊日期
     */
    @TableField(value = "in_hospital_date")
    private Date inHospitalDate;

    /**
     * 医保类型,1.社保；2.普通
     */
    @TableField(value = "bill_type")
    private String billType;

    /**
     * 就诊人性别,F:女；M:男
     */
    @TableField(value = "gender")
    private String gender;

    /**
     * 第三方支付金额
     */
    @TableField(value = "third_pay_amount")
    private BigDecimal thirdPayAmount;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 个人账户金额
     */
    @TableField(value = "self_account_amount")
    private BigDecimal selfAccountAmount;

    /**
     * 个人现金支付
     */
    @TableField(value = "self_cash_amount")
    private BigDecimal selfCashAmount;

    /**
     * 医院名称
     */
    @TableField(value = "hospital_name")
    private String hospitalName;

    /**
     * 数据渠道
     */
    @TableField(value = "channel")
    private String channel;

    /**
     * 文件地址
     */
    @TableField(value = "upload_path")
    private String uploadPath;

    /**
     * 文件名
     */
    @TableField(value = "file_name")
    private String fileName;

    /**
     * 创建人员
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "sys_ctime")
    private Date sysCtime;

    /**
     * 修改人员
     */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(value = "sys_utime")
    private Date sysUtime;

    /**
     * 发票分类，01-门诊，02-住院，03-药房，04-废票
     */
    @TableField(value = "bill_classify")
    private String billClassify;

    /**
     * 风险提示信息代码
     */
    @TableField(value = "risk_info_code_list")
    private String riskInfoCodeList;

    /**
     * 医疗机构类型
     */
    @TableField(value = "hospital_type")
    private String hospitalType;

    /**
     * 医保统筹支付
     */
    @TableField(value = "medical_insurance_fund_payment")
    private BigDecimal medicalInsuranceFundPayment;

    private static final long serialVersionUID = 1L;
}