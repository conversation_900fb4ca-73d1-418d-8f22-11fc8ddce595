package com.paic.ncbs.claim.dao.mapper.checkloss;

import com.paic.ncbs.claim.model.dto.endcase.CaseInfoParameterDTO;
import com.paic.ncbs.claim.model.dto.checkloss.ChannelProcessDTO;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface ChannelProcessMapper {

    List<String> getChannelProcessId(CaseInfoParameterDTO caseInfoParameter);

    void saveChannelProcess(ChannelProcessDTO channelProcess);

    String getChannelProcessIdByreportNo(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes);

    List<ChannelProcessDTO> getChannelProcessIds(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes);

    List<ChannelProcessDTO> getChannelProcessIdsBySurveyId(@Param("reportNo") String reportNo,
                                                           @Param("caseTimes") int caseTimes,
                                                           @Param("idAhcsAdditionalSurvey") String idAhcsAdditionalSurvey);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);
}
