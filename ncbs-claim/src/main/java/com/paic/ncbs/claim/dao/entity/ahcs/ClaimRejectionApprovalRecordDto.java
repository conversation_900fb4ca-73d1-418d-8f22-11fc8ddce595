package com.paic.ncbs.claim.dao.entity.ahcs;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel("拒赔申明")
public class ClaimRejectionApprovalRecordDto {

    @ApiModelProperty("主键")
    private String idAhcsCaseRegisterApply;

    @ApiModelProperty("发起人UM")
    private String initiatorUm;

    @ApiModelProperty("发起时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date applyDate;

    @ApiModelProperty("审批人UM")
    private String auditUm;

    @ApiModelProperty("审批时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date auditDate;

    @ApiModelProperty("审批意见, 1-同意，2-不同意")
    private String auditOpinion;

    @ApiModelProperty("审批说明")
    private String auditRemark;

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("修改人员")
    private String updatedBy;

    @ApiModelProperty("修改时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date updatedDate;

    @ApiModelProperty("拒赔原因")
    private String rejectReason;

    @ApiModelProperty("拒赔原因CODE")
    private String rejectReasonCode;

    @ApiModelProperty("减损金额")
    private BigDecimal reduceAmount;


}