package com.paic.ncbs.claim.model.vo.report;

import com.paic.ncbs.claim.dao.entity.report.LinkManEntity;
import com.paic.ncbs.claim.model.convert.BeanConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

@ApiModel("联系人Vo")
@Data
public class LinkManVO {

	@ApiModelProperty("主键")
	private String idAhcsLinkMan;
	@ApiModelProperty("报案号")
	private String reportNo;
	@ApiModelProperty("赔付次数")
	private Short caseTimes;
	@ApiModelProperty("联系人姓名")
	private String linkManName;
	@ApiModelProperty("联系人电话")
	private String linkManTelephone;
	@ApiModelProperty("短信发送 Y：发送 N：不发送")
	private String sendMessage;

	public LinkManEntity convertToEntity(){
		LinkManVOConvert convert = new LinkManVOConvert();
		return convert.convert(this);
	}

	private static class LinkManVOConvert implements BeanConvert<LinkManVO,LinkManEntity> {

		@Override
		public LinkManEntity convert(LinkManVO linkManVO) {
			LinkManEntity linkManEntity = new LinkManEntity();
			BeanUtils.copyProperties(linkManVO,linkManEntity);
			return linkManEntity;
		}
	}

}
