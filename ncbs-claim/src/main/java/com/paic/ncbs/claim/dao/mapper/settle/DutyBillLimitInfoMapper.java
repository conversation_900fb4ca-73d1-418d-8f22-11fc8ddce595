package com.paic.ncbs.claim.dao.mapper.settle;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO;
import com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO;
import com.paic.ncbs.claim.model.vo.duty.DutyBillLimitDto;
import com.paic.ncbs.claim.model.vo.duty.DutyLimitQueryVo;
import com.paic.ncbs.claim.model.vo.settle.DutyBillLimitInfoVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@MapperScan
public interface DutyBillLimitInfoMapper extends BaseDao<DutyBillLimitInfoDTO> {
    /**
     * 根据报案信息查询保单责任信息
     * @param dutyBillLimitInfoDTO
     */
    void addDutyBillLimit(DutyBillLimitInfoDTO dutyBillLimitInfoDTO);

    /**
     * 查询
     * @param dutyBillLimitInfoDTO
     * @return
     */
    DutyBillLimitInfoDTO getBillDayLimit(DutyBillLimitInfoDTO dutyBillLimitInfoDTO);

    List<DutyBillLimitInfoDTO> getLimitBilltByReportNoAndCaseTimes(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    List<DutyBillLimitInfoDTO> getBillDate(String policyNo);

    List<DutyBillLimitInfoDTO> selectAll(String policyNo);


    String getPolicyNo(@Param("reportNo") String reportNo);

    BigDecimal getDaySum(DutyBillLimitInfoDTO dto);

    /**
     * 删除日限额
     * @param reportNo
     * @param caseTimes
     */
    void deleteByReportNoAndCaseTimes(@Param("reportNo")String reportNo, @Param("caseTimes") Integer caseTimes);

    BigDecimal getUpCasetimesAmount(DutyBillLimitInfoDTO paramsDto);

    /**
     * 更新上一次赔付的每日限额记录状态
     * @param reportNo
     * @param caseTimes
     */
    void updateUpCaseTimesHistory(@Param("reportNo")String reportNo, @Param("caseTimes") Integer caseTimes);

    List<DutyBillLimitInfoDTO> getDutyBillDateInfo(DutyBillLimitInfoDTO paramsDto);

    /**
     * 查询发票日已经核赔了的日限额赔付记录
     *
     * @param
     * @param parmDto
     */
    List<DutyBillLimitInfoDTO> getAllPayDutyLimitDate(DutyBillLimitInfoDTO parmDto);

    /**
     * 查询历史日限额赔付记录
     *     案件重开后之前的记录状态不是approval_status = ‘1’ 了。
     *
     * @param paramDTO
     */
    List<DutyBillLimitInfoDTO> getHisDutyBillLimitList(DutyBillLimitInfoDTO paramDTO);

    /**
     * 批量更新
     *
     * @param paramList
     */
    void updateBatchDutyBillLimit(@Param("paramList") List<DutyBillLimitInfoDTO> paramList);

    List<DutyBillLimitInfoDTO> getAutoSettleAmountByReportNo(@Param("reportNo") String reportNo,
                                                             @Param("caseTimes") int caseTimes);

    void updateByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes);

    // 查询参与计算的发票日期
    List<String> getAllDate(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes);

    List<DutyBillLimitDto> getAllAlreadyPayTimes(DutyLimitQueryVo dutyLimitQueryVo);

    List<DutyBillLimitDto> getPolicyAlreadyPayTimes(DutyLimitQueryVo dutyLimitQueryVo);

    List<DutyBillLimitDto> getAllAlreadyPayPolicy(DutyLimitQueryVo dutyLimitQueryVo);

    List<DutyBillLimitInfoDTO> getReportNoPolicyBillLimitInfo(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes);

    void saveList(List<DutyBillLimitInfoDTO> list);

    void updateNoLimitInfo(@Param("reportNo") String reportNo, @Param("caseTimes")Integer caseTimes);

    /**
     * 查询当前本次报案号下的赔付天数
     * @param reportNo
     * @param caseTimes
     * @return
     */
    List<DutyBillLimitInfoDTO> getYearlyPayDaysInfo(@Param("reportNo")String reportNo,@Param("caseTimes")Integer caseTimes);

    List<DutyBillLimitInfoDTO> getHistoryYearlyPayDaysInfo(DutyLimitQueryVo dutyLimitQueryVo);


    List<DutyBillLimitInfoVO> getDutyLimitPayList(DutyBillLimitInfoDTO dutyBillLimitInfoDTO);

    BigDecimal getHistoryAmount(@Param("reportNo") String reportNo,@Param("policyNo") String policyNo,@Param("startDate") Date startDate,@Param("endDate") Date endDate);

    /**
     * 查询剩余月限额
     * @param policyNo
     * @param planList
     * @param dutyList
     * @param satrtDate
     * @param endDate
     */
    List<DutyBillLimitInfoDTO> getUsedAmount(@Param("policyNo") String policyNo,
                                             @Param("planList") List<String> planList,
                                             @Param("dutyList") List<String> dutyList,
                                             @Param("satrtDate") String satrtDate, @Param("endDate") String endDate);

    List<DutyBillLimitInfoDTO> getBillLimitInfo(@Param("reportNo") String reportNo,
                                                @Param("caseTimes") Integer caseTimes);


    void updateDutyLimitById(DutyBillLimitInfoDTO dutyBillLimitInfoDTO);

    /**
     * 删除
     * @param dutyBillLimitInfoDTO
     */
    void deleteDutyBillByDutyCode(DutyBillLimitInfoDTO dutyBillLimitInfoDTO);

    void deleteDutyBillByDutyDetailCode(DutyBillLimitInfoDTO dutyBillLimitInfoDTO);

    List<DutyBillLimitInfoDTO> getDutyLimitData(DutyBillLimitInfoDTO dto);

    void updateDutyBillLimitAmount(List<DutyBillLimitInfoDTO> list);
}
