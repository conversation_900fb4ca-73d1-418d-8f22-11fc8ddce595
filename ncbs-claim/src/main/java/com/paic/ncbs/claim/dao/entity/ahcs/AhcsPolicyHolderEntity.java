package com.paic.ncbs.claim.dao.entity.ahcs;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

/**
 *投保人
 */
public class AhcsPolicyHolderEntity extends EntityDTO {

    private static final long serialVersionUID = 8187669029514724646L;

    private String idAhcsPolicyHolder;

    private String idAhcsPolicyInfo;

    private String name;

    private String address;

    private String telephone;

    private String personnelType;

    private String email;

    private String certificateType;

    private String certificateNo;

    private String clientNo;

    public String getIdAhcsPolicyHolder() {
        return idAhcsPolicyHolder;
    }

    public void setIdAhcsPolicyHolder(String idAhcsPolicyHolder) {
        this.idAhcsPolicyHolder = idAhcsPolicyHolder == null ? null : idAhcsPolicyHolder.trim();
    }

    public String getIdAhcsPolicyInfo() {
        return idAhcsPolicyInfo;
    }

    public void setIdAhcsPolicyInfo(String idAhcsPolicyInfo) {
        this.idAhcsPolicyInfo = idAhcsPolicyInfo == null ? null : idAhcsPolicyInfo.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone == null ? null : telephone.trim();
    }

    public String getPersonnelType() {
        return personnelType;
    }

    public void setPersonnelType(String personnelType) {
        this.personnelType = personnelType;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(String certificateType) {
        this.certificateType = certificateType;
    }

    public String getCertificateNo() {
        return certificateNo;
    }

    public void setCertificateNo(String certificateNo) {
        this.certificateNo = certificateNo;
    }

    public String getClientNo() {
        return clientNo;
    }

    public void setClientNo(String clientNo) {
        this.clientNo = clientNo;
    }
}