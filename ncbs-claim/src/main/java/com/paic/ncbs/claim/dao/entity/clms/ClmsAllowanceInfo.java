package com.paic.ncbs.claim.dao.entity.clms;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 给付津贴信息表(ClmsAllowanceInfo)实体类
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:45
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ClmsAllowanceInfo implements Serializable {
    private static final long serialVersionUID = -21512334022224557L;
    /**
     * 主键
     */
    private String id;
    /**
     * 报案号
     */
    private String reportNo;
    /**
     * 赔付次数
     */
    private Integer caseTimes;
    /**
     * 津贴类型 1 住院 和 2其他
     */
    private String allowanceType;

    /**
     * 其他津贴类型描述
     */
    private String otherTypeDescription;
    /**
     * 每日津贴金额
     */
    private BigDecimal dailyAllowanceAmount;

    /**
     * 津贴给付金额
     */
    private BigDecimal allowanceAmount;
    /**
     * '时间段 多个时间，逗号分隔 类似 2023.08.01-2023.08.03,2023.08.05-2023.08.08,2023.08.11-2023.08.13
     */
    private String datePeriod;
    /**
     * 创建人员
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDate;
    /**
     * 修改人员
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDate;

    /*
    * 津贴单位 1-日津贴；2-次津贴；3-其他
    */
    private String allowanceUnit;

    /*
     * 津贴名称
     */
    private String allowanceName;
    /*
     * 单价
     */
    private BigDecimal monovalent;
    /*
     * 定损金额
     */
    private BigDecimal estimateAmount;
    /*
     * 次数
     */
    private String frequency;
    /*
     * 数量
     */
    private String number;
    /*
     * 开始时间
     */
    private Date startTime;
    /*
     * 结束时间
     */
    private Date endTime;
    /*
     * 天数
     */
    private Integer days;
}

