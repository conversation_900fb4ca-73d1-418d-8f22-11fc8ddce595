package com.paic.ncbs.claim.dao.entity.report;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class BatchReportTempEntity {

    private String idAhcsBatchReportTemp;

    private String createdBy;

    private Date createdDate;

    private String updatedBy;

    private Date updatedDate;

    private String reportBatchNo;

    private String orderNo;

    private String batchResult;

    private String batchStatus;

    private String policyNo;

    private String reportNo;

    private String caseNo;

    private Date accidentDate;

    private String clientName;

    private String planCode;

    private String dutyCode;

    private BigDecimal dutyAmount;

    private String certificateType;

    private String certificateNo;

    private Short caseTimes;

    private String bankAccount;

    private String payeeName;

    private BigDecimal paymentAmount;

    private String accidentType;

    private String mobileNo;

    private String isSuperMaxPayment;

    private String dutyAmountList;

    private Integer mergeCount;

    private List<String> policyNos;

}