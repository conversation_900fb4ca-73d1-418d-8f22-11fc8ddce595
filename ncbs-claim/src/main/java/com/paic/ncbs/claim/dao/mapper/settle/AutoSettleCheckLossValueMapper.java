package com.paic.ncbs.claim.dao.mapper.settle;

import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface AutoSettleCheckLossValueMapper {

    String getCheckLossPayPattern(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    String getCustomerAge(@Param("reportNo") String reportNo);

    String getPersonDeathPaperType(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("taskId") String taskId);

    String getPersonDeathThirdPayAmount(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    String getPersonDeathThirdPayAmountByTaskId(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("taskId") String taskId);

    int getCaseTimesWhole(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo);

    int getCaseTimesDuty(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo, @Param("planCode") String planCode, @Param("dutyCode") String dutyCode);
}
