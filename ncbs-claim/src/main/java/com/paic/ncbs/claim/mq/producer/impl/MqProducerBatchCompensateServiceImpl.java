//package com.paic.ncbs.claim.mq.producer.impl;
//
//import cn.hutool.core.collection.CollectionUtil;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.alibaba.nacos.common.utils.CollectionUtils;
//import com.paic.ncbs.claim.common.constant.*;
//import com.paic.ncbs.claim.common.enums.*;
//import com.paic.ncbs.claim.dao.entity.ahcs.AdressSearchDto;
//import com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity;
//import com.paic.ncbs.claim.dao.entity.report.ReportAccidentEntity;
//import com.paic.ncbs.claim.dao.entity.report.ReportAccidentExEntity;
//import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
//import com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity;
//import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
//import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
//import com.paic.ncbs.claim.model.dto.duty.PersonDiagnoseDTO;
//import com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO;
//import com.paic.ncbs.claim.model.dto.mq.CompensateDto;
//import com.paic.ncbs.claim.model.dto.mq.prpL.*;
//import com.paic.ncbs.claim.model.dto.mq.swf.SwfFlowMainDto;
//import com.paic.ncbs.claim.model.dto.mq.swf.SwfLogStoreDto;
//import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
//import com.paic.ncbs.claim.model.dto.report.ReportBaseInfoResData;
//import com.paic.ncbs.claim.model.dto.settle.*;
//import com.paic.ncbs.claim.model.vo.investigate.InvestigateTaskAuditVO;
//import com.paic.ncbs.claim.model.vo.investigate.InvestigateVO;
//import com.paic.ncbs.claim.mq.producer.MqProducerBatchCompensateService;
//import com.paic.ncbs.claim.mq.producer.MqProducerCompensateService;
//import com.paic.ncbs.claim.mq.producer.MqProducerRegistService;
//import com.paic.ncbs.claim.common.constant.*;
//import com.paic.ncbs.claim.common.enums.*;
//import com.paic.ncbs.claim.common.util.BigDecimalUtils;
//import com.paic.ncbs.claim.common.util.CodeUtil;
//import com.paic.ncbs.claim.common.util.DateUtils;
//import com.paic.ncbs.claim.common.util.StringUtils;
//import com.paic.ncbs.claim.dao.mapper.checkloss.LossReduceMapper;
//import com.paic.ncbs.claim.dao.mapper.endcase.CaseClassMapper;
//import com.paic.ncbs.claim.dao.mapper.estimate.EstimatePolicyMapper;
//import com.paic.ncbs.claim.dao.mapper.fee.FeePayMapper;
//import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateMapper;
//import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateTaskAuditMapper;
//import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateTaskMapper;
//import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
//import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
//import com.paic.ncbs.claim.dao.mapper.settle.MedicalBillInfoMapper;
//import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
//import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
//import com.paic.ncbs.claim.model.dto.checkloss.LossReduceDTO;
//import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
//import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
//import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
//import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskDTO;
//import com.paic.ncbs.claim.model.dto.mq.claim.ClaimRepayCalReqDto;
//import com.paic.ncbs.claim.model.dto.mq.prpL.*;
//import com.paic.ncbs.claim.model.dto.settle.*;
//import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
//import com.paic.ncbs.claim.model.vo.duty.PeopleHurtVO;
//import com.paic.ncbs.claim.service.duty.DutySurveyService;
//import com.paic.ncbs.claim.service.endcase.CaseBaseService;
//import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
//import com.paic.ncbs.claim.service.endcase.WholeCaseService;
//import com.paic.ncbs.claim.service.fileupload.FileUploadService;
//import com.paic.ncbs.claim.service.other.CommonParameterService;
//import com.paic.ncbs.claim.service.report.ReportAccidentExService;
//import com.paic.ncbs.claim.service.report.ReportAccidentService;
//import com.paic.ncbs.claim.service.report.ReportCustomerInfoService;
//import com.paic.ncbs.claim.service.report.ReportInfoService;
//import com.paic.ncbs.claim.service.settle.PolicyPayService;
//import lombok.SneakyThrows;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.MapUtils;
////import org.springframework.amqp.rabbit.core.RabbitTemplate;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.scheduling.annotation.Async;
//import org.springframework.stereotype.Service;
//
//import java.math.BigDecimal;
//import java.text.SimpleDateFormat;
//import java.util.*;
//import java.util.stream.Collectors;
//
//import static com.paic.ncbs.claim.common.constant.Constants.PAYMENT_ITEM_STATUS_11;
//import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;
//
//@Service
//@Slf4j
//public class MqProducerBatchCompensateServiceImpl implements MqProducerBatchCompensateService {
//    @Value("${mq.rabbit.exchange.ncbs.claim.root:ncbsClaimExchange}")
//    private String ncbsClaimExchange;
//    //核赔环节topic
//    @Value("${mq.compensate.topic}")
//    private String compensateTopic;
//    @Autowired
//    private ReportCustomerInfoService reportCustomerInfoService;
//    @Autowired
//    private ReportAccidentService reportAccidentService;
//    @Autowired
//    private ReportAccidentExService reportAccidentExService;
//    @Autowired
//    private WholeCaseBaseService wholeCaseBaseService;
//    @Autowired
//    private ReportInfoService reportInfoService;
//    @Autowired
//    private InvestigateMapper investigateMapper;
//    @Autowired
//    private InvestigateTaskMapper investigateTaskMapper;
//    @Autowired
//    private InvestigateTaskAuditMapper investigateTaskAuditMapper;
//    @Autowired
//    private PolicyInfoMapper policyInfoMapper;
//    @Autowired
//    private PolicyPayService policyPayService;
//    @Autowired
//    private MqProducerRegistService mqProducerRegistService;
//    @Autowired
//    private FileUploadService fileUploadService;
//    @Autowired
//    private PaymentItemMapper paymentItemMapper;
//    @Autowired
//    private WholeCaseService wholeCaseService;
//    /*@Autowired
//    private RabbitTemplate rabbitTemplate;*/
//    @Autowired
//    private OcasMapper ocasMapper;
//    @Autowired
//    private DepartmentDefineMapper departmentDefineMapper;
//    @Autowired
//    private EstimatePolicyMapper estimatePolicyMapper;
//    @Autowired
//    private MedicalBillInfoMapper medicalBillInfoMapper;
//    @Autowired
//    private DutySurveyService dutySurveyService;
//    @Autowired
//    private LossReduceMapper lossReduceMapper;
//    @Autowired
//    private FeePayMapper feePayMapper;
//    @Autowired
//    private CaseBaseService caseBaseService;
//
//    @Autowired
//    private CaseClassMapper caseClassDao;
//
//    @Autowired
//    private CommonParameterService commonService;
//    @Autowired
//    private MqProducerCompensateService mqProducerCompensateService;
//    @Override
//    @Async("asyncPool")
//    public void syncProducerBatchCompensateLink(String reportNo, Integer caseTimes) {
//        // 数据组装
//        CompensateDto compensateDto=initProducerCompensateDto(reportNo,caseTimes);
//        //发送MQ消息
//        JSONObject jsonObj = (JSONObject) JSON.toJSON(compensateDto);
//        log.info("syncProducerBatchCompensateLink===sendMessage: {}",jsonObj);
//        // rabbitTemplate.convertAndSend(ncbsClaimExchange, compensateTopic, compensateDto);
//    }
//
//    private CompensateDto initProducerCompensateDto(String reportNo, Integer caseTimes) {
//        ReportInfoEntity reportInfo = reportInfoService.getReportInfo(reportNo);
//        //报案方式
//        reportInfo.setReportMode(ReportModeEnum.getName(reportInfo.getReportMode() == null ? "2" : reportInfo.getReportMode()));
//        //查询 报案客户 被保险人 信息表
//        ReportCustomerInfoEntity customerInfo = reportCustomerInfoService.getReportCustomerInfoByReportNo(reportNo);
//        //查询 意键险报案信息扩展表
//        ReportAccidentExEntity reportAccidentEx = reportAccidentExService.getReportAccidentEx(reportNo);
//        //查询 事故信息表
//        ReportAccidentEntity reportAccident = reportAccidentService.getReportAccident(reportNo);
//        WholeCaseBaseDTO wholeCaseBaseEntity = wholeCaseBaseService.getWholeCaseBase(reportNo, caseTimes);
//        //查询保单信息表
//        List<PolicyInfoDTO> policyInfoDTOList = policyInfoMapper.getPolicyInfoListByReportNo(reportNo);
//        PolicyInfoDTO policyInfoDTO = policyInfoDTOList.get(0);
//        // 人伤 --事故地点联动查询
//        ReportBaseInfoResData reportBaseInfo = wholeCaseService.getReportBaseInfo(reportNo, caseTimes);
//        // 查理赔理算保单赔付金额
//        List<PolicyPayDTO> policyPayList = policyPayService.getByReportNo(reportNo,caseTimes);
//        List<EstimatePolicyDTO> policyList = estimatePolicyMapper.getByReportNoAndCaseTimes(reportNo, caseTimes);
////        SttleBatchInfoVO settleAmountsSum = settleBatchService.getSettleAmountsSum(reportNo, caseTimes);
//        List<CaseBaseEntity> caseBaseInfoList = caseBaseService.getCaseBaseInfoByReportNoAndCasetimes(reportNo, String.valueOf(caseTimes));
//
//        Map<String, String> productMap = ocasMapper.getPlyBaseInfo(policyInfoDTO.getPolicyNo());
//        String productCode = MapUtils.getString(productMap,"productCode");
//        //减损金额
//        LossReduceDTO lossReduce = lossReduceMapper.getLossReduce(reportNo, caseTimes);
//        CompensateDto compensateDto = new CompensateDto();
//        //意健险调查主信息==============OK
//        List<InvestigateVO> investigateRecord = investigateMapper.getInvestigateRecord(reportNo, caseTimes);
//        if (CollectionUtils.isNotEmpty(investigateRecord)) {
//            PrpLAcciCheckDto prpLAcciCheckDto = initPrpLacciCheckDto(reportNo, policyInfoDTO, investigateRecord);
//            List<PrpLAcciCheckDto> prpLAcciCheckDtoList=new ArrayList<>();
//            prpLAcciCheckDtoList.add(prpLAcciCheckDto);
//            compensateDto.setPrpLAcciCheckDtoList(prpLAcciCheckDtoList);
//        }
//
//
//        // 单证收集表 ==============OK
//        PrpLCertifyCollectDto prpLCertifyCollectDto = getPrpLCertifyCollectDto(reportNo, caseTimes, policyInfoDTO);
//        compensateDto.setPrpLCertifyCollectDto(prpLCertifyCollectDto);
//
//        PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
//        paymentItemDTO.setReportNo(reportNo);
//        paymentItemDTO.setCaseTimes(caseTimes);
//        paymentItemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_10);
//        List<PaymentItemDTO> allItems = paymentItemMapper.getPaymentItem(paymentItemDTO);
//        // claimType=1 赔款类型：(13-赔款 1J直接理赔费用 "C13", "共保代付赔款" "C1J", "共保代付费用"
//        // claimType=2 11-预赔赔款 "11J", "预赔费用" ,"P13", "共保代付预赔赔款" "P1J", "共保代付预赔费用" )
//        Map<String, List<PaymentItemDTO>> itemGroupByType = allItems.stream().collect(Collectors.groupingBy(PaymentItemDTO::getClaimType));
//
//        //根据条件过滤 筛出实际核赔的
//        List<PaymentItemDTO> paymentItems = itemGroupByType.get("1");
//
//        // 赔款计算书表==============OK
//        // 计算书号去重
//        List<PaymentItemDTO> items = paymentItems.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(()
//                -> new TreeSet<>(Comparator.comparing(PaymentItemDTO::getCompensateNo))), ArrayList::new));
//        List<PrpLCompensateDto> prpLCompensateDtos = getPrpLCompensateDto(wholeCaseBaseEntity, reportBaseInfo,items,reportInfo,policyPayList,caseBaseInfoList);
//        compensateDto.setPrpLCompensateDtoList(prpLCompensateDtos);
//
//        List<PrpLPersonLossDto> prpLPersonLossDtoList=new ArrayList<>();
//        List<PrpLPersonLossTraceDto> prpLPersonLossTraceDtoList=new ArrayList<>();
//        for (PolicyPayDTO policy : policyPayList) {
//            int i=0;
//            List<PlanPayDTO> planPayList = policy.getPlanPayArr();
//            for (PlanPayDTO plan : planPayList) {
//                List<DutyPayDTO> dutyPayList = plan.getDutyPayArr();
//                for (DutyPayDTO duty : dutyPayList) {
//                    List<DutyDetailPayDTO> detailPayList = duty.getDutyDetailPayArr();
//                    for (DutyDetailPayDTO detail : detailPayList) {
//                        PrpLPersonLossDto prpLPersonLossDto = getPrpLPersonLossDto(detail, i, customerInfo, paymentItems, lossReduce,duty);
//                        prpLPersonLossDtoList.add(prpLPersonLossDto);
//                        PrpLPersonLossTraceDto prpLPersonLossTraceDto = getPrpLPersonLossTraceDto(detail, i, customerInfo, paymentItems, lossReduce,duty);
//                        prpLPersonLossTraceDtoList.add(prpLPersonLossTraceDto);
//                        i++;
//                    }
//                }
//            }
//        }
//        //意健险索赔申请人信息表 ==============OK
//        // 人伤全量数据
//        PeopleHurtVO peopleHurtVO = dutySurveyService.getPeopleHurtVO(reportNo, caseTimes);
//        List<PrpLAcciPersonDto> prpLAcciPersonDtoList = mqProducerRegistService.getPrpLAcciPersonDto(reportNo, customerInfo, reportAccidentEx, reportAccident,policyInfoDTO,reportBaseInfo,peopleHurtVO,caseBaseInfoList,"04",items);
//        compensateDto.setPrpLAcciPersonDtoList(prpLAcciPersonDtoList);
//        //人员赔付信息表==============OK
//        compensateDto.setPrpLPersonLossDtoList(prpLPersonLossDtoList);
//        //人员赔付信息轨迹表==============OK
//        compensateDto.setPrpLPersonLossTraceDtoList(prpLPersonLossTraceDtoList);
//
//        //赔款费用信息表==============OK    多个  1j
//        List<PrpLChargeDto> prpLChargeDtoList = initPrpLChargeDto(paymentItems,policyList);
//        compensateDto.setPrpLChargeDtoList(prpLChargeDtoList);
//
//        //赔款费用信息轨迹表==============OK
//        List<PrpLChargeTraceDto> prpLChargeTraceDtoList = initPrpLChargeTraceDto(paymentItems,policyList);
//        compensateDto.setPrpLChargeTraceDtoList(prpLChargeTraceDtoList);
//
//        //理赔支付对象表==============OK
//        List<PrpLClaimPaymentDto> prpLClaimPaymentDtoList = initPrpLClaimPaymentDto(paymentItems, reportInfo);
//        compensateDto.setPrpLClaimPaymentDtoList(prpLClaimPaymentDtoList);
//
//        //理赔支付对象轨迹表==============OK
//        List<PrpLClaimPaymentTraceDto> prpLClaimPaymentTraceDtoList = initPrpLClaimPaymentTraceDto(paymentItems,reportInfo);
//        compensateDto.setPrpLClaimPaymentTraceDtoList(prpLClaimPaymentTraceDtoList);
//
//        //出险结果表")
//        List<PrpLAccidentResultDto> prpLAccidentResultDtoList = getPrpLAccidentResultDto(wholeCaseBaseEntity, policyInfoDTO, productCode, paymentItems,caseBaseInfoList,customerInfo);
//        compensateDto.setPrpLAccidentResultDtoList(prpLAccidentResultDtoList);
//
//        //垫付支付对象表==============OK
//        //根据条件过滤 筛出垫付的
//        List<PrpLAdvancePaymentDto> prpLAdvancePaymentDtoList = initPrpLAdvancePaymentDto(allItems,caseBaseInfoList);
//        compensateDto.setPrpLAdvancePaymentDtoList(prpLAdvancePaymentDtoList);
//
//        MedicalBillInfoDTO medicalBillInfoQuery=new MedicalBillInfoDTO();
//        medicalBillInfoQuery.setReportNo(reportNo);
//        medicalBillInfoQuery.setCaseTimes(caseTimes);
//        List<MedicalBillInfoDTO> medicalBillInfoList = medicalBillInfoMapper.getBillInfoByPage(medicalBillInfoQuery);
//
//        //赔款计算金额表"==============OK
//        List<PrpLCFeeDto> prpLCFeeDtoList = initPrpLCFeeDto(paymentItems);
//        compensateDto.setPrpLCFeeDtoList(prpLCFeeDtoList);
//
//        //收据信息表==============OK    多个
//        if(CollectionUtil.isNotEmpty(medicalBillInfoList) && CollectionUtil.isNotEmpty(paymentItems)) {
//            List<PrpLReceiptDto> prpLReceiptDtoList = mqProducerCompensateService.initPrpLReceiptDto(policyInfoDTO, paymentItems.get(0), medicalBillInfoList,peopleHurtVO);
//            compensateDto.setPrpLReceiptDtoList(prpLReceiptDtoList);
//        }
//
//
//        //收据信息轨迹表==============OK 多个
//        if(CollectionUtil.isNotEmpty(medicalBillInfoList) && CollectionUtil.isNotEmpty(paymentItems)){
//            List<PrpLReceiptTrackDto> prpLReceiptTrackDtoList = mqProducerCompensateService.initPrpLReceiptTrackDto(policyInfoDTO, paymentItems.get(0), medicalBillInfoList,peopleHurtVO);
//            compensateDto.setPrpLReceiptTrackDtoList(prpLReceiptTrackDtoList);
//        }
//
//        //医疗费用信息表"==============OK 多个
//        if(CollectionUtil.isNotEmpty(medicalBillInfoList)){
//            List<PrpLMedicalFeeDto> prpLMedicalFeeDtoList = initPrpLMedicalFeeDto(reportNo, medicalBillInfoList);
//            compensateDto.setPrpLMedicalFeeDtoList(prpLMedicalFeeDtoList);
//        }
//        //医疗费用信息轨迹表")==============OK 多个
//        if(CollectionUtil.isNotEmpty(medicalBillInfoList)) {
//            List<PrpLMedicalFeeTrackDto> prpLMedicalFeeTrackDto = initPrpLMedicalFeeTrackDto(reportNo, medicalBillInfoList);
//            compensateDto.setPrpLMedicalFeeTrackDtoList(prpLMedicalFeeTrackDto);
//        }
//        //疾病手术信息表==============OK 多个
//        PrpLDiseaseInfoDto prpLDiseaseInfoDto = initPrpLDiseaseInfoDto(reportNo, peopleHurtVO);
//        compensateDto.setPrpLDiseaseInfoDto(prpLDiseaseInfoDto);
//
//        // 流程主表
//        SwfFlowMainDto swfFlowMainDto = getSwfFlowMainDto(reportNo, reportInfo, policyInfoDTO);
//        compensateDto.setSwfFlowMainDto(swfFlowMainDto);
//
//        // 理赔工作流转储表==============OK
//        TaskInfoDTO taskInfoDTO = new TaskInfoDTO();
//        taskInfoDTO.setDepartmentCode(reportInfo.getAcceptDepartmentCode());
//        taskInfoDTO.setAssigner(reportInfo.getReportRegisterUm());
//        taskInfoDTO.setReportNo(reportNo);
//        taskInfoDTO.setCaseTimes(caseTimes);
//
//        List<SwfLogStoreDto> allLogStoreDto = new ArrayList<>();
//        SwfLogStoreDto certifyCollectSwfLogStoreDto = mqProducerCompensateService.initCertifyCollectSwfLogStoreDto(reportNo, customerInfo,taskInfoDTO, reportInfo,prpLCertifyCollectDto);
//        allLogStoreDto.add(certifyCollectSwfLogStoreDto);
//        List<SwfLogStoreDto> swfLogStoreDtoList = initSwfLogStoreDto(reportNo, customerInfo, policyInfoDTO,reportInfo,caseBaseInfoList);
//        allLogStoreDto.addAll(swfLogStoreDtoList);
//        compensateDto.setSwfLogStoreDtoList(allLogStoreDto);
//        compensateDto.setSwfLogStoreDtoList(allLogStoreDto);
//
//        ClaimRepayCalReqDto claimRepayCalReqDto = mqProducerCompensateService.initClaimRepayCalReqDto(reportAccident, taskInfoDTO, caseBaseInfoList, paymentItems, "3");
//        compensateDto.setClaimRepayCalReqDto(claimRepayCalReqDto);
//        List<PrpLRegistRPolicyDto> prpLRegistRPolicyDtoList = new ArrayList<>();
//        List<EstimatePolicyDTO> estimatePolicyDTOS = estimatePolicyMapper.getByReportNoAndCaseTimes(reportNo, caseTimes);
//        if (CollectionUtils.isNotEmpty(estimatePolicyDTOS)){
//            for (int i = 0; i < estimatePolicyDTOS.size(); i++) {
//                PrpLRegistRPolicyDto prpLRegistRPolicyDto = new PrpLRegistRPolicyDto();
//                prpLRegistRPolicyDto.setRegistNo(reportNo);
//                String policyNo = estimatePolicyDTOS.get(i).getPolicyNo();
//                CaseBaseEntity caseBaseEntity = caseBaseInfoList.stream().filter(it -> it.getPolicyNo().equals(policyNo)).findFirst().orElse(null);
//                prpLRegistRPolicyDto.setPolicyNo(estimatePolicyDTOS.get(i).getPolicyNo());
//                prpLRegistRPolicyDto.setClaimNo(caseBaseEntity.getRegistNo());
//                Map<String, String> plyBaseInfo = ocasMapper.getPlyBaseInfo(policyNo);
//                String productCode1 = MapUtils.getString(plyBaseInfo,"productCode");
//                String businessType = MapUtils.getString(plyBaseInfo,"businessType");
//                prpLRegistRPolicyDto.setPolicyType(businessType);
//                prpLRegistRPolicyDto.setFlowID("");
//                prpLRegistRPolicyDto.setRemark(reportInfo.getRemark());
//                prpLRegistRPolicyDto.setValidStatus("1");
//                prpLRegistRPolicyDto.setRiskCode(CodeUtil.subStringRiskCode(productCode1));
//                prpLRegistRPolicyDto.setRegistFlag("1");
//                prpLRegistRPolicyDto.setRegistCancelDate(null);
//                PaymentItemDTO itemDTO = items.stream().filter(item -> policyNo.equals(item.getPolicyNo())).findFirst().orElse(null);
//                prpLRegistRPolicyDto.setCompensateNo(itemDTO.getCompensateNo());
//                prpLRegistRPolicyDto.setSerialNo(i+1);
//                prpLRegistRPolicyDto.setUpdateTime(new Date());
//                prpLRegistRPolicyDtoList.add(prpLRegistRPolicyDto);
//            }
//        }
//        compensateDto.setPrpLRegistRPolicyDtoList(prpLRegistRPolicyDtoList);
//        return compensateDto;
//    }
//
//    private SwfFlowMainDto getSwfFlowMainDto(String reportNo, ReportInfoEntity reportInfo, PolicyInfoDTO policyInfoDTO) {
//        SwfFlowMainDto swfFlowMainDto = new SwfFlowMainDto();
//        swfFlowMainDto.setFlowID("");
//        swfFlowMainDto.setFlowName(reportNo);
//        swfFlowMainDto.setFlowStatus("0");
//        swfFlowMainDto.setPolicyNo(policyInfoDTO.getPolicyNo());
//        swfFlowMainDto.setCreatDate(reportInfo.getReportDate());
//        swfFlowMainDto.setCloseDate(new Date());
//        swfFlowMainDto.setModelNo(12);
//        swfFlowMainDto.setStoreFlag("2");
//        swfFlowMainDto.setUpdateTime(new Date());
//        return swfFlowMainDto;
//    }
//
//    private List<PrpLCFeeDto> initPrpLCFeeDto(List<PaymentItemDTO> paymentItems) {
//        Map<String, List<PaymentItemDTO>> itemMap = paymentItems.stream().collect(Collectors.groupingBy(PaymentItemDTO::getCompensateNo));
//        List<PrpLCFeeDto> prpLCFeeDtoList = new ArrayList<>();
//        itemMap.forEach((k,itemDTOs)->{
//            PrpLCFeeDto prpLCFeeDto = new PrpLCFeeDto();
////        赔款计算书号	COMPENSATENO	Y
//            prpLCFeeDto.setCompensateNo(k);
//            String policyNo = itemDTOs.get(0).getPolicyNo();
//            Map<String, String> productMap = ocasMapper.getPlyBaseInfo(policyNo);
//            String productCode = MapUtils.getString(productMap,"productCode");
////        险种代码	RISKCODE	Y
//            prpLCFeeDto.setRiskCode(CodeUtil.subStringRiskCode(productCode));
////        保单号	POLICYNO	Y
//            prpLCFeeDto.setPolicyNo(policyNo);
////        币别代码（人民币）	CURRENCY	Y
//            prpLCFeeDto.setCurrency("CNY");
//            BigDecimal sum = itemDTOs.stream().filter(item -> "13".equals(item.getPaymentType())).map(PaymentItemDTO::getPaymentAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
////        赔付金额	SUMPAID	Y
//            prpLCFeeDto.setSumPaid(sum);
////        更新时间	UPDATETIME	CY	只要对表操作就修改updatetime
//            prpLCFeeDto.setUpdateTime(new Date());
//            prpLCFeeDtoList.add(prpLCFeeDto);
//        });
//
//
//        return prpLCFeeDtoList;
//    }
//
//    private List<PrpLMedicalFeeDto> initPrpLMedicalFeeDto(String reportNo, List<MedicalBillInfoDTO> medicalBillInfoList) {
//        List<PrpLMedicalFeeDto> prpLMedicalFeeDtoList=new ArrayList<>();
//        int no=0;
//        for(int i=0;i<medicalBillInfoList.size();i++){
//            if(CollectionUtil.isNotEmpty(medicalBillInfoList.get(i).getMedicalBillReduceDetailDTOList())){
//                for(int j=0 ;j<medicalBillInfoList.get(i).getMedicalBillReduceDetailDTOList().size();j++){
//                    PrpLMedicalFeeDto prpLMedicalFeeDto = new PrpLMedicalFeeDto();
//                    no=no+1;
//                    MedicalBillReduceDetailDTO medicalBillReduceDetailDTO=medicalBillInfoList.get(i).getMedicalBillReduceDetailDTOList().get(j);
//                    if(null==medicalBillReduceDetailDTO){
//                        continue;
//                    }
//                    //            报案号  	REGISTNO	Y
//                    prpLMedicalFeeDto.setRegistNo(reportNo);
////            序号    	SERIALNO	Y
//                    prpLMedicalFeeDto.setSerialNo(String.valueOf(no));
////            关联序号	RELATESERIALNO	Y
//                    prpLMedicalFeeDto.setRelateSerialNo(String.valueOf(no));
//                    //            医疗费用类型	CONSTCATEGORY	Y	北京意健险
//                    prpLMedicalFeeDto.setConstCateGory(medicalBillReduceDetailDTO.getFeeCode());
////            费用名称    	MEDICALDETAILNAME	Y
//                    prpLMedicalFeeDto.setMedicalDetailName(medicalBillReduceDetailDTO.getFeeName());
////            费用金额    	FEEAMOUNT	Y	默认“0.00”
//                    prpLMedicalFeeDto.setFeeAmount(nvl(medicalBillReduceDetailDTO.getBillAmount(),0));
////            费用扣减金额	FEEDEDUCTIBLEAMOUNT	Y
//                    prpLMedicalFeeDto.setFeeDeductibleAmount(nvl(medicalBillReduceDetailDTO.getDeductible(),0));
////            数量        	QUANTITY	Y
//                    prpLMedicalFeeDto.setQuantity(nvl(new BigDecimal(medicalBillReduceDetailDTO.getAmount()),0));
////            单价        	UNITPRICE	Y
//                    prpLMedicalFeeDto.setUnitPrice(medicalBillReduceDetailDTO.getUnitPrice());
////            规格与单位  	UNIT	Y
//                    prpLMedicalFeeDto.setUnit(new BigDecimal(medicalBillReduceDetailDTO.getUnits()));
////            金额        	AMOUNT	Y
//                    prpLMedicalFeeDto.setAmount(nvl(medicalBillReduceDetailDTO.getFeeAmount(),0));
////            更新时间	UPDATETIME	CY	只要对表操作就修改updatetime
//                    prpLMedicalFeeDto.setUpdateTime(new Date());
//                    prpLMedicalFeeDtoList.add(prpLMedicalFeeDto);
//                }
//            }
//        }
//        return prpLMedicalFeeDtoList;
//    }
//
//    private List<PrpLMedicalFeeTrackDto> initPrpLMedicalFeeTrackDto(String reportNo, List<MedicalBillInfoDTO> medicalBillInfoList) {
//        List<PrpLMedicalFeeTrackDto> prpLMedicalFeeTrackDtoList=new ArrayList<>();
//        int no=0;
//        for(int i=0;i<medicalBillInfoList.size();i++){
//            if(CollectionUtil.isNotEmpty(medicalBillInfoList.get(i).getMedicalBillReduceDetailDTOList())){
//                for(int j=0 ;j<medicalBillInfoList.get(i).getMedicalBillReduceDetailDTOList().size();j++) {
//                    PrpLMedicalFeeTrackDto prpLMedicalFeeTrackDto = new PrpLMedicalFeeTrackDto();
//                    no=no+1;
////            流序号	TRACKNO	Y
//                    prpLMedicalFeeTrackDto.setTrackNo(String.valueOf(no));
////            报案号  	REGISTNO	Y
//                    prpLMedicalFeeTrackDto.setRegistNo(reportNo);
////            序号    	SERIALNO	Y
//                    prpLMedicalFeeTrackDto.setSerialNo(String.valueOf(no));
////            关联序号	RELATESERIALNO	Y
//                    prpLMedicalFeeTrackDto.setRelateSerialNo(String.valueOf(no));
//                    MedicalBillReduceDetailDTO medicalBillReduceDetailDTO = medicalBillInfoList.get(0).getMedicalBillReduceDetailDTOList().get(0);
//                    if(null==medicalBillReduceDetailDTO){
//                        continue;
//                    }
//                    //            医疗费用类型	CONSTCATEGORY	Y	北京意健险
//                    prpLMedicalFeeTrackDto.setConstCateGory(medicalBillReduceDetailDTO.getFeeCode());
////            费用名称    	MEDICALDETAILNAME	Y
//                    prpLMedicalFeeTrackDto.setMedicalDetailName(medicalBillReduceDetailDTO.getFeeName());
////            费用金额    	FEEAMOUNT	Y	默认“0.00”
//                    prpLMedicalFeeTrackDto.setFeeAmount(nvl(medicalBillReduceDetailDTO.getBillAmount(),0));
////            费用扣减金额	FEEDEDUCTIBLEAMOUNT	Y
//                    prpLMedicalFeeTrackDto.setFeeDeductibleAmount(nvl(medicalBillReduceDetailDTO.getDeductible(),0));
////            数量        	QUANTITY	Y
//                    prpLMedicalFeeTrackDto.setQuantity(new BigDecimal(medicalBillReduceDetailDTO.getAmount()));
////            单价        	UNITPRICE	Y
//                    prpLMedicalFeeTrackDto.setUnitPrice(medicalBillReduceDetailDTO.getUnitPrice());
////            规格与单位  	UNIT	Y
//                    prpLMedicalFeeTrackDto.setUnit(new BigDecimal(medicalBillReduceDetailDTO.getUnits()));
////            金额        	AMOUNT	Y
//                    prpLMedicalFeeTrackDto.setAmount(nvl(medicalBillReduceDetailDTO.getFeeAmount(),0));
////            更新时间	UPDATETIME	CY	只要对表操作就修改updatetime
//                    prpLMedicalFeeTrackDto.setUpdateTime(new Date());
//                    prpLMedicalFeeTrackDtoList.add(prpLMedicalFeeTrackDto);
//                }
//            }
//        }
//        return prpLMedicalFeeTrackDtoList;
//    }
//
//    private List<PrpLAdvancePaymentDto> initPrpLAdvancePaymentDto(List<PaymentItemDTO> dfPays,List<CaseBaseEntity> caseBaseInfoList) {
//        List<PrpLAdvancePaymentDto> prpLAdvancePaymentDtoList = new ArrayList<>();
//        int n = 1;
//        int size = dfPays.size();
//        for (int i = 0; i < size; i++) {
//            PaymentItemDTO itemDTO = dfPays.get(i);
//            // claimType=1 赔款类型：(13-赔款 1J直接理赔费用 "C13", "共保代付赔款" "C1J", "共保代付费用"
//            // claimType=2 11-预赔赔款 "11J", "预赔费用" ,"P13", "共保代付预赔赔款" "P1J", "共保代付预赔费用" )
//            String paymentType = itemDTO.getPaymentType();
//            if (checkIsAdvance(paymentType)) {
//                continue;
//            }
//            PrpLAdvancePaymentDto prpLAdvancePaymentDto = new PrpLAdvancePaymentDto();
//            String compensateNo =  paymentItemMapper.getCompensateNoByPayment(itemDTO);
//            if (StringUtils.isEmptyStr(compensateNo)) {
//                continue;
//            }
//            CaseBaseEntity caseBaseEntity = caseBaseInfoList.stream().filter(it -> it.getPolicyNo().equals(itemDTO.getPolicyNo())).findFirst().orElse(null);
//
//            //        计算书号	COMPENSATENO	Y
//            prpLAdvancePaymentDto.setCompensateNo(itemDTO.getCompensateNo());
//            //        序号	SERIALNO	Y
//            int i1 = n++;
//            prpLAdvancePaymentDto.setSerialNo(i1);
//            itemDTO.setSerialNo(i1);
//            itemDTO.setCompensateNo(compensateNo);
//            itemDTO.setPaymentItemStatus(PAYMENT_ITEM_STATUS_11);
//            paymentItemMapper.updatePaymentItem(itemDTO);
//            //        立案号	CLAIMNO	Y
//            prpLAdvancePaymentDto.setClaimNo(caseBaseEntity.getRegistNo());
//            //        保单号	POLICYNO	Y
//            String policyNo = itemDTO.getPolicyNo();
//            prpLAdvancePaymentDto.setPolicyNo(policyNo);
//            //        险种代码	RISKCODE	Y
//            Map<String, String> productMap = ocasMapper.getPlyBaseInfo(policyNo);
//            String productCode = MapUtils.getString(productMap,"productCode");
//            prpLAdvancePaymentDto.setRiskCode(CodeUtil.subStringRiskCode(productCode));
//            //        垫付方代码（共保人代码）	COINSCODE	CY	共保保单，共保方有垫付信息需要录入
//            prpLAdvancePaymentDto.setCoinsCode(itemDTO.getIdClmPaymentInfo());
//            //        垫付方名称（共保人名称）	COINSNAME	CY
//            prpLAdvancePaymentDto.setCoinsName(itemDTO.getClientName());
//            //        币别 (人民币)	CURRENCY	Y	默认“CNY”
//            prpLAdvancePaymentDto.setCurrency("CNY");
//            //        垫付类别	PAYTYPE	Y C-赔款，F-费用
//            if ("C13".equals(paymentType) || "P13".equals(paymentType)){
//                prpLAdvancePaymentDto.setPayType("C");
//            } else {
//                prpLAdvancePaymentDto.setPayType("F");
//            }
//            //        垫付金额	ADVANCEPAYMENT	Y
//            prpLAdvancePaymentDto.setAdvancePayment(nvl(itemDTO.getPaymentAmount(),0));
//            //        费用代码（其他）	CHARGECODE	CY	存在费用
//            String chargeCode = tranChargeCode(itemDTO.getPaymentType(), itemDTO.getIdClmPaymentItem());
//            prpLAdvancePaymentDto.setChargeCode(chargeCode);
//            //        更新时间	UPDATETIME	CY	只要对表操作就修改updatetime
//            prpLAdvancePaymentDto.setUpdateTime(new Date());
//            prpLAdvancePaymentDtoList.add(prpLAdvancePaymentDto);
//        }
//        return prpLAdvancePaymentDtoList;
//    }
//
//    private String tranChargeCode(String paymentType, String idClmPaymentItem) {
//        String chargeCode ;
//        switch (paymentType) {
//            case "11":
//            case "13":
//            case "C13":
//            case "P13":
//                chargeCode = "";
//                break;
//            case "1J":
//            case "C1J":
//            case "11J":
//            case "P1J":
//                List<FeeInfoDTO> feeInfoDTOList=  feePayMapper.getFeePayByIdClmPaymentInfo(idClmPaymentItem);
//                if (CollectionUtils.isNotEmpty(feeInfoDTOList)){
//                    FeeInfoDTO feeInfoDTO = feeInfoDTOList.get(0);
//                    chargeCode = feeInfoDTO==null?"36": ChargeCodeEnum.getCode(feeInfoDTO.getFeeType());
//                } else {
//                    chargeCode = "36";
//                }
//                break;
//            default:
//                chargeCode = "";
//                break;
//        }
//        return chargeCode;
//    }
//
//    private boolean checkIsAdvance(String paymentType) {
//        // claimType=1 赔款类型：(13-赔款 1J直接理赔费用 ,"C13", "共保代付赔款" "C1J", "共保代付费用"
//        // claimType=2 11-预赔赔款 "11J", "预赔费用" ,"P13", "共保代付预赔赔款" "P1J", "共保代付预赔费用" )
//        return "13".equals(paymentType) || "11".equals(paymentType) || "1J".equals(paymentType) || "11J".equals(paymentType);
//    }
//
//    private String tranPaymentType(String paymentType, String idClmPaymentItem) {
//        String payType ;
//        switch (paymentType) {
//            case "11":
//            case "13":
//            case "C13":
//            case "P13":
//                payType = "c";
//                break;
//            case "1J":
//            case "C1J":
//            case "11J":
//            case "P1J":
//                List<FeeInfoDTO> feeInfoDTOList=  feePayMapper.getFeePayByIdClmPaymentInfo(idClmPaymentItem);
//                if (CollectionUtils.isNotEmpty(feeInfoDTOList)){
//                    FeeInfoDTO feeInfoDTO = feeInfoDTOList.get(0);
//                    payType = feeInfoDTO==null?"36": ChargeCodeEnum.getCode(feeInfoDTO.getFeeType());
//                } else {
//                    payType = "36";
//                }
//                break;
//            default:
//                payType = "c";
//                break;
//        }
//        return payType;
//    }
//
//    private PrpLDiseaseInfoDto initPrpLDiseaseInfoDto(String reportNo, PeopleHurtVO peopleHurtVO) {
//        PrpLDiseaseInfoDto prpLDiseaseInfoDto = new PrpLDiseaseInfoDto();
////        报案号  	REGISTNO	Y
//        prpLDiseaseInfoDto.setRegistNo(reportNo);
////        序号    	SERIALNO	Y
//        prpLDiseaseInfoDto.setSerialNo("1");
////        关联序号	RELATESERIALNO	Y
//        prpLDiseaseInfoDto.setRelateSerialNo("1");
////        疾病代码	DISEASECODE	Y	详情见意健险疾病代码表
//        if (peopleHurtVO.getDiagnoseVO()!= null && peopleHurtVO.getDiagnoseVO().getDiagnoseDTOs() != null) {
//            List<PersonDiagnoseDTO> diagnoseDTOs = peopleHurtVO.getDiagnoseVO().getDiagnoseDTOs();
//            PersonDiagnoseDTO personDiagnoseDTO = diagnoseDTOs.get(0);
//            //        疾病名称	DISEASENAME          	Y	详情见意健险疾病代码表
//            prpLDiseaseInfoDto.setDiseaseCode(personDiagnoseDTO.getDiagnoseCode());
//        } else {
//            prpLDiseaseInfoDto.setDiseaseCode("");
//        }
//        if(null!=peopleHurtVO&&null!=peopleHurtVO.getDiagnoseVO()&& CollectionUtil.isNotEmpty(peopleHurtVO.getDiagnoseVO().getDiagnoseDTOs())){
//            PersonDiagnoseDTO personDiagnoseDTO= peopleHurtVO.getDiagnoseVO().getDiagnoseDTOs().get(0);
//            //        手术代码	OPERATORCODE	CY	当是否手术为是时，手术代码必录（详情见意健险手术代码表）
//            prpLDiseaseInfoDto.setOperatorCode(personDiagnoseDTO.getSurgicalCode());
//            //        治疗方式	TREATMENT	CY	北京的案件增加门诊/医疗信息
//            prpLDiseaseInfoDto.setTreatment(personDiagnoseDTO.getIsSurgical());
//            //        诊断类型	DIAGNOSISTYPE	Y	北京上海
//            prpLDiseaseInfoDto.setDiagnosisType(personDiagnoseDTO.getDiagnosticTypologyCode());
//        }else{
//            //        手术代码	OPERATORCODE	CY	当是否手术为是时，手术代码必录（详情见意健险手术代码表）
//            prpLDiseaseInfoDto.setOperatorCode("");
//            //        诊断类型	DIAGNOSISTYPE	Y	北京上海
//            prpLDiseaseInfoDto.setDiagnosisType("");
//            //        治疗方式	TREATMENT	CY	北京的案件增加门诊/医疗信息
//            prpLDiseaseInfoDto.setTreatment("");
//        }
////        更新时间	UPDATETIME	CY	只要对表操作就修改updatetime
//        prpLDiseaseInfoDto.setUpdateTime(new Date());
//        return prpLDiseaseInfoDto;
//    }
//
//    private List<PrpLAccidentResultDto> getPrpLAccidentResultDto(WholeCaseBaseDTO wholeCaseBaseEntity, PolicyInfoDTO policyInfoDTO, String productCode, List<PaymentItemDTO> items, List<CaseBaseEntity> caseBaseEntityList, ReportCustomerInfoEntity customerInfo) {
//        List<PrpLAccidentResultDto> prpLAccidentResultDtoList=new ArrayList<>();
//        for(CaseBaseEntity caseBaseEntity:caseBaseEntityList){
//            PrpLAccidentResultDto prpLAccidentResultDto = new PrpLAccidentResultDto();
//            Map<String, String> productMap = ocasMapper.getPlyBaseInfo(caseBaseEntity.getPolicyNo());
//            productCode = MapUtils.getString(productMap,"productCode");
//            PaymentItemDTO item=items.stream().filter(it->it.getPolicyNo().equals(caseBaseEntity.getPolicyNo())).findFirst().orElse(null);
//            if(null==item){
//                continue;
//            }
//            //        计算书号	COMPENSATENO	Y
//            prpLAccidentResultDto.setCompensateNo(item.getCompensateNo());
//            //        立案号	CLAIMNO	Y
//            prpLAccidentResultDto.setClaimNo(caseBaseEntity.getRegistNo());
//            //        保单号	POLICYNO	Y
//            prpLAccidentResultDto.setPolicyNo(caseBaseEntity.getPolicyNo());
//            //        险种	RISKCODE	Y
//            prpLAccidentResultDto.setRiskCode(CodeUtil.subStringRiskCode(productCode));
//            //        序号	SERIALNO	Y
//            prpLAccidentResultDto.setSerialNo(1);
//            //        被保险人序号	FAMILYNO	Y	从承保取
//            String riskPersonNo = ocasMapper.getRiskPersonNo(caseBaseEntity.getPolicyNo(), customerInfo.getCertificateNo(), customerInfo.getName());
//            prpLAccidentResultDto.setFamilyNo(riskPersonNo);
//            //        被保险人名称	FAMILYNAME	Y
//            prpLAccidentResultDto.setFamilyName(wholeCaseBaseEntity.getInsuredName());
//            //        出险结果	ACCIDENTRESULT	Y
//            prpLAccidentResultDto.setAccidentResult("99");
//            //        出险结果时间	ACCIDENTRESULTDATE	Y
//            prpLAccidentResultDto.setAccidentResultDate(new Date());
//            //        疾病代码	SERIOUSDISEASECODE	CY	当出险结果为“重大疾病”时，该字段必录（详情见意健险疾病代码表）
//            prpLAccidentResultDto.setSeriousDiseaseCode("");
//            //        更新时间	UPDATETIME	CY	只要对表操作就修改updatetime
//            prpLAccidentResultDto.setUpdateTime(new Date());
//            prpLAccidentResultDtoList.add(prpLAccidentResultDto);
//        }
//
//        return prpLAccidentResultDtoList;
//    }
//
//
//
//    private List<PrpLClaimPaymentTraceDto> initPrpLClaimPaymentTraceDto(List<PaymentItemDTO> paymentItems,ReportInfoEntity reportInfo) {
//        int n = 1;
//        List<PrpLClaimPaymentTraceDto> prpLClaimPaymentTraceDtoList=new ArrayList<>();
//        for (int i = 0; i < paymentItems.size(); i++) {
//            PaymentItemDTO itemDTO = paymentItems.get(i);
//            PrpLClaimPaymentTraceDto prpLClaimPaymentTraceDto = new PrpLClaimPaymentTraceDto();
////        流序号	TRACKNO	Y
//            int i1 = n++;
//            prpLClaimPaymentTraceDto.setTrackNo(i1);
//            //        收款人证件类型（身份证）	IDENTIFYTYPE	Y
//            if ("0".equals(itemDTO.getBankAccountAttribute())){
//                prpLClaimPaymentTraceDto.setIdentifyType("71");
//            } else {
//                prpLClaimPaymentTraceDto.setIdentifyType(mqProducerRegistService.transZRRIdentifyType(itemDTO.getClientCertificateType()));
//            }
//            //        收款人证件号	IDENTIFYNUMBER	Y
//            prpLClaimPaymentTraceDto.setIdentifyNumber(itemDTO.getClientCertificateNo());
////        业务号	BUSINESSNO	Y
//            prpLClaimPaymentTraceDto.setBusinessNo(itemDTO.getCompensateNo());
////        报案号	REGISTNO	Y
//            prpLClaimPaymentTraceDto.setRegistNo(itemDTO.getReportNo());
////        保单号	POLICYNO	Y
//            prpLClaimPaymentTraceDto.setPolicyNo(itemDTO.getPolicyNo());
////        险种代码	RISKCODE	Y
//            Map<String, String> productMap = ocasMapper.getPlyBaseInfo(itemDTO.getPolicyNo());
//            String productCode = MapUtils.getString(productMap,"productCode");
//            prpLClaimPaymentTraceDto.setRiskCode(CodeUtil.subStringRiskCode(productCode));
////        录入信息节点类型	NODETYPE	Y
//            prpLClaimPaymentTraceDto.setNodeType("veric");
////        序号	SERIALNO	Y
//            prpLClaimPaymentTraceDto.setSerialNo(String.valueOf(i1));
////        录入人代码	OPERATORCODE	Y
//            prpLClaimPaymentTraceDto.setOperatorCode(itemDTO.getCreatedBy());
////        付款方式，如现金、转账、支票等	PAYWAY	Y
//            prpLClaimPaymentTraceDto.setPayWay("02");
////        付款币种	CURRENCY	Y
//            prpLClaimPaymentTraceDto.setCurrency("CNY");
////        付款类型如赔款、具体费用名称等	PAYTYPE	Y.
//            String payType = tranPaymentType(itemDTO.getPaymentType(), itemDTO.getIdClmPaymentItem());
//            prpLClaimPaymentTraceDto.setPayType(payType);
////        付款金额	PAYREFFEE	Y
//            prpLClaimPaymentTraceDto.setPayRefFee(itemDTO.getPaymentAmount());
////        付款性质如赔款、费用等	PAYNATURE	Y
//            prpLClaimPaymentTraceDto.setPayNature(PaymentTypeEnum.getCode(itemDTO.getPaymentType()));
////        收款人性质1：法人2：自然人	PAYEENATURE	Y
//            String accountAttribute = itemDTO.getBankAccountAttribute();
//            prpLClaimPaymentTraceDto.setPayeeNature("0".equals(accountAttribute) ? "1" : "2");
////        收款人银行户名或开户行名称	UNITNAME	Y
//            prpLClaimPaymentTraceDto.setUnitName(itemDTO.getBankDetail());
////        收款人账户	ACCOUNTCODE	Y
//            prpLClaimPaymentTraceDto.setAccountCode(itemDTO.getClientBankCode());
////        收款人户名	PAYFEENAME	Y
//            prpLClaimPaymentTraceDto.setPayFeeName(itemDTO.getClientName());
////        付款摘要	REMARK	CY	选填
//            prpLClaimPaymentTraceDto.setRemark(null);
////        收款人银行类别码	PAYFEEBANKTYPECODE	CY	非意健险自动全流程需要录入
//            prpLClaimPaymentTraceDto.setPayFeeBankTypeCode(itemDTO.getClientBankCode());
////        收款人银行类别名称	PAYFEEBANKTYPENAME	CY	非意健险自动全流程需要录入
//            prpLClaimPaymentTraceDto.setPayFeeBankTypeName(itemDTO.getClientBankName());
//            AdressSearchDto adressSearchDto  = new AdressSearchDto();
//            adressSearchDto.setOverseasOccur(BaseConstant.STRING_0).setAccidentProvinceCode(itemDTO.getProvinceName())
//                    .setAccidentCountyCode(itemDTO.getRegionCode()).setAccidentCityCode(itemDTO.getCityName());
//            AdressSearchDto detailAdressFormCode = commonService.getDetailAdressFormCode(adressSearchDto);
////        省份	PROVINCE	Y
//            prpLClaimPaymentTraceDto.setProvince(detailAdressFormCode.getAccidentProvinceName());
////        城市	CITY	Y
//            prpLClaimPaymentTraceDto.setCity(detailAdressFormCode.getAccidentCityName());
////        机构号	ORGANIZATIONID	Y
//            prpLClaimPaymentTraceDto.setOrganizationID(reportInfo.getAcceptDepartmentCode());
////        联行号	LASALLEROAD	CY	从资金系统获取
//            prpLClaimPaymentTraceDto.setLasalleroad(null);
////        支付对象	PAYOBJECTCODE	Y
//            prpLClaimPaymentTraceDto.setPayObjectCode(ClientTypeEnum.getCode(itemDTO.getClientType()));
////        区域名称	COUNTY	Y
//            prpLClaimPaymentTraceDto.setCounty(itemDTO.getCityName());
////        区域码	AREACODE	Y
//            prpLClaimPaymentTraceDto.setAreaCode(itemDTO.getRegionCode());
////        更新时间	UPDATETIME	CY	只要对表操作就修改updatetime
//            prpLClaimPaymentTraceDto.setUpdateTime(new Date());
//            prpLClaimPaymentTraceDtoList.add(prpLClaimPaymentTraceDto);
//        }
//        return prpLClaimPaymentTraceDtoList;
//    }
//
//    private List<PrpLClaimPaymentDto> initPrpLClaimPaymentDto( List<PaymentItemDTO> paymentItems,ReportInfoEntity reportInfo) {
//        List<PrpLClaimPaymentDto> prpLClaimPaymentDtoList=new ArrayList<>();
//        int n = 1;
//        for (int i = 0; i < paymentItems.size(); i++) {
//            PaymentItemDTO itemDTO = paymentItems.get(i);
//            PrpLClaimPaymentDto prpLClaimPaymentDto = new PrpLClaimPaymentDto();
//            //        业务号	BUSINESSNO	Y
//            prpLClaimPaymentDto.setBusinessNo(itemDTO.getCompensateNo());
//            //        报案号	REGISTNO	Y
//            prpLClaimPaymentDto.setRegistNo(itemDTO.getReportNo());
//            //        保单号	POLICYNO	Y
//            prpLClaimPaymentDto.setPolicyNo(itemDTO.getPolicyNo());
//            Map<String, String> productMap = ocasMapper.getPlyBaseInfo(itemDTO.getPolicyNo());
//            String productCode = MapUtils.getString(productMap,"productCode");
//            //        险种代码	RISKCODE	Y
//            prpLClaimPaymentDto.setRiskCode(CodeUtil.subStringRiskCode(productCode));
//            //        录入信息节点类型	NODETYPE	Y
//            prpLClaimPaymentDto.setNodeType("veric");
//            //        序号	SERIALNO	Y
//            int i1 = n++;
//            prpLClaimPaymentDto.setSerialNo(String.valueOf(i1));
//            itemDTO.setSerialNo(i1);
//            itemDTO.setPaymentItemStatus(PAYMENT_ITEM_STATUS_11);
//            paymentItemMapper.updatePaymentItem(itemDTO);
//            //        录入人代码	OPERATORCODE	Y
//            prpLClaimPaymentDto.setOperatorCode(itemDTO.getCreatedBy());
//            //        付款方式，如现金、转账、支票等	PAYWAY	Y
//            prpLClaimPaymentDto.setPayWay("4");
//            //        付款币种	CURRENCY	Y
//            prpLClaimPaymentDto.setCurrency("CNY");
//            //        付款类型如赔款、具体费用名称等	PAYTYPE	Y
//            String payType = tranPaymentType(itemDTO.getPaymentType(),itemDTO.getIdClmPaymentItem());
//            prpLClaimPaymentDto.setPayType(payType);
//            //        付款金额	PAYREFFEE	Y
//            prpLClaimPaymentDto.setPayRefFee(nvl(itemDTO.getPaymentAmount(),0));
//            //        付款性质如赔款、费用等	PAYNATURE	Y
//            prpLClaimPaymentDto.setPayNature(PaymentTypeEnum.getCode(itemDTO.getPaymentType()));
//            //        收款人性质	PAYEENATURE	Y
//            String accountAttribute = itemDTO.getBankAccountAttribute();
//            prpLClaimPaymentDto.setPayeeNature("0".equals(accountAttribute) ? "1" : "2");
//            //        收款人银行户名或开户行名称	UNITNAME	Y
//            prpLClaimPaymentDto.setUnitName(itemDTO.getBankDetail());
//            //        收款人证件号码	CARDCODE	Y
//            prpLClaimPaymentDto.setCardCode(itemDTO.getClientCertificateNo());
//            //        收款人账户	ACCOUNTCODE	Y
//            prpLClaimPaymentDto.setAccountCode(itemDTO.getClientBankCode());
//            //        收款人户名	PAYFEENAME	Y
//            prpLClaimPaymentDto.setPayFeeName(itemDTO.getClientName());
//            //        收款人银行类别码	PAYFEEBANKTYPECODE	Y
//            prpLClaimPaymentDto.setPayFeeBankTypeCode(itemDTO.getClientBankCode());
//            //        收款人银行类别名称	PAYFEEBANKTYPENAME	Y
//            prpLClaimPaymentDto.setPayFeeBankTypeName(itemDTO.getClientBankName());
//
//            AdressSearchDto adressSearchDto  = new AdressSearchDto();
//            adressSearchDto.setOverseasOccur(BaseConstant.STRING_0).setAccidentProvinceCode(itemDTO.getProvinceName())
//                    .setAccidentCountyCode(itemDTO.getRegionCode()).setAccidentCityCode(itemDTO.getCityName());
//            AdressSearchDto detailAdressFormCode = commonService.getDetailAdressFormCode(adressSearchDto);
//            //        省份	PROVINCE	Y
//            prpLClaimPaymentDto.setProvince(detailAdressFormCode.getAccidentProvinceName());
//            //        城市	CITY	Y
//            prpLClaimPaymentDto.setCity(detailAdressFormCode.getAccidentCityName());
//            //        机构号	ORGANIZATIONID	Y
//            prpLClaimPaymentDto.setOrganizationID( reportInfo.getAcceptDepartmentCode());
//            //        联行号	LASALLEROAD	CY	从资金系统获取
//            prpLClaimPaymentDto.setLasalleroad(null);
//            //        送集中支付表时间	SENDDATE	CY	送收付之后回写
//            prpLClaimPaymentDto.setSendDate(null);
//            //        支付时间 年月日时分秒	PAYREFDATE	CY	支付成功之后回写
//            prpLClaimPaymentDto.setPayRefDate(null);
//            //        会计机构	CENTERCODE	CY	支付成功之后回写
//            prpLClaimPaymentDto.setCenterCode(null);
//            //        会计月	YEARMONTH	CY	支付成功之后回写
//            prpLClaimPaymentDto.setYearMonth(null);
//            //        凭证号	VOUCHERNO	CY	支付成功之后回写
//            prpLClaimPaymentDto.setVoucherNo(null);
//            //        上级会计机构	UPPERCENTERCODE	CY	支付成功之后回写
//            prpLClaimPaymentDto.setUpperCenterCode(null);
//            //        上级机构凭证号	UPPERVOUCHERNO	CY	支付成功之后回写
//            prpLClaimPaymentDto.setUpperVoucherNo(null);
//            //        凭证时间	VOUCHERDATE	CY	支付成功之后回写
//            prpLClaimPaymentDto.setVoucherDate(null);
//            //        从资金系统生获取的主键ID	ZJ_ID	CY	支付成功之后回写
//            prpLClaimPaymentDto.setZjId(null);
//            //        快处快赔标志	FASTPAYFLAG	CY	意健险自动全流程自动赋值
//            prpLClaimPaymentDto.setFastPayFlag(null);
//            //        支付对象	PAYOBJECTCODE	Y
//            prpLClaimPaymentDto.setPayObjectCode(ClientTypeEnum.getCode(itemDTO.getClientType()));
//            //        收款人证件类型（身份证）	IDENTIFYTYPE	Y
//            if ("0".equals(itemDTO.getBankAccountAttribute())){
//                prpLClaimPaymentDto.setIdentifyType("71");
//            } else {
//                prpLClaimPaymentDto.setIdentifyType(mqProducerRegistService.transZRRIdentifyType(itemDTO.getClientCertificateType()));
//            }
//            //        收款人证件号	IDENTIFYNUMBER	Y
//            prpLClaimPaymentDto.setIdentifyNumber(itemDTO.getClientCertificateNo());
//            //        区域名称	COUNTY	Y
//            prpLClaimPaymentDto.setCounty(itemDTO.getCityName());
//            //        区域码	AREACODE	Y
//            prpLClaimPaymentDto.setAreaCode(itemDTO.getRegionCode());
//            //        付款通道	PAYCHANNEL
//            prpLClaimPaymentDto.setPayChannel("1");
//            //        更新时间	UPDATETIME	CY	 只要对表操作就修改updatetime
//            prpLClaimPaymentDto.setUpdateTime(new Date());
//            prpLClaimPaymentDtoList.add(prpLClaimPaymentDto);
//        }
//
//        return prpLClaimPaymentDtoList;
//    }
//
//    private List<PrpLChargeTraceDto> initPrpLChargeTraceDto(List<PaymentItemDTO> itemDTOS,List<EstimatePolicyDTO> policyList) {
//        List<PrpLChargeTraceDto> prpLChargeTraceDtoList=new ArrayList<>();
//        int n = 1;
//        if(CollectionUtil.isNotEmpty(itemDTOS)){
//            for(int i=0;i<itemDTOS.size();i++){
//                PaymentItemDTO itemDTO = itemDTOS.get(i);
//                if (checkIsCharge(itemDTO.getPaymentType())){
//                    continue;
//                }
//                log.info("getFeePayByIdClmPaymentInfo == in: {}",itemDTO.getIdClmPaymentInfo());
//                List<FeeInfoDTO> feeInfoDTOList=  feePayMapper.getFeePayByIdClmPaymentInfo(itemDTO.getIdClmPaymentItem());
//                log.info("getFeePayByIdClmPaymentInfo == out: {}",feeInfoDTOList);
//                PrpLChargeTraceDto prpLChargeTraceDto = new PrpLChargeTraceDto();
////        流序号	TRACKNO	Y		无明确字段（生产流程表时对应的字段信息）
//                int i1 = n++;
//                prpLChargeTraceDto.setTrackNo(i1);
//                String policyNo = itemDTO.getPolicyNo();
////        赔款计算书号	COMPENSATENO	Y		无明确字段（同上）
//                prpLChargeTraceDto.setCompensateNo(itemDTO.getCompensateNo());
//                Map<String, String> productMap = ocasMapper.getPlyBaseInfo(policyNo);
//                String productCode = MapUtils.getString(productMap,"productCode");
////        险种	RISKCODE	Y
//                prpLChargeTraceDto.setRiskCode(CodeUtil.subStringRiskCode(productCode));
////        保单号	POLICYNO	Y
//                prpLChargeTraceDto.setPolicyNo(policyNo);
////        序号	SERIALNO	Y
//                prpLChargeTraceDto.setSerialNo(i1);
////        险别代码	KINDCODE	CY	详细见险别代码表
//                prpLChargeTraceDto.setKindCode(CodeUtil.subStringCode(policyList.get(0).getEstimatePlanList().get(0).getPlanCode()));
//                if(CollectionUtil.isNotEmpty(feeInfoDTOList)){
//                    FeeInfoDTO feeInfoDTO=feeInfoDTOList.get(0);
//                    //        费用类别代码	CHARGECODE	Y
//                    prpLChargeTraceDto.setChargeCode(feeInfoDTO==null?"36": ChargeCodeEnum.getCode(feeInfoDTO.getFeeType()));
//                    //        费用名称	CHARGENAME	Y
//                    prpLChargeTraceDto.setChargeName(feeInfoDTO==null?"其他":ChargeCodeEnum.getName(feeInfoDTO.getFeeType()));
//                }else{
//                    //        费用类别代码	CHARGECODE	Y
//                    prpLChargeTraceDto.setChargeCode("36");
//                    //        费用名称	CHARGENAME	Y
//                    prpLChargeTraceDto.setChargeName("其他");
//                }
////        币别代码（人民币）	CURRENCY	Y	默认为“CNY"
//                prpLChargeTraceDto.setCurrency("CNY");
////        费用金额	CHARGEAMOUNT	Y
//                prpLChargeTraceDto.setChargeAmount(itemDTO.getPaymentAmount());
////        计入赔款金额	SUMREALPAY	Y		无明确字段(费用金额中，涉及到赔款的金额)
//                prpLChargeTraceDto.setSumRealPay(new BigDecimal("0"));
////        上报费用	CHARGEREPORT	Y	默认“0.00”	无明确字段（默认0）
//                prpLChargeTraceDto.setChargeReport(new BigDecimal("0.00"));
////        对费用主表币别兑换率	EXCHRATE	Y	默认“1.000000”
//                prpLChargeTraceDto.setExCHRate(new BigDecimal("1.000000"));
////        更新时间	UPDATETIME	CY	只要对表操作就修改updatetime
//                prpLChargeTraceDto.setUpdateTime(new Date());
//                prpLChargeTraceDtoList.add(prpLChargeTraceDto);
//            }
//        }
//        return prpLChargeTraceDtoList;
//    }
//
//    private List<PrpLChargeDto> initPrpLChargeDto(List<PaymentItemDTO> itemDTOs,List<EstimatePolicyDTO> policyList) {
//
//        List<PrpLChargeDto> prpLChargeDtoList=new ArrayList<>();
//        int n = 1;
//        if(CollectionUtil.isNotEmpty(itemDTOs)){
//            for(int i=0;i<itemDTOs.size();i++){
//                PaymentItemDTO  itemDTO=itemDTOs.get(i);
//                // claimType=1 赔款类型：(13-赔款 1J直接理赔费用 "C13", "共保代付赔款" "C1J", "共保代付费用"
//                // claimType=2 11-预赔赔款 "11J", "预赔费用" ,"P13", "共保代付预赔赔款" "P1J", "共保代付预赔费用" )
//                if (checkIsCharge(itemDTO.getPaymentType())){
//                    continue;
//                }
//                log.info("getFeePayByIdClmPaymentInfo == in: {}",itemDTO.getIdClmPaymentInfo());
//                List<FeeInfoDTO> feeInfoDTOList=  feePayMapper.getFeePayByIdClmPaymentInfo(itemDTO.getIdClmPaymentItem());
//                log.info("getFeePayByIdClmPaymentInfo == out: {}",feeInfoDTOList);
//                PrpLChargeDto prpLChargeDto = new PrpLChargeDto();
////        赔款计算书号	COMPENSATENO	Y		无明确字段（同上）
//                prpLChargeDto.setCompensateNo(itemDTO.getCompensateNo());
//                String policyNo = itemDTO.getPolicyNo();
//                Map<String, String> productMap = ocasMapper.getPlyBaseInfo(policyNo);
//                String productCode = MapUtils.getString(productMap,"productCode");
////        险种	RISKCODE	Y
//                prpLChargeDto.setRiskCode(CodeUtil.subStringRiskCode(productCode));
////        保单号	POLICYNO	Y
//                prpLChargeDto.setPolicyNo(policyNo);
////        序号	SERIALNO	Y
//                prpLChargeDto.setSerialNo(n++);
////        险别代码	KINDCODE	Y		赔款只生成到保单，不到险种（如微信聊天到条款）
//                prpLChargeDto.setKindCode(CodeUtil.subStringCode(policyList.get(0).getEstimatePlanList().get(0).getPlanCode()));
//                if(CollectionUtil.isNotEmpty(feeInfoDTOList)){
//                    FeeInfoDTO feeInfoDTO=feeInfoDTOList.get(0);
//                    //        费用类别代码	CHARGECODE	Y
//                    prpLChargeDto.setChargeCode(feeInfoDTO==null?"36": ChargeCodeEnum.getCode(feeInfoDTO.getFeeType()));
//                    //        费用名称	CHARGENAME	Y
//                    prpLChargeDto.setChargeName(feeInfoDTO==null?"其他":ChargeCodeEnum.getName(feeInfoDTO.getFeeType()));
//                }else{
//                    //        费用类别代码	CHARGECODE	Y
//                    prpLChargeDto.setChargeCode("36");
//                    //        费用名称	CHARGENAME	Y
//                    prpLChargeDto.setChargeName("其他");
//                }
//
////        币别代码	CURRENCY	Y	默认为“CNY"
//                prpLChargeDto.setCurrency("CNY");
////        费用金额	CHARGEAMOUNT	Y
//                prpLChargeDto.setChargeAmount(nvl(itemDTO.getPaymentAmount(),0));
////        计入赔款金额	SUMREALPAY	Y		无明确字段(费用金额中，涉及到赔款的金额)
//                prpLChargeDto.setSumRealPay(new BigDecimal("0.00"));
////        上报费用	CHARGEREPORT	Y	默认“0.00”
//                prpLChargeDto.setChargeReport(new BigDecimal("0.00"));
////        对理算主表币别兑换率	EXCHRATE	Y	默认“1.000000”
//                prpLChargeDto.setExCHRate(new BigDecimal("1.00000"));
////        公估费抵扣率，非公估费默认为0	CHARGERATE	CY	存在费用
//                prpLChargeDto.setChargeRate("0.00");
//                prpLChargeDto.setUpdateTime(new Date());
//                prpLChargeDtoList.add(prpLChargeDto);
//            }
//        }
//
//        return prpLChargeDtoList;
//    }
//
//    private boolean checkIsCharge(String paymentType) {
//        // claimType=1 赔款类型：(13-赔款 1J直接理赔费用 ,"C13", "共保代付赔款" "C1J", "共保代付费用"
//        // claimType=2 11-预赔赔款 "11J", "预赔费用" ,"P13", "共保代付预赔赔款" "P1J", "共保代付预赔费用" )
//        return "13".equals(paymentType) || "11".equals(paymentType) || "C13".equals(paymentType) || "P13".equals(paymentType);
//    }
//
//    @SneakyThrows
//    private List<SwfLogStoreDto> initSwfLogStoreDto(String reportNo, ReportCustomerInfoEntity customerInfo, PolicyInfoDTO policyInfoDTO, ReportInfoEntity reportInfoEntity,List<CaseBaseEntity> caseBaseInfoList) {
//       List<SwfLogStoreDto> swfLogStoreDtoList=new ArrayList<>();
//        for(CaseBaseEntity caseBaseEntity:caseBaseInfoList){
//            SwfLogStoreDto swfLogStoreDto = new SwfLogStoreDto();
//            swfLogStoreDto.setLogNo(4);
//            swfLogStoreDto.setModelNo(12);
//            swfLogStoreDto.setNodeName("核赔");
//            swfLogStoreDto.setBusinessNo(caseBaseEntity.getRegistNo());
//            swfLogStoreDto.setHandleDept(reportInfoEntity.getAcceptDepartmentCode());
//            swfLogStoreDto.setHandlerCode( NcbsConstant.ZK_ADMIN_UM);
//            swfLogStoreDto.setHandlerName( NcbsConstant.ZK_ADMIN_NAME);
//            swfLogStoreDto.setFlowInTime(DateUtils.parseToFormatString(new Date(),DateUtils.FULL_DATE_STR));
//            swfLogStoreDto.setTimeLimit(0);
//            swfLogStoreDto.setHandleTime(DateUtils.parseToFormatString(new Date(),DateUtils.FULL_DATE_STR));
//            swfLogStoreDto.setSubmitTime(DateUtils.parseToFormatString(new Date(),DateUtils.FULL_DATE_STR));
//            swfLogStoreDto.setPackageID("0");
//            swfLogStoreDto.setTaskNo(0);
//            swfLogStoreDto.setNodeType("veric");
//            swfLogStoreDto.setTitleStr("");
//            swfLogStoreDto.setBusinessType("1");
//            Map<String, String> productMap = ocasMapper.getPlyBaseInfo(caseBaseEntity.getPolicyNo());
//            String productCode = MapUtils.getString(productMap,"productCode");
//            swfLogStoreDto.setRiskCode(CodeUtil.subStringRiskCode(productCode));
//            swfLogStoreDto.setKeyIn(reportNo);
//            swfLogStoreDto.setKeyOut(reportNo);
//            swfLogStoreDto.setDeptName(departmentDefineMapper.queryDepartmentNameByDeptCode(reportInfoEntity.getAcceptDepartmentCode()));
//            swfLogStoreDto.setMainFlowID("0");
//            swfLogStoreDto.setSubFlowID("0");
//            swfLogStoreDto.setPosX(0);
//            swfLogStoreDto.setPosY(0);
//            swfLogStoreDto.setEndFlag("");
//            swfLogStoreDto.setBeforeHandlerCode("");
//            swfLogStoreDto.setBeforeHandlerName("");
//            swfLogStoreDto.setPolicyNo(caseBaseEntity.getPolicyNo());
//            swfLogStoreDto.setComCode(reportInfoEntity.getAcceptDepartmentCode());
//            swfLogStoreDto.setRegistNo(reportNo);
//            swfLogStoreDto.setInsuredName(customerInfo.getName());
//            swfLogStoreDto.setEntrustFlag("1");
//            swfLogStoreDto.setEntrustNodeStatus("1");
//            swfLogStoreDto.setDamageEndDate(new Date());
//            swfLogStoreDto.setDamageStartDate(new Date());
//            swfLogStoreDto.setUpdateTime(new Date());
//
//            swfLogStoreDto.setFlowID("0");
//            swfLogStoreDto.setNodeNo(5);
//            swfLogStoreDto.setNodeStatus("4");
//            swfLogStoreDto.setFlowStatus("0");
//            swfLogStoreDtoList.add(swfLogStoreDto);
//        }
//
//        return swfLogStoreDtoList;
//    }
//
//    private PrpLPersonLossTraceDto getPrpLPersonLossTraceDto(DutyDetailPayDTO detail,int i,ReportCustomerInfoEntity customerInfo,List<PaymentItemDTO> items,LossReduceDTO lossReduce,DutyPayDTO duty) {
//        PrpLPersonLossTraceDto prpLPersonLossTraceDto = new PrpLPersonLossTraceDto();
//        //        流序号	TRACKNO	Y		无明确字段（生产流程表时对应的字段信息）
//        prpLPersonLossTraceDto.setTrackNo(i+1);
//        BigDecimal payAmount = BigDecimalUtils.isNullBigDecimal(detail.getSettleAmount()) ? detail.getAutoSettleAmount() : detail.getSettleAmount();
//        PaymentItemDTO itemDTO = items.stream().filter(item -> detail.getPolicyNo().equals(item.getPolicyNo())).findFirst().orElse(null);
////        赔款计算书号	COMPENSATENO	Y		无明确字段（理算提交生成的计算书号）
//        prpLPersonLossTraceDto.setCompensateNo(itemDTO == null ? "1" : itemDTO.getCompensateNo());
//        Map<String, String> productMap = ocasMapper.getPlyBaseInfo(detail.getPolicyNo());
//        String productCode = MapUtils.getString(productMap,"productCode");
////        险种	RISKCODE	Y
//        prpLPersonLossTraceDto.setRiskCode(CodeUtil.subStringRiskCode(productCode));
////        保单号	POLICYNO	Y
//        prpLPersonLossTraceDto.setPolicyNo(detail.getPolicyNo());
////        赔付标的序号	SERIALNO	Y
//        prpLPersonLossTraceDto.setSerialNo(i+1);
////        人员序号	PERSONNO	Y
//        prpLPersonLossTraceDto.setPersonNo(1);
////        人员名称	PERSONNAME	Y
//        prpLPersonLossTraceDto.setPersonName(customerInfo.getName());
////        身份证号码	IDENTIFYNUMBER	Y
//        prpLPersonLossTraceDto.setIdentifyNumber(customerInfo.getCertificateNo());
////        性别	SEX	Y
//        prpLPersonLossTraceDto.setSex("F".equalsIgnoreCase(customerInfo.getSexCode()) ? "2" : "1");
////        年龄	AGE	Y
//        prpLPersonLossTraceDto.setAge(customerInfo.getAge());
////        标的险别序号	ITEMKINDNO	Y
//        prpLPersonLossTraceDto.setItemKindNo(0);
////        家庭序号	FAMILYNO	Y	从承保取被保险人序号
//        String riskPersonNo = ocasMapper.getRiskPersonNo(detail.getPolicyNo(), customerInfo.getCertificateNo(), customerInfo.getName());
//        prpLPersonLossTraceDto.setFamilyNo(Integer.valueOf(riskPersonNo));
////        承保险别代码	KINDCODE	CY
//        prpLPersonLossTraceDto.setKindCode(CodeUtil.subStringCode(detail.getPlanCode()));
////        责任分类代码	LIABCODE	Y	详情请查阅意健险责任分类代码表
//        prpLPersonLossTraceDto.setLiabCode(CodeUtil.subStringCode(duty.getDutyCode()));
////        责任分类名称	LIABNAME	Y	详情请查阅意健险责任分类代码表
//        prpLPersonLossTraceDto.setLiabName(duty.getDutyName());
////        责任名细分类代码	LIABDETAILCODE	CY	有赔款金额责任明细必填
//        prpLPersonLossTraceDto.setLiabDetailCode(detail.getDutyDetailType());
////        责任名细分类名称	LIABDETAILNAME	CY
//        prpLPersonLossTraceDto.setLiabDetailName(SettleConst.DETAIL_TYPE_NAME.get(detail.getDutyDetailType()));
////        人数	LOSSQUANTITY	Y
//        prpLPersonLossTraceDto.setLossQuantity(0);
////        单位保额/赔偿限额	UNITAMOUNT	Y
//        prpLPersonLossTraceDto.setUnitAmount(nvl(detail.getBaseAmountPay(),0));
////        币别（现存：人名币）	CURRENCY	Y	默认“CNY”
//        prpLPersonLossTraceDto.setCurrency("CNY");
////        保险金额	AMOUNT	Y
//        prpLPersonLossTraceDto.setAmount(new BigDecimal("0"));
////        标的价值币别	CURRENCY1	Y
//        prpLPersonLossTraceDto.setCurrency1("CNY");
////        标的价值	ITEMVALUE	Y	默认“0.00”
//        prpLPersonLossTraceDto.setItemValue(new BigDecimal("0"));
////        受损金额币别	CURRENCY2	Y	默认“CNY”
//        prpLPersonLossTraceDto.setCurrency2("CNY");
////        受损金额	SUMLOSS	Y		无明确字段(申请赔款金额)
//        prpLPersonLossTraceDto.setSumLoss(nvl(payAmount,0));
////                剔除金额/残值/损余;意健险自费金额（noMedicalCost+thirdPartyPaid）	SUMREST	Y
//        prpLPersonLossTraceDto.setSumRest(new BigDecimal("0"));
////        责任比例	INDEMNITYDUTYRATE	Y	默认值，为：0.0000
//        prpLPersonLossTraceDto.setIndemnityDutyRate(new BigDecimal("0"));
////        赔付比例	CLAIMRATE	Y
//        prpLPersonLossTraceDto.setClaimRate(nvl(detail.getPayProportion(),0));
////        免赔额币别	CURRENCY3	Y	默认为“CNY"
//        prpLPersonLossTraceDto.setCurrency3("CNY");
////        免赔率	DEDUCTIBLERATE	Y
//        prpLPersonLossTraceDto.setDeductibleRate(new BigDecimal("0"));
////        免赔额	DEDUCTIBLE	Y
//        prpLPersonLossTraceDto.setDeductible(nvl(detail.getRemitAmount(),0));
////        实赔币别	CURRENCY4	Y	默认为“CNY"
//        prpLPersonLossTraceDto.setCurrency4("CNY");
////        实赔金额	SUMREALPAY	Y
//
//        prpLPersonLossTraceDto.setSumRealPay(nvl(payAmount,0));
////        事故责任免赔率	DUTYDEDUCTIBLERATE	Y	默认值，为：0.0000
//        prpLPersonLossTraceDto.setDutyDeductibleRate(new BigDecimal("0"));
////        伤残等级	INJURYGRADE	CY	理算环节选填
//        prpLPersonLossTraceDto.setInjuryGrade(BigDecimalUtils.isNullOrZero(detail.getDisabilityRate()) ? "" : detail.getDisabilityGrade().toString());
////        入院日期	INHOSPDATE	CY	有住院需要录入入院时间
//        prpLPersonLossTraceDto.setInHospDate(new Date());
////        出院日期	OUTHOSPDATE	CY	有住院需要录入出院时间
//        prpLPersonLossTraceDto.setOutHospDate(new Date());
////        住院天数	HOSPITALDAYS	Y
//        prpLPersonLossTraceDto.setHospitalDays(0);
////        备注	REMARK	CY	可以在人员赔付信息栏添加备注信息
//        prpLPersonLossTraceDto.setRemark("");
////        历史赔付金额	HISPAID	Y
//        prpLPersonLossTraceDto.setHisPaid(new BigDecimal("0"));
////        最高赔付金额	MAXPAID	Y		无明确字段（该保单对应的赔款条款最高的赔付金额）
//        prpLPersonLossTraceDto.setMaxPaid(nvl(detail.getMaxAmountPay(),0));
////        协商赔偿比例	ARRANGERATE	Y		无明确字段(商定赔付比例，若没有，则默认100，表示按照赔款比例全部赔付)
//        prpLPersonLossTraceDto.setArrangeRate(new BigDecimal("100"));
////        核定赔偿	SUMDEFPAY	Y
//        prpLPersonLossTraceDto.setSumDefPay(nvl(payAmount,0));
////        每日补贴金额	AMOUNTPERDAY	Y
//        prpLPersonLossTraceDto.setAmountPerDay(new BigDecimal("0"));
////        不计免赔率特约金额	NODUTYDEDUCTAMOUNT	Y	默认“0.00”
//        prpLPersonLossTraceDto.setNoDutyDeductAmount(new BigDecimal("0"));
////        是否和解	COMPROMISEFLAG	Y		无明确字段（如果此信息， 默认没有和解）
//        prpLPersonLossTraceDto.setComPromiseFlag("1");
////        赔偿系数(比例)	LOSSRATE	Y	默认为“0”
//        prpLPersonLossTraceDto.setLossRate(nvl(detail.getDetailLimitAmount(),0));
////        应付赔偿金额	SHOULDPAY	Y
//        prpLPersonLossTraceDto.setShouldPay(nvl(payAmount,0));
////        赔偿限额	PAYLIMIT	Y
//        prpLPersonLossTraceDto.setPayLimit(new BigDecimal("0"));
////        绝对免赔额	ABDEDUCTIBLE	Y	默认值“0”
//        prpLPersonLossTraceDto.setAbDeductible(new BigDecimal("0"));
////        对人伤损失主表币别兑换率	EXCHRATE	Y
//        prpLPersonLossTraceDto.setExCHRate(new BigDecimal("0"));
////        出险时最高赔付额	DAMPAID	Y
//        prpLPersonLossTraceDto.setDamPaid(new BigDecimal("0"));
////        非医保费用	NOMEDICALCOST	CY	有非医保费用就填
//        prpLPersonLossTraceDto.setNoMedicalCost(new BigDecimal("0"));
////        第三方已支付	THIRDPARTYPAID	CY	有第三方已支付就填
//        prpLPersonLossTraceDto.setThirdPartyPaid(nvl(detail.getThirdPartyPayment(),0));
////        人身意外险伤残代码	DISABILITYCODE	CY
//        prpLPersonLossTraceDto.setDisabilityCode("");
////        减损原因代码	IMPAIRMENTAMOUNTCODE	CY		无明确字段（如果没有则默认为空）
//        prpLPersonLossTraceDto.setImpairmentAmountCode("");
////        减损金额	IMPAIRMENTAMOUNT	CY
//        if (lossReduce!=null){
//            prpLPersonLossTraceDto.setImpairmentAmount(lossReduce.getReduceAmount());
//        }
////        更新时间	UPDATETIME	CY	只要对表操作就修改updatetime
//        prpLPersonLossTraceDto.setUpdateTime(new Date());
//        //赔款费用信息表")
//        return prpLPersonLossTraceDto;
//    }
//
//    private PrpLPersonLossDto getPrpLPersonLossDto(DutyDetailPayDTO detail,int i,ReportCustomerInfoEntity customerInfo,List<PaymentItemDTO> items,LossReduceDTO lossReduce,DutyPayDTO duty) {
//        PrpLPersonLossDto prpLPersonLossDto = new PrpLPersonLossDto();
//        BigDecimal payAmount = BigDecimalUtils.isNullBigDecimal(detail.getSettleAmount()) ? detail.getAutoSettleAmount() : detail.getSettleAmount();
//        //        赔款计算书号	COMPENSATENO	Y		无明确字段(理算时生成的号码)
//        PaymentItemDTO itemDTO = items.stream().filter(item -> detail.getPolicyNo().equals(item.getPolicyNo())).findFirst().orElse(null);
//        prpLPersonLossDto.setCompensateNo(itemDTO == null ? "1" : itemDTO.getCompensateNo());
//        Map<String, String> productMap = ocasMapper.getPlyBaseInfo(detail.getPolicyNo());
//        String productCode = MapUtils.getString(productMap,"productCode");
//        //                险种	RISKCODE	Y
//        prpLPersonLossDto.setRiskCode(CodeUtil.subStringRiskCode(productCode));
//        //        保单号	POLICYNO	Y
//        prpLPersonLossDto.setPolicyNo(detail.getPolicyNo());
//        //        赔付标的序号	SERIALNO	Y		无明确字段(序号递加)
//        prpLPersonLossDto.setSerialNo(i+1);
//        //                人员序号	PERSONNO	Y		无明确字段（从承保系统获取）
//        prpLPersonLossDto.setPersonNo(1);
//        //        人员名称	PERSONNAME	Y
//        prpLPersonLossDto.setPersonName(customerInfo.getName());
//        //        身份证号码	IDENTIFYNUMBER	Y
//        prpLPersonLossDto.setIdentifyNumber(customerInfo.getCertificateNo());
//        //        性别	SEX	Y
//        prpLPersonLossDto.setSex("F".equalsIgnoreCase(customerInfo.getSexCode()) ? "2" : "1");
//        //        年龄	AGE	Y
//        prpLPersonLossDto.setAge(customerInfo.getAge());
//        //        标的险别序号	ITEMKINDNO	Y		无明确字（取值PrpCitemKind中ItemKindNo字段）
//        prpLPersonLossDto.setItemKindNo(0);
//        //        家庭序号	FAMILYNO	Y	从承保取被保险人序号	无明确字段(取值prpCinsured中SerialNo序号)
//        String riskPersonNo = ocasMapper.getRiskPersonNo(detail.getPolicyNo(), customerInfo.getCertificateNo(), customerInfo.getName());
//        prpLPersonLossDto.setFamilyNo(Integer.valueOf(riskPersonNo));
//        //        承保险别代码	KINDCODE	Y
//        prpLPersonLossDto.setKindCode(CodeUtil.subStringCode(detail.getPlanCode()));
//        //        责任分类代码	LIABCODE	Y	详情请查阅意健险责任分类代码表
//        prpLPersonLossDto.setLiabCode(CodeUtil.subStringCode(duty.getDutyCode()));
//        //        责任分类名称	LIABNAME	Y	详情请查阅意健险责任分类代码表
//        prpLPersonLossDto.setLiabName(duty.getDutyName());
//        //        责任名细分类代码	LIABDETAILCODE	CY	有赔款金额这两个字段必填
//        prpLPersonLossDto.setLiabDetailCode(detail.getDutyDetailType());
//        //        责任名细分类名称	LIABDETAILNAME	CY
//        prpLPersonLossDto.setLiabDetailName(SettleConst.DETAIL_TYPE_NAME.get(detail.getDutyDetailType()));
//        //        人数	LOSSQUANTITY	Y
//        prpLPersonLossDto.setLossQuantity(0);
//        //        单位保额/赔偿限额	UNITAMOUNT	Y
//        prpLPersonLossDto.setUnitAmount(nvl(detail.getBaseAmountPay(),0));
//        //        币别(现存人民币)	CURRENCY	Y
//        prpLPersonLossDto.setCurrency("CNY");
//        //        保险金额	AMOUNT	Y		责任保额？(责任条款的保额)
//        prpLPersonLossDto.setAmount(new BigDecimal("0"));
//        //                标的价值币别	CURRENCY1	Y	默认“CNY”
//        prpLPersonLossDto.setCurrency1("CNY");
//        //        标的价值	ITEMVALUE	Y	默认“0.00”
//        prpLPersonLossDto.setItemValue(new BigDecimal("0"));
//        //        受损金额币别	CURRENCY2	Y	默认“CNY”
//        prpLPersonLossDto.setCurrency2("CNY");
//        //        受损金额	SUMLOSS	Y		无明确字段(申请赔款金额)
//        prpLPersonLossDto.setSumLoss(nvl(payAmount,0));
//        //                剔除金额/残值/损余;意健险自费金额（noMedicalCost+thirdPartyPaid）	SUMREST	Y
//        prpLPersonLossDto.setSumRest(new BigDecimal("0"));
//        //        责任比例	INDEMNITYDUTYRATE	Y		无明确字段(默认值0)
//        prpLPersonLossDto.setIndemnityDutyRate(new BigDecimal("0"));
//        //                赔付比例	CLAIMRATE	Y
//        prpLPersonLossDto.setClaimRate(nvl(detail.getPayProportion(),0));
//        //        免赔额币别	CURRENCY3	Y	默认“CNY”
//        prpLPersonLossDto.setCurrency3("CNY");
//        //        免赔率	DEDUCTIBLERATE	Y		无免赔传什么？（没有默认0）
//        prpLPersonLossDto.setDeductibleRate(new BigDecimal("0"));
//        //        免赔额	DEDUCTIBLE	Y
//        prpLPersonLossDto.setDeductible(nvl(detail.getRemitAmount(),0));
//        //        实赔币别	CURRENCY4	Y	默认“CNY”
//        prpLPersonLossDto.setCurrency4("CNY");
//        //        实赔金额	SUMREALPAY	Y
//
//        prpLPersonLossDto.setSumRealPay(nvl(payAmount,0));
//        //        事故责任免赔率	DUTYDEDUCTIBLERATE	Y	默认值，为：0.0000
//        prpLPersonLossDto.setDutyDeductibleRate(new BigDecimal("0"));
//        //        INJURYGRADE	INJURYGRADE	CY	存在伤残时录入
//        prpLPersonLossDto.setInjuryGrade("");
//        //        入院日期	INHOSPDATE	CY	有住院需要录入入院时间
//        prpLPersonLossDto.setInHospDate(new Date());
//        //        出院日期	OUTHOSPDATE	CY	有住院需要录入出院时间
//        prpLPersonLossDto.setOutHospDate(new Date());
//        //        住院天数	HOSPITALDAYS	Y
//        prpLPersonLossDto.setHospitalDays(0);
//        //        备注	REMARK	CY	可以在人员赔付信息栏添加备注信息
//        prpLPersonLossDto.setRemark("");
//        //        历史赔付金额	HISPAID	Y
//        prpLPersonLossDto.setHisPaid(new BigDecimal("0"));
//        //        最高赔付金额	MAXPAID	Y
//        prpLPersonLossDto.setMaxPaid(nvl(detail.getMaxAmountPay(),0));
//        //        协商赔偿比例	ARRANGERATE	Y		无明确字段(商定赔付比例，若没有，则默认100，表示按照赔款比例全部赔付)
//        prpLPersonLossDto.setArrangeRate(new BigDecimal("100"));
//        //        核定赔偿	SUMDEFPAY	Y
//        prpLPersonLossDto.setSumDefPay(nvl(payAmount,0));
//        //        每日补贴金额	AMOUNTPERDAY	Y
//        prpLPersonLossDto.setAmountPerDay(new BigDecimal("0"));
//        //        不计免赔率特约金额	NODUTYDEDUCTAMOUNT	Y	默认值“0.00”
//        prpLPersonLossDto.setNoDutyDeductAmount(new BigDecimal("0"));
//        //        是否和解	COMPROMISEFLAG	Y		无明确字段（如果此信息， 默认没有和解）
//        prpLPersonLossDto.setComPromiseFlag("1");
//        //        赔偿系数(比例)	LOSSRATE	Y	默认为“0”
//        prpLPersonLossDto.setLossRate(nvl(detail.getDetailLimitAmount(),0));
//        //        应付赔偿金额	SHOULDPAY	Y
//        prpLPersonLossDto.setShouldPay(nvl(payAmount,0));
//        //        赔偿限额	PAYLIMIT	Y
//        prpLPersonLossDto.setPayLimit(new BigDecimal("0"));
//        //        对理算主表币别兑换率	EXCHRATE	Y
//        prpLPersonLossDto.setExCHRate(new BigDecimal("0"));
//        //        出险时最高赔付额	DAMPAID	Y		能否传责任最大给付额（改保单改条款可以赔款的最高金额）
//        prpLPersonLossDto.setDamPaid(new BigDecimal("0"));
//        //        非医保费用	NOMEDICALCOST	CY	有非医保费用就填
//        prpLPersonLossDto.setNoMedicalCost(new BigDecimal("0"));
//        //        第三方已支付	THIRDPARTYPAID	CY	有第三方已支付就填
//        prpLPersonLossDto.setThirdPartyPaid(nvl(detail.getThirdPartyPayment(),0));
//        //        人身意外险伤残代码	DISABILITYCODE	CY	存在伤残时录入
//        prpLPersonLossDto.setDisabilityCode("");
//        //        减损原因代码	IMPAIRMENTAMOUNTCODE	CY		无明确字段（如果没有则默认为空）
//        prpLPersonLossDto.setImpairmentAmountCode("");
//        //        减损金额	IMPAIRMENTAMOUNT	CY
//        if (lossReduce!=null){
//            prpLPersonLossDto.setImpairmentAmount(lossReduce.getReduceAmount());
//        }
//        //        更新时间	UPDATETIME	CY	只要对表操作就修改updatetime
//        prpLPersonLossDto.setUpdateTime(new Date());
//        return prpLPersonLossDto;
//    }
//
//    @SneakyThrows
//    private List<PrpLCompensateDto> getPrpLCompensateDto(WholeCaseBaseDTO wholeCaseBaseEntity, ReportBaseInfoResData reportBaseInfo,
//                                                         List<PaymentItemDTO> items, ReportInfoEntity reportInfo,
//                                                         List<PolicyPayDTO> policyPayList, List<CaseBaseEntity> caseBaseInfoList) {
//        List<PrpLCompensateDto> prpLCompensateDtoList = new ArrayList<>();
//        for (int i = 0; i < items.size(); i++) {
//            PaymentItemDTO itemDTO = items.get(i);
//            // claimType=1 赔款类型：(13-赔款 1J直接理赔费用 ,"C13", "共保代付赔款" "C1J", "共保代付费用"
//            // claimType=2 11-预赔赔款 "11J", "预赔费用" ,"P13", "共保代付预赔赔款" "P1J", "共保代付预赔费用" )
//            String paymentType = itemDTO.getPaymentType();
//            CaseBaseEntity caseBaseEntity = caseBaseInfoList.stream().filter(it -> it.getPolicyNo().equals(itemDTO.getPolicyNo())).findFirst().orElse(null);
//            PolicyPayDTO policyPayDTO = policyPayList.stream().filter(it -> it.getPolicyNo().equals(itemDTO.getPolicyNo())).findFirst().orElse(null);
//            if (caseBaseEntity == null || policyPayDTO == null) {
//                log.info("getPrpLCompensateDto -数据异常 ：" + wholeCaseBaseEntity.getReportNo());
//                continue;
//            }
//            PrpLCompensateDto prpLCompensateDto = new PrpLCompensateDto();
//            //        赔款计算书号码	COMPENSATENO	Y		无明确字段(理算时生成的号码)
//            prpLCompensateDto.setCompensateNo(itemDTO.getCompensateNo());
//            //                理赔类型	LFLAG	Y
//            prpLCompensateDto.setlFlag("L");
//            //        结案号	CASENO	CY	结案回写	无明确字段（结案后生成的号码）
//            prpLCompensateDto.setCaseNo(caseBaseEntity.getCaseNo());
//            //        次数	TIMES	Y
//            prpLCompensateDto.setTimes(wholeCaseBaseEntity.getCaseTimes());
//            //        险类代码	CLASSCODE	Y
//            Map<String, String> productMap = ocasMapper.getPlyBaseInfo(itemDTO.getPolicyNo());
//            String productCode = MapUtils.getString(productMap, "productCode");
////            String productClass = MapUtils.getString(productMap, "productClass");
//            String riskCode = CodeUtil.subStringRiskCode(productCode);
//            prpLCompensateDto.setClassCode(riskCode.substring(0,2));
//            //        险种代码	RISKCODE	Y
//            prpLCompensateDto.setRiskCode(riskCode);
//            //        立案号码	CLAIMNO	Y		无明确字段（立案后生成的号码）
//
//            prpLCompensateDto.setClaimNo(caseBaseEntity.getRegistNo());
//            //        保单号码	POLICYNO	Y
//            prpLCompensateDto.setPolicyNo(itemDTO.getPolicyNo());
//            //        终到日期	PRESERVEDATE	Y		无明确字段（理算提交当前时间）
//            prpLCompensateDto.setPreserveDate(new Date());
//            //        币别代码(现存:CNY)	CURRENCY	Y
//            prpLCompensateDto.setCurrency("CNY");
//            //        标的损失金额(同保单币别)	SUMLOSS	Y		理算金额？(默认为0.0)
//            prpLCompensateDto.setSumLoss(new BigDecimal("0.0"));
//            //        损余金额(同保单币别)	SUMREST	Y		剩余理赔金额？(默认为0.0)
//            prpLCompensateDto.setSumRest(new BigDecimal("0.0"));
//            //        责任赔款合计(同保单币别)	SUMDUTYPAID	Y	--** = PrpLloss表标的赔款合计--** + PrpLcharge计入赔款的费用合计
//            BigDecimal policyPay = nvl(policyPayDTO.getPolicyPay(), 0);
//            BigDecimal policyPrePay = nvl(policyPayDTO.getPolicyPrePay(), 0);
//            BigDecimal sumDutyPaid = policyPay.add(policyPrePay);
//            prpLCompensateDto.setSumDutyPaid(sumDutyPaid);
//            //        不计入赔款的费用金额(同保单币别)	SUMNODUTYFEE	Y
//            BigDecimal policySumFee = nvl(policyPayDTO.getPolicySumFee(), 0);
//            BigDecimal policyPreFee = nvl(policyPayDTO.getPolicyPreFee(), 0);
//            BigDecimal sumNoDutyFee = policySumFee.add(policyPreFee);
//            prpLCompensateDto.setSumNoDutyFee(sumNoDutyFee);
//            //        已预付赔款(同保单币别)	SUMPREPAID	Y	** 第一张计算书--** =SUM(PrpLprepay.SumPrePaid)--** 第二张后金额=0
//            BigDecimal prePaid = policyPrePay.add(policyPreFee);
//            prpLCompensateDto.setSumPrepaid(prePaid);
//            //        总赔付金额(同保单币别)	SUMPAID	Y		赔款+费用？（是）
//            BigDecimal sumPaid = sumDutyPaid.add(sumNoDutyFee);
//            prpLCompensateDto.setSumPaid(sumPaid);
//
//            BigDecimal sumThisPaid = sumPaid.subtract(prePaid);
//            //        本次赔付金额（同保单币别）--** = SumPaid-SumPrePaid	SUMTHISPAID	Y
//            prpLCompensateDto.setSumThisPaid(sumThisPaid);
//            //        开户银行	BANK	Y
//            prpLCompensateDto.setBank(itemDTO.getBankDetail());
//            //        银行帐号	ACCOUNT	Y
//            prpLCompensateDto.setAccount(itemDTO.getClientBankAccount());
//            //        出单机构	MAKECOM	Y
//            prpLCompensateDto.setMakeCom(reportInfo.getAcceptDepartmentCode());
//            //        业务归属机构代码	COMCODE	Y
//            prpLCompensateDto.setComCode(reportInfo.getAcceptDepartmentCode());
//            //        经办人代码	HANDLERCODE	Y
//            prpLCompensateDto.setHandlerCode( NcbsConstant.ZK_ADMIN_UM);
//            //        归属业务员代码	HANDLER1CODE	Y
//            prpLCompensateDto.setHandler1Code( NcbsConstant.ZK_ADMIN_UM);
//            //        最终核赔人代码	UNDERWRITECODE	Y
//            prpLCompensateDto.setUnderWriteCode( NcbsConstant.ZK_ADMIN_UM);
//            //        最终核赔人名称	UNDERWRITENAME	Y
//            prpLCompensateDto.setUnderWriteName( NcbsConstant.ZK_ADMIN_NAME);
//            //        统计年月	STATISTICSYM	Y		无明确字段（理算提交当前时间）
//            SimpleDateFormat formatter2 = new SimpleDateFormat("yyyy/MM/dd");
//            prpLCompensateDto.setStatisticsYM(formatter2.parse(formatter2.format(new Date())));
//            //        操作员代码	OPERATORCODE	Y
//            prpLCompensateDto.setOperatorCode(reportInfo.getReportRegisterUm());
//            //        计算机输入日期	cccc	Y	系统自动获取当前操作时间
//            prpLCompensateDto.setInputDate(new Date());
//            //        核赔完成日期	UNDERWRITEENDDATE	Y	核赔通过填写
//            prpLCompensateDto.setUnderWriteEndDate(new Date());
//            //        核赔标志	UNDERWRITEFLAG	Y
//            prpLCompensateDto.setUnderWriteFlag("1");
//            //        备注	REMARK	CY		无明确字段(没有不填)
//            prpLCompensateDto.setRemark("");
//            //                案件性质	CASETYPE	CY		无明确字段
//            prpLCompensateDto.setCaseType("2");
//            //        责任比例	INDEMNITYDUTYRATE	Y	默认值，为：0.0000
//            prpLCompensateDto.setIndemnityDutyRate(new BigDecimal("0.0000"));
//            //        最终计算书标志	FINALLYFLAG	Y	默认“0”
//            prpLCompensateDto.setFinallyFlag("0");
//            //        理赔结论	RESULT	Y
//            prpLCompensateDto.setResult("01");
//            //        赔案类型	CLAIMTYPE	Y		无明确字段(按照枚举值填写)
//            prpLCompensateDto.setClaimType("0");
//            //                委托标志	ENTRUSTFLAG	Y	默认“01”
//            prpLCompensateDto.setEntrustFlag("0");
//            //        赔付数量	LOSSESNUMBER	Y		无明确字段(默认0)
//            prpLCompensateDto.setLossesNumber(new BigDecimal("0"));
//            //                已经支付金额	PAYSUMDUTYPAID	Y
//            prpLCompensateDto.setPaySumDutyPaid(new BigDecimal("0"));
//            //        支付金额的实际时间	PAYTIME	CY	支付回写字段
//            prpLCompensateDto.setPayTime(null);
//            //        支付方式(支付回写):"00"现金，"01"支票 ，"02"转帐 	PAYTYPE	CY	支付回写字段
//            prpLCompensateDto.setPayType("");
//            //        银行户名(支付回写)	CARDNAME	CY	支付回写字段
//            prpLCompensateDto.setCardName("");
//            //        银行帐号/身份证号(支付回写)	ACCOUNTCODE	CY	支付回写字段
//            prpLCompensateDto.setAccountCode("");
//            //        意健险报告书打印次数	PRINTADD	Y		无明确字段(默认为0)
//            prpLCompensateDto.setPrintAdd(0);
//            //                意健险给付收据打印次数	PRINTTIMES	Y		无明确字段(默认为0)
//            prpLCompensateDto.setPrintTimes(0);
//            //        出险日期	DAMAGESTARTDATE	Y
//            SimpleDateFormat formatter = new SimpleDateFormat("HH:mm:ss");
//            SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy/MM/dd");
//            Date accidentDate = reportBaseInfo.getAccidentDate();
//            prpLCompensateDto.setDamageStartDate(formatter1.parse(formatter1.format(accidentDate)));
//            //        出险小时	DAMAGESTARTHOUR	Y
//            prpLCompensateDto.setDamageStartHour(formatter.format(accidentDate));
//            //        出险地点	DAMAGEADDRESS	Y
//            prpLCompensateDto.setDamageAddress(reportBaseInfo.getAccidentPlace());
//            List<String> caseSubClassList = caseClassDao.getCaseClassList(itemDTO.getReportNo(), itemDTO.getCaseTimes(), "report1");
//            if (CollectionUtils.isNotEmpty(caseSubClassList)) {
//                //        出险类型	DAMAGETYPECODE	Y
//                prpLCompensateDto.setDamageTypeCode(InsuredApplyTypeEnum.getCode(caseSubClassList.get(0)));
//                //        出险类型名称	DAMAGETYPENAME	Y
//                prpLCompensateDto.setDamageTypeName(InsuredApplyTypeEnum.getName(caseSubClassList.get(0)));
//            } else {
//                String accidentTypeName = reportBaseInfo.getAccidentTypeName();
//                prpLCompensateDto.setDamageTypeCode(reportBaseInfo.getAccidentType());
//                prpLCompensateDto.setDamageTypeName(accidentTypeName.split(",")[0]);
//            }
//            // 根据保单号查询 共保金额
//            BigDecimal sumAdvancePay = paymentItemMapper.getSumAdvancePay(itemDTO);
//            if ( BigDecimalUtils.compareBigDecimalPlus(sumAdvancePay,BigDecimal.ZERO)){
//                //        共保垫付标志	ADVANCEPAYMENTFLAG	CY	意健险主共报单需要填写
//                prpLCompensateDto.setAdvancePaymentFlag("1");
//                //        垫付总金额	ADVANCEAMOUNT	CY	意健险主共报单需要填写
//                prpLCompensateDto.setAdvanceAmount(sumAdvancePay);
//                //        是否收回垫付赔款	DRAWBACKFLAG	CY	意健险主共报单需要填写	无明确字段（不填）
//                prpLCompensateDto.setDrawbackFlag("0");
//            } else {
//                prpLCompensateDto.setAdvancePaymentFlag("0");
//                prpLCompensateDto.setAdvanceAmount(null);
//                prpLCompensateDto.setDrawbackFlag("");
//            }
//            //        是否追偿案件	ISREPLEVYCASE	Y
//            prpLCompensateDto.setIsReplevyCase(wholeCaseBaseEntity.getAgentType());
//            //        计算书类型	COMPENSATETYPE	Y
//            prpLCompensateDto.setCompensateType("2");
//            //        对CNY兑换率	EXCHRATE	Y	默认“1.000000”
//            prpLCompensateDto.setExCHRate(new BigDecimal("1.000000"));
//            //        出险结果	ACCIDENTRESULT	Y		无明确字段（不填）
//            prpLCompensateDto.setAccidentResult("");
//            //        重大疾病代码	SERIOUSDISEASECODE	CY	意健险理算环节选填	枚举？（可不填）
//            prpLCompensateDto.setSeriousDiseaseCode("");
//            //        涉诉标志	LAWSUITFLAG	Y
//            prpLCompensateDto.setLawsuitFlag("0");
//            //        本案是否适用超赔合约	ISBEYONDPAIDFLAG	CY	是，否
//            prpLCompensateDto.setIsBeyondPaidFlag("");
//            //        超赔合约摊回金额	BEYONDPAIDSUM	CY	本案是否适用超赔合约为是，需要录入金额；为否，不需要录入金额
//            prpLCompensateDto.setBeyondPaidSum(new BigDecimal("0"));
//            //        更新时间	UPDATETIME	Y
//            prpLCompensateDto.setUpdateTime(new Date());
//            prpLCompensateDtoList.add(prpLCompensateDto);
//        }
//
//        return prpLCompensateDtoList;
//    }
//
//    @SneakyThrows
//    private PrpLCertifyCollectDto getPrpLCertifyCollectDto(String reportNo, Integer caseTimes, PolicyInfoDTO policyInfoDTO) {
//        PrpLCertifyCollectDto prpLCertifyCollectDto = new PrpLCertifyCollectDto();
//        FileInfoDTO fileInfoDTO = new FileInfoDTO();
//        fileInfoDTO.setReportNo(reportNo);
//        fileInfoDTO.setCaseTimes(caseTimes);
//        fileInfoDTO.setFileType(FileUploadConstants.FILE_TYPE_DOCUMENT);
//        List<FileInfoDTO> groupIdList = fileUploadService.getFileGroupIdForDocument(fileInfoDTO);
//        //        业务号码	BUSINESSNO	Y		无明确字段(报案号)
//        prpLCertifyCollectDto.setBusinessNo(reportNo);
//        //                标的代码	LOSSITEMCODE	Y	默认“1”
//        prpLCertifyCollectDto.setLossItemCode("1");
//        //        标的名称	LOSSITEMNAME	Y	默认“标的名称”
//        prpLCertifyCollectDto.setLossItemName("标的名称");
//
//
//        SimpleDateFormat formatter = new SimpleDateFormat("HH:mm:ss");
//        SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy/MM/dd");
//        if (CollectionUtils.isEmpty(groupIdList)){
//            //        单证份数	PICCOUNT	Y	默认值“0”
//            prpLCertifyCollectDto.setPicCount(0);
//
//            //        单证开始收集日期	STARTDATE	Y		能否传收单任务的开始-结束日期(单证的日期小于核赔时间，大于报案时间，大于出险时间)
//            prpLCertifyCollectDto.setStartDate(formatter1.parse(formatter1.format(new Date())));
//            //        单证开始收集小时	STARTHOUR	Y
//            prpLCertifyCollectDto.setStartHour(formatter.format(new Date()));
//            //        单证结束收集日期	ENDDATE	Y
//            prpLCertifyCollectDto.setEndDate(formatter1.parse(formatter1.format(new Date())));
//            //        单证结束收集小时	ENDHOUR	Y
//            prpLCertifyCollectDto.setEndHour(formatter.format(new Date()));
//            //        操作员	OPERATORCODE	Y
//            prpLCertifyCollectDto.setOperatorCode(NcbsConstant.ZK_ADMIN_UM);
//            //        收集标志	COLLECTFLAG	Y
//            prpLCertifyCollectDto.setCollectFlag("0");
//        }  else {
//            Date createdDate = groupIdList.get(CommonConstant.ZERO).getCreatedDate();
//            Date updatedDate = groupIdList.get(CommonConstant.ZERO).getUpdatedDate();
//            //        单证份数	PICCOUNT	Y	默认值“0”
//            prpLCertifyCollectDto.setPicCount(groupIdList.size());
//
//            //        单证开始收集日期	STARTDATE	Y		能否传收单任务的开始-结束日期(单证的日期小于核赔时间，大于报案时间，大于出险时间)
//            prpLCertifyCollectDto.setStartDate(formatter1.parse(formatter1.format(createdDate)));
//            //        单证开始收集小时	STARTHOUR	Y
//            prpLCertifyCollectDto.setStartHour(formatter.format(createdDate));
//            //        单证结束收集日期	ENDDATE	Y
//            prpLCertifyCollectDto.setEndDate(formatter1.parse(formatter1.format(updatedDate)));
//            //        单证结束收集小时	ENDHOUR	Y
//            prpLCertifyCollectDto.setEndHour(formatter.format(updatedDate));
//
//            //        操作员	OPERATORCODE	Y
//            prpLCertifyCollectDto.setOperatorCode(groupIdList.get(CommonConstant.ZERO).getCreatedBy());
//            //        收集标志	COLLECTFLAG	Y
//            prpLCertifyCollectDto.setCollectFlag("1");
//        }
//
//        //        存放事故类型	CASEFLAG	Y	默认值“0000”
//        prpLCertifyCollectDto.setCaseFlag("0000");
//        //        案件处理意见	CONTENT	CY		能否传核赔说明(可以不填)
//        prpLCertifyCollectDto.setContent(null);
//        //                保单号码	POLICYNO	Y		多个保单如何传(主保单号，报案时录入的保单号)
//        prpLCertifyCollectDto.setPolicyNo(policyInfoDTO.getPolicyNo());
//        //        险种代码	RISKCODE	Y		多个险种如何传(主保单对应的险种，报案时录入的保单号对应险种)
//        prpLCertifyCollectDto.setRiskCode(CodeUtil.subStringRiskCode(policyInfoDTO.getProductCode()));
//        //        年	UPLOADYEAR	Y
//        SimpleDateFormat formatter2 = new SimpleDateFormat("yyyy");
//        prpLCertifyCollectDto.setUploadYear(formatter2.format(new Date()));
//        //        委托赔案标志	ENTRUSTFLAG	Y	默认值“01”
//        prpLCertifyCollectDto.setEntrustFlag("01");
//        //        线上化理赔:是否面见客户收单	ISMEETCUSTOMERCERTI	Y		无明确字段(默认为否)
//        prpLCertifyCollectDto.setIsMeetCustomerCerti("0");
//        //                修改时间	UPDATETIME	CY	只要对表操作就修改updatetime
//        prpLCertifyCollectDto.setUpdateTime(new Date());
//        return prpLCertifyCollectDto;
//    }
//
//    @SneakyThrows
//    private PrpLAcciCheckDto initPrpLacciCheckDto(String reportNo, PolicyInfoDTO policyInfoDTO, List<InvestigateVO> investigateRecord) {
//        PrpLAcciCheckDto prpLAcciCheckDto = new PrpLAcciCheckDto();
//
////        报案号码	REGISTNO	Y
//        prpLAcciCheckDto.setRegistNo(reportNo);
//        InvestigateVO investigateVO = investigateRecord.get(CommonConstant.ZERO);
//        String idAhcsInvestigate = investigateVO.getIdAhcsInvestigate();
//        //        调查次数	TIMES	Y
//        prpLAcciCheckDto.setTimes(investigateRecord.size());
////        调查号(唯一索引)	CHECKNO	Y
//        prpLAcciCheckDto.setCheckNo(reportNo+"-001");
////        发起节点	CERTITYPE	Y
//        prpLAcciCheckDto.setCertiType("04");
////        发起节点的业务号码	CERTINO	Y		无明确字段(01.02.03时,为报案号；05时为立案号；07时为理算书号)
//        prpLAcciCheckDto.setCertiNo(reportNo);
////        险种代码 	RISKCODE	Y		调查是案件维度，需要降维传？险种代码只有4位长度，可能不够；就是传四位险种代码
//        prpLAcciCheckDto.setRiskCode(CodeUtil.subStringRiskCode(policyInfoDTO.getProductCode()));
////        保单号码	POLICYNO	Y
//        prpLAcciCheckDto.setPolicyNo(policyInfoDTO.getPolicyNo());
////        调查类型	CHECKTYPE	Y		枚举？是的
//        prpLAcciCheckDto.setCheckType("1");
//        //        调查内容简要描述	CHECKCONTEXT	Y		能否传提调项（不明白）
//        prpLAcciCheckDto.setCheckContext(investigateVO.getInvestigateItems());
//        //        调查对象描述	CHECKOBJECTDESC	Y		无明确字段（调查人的名字等等）
//        prpLAcciCheckDto.setCheckObjectDesc(investigateVO.getInvestigateItems());
//        //        调查方式	CHECKNATURE	Y
//        prpLAcciCheckDto.setCheckNature("1");
//
//        Date startDate = investigateVO.getCreatedDate();
//        Date endDate = investigateVO.getUpdatedDate();
//        String startDateDateStr2 = DateUtils.parseToFormatString(startDate, "HH:mm");
//        String endDateStr2 = DateUtils.parseToFormatString(endDate, "HH:mm");
//        SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy/MM/dd");
//        //        调查起始日期	CHECKDATE	Y
//        prpLAcciCheckDto.setCheckDate(formatter1.parse(formatter1.format(startDate)));
//        //        调查起始时间（精确到分）	CHECKHOUR	Y
//        prpLAcciCheckDto.setCheckHour(startDateDateStr2);
//        //        调查结束日期	CHECKENDDATE	Y
//        prpLAcciCheckDto.setCheckEndDate(formatter1.parse(formatter1.format(endDate)));
//        //        调查结束时间（精确到分）	CHECKENDHOUR	Y
//        prpLAcciCheckDto.setCheckSite(endDateStr2);
////        查勘/代查勘地点	CHECKSITE	Y		无明确字段(调查人员的地点，或者填写医院)
//        prpLAcciCheckDto.setCheckSite("1");
////        调查地点	DAMAGECODE	CY	数据来自页面，页面上事故原因代码和说明被隐藏；页面上的事故原因代码和说明从报案表取
//        prpLAcciCheckDto.setDamageCode("1805");
////        事故原因说明	DAMAGENAME	CY
//
//        prpLAcciCheckDto.setDamageName("其他");
////        事故类型代码 	DAMAGETYPECODE	Y
////        事故类型说明	DAMAGETYPENAME	Y
//        InvestigateTaskDTO taskByInvestigateId = investigateTaskMapper.getMajorInvestigateTaskByInvestigateId(idAhcsInvestigate);
//        InvestigateTaskAuditVO audit = investigateTaskAuditMapper.getInvestigateTaskAuditForReportByInvestigateId(idAhcsInvestigate);
////        调查人代码	CHECKERCODE	Y		工号?userCode?是的
//        prpLAcciCheckDto.setCheckerCode(investigateVO.getPrimaryInvestigatorUm());
////        审核人代码	APPROVERCODE	Y		工号?userCode?是的
//        prpLAcciCheckDto.setAppRoverCode(audit.getReviewUserUm());
////        审核日期	APPROVERDATE	Y
//        prpLAcciCheckDto.setAppRoverDate(audit.getUpdatedDate());
////        审核状态	APPROVERSTATUS	Y		枚举？默认为0
//        prpLAcciCheckDto.setAppRoverStatus(audit.getReviewOpinion());
////        备注	REMARK	Y
//        prpLAcciCheckDto.setRemark(audit.getDescription());
////        调查费用	CHECKFEE	Y		无明确字段(调查时产生的金钱，调查人员花费的金额)
//        prpLAcciCheckDto.setCheckFee(audit.getCommonEstimateFee());
////        调查费用币别（人民币）	CURRENCY	Y
//
////        申请人归属机构代码	APPLYCHECKCOMCODE	Y
//        prpLAcciCheckDto.setApplyCheckComCode(investigateVO.getInitiateDepartment());
//        //        调查申请人	APPLYCHECKPERSON	Y
//        prpLAcciCheckDto.setApplyCheckPerson(investigateVO.getInitiatorUmName());
//        //        申请人代码	APPLYCHECKPERSONCODE	Y
//        prpLAcciCheckDto.setApplyCheckPersonCode(investigateVO.getInitiatorUm());
////        调查人归属机构代码	CHECKERCOMCODE	Y
//
////        是否代查勘	ISSUPPLYCHECK	CY		无明确字段（默认为否）
//        prpLAcciCheckDto.setIsSupplyCheck("1");
////        是否异地调查 	ISREMOTESURVEY	CY
//        prpLAcciCheckDto.setIsRemoteSurvey(taskByInvestigateId.getIsOffsiteTask());
////        异地理赔调查代码	OUTSIDECODE	Y		无明确字段（枚举中匹配）
////        更新时间	UPDATETIME	Y
//        prpLAcciCheckDto.setUpdateTime(new Date());
//        return prpLAcciCheckDto;
//    }
//
//}
