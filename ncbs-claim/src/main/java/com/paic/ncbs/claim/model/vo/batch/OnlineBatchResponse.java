package com.paic.ncbs.claim.model.vo.batch;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
  *
  * @Description 退运险线上批量结案返回封装
  * <AUTHOR>
  * @Date 2023/7/13 9:26
  **/
@Data
@ApiModel(description = "退运险线上批量结案返回封装")
public class OnlineBatchResponse {

    @ApiModelProperty(value = "核心批次号")
    private String batchNo ;
    
    @ApiModelProperty(value = "报案失败返回失败原因")
    private String msg ;

    @ApiModelProperty(value = "reportInfos ：reportNo and policyNo")
    private List<Map<String, String>> reportInfos ;

}
