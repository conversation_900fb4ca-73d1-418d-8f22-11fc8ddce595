package com.paic.ncbs.claim.dao.mapper.duty;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.settle.HistoryPayInfoDTO;
import com.paic.ncbs.claim.model.vo.settle.MaxPayParam;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.List;

@MapperScan
public interface DutyDetailPayMapper extends BaseDao<DutyDetailPayDTO> {

    DutyDetailPayDTO getById(@Param("id") String dutyDetailPayId);

    void insertDutyDetailPayList(@Param("list") List<DutyDetailPayDTO> list);

    void updateDutyDetailPayList(DutyDetailPayDTO dutyDetailPayArr);

    BigDecimal getDutyDetailHistoryPay(MaxPayParam maxPayParam);

    BigDecimal getDutyDetailBaseAmount(MaxPayParam maxPayParam);

    void deleteByBatchId(@Param("idAhcsBatch") String idAhcsBatch, @Param("claimType") String claimType);

    void updateEffective(DutyDetailPayDTO dutyDetailPayDTO);

    List<String> getInsuredStatus(@Param("dutyCode") String dutyCode);

    List<DutyDetailPayDTO> getDetailGroupAmount(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    String getDutyDetailTypeCode(@Param("planCode") String planCode, @Param("dutyCode") String dutyCode, @Param("dutyDetailCode") String dutyDetailCode);

    List<DutyDetailPayDTO> getHistoryCaseDetails(MaxPayParam maxPayParam);

    List<HistoryPayInfoDTO> getDutyDetailHistoryPayInfo(MaxPayParam maxPayParam);

    BigDecimal getDutyHistoryPayAmount(MaxPayParam maxPayParam);

    BigDecimal getDutyHistoryPrePayAmount(MaxPayParam maxPayParam);

    BigDecimal getDutyDetailHistoryPayAmount(MaxPayParam maxPayParam);

    /**
     * 责任明细历史理赔金额
     */
    BigDecimal getPrePayDutyDetailHistoryPayAmount(MaxPayParam maxPayParam);

    List<DutyDetailPayDTO> getDutyDetailListByIdDuty(@Param("idAhcsDutyPay") String idAhcsDutyPay);

    BigDecimal selectTotalDeductible(@Param("policyNo") String policyNo, @Param("insuredCode") String insuredCode);

    List<DutyDetailPayDTO> getSettleResultByCaseNo(@Param("caseNo") String caseNo,
                                                   @Param("caseTimes") Integer caseTimes);

    BigDecimal getSumDetailCode(@Param("policyNo") String policyNo,
                                @Param("dutyCode") String dutyCode,
                                @Param("dutyDetailCode") String dutyDetailCode);

    Integer getIndmenityInfo(@Param("list")List<String> list,@Param("dutyCode")String dutyCode);

    /**
     * 更新责任明细金额
     * @param detailPayDTO
     */
    void updateDetailAmountByDetailInfo(DutyDetailPayDTO detailPayDTO);

    BigDecimal getDutyDetailCodeAmount(DutyDetailPayDTO detailPayDTO);
}
