package com.paic.ncbs.claim.dao.mapper.duty;

import com.paic.ncbs.claim.model.dto.checkloss.BigDiseaseDetailDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;


@MapperScan
public interface BigDiseaseDetailMapper {


	public void saveBigDiseaseDetail(BigDiseaseDetailDTO bigDiseaseDetailDTO);


	public void removeBigDiseaseDetail(@Param("bigDiseaseId") String bigDiseaseId);


	public List<String> getBigDiseaseDetailList(@Param("bigDiseaseId") String bigDiseaseId);


	public List<BigDiseaseDetailDTO> getBigDiseaseDetailDTOList(@Param("bigDiseaseId") String bigDiseaseId);


	public void addBigDiseaseDetailList(@Param("bigDiseaseDetailList") List<BigDiseaseDetailDTO> bigDiseaseDetailList,
										@Param("userId") String userId , @Param("idBigDisease") String idBigDisease);

}
