package com.paic.ncbs.claim.dao.entity.clms;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 物质损失信息表(ClmsSubstanceLossInfo)实体类
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:50
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ClmsSubstanceLossInfo implements Serializable {
    private static final long serialVersionUID = -34407180066548474L;
    /**
     * 主键
     */
    private String id;
    /**
     * 报案号
     */
    private String reportNo;
    /**
     * 赔付次数
     */
    private Integer caseTimes;
    /**
     * 序号
     */
    private Integer serialNo;
    /**
     * 损失项目名称
     */
    private String lossName;
    /**
     * 损失金额
     */
    private BigDecimal lossAmount;
    /**
     * 免赔额
     */
    private BigDecimal deductionAmount;
    /**
     * 理算金额
     */
    private BigDecimal payableAmount;
    /**
     * 损失原因
     */
    private String lossReason;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人员
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDate;
    /**
     * 修改人员
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDate;
    /**
     * 保险标的
     */
    private String insuranceTarget;
    /**
     * 受损财产类型
     */
    private String damagedPropertyType;
    /**
     * 标的描述
     */
    private String targetDescribe;
    /**
     * 报损金额
     */
    private BigDecimal reportAmount;
    /**
     * 定损金额
     */
    private BigDecimal estimateAmount;
    /**
     * 残值
     */
    private BigDecimal salvageValue;
    /**
     * 投保比例
     */
    private BigDecimal insuranceRate;

}

