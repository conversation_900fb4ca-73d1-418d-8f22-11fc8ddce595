package com.paic.ncbs.claim.dao.entity.ahcs;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.util.Date;

public class AhcsDutyAttributeDetailEntity extends EntityDTO {

    private static final long serialVersionUID = 370216607185311173L;

    private String idAhcsDutyAttributeDetail;

    private String createdBy;

    private Date createdDate;

    private String updatedBy;

    private Date updatedDate;

    private String idAhcsDutyAttribute;

    private String attributeDetailCode;

    private String attributeDetailValue;

    private String attributeRowNo;

    private String attributeColumnNo;

    public String getIdAhcsDutyAttributeDetail() {
        return idAhcsDutyAttributeDetail;
    }

    public void setIdAhcsDutyAttributeDetail(String idAhcsDutyAttributeDetail) {
        this.idAhcsDutyAttributeDetail = idAhcsDutyAttributeDetail == null ? null : idAhcsDutyAttributeDetail.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(Date updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getIdAhcsDutyAttribute() {
        return idAhcsDutyAttribute;
    }

    public void setIdAhcsDutyAttribute(String idAhcsDutyAttribute) {
        this.idAhcsDutyAttribute = idAhcsDutyAttribute == null ? null : idAhcsDutyAttribute.trim();
    }

    public String getAttributeDetailCode() {
        return attributeDetailCode;
    }

    public void setAttributeDetailCode(String attributeDetailCode) {
        this.attributeDetailCode = attributeDetailCode == null ? null : attributeDetailCode.trim();
    }

    public String getAttributeDetailValue() {
        return attributeDetailValue;
    }

    public void setAttributeDetailValue(String attributeDetailValue) {
        this.attributeDetailValue = attributeDetailValue == null ? null : attributeDetailValue.trim();
    }

    public String getAttributeRowNo() {
        return attributeRowNo;
    }

    public void setAttributeRowNo(String attributeRowNo) {
        this.attributeRowNo = attributeRowNo == null ? null : attributeRowNo.trim();
    }

    public String getAttributeColumnNo() {
        return attributeColumnNo;
    }

    public void setAttributeColumnNo(String attributeColumnNo) {
        this.attributeColumnNo = attributeColumnNo == null ? null : attributeColumnNo.trim();
    }
}