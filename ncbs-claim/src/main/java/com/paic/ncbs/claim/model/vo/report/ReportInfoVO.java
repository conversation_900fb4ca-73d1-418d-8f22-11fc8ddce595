package com.paic.ncbs.claim.model.vo.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

public class ReportInfoVO {

	private String reportNo;
	private String reportMode;
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date reportDate;
	private String reporterName;
	private String reporterCallNo;
	private String reporterRegisterTel;
	private String reportRegisterUm;
	private String acceptDepartmentCode;
	private String remark;
	private String migrateFrom;
	private String reportType;
	private String createdBy;

	public String getReportNo() {
		return reportNo;
	}

	public void setReportNo(String reportNo) {
		this.reportNo = reportNo;
	}

	public String getReportMode() {
		return reportMode;
	}

	public void setReportMode(String reportMode) {
		this.reportMode = reportMode;
	}

	public Date getReportDate() {
		return reportDate;
	}

	public void setReportDate(Date reportDate) {
		this.reportDate = reportDate;
	}

	public String getReporterName() {
		return reporterName;
	}

	public void setReporterName(String reporterName) {
		this.reporterName = reporterName;
	}

	public String getReporterCallNo() {
		return reporterCallNo;
	}

	public void setReporterCallNo(String reporterCallNo) {
		this.reporterCallNo = reporterCallNo;
	}

	public String getReporterRegisterTel() {
		return reporterRegisterTel;
	}

	public void setReporterRegisterTel(String reporterRegisterTel) {
		this.reporterRegisterTel = reporterRegisterTel;
	}

	public String getReportRegisterUm() {
		return reportRegisterUm;
	}

	public void setReportRegisterUm(String reportRegisterUm) {
		this.reportRegisterUm = reportRegisterUm;
	}

	public String getAcceptDepartmentCode() {
		return acceptDepartmentCode;
	}

	public void setAcceptDepartmentCode(String acceptDepartmentCode) {
		this.acceptDepartmentCode = acceptDepartmentCode;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getMigrateFrom() {
		return migrateFrom;
	}

	public void setMigrateFrom(String migrateFrom) {
		this.migrateFrom = migrateFrom;
	}

	public String getReportType() {
		return reportType;
	}

	public void setReportType(String reportType) {
		this.reportType = reportType;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}
}
