package com.paic.ncbs.claim.model.vo.ahcs;

import com.paic.ncbs.claim.dao.entity.prepay.ClmsPolicyPrepayDutyDetailEntity;
import com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO;
import com.paic.ncbs.claim.model.dto.prepayinfo.ClmsPolicyPrepayDutyDetailDTO;
import lombok.Data;

import java.util.List;

@Data
public class PayPrepayDetailVo {
    //保单号
    private String policyNo;
    //理赔费用明细
    private List<FeeInfoDTO> feeInfoDTOList;
    //预赔责任明细
    private List<ClmsPolicyPrepayDutyDetailEntity> prepayDutyDetailDTOList;

}
