package com.paic.ncbs.claim.dao.mapper.endcase;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity;
import com.paic.ncbs.claim.model.dto.endcase.CaseBaseDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CaseBaseMapper extends BaseDao<CaseBaseEntity> {

    List<CaseBaseEntity> getCaseBaseInfoByReportNo(@Param("reportNo") String reportNo);

    List<CaseBaseEntity> getCaseBaseInfoByReportNoAndCasetimes(@Param("reportNo") String reportNo, @Param("casetimes") String casetimes);

    List<CaseBaseEntity> getCaseBaseInfoByReportNoAndPolicytNo(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo);

    public void batchModifyCaseBaseDTO(WholeCaseBaseDTO wholeCaseBaseDTO);

    public List<CaseBaseDTO> getCaseBaseList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    void insertList(@Param("list") List<CaseBaseEntity> list);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);

    CaseBaseEntity getCaseBaseInfo(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo, @Param("caseTimes") Integer caseTimes);

	void updateRiskGroup(@Param("idClmCaseBase")String idClmCaseBase, @Param("riskGroupNo")String riskGroupNo, @Param("riskGroupName")String riskGroupName);

    void updateEsUpdatedDate(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);
}