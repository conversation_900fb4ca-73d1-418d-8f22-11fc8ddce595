package com.paic.ncbs.claim.model.vo.report;

import java.util.Date;

import com.paic.ncbs.claim.exception.GlobalBusinessException;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

@Data
public class ReportQueryVO {
	
	// 被保险人证件类型
    private String certificateType;
    // 被保险人证件号码
    private String certificateNo;
    // 保单号
    private String policyNo;    
    // 事故日期
    private Date accidentDate;    

    public ReportQueryVO buildGetAccidentDateReportStatQueryParam() {  	
    	 if (StrUtil.isEmpty(this.getCertificateNo()) || StrUtil.isEmpty(this.getPolicyNo()) || StrUtil.isEmpty(this.getCertificateType()) || this.getAccidentDate() == null) {
             throw new GlobalBusinessException("保单号、被保人证件类型、被保人证件号码、事故日期都必填");
         } 
    	return this;
    } 

}
