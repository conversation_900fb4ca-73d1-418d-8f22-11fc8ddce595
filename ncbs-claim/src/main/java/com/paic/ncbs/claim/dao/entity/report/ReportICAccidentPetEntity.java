package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.math.BigDecimal;
import java.util.Date;

public class ReportICAccidentPetEntity extends EntityDTO {

    private String idAhcsReportAccidentPet;
    private String reportNo;
    private String id;
    private Date therapyDate;
    private String petName;
    private String petBreed;
    private String petHairColour;
    private double petWeight;
    private String isSterilize;
    private String isImmune;
    private String isPetRepellent;
    private String isDeath;

    private Date petBirthday;
    private String petSex;
    private String petNo;
    private String mainSuitRecord;
    private String clinicalExamination;
    private String InspectionAnalysis;
    private Date hospitalizedDate;
    private String cagePosition;
    private String diseaseClassification;
    private String diseaseDiagnosis;
    private String treatment;
    private String laboratoryNos;
    private String diagnosisDetail;
    private String inputBy;
    private String petImageId;
    private String manualReview;
    private String illnessDesc;
    private String firstDiagnosisResult;

    private String hospitalNo;
    private String petInjuredType;
    private String accidentPet;
    private BigDecimal costEstimate;
    private String treatCondition;
    private String accidentCity;
    private String accidentProvince;
    private String hospitalName;
    private String hospitalPlace;

    public String getIdAhcsReportAccidentPet() {
        return idAhcsReportAccidentPet;
    }

    public void setIdAhcsReportAccidentPet(String idAhcsReportAccidentPet) {
        this.idAhcsReportAccidentPet = idAhcsReportAccidentPet;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getTherapyDate() {
        return therapyDate;
    }

    public void setTherapyDate(Date therapyDate) {
        this.therapyDate = therapyDate;
    }

    public String getPetName() {
        return petName;
    }

    public void setPetName(String petName) {
        this.petName = petName;
    }

    public String getPetBreed() {
        return petBreed;
    }

    public void setPetBreed(String petBreed) {
        this.petBreed = petBreed;
    }

    public String getPetHairColour() {
        return petHairColour;
    }

    public void setPetHairColour(String petHairColour) {
        this.petHairColour = petHairColour;
    }

    public double getPetWeight() {
        return petWeight;
    }

    public void setPetWeight(double petWeight) {
        this.petWeight = petWeight;
    }

    public String getIsSterilize() {
        return isSterilize;
    }

    public void setIsSterilize(String isSterilize) {
        this.isSterilize = isSterilize;
    }

    public String getIsImmune() {
        return isImmune;
    }

    public void setIsImmune(String isImmune) {
        this.isImmune = isImmune;
    }

    public String getIsPetRepellent() {
        return isPetRepellent;
    }

    public void setIsPetRepellent(String isPetRepellent) {
        this.isPetRepellent = isPetRepellent;
    }

    public String getIsDeath() {
        return isDeath;
    }

    public void setIsDeath(String isDeath) {
        this.isDeath = isDeath;
    }

    public Date getPetBirthday() {
        return petBirthday;
    }

    public void setPetBirthday(Date petBirthday) {
        this.petBirthday = petBirthday;
    }

    public String getPetSex() {
        return petSex;
    }

    public void setPetSex(String petSex) {
        this.petSex = petSex;
    }

    public String getPetNo() {
        return petNo;
    }

    public void setPetNo(String petNo) {
        this.petNo = petNo;
    }

    public String getMainSuitRecord() {
        return mainSuitRecord;
    }

    public void setMainSuitRecord(String mainSuitRecord) {
        this.mainSuitRecord = mainSuitRecord;
    }

    public String getClinicalExamination() {
        return clinicalExamination;
    }

    public void setClinicalExamination(String clinicalExamination) {
        this.clinicalExamination = clinicalExamination;
    }

    public String getInspectionAnalysis() {
        return InspectionAnalysis;
    }

    public void setInspectionAnalysis(String inspectionAnalysis) {
        InspectionAnalysis = inspectionAnalysis;
    }

    public Date getHospitalizedDate() {
        return hospitalizedDate;
    }

    public void setHospitalizedDate(Date hospitalizedDate) {
        this.hospitalizedDate = hospitalizedDate;
    }

    public String getCagePosition() {
        return cagePosition;
    }

    public void setCagePosition(String cagePosition) {
        this.cagePosition = cagePosition;
    }

    public String getDiseaseClassification() {
        return diseaseClassification;
    }

    public void setDiseaseClassification(String diseaseClassification) {
        this.diseaseClassification = diseaseClassification;
    }

    public String getDiseaseDiagnosis() {
        return diseaseDiagnosis;
    }

    public void setDiseaseDiagnosis(String diseaseDiagnosis) {
        this.diseaseDiagnosis = diseaseDiagnosis;
    }

    public String getTreatment() {
        return treatment;
    }

    public void setTreatment(String treatment) {
        this.treatment = treatment;
    }

    public String getLaboratoryNos() {
        return laboratoryNos;
    }

    public void setLaboratoryNos(String laboratoryNos) {
        this.laboratoryNos = laboratoryNos;
    }

    public String getDiagnosisDetail() {
        return diagnosisDetail;
    }

    public void setDiagnosisDetail(String diagnosisDetail) {
        this.diagnosisDetail = diagnosisDetail;
    }

    public String getInputBy() {
        return inputBy;
    }

    public void setInputBy(String inputBy) {
        this.inputBy = inputBy;
    }

    public String getPetImageId() {
        return petImageId;
    }

    public void setPetImageId(String petImageId) {
        this.petImageId = petImageId;
    }

    public String getManualReview() {
        return manualReview;
    }

    public void setManualReview(String manualReview) {
        this.manualReview = manualReview;
    }

    public String getHospitalNo() {
        return hospitalNo;
    }

    public void setHospitalNo(String hospitalNo) {
        this.hospitalNo = hospitalNo;
    }

    public String getPetInjuredType() {
        return petInjuredType;
    }

    public void setPetInjuredType(String petInjuredType) {
        this.petInjuredType = petInjuredType;
    }

    public String getAccidentPet() {
        return accidentPet;
    }

    public void setAccidentPet(String accidentPet) {
        this.accidentPet = accidentPet;
    }

    public BigDecimal getCostEstimate() {
        return costEstimate;
    }

    public void setCostEstimate(BigDecimal costEstimate) {
        this.costEstimate = costEstimate;
    }

    public String getTreatCondition() {
        return treatCondition;
    }

    public void setTreatCondition(String treatCondition) {
        this.treatCondition = treatCondition;
    }

    public String getAccidentCity() {
        return accidentCity;
    }

    public void setAccidentCity(String accidentCity) {
        this.accidentCity = accidentCity;
    }

    public String getAccidentProvince() {
        return accidentProvince;
    }

    public void setAccidentProvince(String accidentProvince) {
        this.accidentProvince = accidentProvince;
    }

    public String getHospitalName() {
        return hospitalName;
    }

    public void setHospitalName(String hospitalName) {
        this.hospitalName = hospitalName;
    }

    public String getHospitalPlace() {
        return hospitalPlace;
    }

    public void setHospitalPlace(String hospitalPlace) {
        this.hospitalPlace = hospitalPlace;
    }

    public String getIllnessDesc() {
        return illnessDesc;
    }

    public void setIllnessDesc(String illnessDesc) {
        this.illnessDesc = illnessDesc;
    }

    public String getFirstDiagnosisResult() {
        return firstDiagnosisResult;
    }

    public void setFirstDiagnosisResult(String firstDiagnosisResult) {
        this.firstDiagnosisResult = firstDiagnosisResult;
    }
}
