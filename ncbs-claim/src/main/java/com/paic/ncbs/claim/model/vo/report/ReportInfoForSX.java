package com.paic.ncbs.claim.model.vo.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class ReportInfoForSX {

	private String reportNo;

	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8" )
	private Date reportDate;

	private String reporterName;

	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8" )
	private Date accidentDate;

	// 出险地方
	private String accidentPlace;

	// 保单号-依次枚举
	private String policys;

	private String reportStatus;

	private String payStatus;

	private String payAmount;

	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8" )
	private Date payDate;

}
