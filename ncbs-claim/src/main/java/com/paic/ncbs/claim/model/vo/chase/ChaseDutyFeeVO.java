package com.paic.ncbs.claim.model.vo.chase;



import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.math.BigDecimal;
import java.util.List;

@JsonIgnoreProperties({"hisArbitrateFee","hisChaseArbitrateFee", "chaseArbitrateFee", "hisLawsuitFee", "hisChaseLawsuitFee", "chaseLawsuitFee",
	"hisLawyerFee","hisChaseLawyerFee", "chaseLawyerFee" ,"hisCheckFee","hisChaseCheckFee","chaseCheckFee","hisExecuteFee","hisChaseExecuteFee",
	"chaseExecuteFee","hisEvaluationFee","hisChaseEvaluationFee","chaseEvaluationFee","hisDecreaseFee","hisChaseDecreaseFee","chaseDecreaseFee"})
public class ChaseDutyFeeVO extends DutyBaseVO{


	private List<ChaseFeeVO> chaseFees;
	

	private BigDecimal hisArbitrateFee;


	private BigDecimal hisChaseArbitrateFee;


	private BigDecimal chaseArbitrateFee;
	
	private BigDecimal hisLawsuitFee;
	
	private BigDecimal hisChaseLawsuitFee;
	
	private BigDecimal chaseLawsuitFee;
	
	private BigDecimal hisLawyerFee;
	
	private BigDecimal hisChaseLawyerFee;
	
	private BigDecimal chaseLawyerFee;
	
	private BigDecimal hisCheckFee;
	
	private BigDecimal hisChaseCheckFee;
	
	private BigDecimal chaseCheckFee;
	
	private BigDecimal hisExecuteFee;
	
	private BigDecimal hisChaseExecuteFee;
	
	private BigDecimal chaseExecuteFee;
	
	private BigDecimal hisEvaluationFee;
	
	private BigDecimal hisChaseEvaluationFee;
	
	private BigDecimal chaseEvaluationFee;

	private BigDecimal hisDecreaseFee;

	private BigDecimal hisChaseDecreaseFee;

	private BigDecimal chaseDecreaseFee;

	public List<ChaseFeeVO> getChaseFees() {
		return chaseFees;
	}

	public void setChaseFees(List<ChaseFeeVO> chaseFees) {
		this.chaseFees = chaseFees;
	}

	public BigDecimal getHisArbitrateFee() {
		return hisArbitrateFee;
	}

	public void setHisArbitrateFee(BigDecimal hisArbitrateFee) {
		this.hisArbitrateFee = hisArbitrateFee;
	}

	public BigDecimal getHisChaseArbitrateFee() {
		return hisChaseArbitrateFee;
	}

	public void setHisChaseArbitrateFee(BigDecimal hisChaseArbitrateFee) {
		this.hisChaseArbitrateFee = hisChaseArbitrateFee;
	}

	public BigDecimal getChaseArbitrateFee() {
		return chaseArbitrateFee;
	}

	public void setChaseArbitrateFee(BigDecimal chaseArbitrateFee) {
		this.chaseArbitrateFee = chaseArbitrateFee;
	}

	public BigDecimal getHisLawsuitFee() {
		return hisLawsuitFee;
	}

	public void setHisLawsuitFee(BigDecimal hisLawsuitFee) {
		this.hisLawsuitFee = hisLawsuitFee;
	}

	public BigDecimal getHisChaseLawsuitFee() {
		return hisChaseLawsuitFee;
	}

	public void setHisChaseLawsuitFee(BigDecimal hisChaseLawsuitFee) {
		this.hisChaseLawsuitFee = hisChaseLawsuitFee;
	}

	public BigDecimal getChaseLawsuitFee() {
		return chaseLawsuitFee;
	}

	public void setChaseLawsuitFee(BigDecimal chaseLawsuitFee) {
		this.chaseLawsuitFee = chaseLawsuitFee;
	}

	public BigDecimal getHisLawyerFee() {
		return hisLawyerFee;
	}

	public void setHisLawyerFee(BigDecimal hisLawyerFee) {
		this.hisLawyerFee = hisLawyerFee;
	}

	public BigDecimal getHisChaseLawyerFee() {
		return hisChaseLawyerFee;
	}

	public void setHisChaseLawyerFee(BigDecimal hisChaseLawyerFee) {
		this.hisChaseLawyerFee = hisChaseLawyerFee;
	}

	public BigDecimal getChaseLawyerFee() {
		return chaseLawyerFee;
	}

	public void setChaseLawyerFee(BigDecimal chaseLawyerFee) {
		this.chaseLawyerFee = chaseLawyerFee;
	}

	public BigDecimal getHisCheckFee() {
		return hisCheckFee;
	}

	public void setHisCheckFee(BigDecimal hisCheckFee) {
		this.hisCheckFee = hisCheckFee;
	}

	public BigDecimal getHisChaseCheckFee() {
		return hisChaseCheckFee;
	}

	public void setHisChaseCheckFee(BigDecimal hisChaseCheckFee) {
		this.hisChaseCheckFee = hisChaseCheckFee;
	}

	public BigDecimal getChaseCheckFee() {
		return chaseCheckFee;
	}

	public void setChaseCheckFee(BigDecimal chaseCheckFee) {
		this.chaseCheckFee = chaseCheckFee;
	}

	public BigDecimal getHisExecuteFee() {
		return hisExecuteFee;
	}

	public void setHisExecuteFee(BigDecimal hisExecuteFee) {
		this.hisExecuteFee = hisExecuteFee;
	}

	public BigDecimal getHisChaseExecuteFee() {
		return hisChaseExecuteFee;
	}

	public void setHisChaseExecuteFee(BigDecimal hisChaseExecuteFee) {
		this.hisChaseExecuteFee = hisChaseExecuteFee;
	}

	public BigDecimal getChaseExecuteFee() {
		return chaseExecuteFee;
	}

	public void setChaseExecuteFee(BigDecimal chaseExecuteFee) {
		this.chaseExecuteFee = chaseExecuteFee;
	}

	public BigDecimal getHisEvaluationFee() {
		return hisEvaluationFee;
	}

	public void setHisEvaluationFee(BigDecimal hisEvaluationFee) {
		this.hisEvaluationFee = hisEvaluationFee;
	}

	public BigDecimal getHisChaseEvaluationFee() {
		return hisChaseEvaluationFee;
	}

	public void setHisChaseEvaluationFee(BigDecimal hisChaseEvaluationFee) {
		this.hisChaseEvaluationFee = hisChaseEvaluationFee;
	}

	public BigDecimal getChaseEvaluationFee() {
		return chaseEvaluationFee;
	}

	public void setChaseEvaluationFee(BigDecimal chaseEvaluationFee) {
		this.chaseEvaluationFee = chaseEvaluationFee;
	}

	public BigDecimal getHisDecreaseFee() {
		return hisDecreaseFee;
	}

	public void setHisDecreaseFee(BigDecimal hisDecreaseFee) {
		this.hisDecreaseFee = hisDecreaseFee;
	}

	public BigDecimal getHisChaseDecreaseFee() {
		return hisChaseDecreaseFee;
	}

	public void setHisChaseDecreaseFee(BigDecimal hisChaseDecreaseFee) {
		this.hisChaseDecreaseFee = hisChaseDecreaseFee;
	}

	public BigDecimal getChaseDecreaseFee() {
		return chaseDecreaseFee;
	}

	public void setChaseDecreaseFee(BigDecimal chaseDecreaseFee) {
		this.chaseDecreaseFee = chaseDecreaseFee;
	}
}
