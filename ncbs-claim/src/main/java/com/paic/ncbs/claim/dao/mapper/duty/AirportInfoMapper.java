package com.paic.ncbs.claim.dao.mapper.duty;

import java.util.List;

import com.paic.ncbs.claim.model.dto.duty.AirportInfoDTO;
import com.paic.ncbs.claim.model.vo.duty.AirportInfoVO;
import com.paic.ncbs.claim.model.vo.duty.CityVO;
import com.paic.ncbs.claim.model.vo.duty.ProvinceVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

/**
 * @description: 机场维护DAO
 */
@MapperScan
public interface AirportInfoMapper {

    /**
     * @Description: 方法描述
     * @param list
     * @return
     */
    List<AirportInfoDTO> getAirportInfoListByAirportCode(@Param("list") List<String> list);

    /**
     * @Description: 根据机场三字码跟机场类型查询机场信息
     * @param airportCode
     * @return
     */
    AirportInfoDTO getAirportInfoByAirportCode(@Param("airportCode") String airportCode, @Param("airportType") String airportType);

    /**
     * @Description: 根据省市机场类型查询机场信息
     * @param provinceCode
     * @param cityCode
     * @return
     * @param airportType
     */
    List<AirportInfoDTO> getAirportInfoListByCode(@Param("provinceCode") String provinceCode, @Param("cityCode") String cityCode, @Param("airportType") String airportType);

    /**
     * @Description: 根据机场类型查询所有的机场数据
     * @param airportType
     * @return
     */
    List<AirportInfoDTO> getAirportInfoByType(@Param("airportType") String airportType);

    /**
     * @Description: 根据机场名称模糊查询机场信息
     * @param airportName
     * @return List<AirportInfoDTO>
     */
    List<AirportInfoDTO> getAirportInfoByAirportName(@Param("airportName") String airportName, @Param("airportType") String airportType);

    /**
     * @Description: 查询所有机场信息
     * @return
     */
    List<AirportInfoDTO> getAirportInfoAll();

    /**
     * @Description: 获取省份
     * @param airportType
     * @return List<ProvinceVO>
     */
    List<ProvinceVO> getProvinceVO(@Param("airportType") String airportType);

    /**
     * @Description: 获取城市
     * @param provinceCode
     * @return List<CityVO>
     */
    List<CityVO> getCity(@Param("provinceCode") String provinceCode);

    /**
     * @Description: 新增机场信息
     * @param airportInfoDTO
     * @return void
     * 
     */
    void saveAirportInfo(AirportInfoDTO airportInfoDTO);

    /**
     * @Description: 根据条件查询机场信息
     * 
     */
    List<AirportInfoVO> getAirportInfoListByParma(AirportInfoVO airportInfoVO);

    /**
     * @Description: 删除机场信息
     * @param idAhcsAirportInfo
     * @return void
     *
     */
    void deleteAirportInfo(@Param("idAhcsAirportInfo") String idAhcsAirportInfo);

    /**
     * @Description: 根据ID 查询机场信息
     * @param idAhcsAirportInfo
     * @return
     * @return AirportInfoDTO
     *
     */
    AirportInfoDTO getAirportInfoById(@Param("idAhcsAirportInfo") String idAhcsAirportInfo);

    /**
     * @Description: 修改机场信息
     * @param airportInfoDTO
     * @return void
     *
     */
    void updateAirportInfoById(AirportInfoDTO airportInfoDTO);

    /**
     * @Description: 新增,修改时校验机场编码是否存在(修改排除当前数据)
     *
     * @param airportCode
     * @param idAhcsAirportInfo
     * @return
     * @return AirportInfoDTO
     *
     */
    AirportInfoDTO checkAirportInfoExist(@Param("airportCode") String airportCode, @Param("idAhcsAirportInfo") String idAhcsAirportInfo);

    /**
     * @Description: 通过机场名字查询机场三字码
     * @param airporName
     * @return 
     * @return String 
     */
    List<String> getAirporCodeByName(@Param("airporName") String airporName);

    /**
     * 根据三字码查询机场类型
     * @param airportCode
     * @return string 机场类型
     */
    String getAirportTypeByCode(@Param("airportCode")String airportCode);
    
    /**
     * 根据三字码查询所在地区
     * @param airportCode
     * @return 
     */
    AirportInfoDTO getAirportAddrByCode(@Param("airportCode")String airportCode);
}
