package com.paic.ncbs.claim.dao.entity.mq;


import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @date 2023-06-12 10:30
 */
@Data
public class MqMessageRecordEntity {
    private Integer id;

    private String systemCode;

    private String businessType;

    private String topic;

    private String messageId;

    private String messageKey;

    private String messageBody;

    private Integer sendStatus;

    private Integer resendCount;

    private String createdBy;

    private Date createdDate;

    private String updatedBy;

    private Date updatedDate;

    private Integer isDeleted;
}
