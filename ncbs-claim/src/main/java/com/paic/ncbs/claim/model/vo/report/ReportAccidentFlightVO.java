package com.paic.ncbs.claim.model.vo.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(description = "航班延误VO")
public class ReportAccidentFlightVO {

    @ApiModelProperty(value = "报案号")
    private String reportNo;

    @ApiModelProperty(value = "0-国内;1-国外")
    private String overseasOccur;

    @ApiModelProperty(value = "出发地")
    private String departurePlace;

    @ApiModelProperty(value = "目的地")
    private String destination;

    @ApiModelProperty(value = "出发省份")
    private String departureProvince;

    @ApiModelProperty(value = "出发城市")
    private String departureCity;

    @ApiModelProperty(value = "出发机场")
    private String departureAirport;

    @ApiModelProperty(value = "出发区域")
    private String departureArea;

    @ApiModelProperty(value = "出发国家")
    private String departureNation;

    @ApiModelProperty(value = "到达省份")
    private String destinationProvince;

    @ApiModelProperty(value = "到达城市")
    private String destinationCity;

    @ApiModelProperty(value = "到达机场")
    private String destinationAirport;

    @ApiModelProperty(value = "到达区域")
    private String destinationArea;

    @ApiModelProperty(value = "到达国家")
    private String destinationNation;

    @ApiModelProperty(value = "航班是否取消")
    private String isFlightCancellation;

    @ApiModelProperty(value = "航班迫降(是：Y 否：N)")
    private String isFlightLand;

    @ApiModelProperty(value = "迫降机场")
    private String landingAirport;

    @ApiModelProperty(value = "代替航班")
    private String replaceFlight;

    @ApiModelProperty(value = "起飞时间")
    private Date takeoffDate;

    @ApiModelProperty(value = "电子客票号")
    private String eleTicketNo;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "行程取消(Y:是，N:否)")
    private String isJourneyCancel;

    @ApiModelProperty(value = "延误时长（分钟）")
    private int delayTime;

    @ApiModelProperty(value = "是否航班延误 Y 是 , N/空 否")
    private String isFlightDelay;

    @ApiModelProperty(value = "迫降时间")
    private Date landingDate;

    @ApiModelProperty(value = "用户延误时长（分钟）")
    private int delayTimeUser;

    @ApiModelProperty(value = "事故原因（1.天气原因、2.航空管制、3.机械故障、4.机场罢工、5.其他）")
    private String accidentReason;


    public String getReportNo() {
        return reportNo;
    }


    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String getOverseasOccur() {
        return overseasOccur;
    }

    public void setOverseasOccur(String overseasOccur) {
        this.overseasOccur = overseasOccur == null ? null : overseasOccur.trim();
    }

    public String getDeparturePlace() {
        return departurePlace;
    }

    public void setDeparturePlace(String departurePlace) {
        this.departurePlace = departurePlace == null ? null : departurePlace.trim();
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination == null ? null : destination.trim();
    }

    public String getDepartureProvince() {
        return departureProvince;
    }

    public void setDepartureProvince(String departureProvince) {
        this.departureProvince = departureProvince == null ? null : departureProvince.trim();
    }

    public String getDepartureCity() {
        return departureCity;
    }

    public void setDepartureCity(String departureCity) {
        this.departureCity = departureCity == null ? null : departureCity.trim();
    }

    public String getDepartureAirport() {
        return departureAirport;
    }

    public void setDepartureAirport(String departureAirport) {
        this.departureAirport = departureAirport == null ? null : departureAirport.trim();
    }

    public String getDepartureArea() {
        return departureArea;
    }

    public void setDepartureArea(String departureArea) {
        this.departureArea = departureArea == null ? null : departureArea.trim();
    }

    public String getDepartureNation() {
        return departureNation;
    }

    public void setDepartureNation(String departureNation) {
        this.departureNation = departureNation == null ? null : departureNation.trim();
    }

    public String getDestinationProvince() {
        return destinationProvince;
    }

    public void setDestinationProvince(String destinationProvince) {
        this.destinationProvince = destinationProvince == null ? null : destinationProvince.trim();
    }

    public String getDestinationCity() {
        return destinationCity;
    }

    public void setDestinationCity(String destinationCity) {
        this.destinationCity = destinationCity == null ? null : destinationCity.trim();
    }

    public String getDestinationAirport() {
        return destinationAirport;
    }

    public void setDestinationAirport(String destinationAirport) {
        this.destinationAirport = destinationAirport == null ? null : destinationAirport.trim();
    }

    public String getDestinationArea() {
        return destinationArea;
    }

    public void setDestinationArea(String destinationArea) {
        this.destinationArea = destinationArea == null ? null : destinationArea.trim();
    }

    public String getDestinationNation() {
        return destinationNation;
    }

    public void setDestinationNation(String destinationNation) {
        this.destinationNation = destinationNation == null ? null : destinationNation.trim();
    }

    public String getIsFlightCancellation() {
        return isFlightCancellation;
    }

    public void setIsFlightCancellation(String isFlightCancellation) {
        this.isFlightCancellation = isFlightCancellation == null ? null : isFlightCancellation.trim();
    }

    public String getIsFlightLand() {
        return isFlightLand;
    }

    public void setIsFlightLand(String isFlightLand) {
        this.isFlightLand = isFlightLand == null ? null : isFlightLand.trim();
    }

    public String getLandingAirport() {
        return landingAirport;
    }

    public void setLandingAirport(String landingAirport) {
        this.landingAirport = landingAirport == null ? null : landingAirport.trim();
    }

    public String getReplaceFlight() {
        return replaceFlight;
    }

    public void setReplaceFlight(String replaceFlight) {
        this.replaceFlight = replaceFlight == null ? null : replaceFlight.trim();
    }

    public Date getTakeoffDate() {
        return takeoffDate;
    }

    public void setTakeoffDate(Date takeoffDate) {
        this.takeoffDate = takeoffDate;
    }

    public String getEleTicketNo() {
        return eleTicketNo;
    }

    public void setEleTicketNo(String eleTicketNo) {
        this.eleTicketNo = eleTicketNo == null ? null : eleTicketNo.trim();
    }

    public String getFlightNo() {
        return flightNo;
    }

    public void setFlightNo(String flightNo) {
        this.flightNo = flightNo == null ? null : flightNo.trim();
    }

    public String getIsJourneyCancel() {
        return isJourneyCancel;
    }

    public void setIsJourneyCancel(String isJourneyCancel) {
        this.isJourneyCancel = isJourneyCancel == null ? null : isJourneyCancel.trim();
    }

    public String getAccidentReason() {
        return accidentReason;
    }

    public void setAccidentReason(String accidentReason) {
        this.accidentReason = accidentReason;
    }

    public int getDelayTime() {
        return delayTime;
    }

    public void setDelayTime(int delayTime) {
        this.delayTime = delayTime;
    }

    public String getIsFlightDelay() {
        return isFlightDelay;
    }

    public void setIsFlightDelay(String isFlightDelay) {
        this.isFlightDelay = isFlightDelay;
    }

    public Date getLandingDate() {
        return landingDate;
    }

    public void setLandingDate(Date landingDate) {
        this.landingDate = landingDate;
    }

    public int getDelayTimeUser() {
        return delayTimeUser;
    }

    public void setDelayTimeUser(int delayTimeUser) {
        this.delayTimeUser = delayTimeUser;
    }
}
