package com.paic.ncbs.claim.model.vo.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.report.LinkManDTO;
import com.paic.ncbs.claim.common.util.RapeDateUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


public class ReportBaseInfoVO {
	private String reportNo;
	private String casetimes;
	private String accidentTypeName;
	private String reporterName;
	private String relationWithReport;
	private String relationWithReportName;
	private List<LinkManDTO> linkManList = new ArrayList<LinkManDTO>();
	private String reporterCallNo;
	private String reporterRegisterTel;
	private String reportMode;
	private String reportModeName;
	private String reportregisterUm;
	private String remark;
	private String reportRemark;
	private String accidentDetail;
	private String accidentPlace;
	private String isSelfHelp;
	private String isStandard;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date reportDate;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date accidentDate;
	private String isAbnormal;
	private String certificateNo;
	private String certificateType;
	private String certificateTypeName;
	private String clientCertificateType;
	private String accidentPersonName;
	@JsonFormat(pattern = RapeDateUtil.SIMPLE_DATE_STR)
	private Date birthday;
	private String departmentCode;
	private String departmentName;
	private String policyNo;
	private String electronicNo;
	private String isVirtual;
	private String reportType;
	private String name;
	private String reasonDesc;
	private String reasonCode;
	private String abnormalCaseStatus;
	private  List<String> remarks;
	private String partnerCode;
	private String isRepeatReport;
	private boolean isShowRepeatInfo;
	private List<String> RepeatReportNoList;

	private String isDirectCompensation;

	public String getIsRepeatReport() {
		return isRepeatReport;
	}

	public void setIsRepeatReport(String isRepeatReport) {
		this.isRepeatReport = isRepeatReport;
	}

	public boolean isShowRepeatInfo() {
		return isShowRepeatInfo;
	}

	public void setShowRepeatInfo(boolean isShowRepeatInfo) {
		this.isShowRepeatInfo = isShowRepeatInfo;
	}

	public List<String> getRepeatReportNoList() {
		return RepeatReportNoList;
	}

	public void setRepeatReportNoList(List<String> repeatReportNoList) {
		RepeatReportNoList = repeatReportNoList;
	}

	public List<String> getRemarks() {
		return remarks;
	}

	public void setRemarks(List<String> remarks) {
		this.remarks = remarks;
	}

	public String getReasonCode() {
		return reasonCode;
	}

	public void setReasonCode(String reasonCode) {
		this.reasonCode = reasonCode;
	}

	public String getClientCertificateType() {
		return clientCertificateType;
	}

	public void setClientCertificateType(String clientCertificateType) {
		this.clientCertificateType = clientCertificateType;
	}

	public String getReasonDesc() {
		return reasonDesc;
	}

	public void setReasonDesc(String reasonDesc) {
		this.reasonDesc = reasonDesc;
	}

	public String getAbnormalCaseStatus() {
		return abnormalCaseStatus;
	}

	public void setAbnormalCaseStatus(String abnormalCaseStatus) {
		this.abnormalCaseStatus = abnormalCaseStatus;
	}

	public String getCertificateTypeName() {
		return certificateTypeName;
	}

	public void setCertificateTypeName(String certificateTypeName) {
		this.certificateTypeName = certificateTypeName;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	public String getElectronicNo() {
		return electronicNo;
	}

	public void setElectronicNo(String electronicNo) {
		this.electronicNo = electronicNo;
	}

	public String getReportType() {
		return reportType;
	}

	public void setReportType(String reportType) {
		this.reportType = reportType;
	}

	public String getDepartmentName() {
		return departmentName;
	}

	public void setDepartmentName(String departmentName) {
		this.departmentName = departmentName;
	}

	public String getIsVirtual() {
		return isVirtual;
	}

	public void setIsVirtual(String isVirtual) {
		this.isVirtual = isVirtual;
	}

	public String getDepartmentCode() {
		return departmentCode;
	}

	public void setDepartmentCode(String departmentCode) {
		this.departmentCode = departmentCode;
	}

	public String getPolicyNo() {
		return policyNo;
	}

	public void setPolicyNo(String policyNo) {
		this.policyNo = policyNo;
	}
	public String getCasetimes() {
		return casetimes;
	}

	public void setCasetimes(String casetimes) {
		this.casetimes = casetimes;
	}

	public String getCertificateNo() {
		return certificateNo;
	}

	public void setCertificateNo(String certificateNo) {
		this.certificateNo = certificateNo;
	}

	public String getCertificateType() {
		return certificateType;
	}

	public void setCertificateType(String certificateType) {
		this.certificateType = certificateType;
	}

	public String getAccidentPersonName() {
		return accidentPersonName;
	}

	public void setAccidentPersonName(String accidentPersonName) {
		this.accidentPersonName = accidentPersonName;
	}

	public Date getBirthday() {
		return birthday;
	}

	public void setBirthday(Date birthday) {
		this.birthday = birthday;
	}

	public String getIsAbnormal() {
		return isAbnormal;
	}

	public void setIsAbnormal(String isAbnormal) {
		this.isAbnormal = isAbnormal;
	}

	public String getAccidentTypeName() {
		return accidentTypeName;
	}

	public void setAccidentTypeName(String accidentTypeName) {
		this.accidentTypeName = accidentTypeName;
	}

	public String getReportNo() {
		return reportNo;
	}

	public void setReportNo(String reportNo) {
		this.reportNo = reportNo;
	}

	public String getReporterName() {
		return reporterName;
	}

	public void setReporterName(String reporterName) {
		this.reporterName = reporterName;
	}

	public String getRelationWithReport() {
		return relationWithReport;
	}

	public void setRelationWithReport(String relationWithReport) {
		this.relationWithReport = relationWithReport;
	}

	public String getRelationWithReportName() {
		return relationWithReportName;
	}

	public void setRelationWithReportName(String relationWithReportName) {
		this.relationWithReportName = relationWithReportName;
	}

	public List<LinkManDTO> getLinkManList() {
		return linkManList;
	}

	public void setLinkManList(List<LinkManDTO> linkManList) {
		this.linkManList = linkManList;
	}

	public String getReporterCallNo() {
		return reporterCallNo;
	}

	public void setReporterCallNo(String reporterCallNo) {
		this.reporterCallNo = reporterCallNo;
	}

	public String getReporterRegisterTel() {
		return reporterRegisterTel;
	}

	public void setReporterRegisterTel(String reporterRegisterTel) {
		this.reporterRegisterTel = reporterRegisterTel;
	}

	public String getReportMode() {
		return reportMode;
	}

	public void setReportMode(String reportMode) {
		this.reportMode = reportMode;
	}

	public String getReportModeName() {
		return reportModeName;
	}

	public void setReportModeName(String reportModeName) {
		this.reportModeName = reportModeName;
	}

	public String getReportregisterUm() {
		return reportregisterUm;
	}

	public void setReportregisterUm(String reportregisterUm) {
		this.reportregisterUm = reportregisterUm;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getReportRemark() {
		return reportRemark;
	}

	public void setReportRemark(String reportRemark) {
		this.reportRemark = reportRemark;
	}

	public String getAccidentDetail() {
		return accidentDetail;
	}

	public void setAccidentDetail(String accidentDetail) {
		this.accidentDetail = accidentDetail;
	}

	public String getAccidentPlace() {
		return accidentPlace;
	}

	public void setAccidentPlace(String accidentPlace) {
		this.accidentPlace = accidentPlace;
	}

	public String getIsSelfHelp() {
		return isSelfHelp;
	}

	public void setIsSelfHelp(String isSelfHelp) {
		this.isSelfHelp = isSelfHelp;
	}

	public String getIsStandard() {
		return isStandard;
	}

	public void setIsStandard(String isStandard) {
		this.isStandard = isStandard;
	}

	public Date getReportDate() {
		return reportDate;
	}

	public void setReportDate(Date reportDate) {
		this.reportDate = reportDate;
	}

	public Date getAccidentDate() {
		return accidentDate;
	}

	public void setAccidentDate(Date accidentDate) {
		this.accidentDate = accidentDate;
	}

	public String getPartnerCode() {
		return partnerCode;
	}

	public void setPartnerCode(String partnerCode) {
		this.partnerCode = partnerCode;
	}

	public String getIsDirectCompensation() {
		return isDirectCompensation;
	}

	public void setIsDirectCompensation(String isDirectCompensation) {
		this.isDirectCompensation = isDirectCompensation;
	}
}
