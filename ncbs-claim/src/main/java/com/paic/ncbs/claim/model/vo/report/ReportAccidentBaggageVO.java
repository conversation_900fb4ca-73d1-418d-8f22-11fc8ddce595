package com.paic.ncbs.claim.model.vo.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "行李延误信息VO")
public class ReportAccidentBaggageVO {

    @ApiModelProperty(value = "报案号")
    private String reportNo;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "0-国内;1-国外")
    private String overseasOccur;

    @ApiModelProperty(value = "出发地")
    private String departurePlace;

    @ApiModelProperty(value = "目的地")
    private String destination;

    @ApiModelProperty(value = "出发省份")
    private String departureProvince;

    @ApiModelProperty(value = "出发城市")
    private String departureCity;

    @ApiModelProperty(value = "出发机场")
    private String departureAirport;

    @ApiModelProperty(value = "出发区域")
    private String departureArea;

    @ApiModelProperty(value = "出发国家")
    private String departureNation;

    @ApiModelProperty(value = "到达省份")
    private String destinationProvince;

    @ApiModelProperty(value = "到达城市")
    private String destinationCity;

    @ApiModelProperty(value = "到达机场")
    private String destinationAirport;

    @ApiModelProperty(value = "到达区域")
    private String destinationArea;

    @ApiModelProperty(value = "到达国家")
    private String destinationNation;

    @ApiModelProperty(value = "计划到达日期")
    private Date planArrivalDate;

    @ApiModelProperty(value = "签约时间")
    private Date signDate;

    @ApiModelProperty(value = "行李延误")
    private String[] baggageDelayType;

    @ApiModelProperty(value = "费用预估")
    private BigDecimal costEstimate;

    @ApiModelProperty(value = "延误时长（分钟）")
    private int delayTime;

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String getFlightNo() {
        return flightNo;
    }

    public void setFlightNo(String flightNo) {
        this.flightNo = flightNo == null ? null : flightNo.trim();
    }

    public String getOverseasOccur() {
        return overseasOccur;
    }

    public void setOverseasOccur(String overseasOccur) {
        this.overseasOccur = overseasOccur == null ? null : overseasOccur.trim();
    }

    public String getDeparturePlace() {
        return departurePlace;
    }

    public void setDeparturePlace(String departurePlace) {
        this.departurePlace = departurePlace == null ? null : departurePlace.trim();
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination == null ? null : destination.trim();
    }

    public String getDepartureProvince() {
        return departureProvince;
    }

    public void setDepartureProvince(String departureProvince) {
        this.departureProvince = departureProvince == null ? null : departureProvince.trim();
    }

    public String getDepartureCity() {
        return departureCity;
    }

    public void setDepartureCity(String departureCity) {
        this.departureCity = departureCity == null ? null : departureCity.trim();
    }

    public String getDepartureAirport() {
        return departureAirport;
    }

    public void setDepartureAirport(String departureAirport) {
        this.departureAirport = departureAirport == null ? null : departureAirport.trim();
    }

    public String getDepartureArea() {
        return departureArea;
    }

    public void setDepartureArea(String departureArea) {
        this.departureArea = departureArea == null ? null : departureArea.trim();
    }

    public String getDepartureNation() {
        return departureNation;
    }

    public void setDepartureNation(String departureNation) {
        this.departureNation = departureNation == null ? null : departureNation.trim();
    }

    public String getDestinationProvince() {
        return destinationProvince;
    }

    public void setDestinationProvince(String destinationProvince) {
        this.destinationProvince = destinationProvince == null ? null : destinationProvince.trim();
    }

    public String getDestinationCity() {
        return destinationCity;
    }

    public void setDestinationCity(String destinationCity) {
        this.destinationCity = destinationCity == null ? null : destinationCity.trim();
    }

    public String getDestinationAirport() {
        return destinationAirport;
    }

    public void setDestinationAirport(String destinationAirport) {
        this.destinationAirport = destinationAirport == null ? null : destinationAirport.trim();
    }

    public String getDestinationArea() {
        return destinationArea;
    }

    public void setDestinationArea(String destinationArea) {
        this.destinationArea = destinationArea == null ? null : destinationArea.trim();
    }

    public String getDestinationNation() {
        return destinationNation;
    }

    public void setDestinationNation(String destinationNation) {
        this.destinationNation = destinationNation == null ? null : destinationNation.trim();
    }

    public Date getPlanArrivalDate() {
        return planArrivalDate;
    }

    public void setPlanArrivalDate(Date planArrivalDate) {
        this.planArrivalDate = planArrivalDate;
    }

    public Date getSignDate() {
        return signDate;
    }

    public void setSignDate(Date signDate) {
        this.signDate = signDate;
    }

    public BigDecimal getCostEstimate() {
        return costEstimate;
    }

    public String[] getBaggageDelayType() {
        return baggageDelayType;
    }

    public void setBaggageDelayType(String[] baggageDelayType) {
        this.baggageDelayType = baggageDelayType;
    }

    public void setCostEstimate(BigDecimal costEstimate) {
        this.costEstimate = costEstimate;
    }

    public int getDelayTime() {
        return delayTime;
    }

    public void setDelayTime(int delayTime) {
        this.delayTime = delayTime;
    }
}
