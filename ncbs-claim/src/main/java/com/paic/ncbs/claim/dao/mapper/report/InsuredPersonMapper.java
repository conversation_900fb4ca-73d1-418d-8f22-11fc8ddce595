package com.paic.ncbs.claim.dao.mapper.report;

import com.paic.ncbs.claim.model.dto.report.InsuredPersonDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;
import java.util.Set;

@MapperScan
public interface InsuredPersonMapper {

    InsuredPersonDTO getInsuredPersonDTO(@Param("reportNo") String reportNo);

    InsuredPersonDTO getInsuredPerson(@Param("idAhcsPolicyInfo") String idAhcsPolicyInfo);

    InsuredPersonDTO getInsuredPersonInfo(@Param("reportNo") String reportNo);

    List<InsuredPersonDTO> getInsuredPersonList(@Param("idAhcsPolicyInfoList") Set<String> idAhcsPolicyInfoList, @Param("birthday") String birthday);

    InsuredPersonDTO getReportCustomer(@Param("reportNo") String reportNo);

    InsuredPersonDTO getReportCustomerByInsuredPerson(@Param("reportNo") String reportNo, @Param("idPolicyInfo") String idPolicyInfo);

    InsuredPersonDTO getReportCustomerNo(@Param("reportNo") String reportNo);

    InsuredPersonDTO getReportCustomerNoByInsuredPerson(@Param("reportNo") String reportNo, @Param("idPolicyInfo") String idPolicyInfo);

    InsuredPersonDTO getCustomerNoByIdPolicy(@Param("reportNo") String reportNo, @Param("idPolicyInfo") String idPolicyInfo);

    InsuredPersonDTO getAcceptNoByIdPolicy(@Param("reportNo") String reportNo, @Param("idPolicyInfo") String idPolicyInfo);

    InsuredPersonDTO getSdInsuredPerson(@Param("reportNo") String reportNo, @Param("policyInfoIdList") Set<String> policyInfoIdList);

    String getInsuredNameByReportNo(@Param("reportNo") String reportNo);

    InsuredPersonDTO getReportCustomerInfo(@Param("reportNo") String reportNo);

    InsuredPersonDTO getInsuredPersonDTOByPolicyId(@Param("idAhcsPolicyInfo") String idAhcsPolicyInfo);

    InsuredPersonDTO getReportCustomerByPolicy(@Param("idPolicyInfo") String idPolicyInfo);

}
