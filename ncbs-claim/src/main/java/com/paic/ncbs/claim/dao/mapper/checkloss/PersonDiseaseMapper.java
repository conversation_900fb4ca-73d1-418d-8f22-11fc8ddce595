package com.paic.ncbs.claim.dao.mapper.checkloss;

import com.paic.ncbs.claim.model.dto.duty.PersonDiseaseDTO;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface PersonDiseaseMapper {

    public void savePersonDisease(PersonDiseaseDTO personDiseaseDTO);

    public void removePersonDisease(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId);

    void updateEffective(PersonDiseaseDTO personDiseaseDTO);

    public PersonDiseaseDTO getPersonDisease(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId, @Param("status") String status);

    public PersonDiseaseDTO getPersonDiseaseDTO(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId);

    public PersonDiseaseDTO getPersonDiseaseByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,
                                                       @Param("status") String status, @Param("taskCode") String taskCode);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);
}
