package com.paic.ncbs.claim.dao.mapper.pay;

import com.paic.ncbs.claim.dao.entity.pay.ClmsPaymentDuty;
import com.paic.ncbs.claim.model.dto.pay.PaymentDutyDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentPlanDutyDTO;
import com.paic.ncbs.claim.replevy.dto.PlanDutyRemainingDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 赔款拆分责任表(ClmsPaymentDuty)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-24 21:00:30
 */
public interface ClmsPaymentDutyMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param idClmsPaymentDuty 主键
     * @return 实例对象
     */
    ClmsPaymentDuty queryById(String idClmsPaymentDuty);

    /**
     * 通过idClmsPaymentPlan查询多条数据
     *
     * @param idClmsPaymentPlan 外键
     * @return 实例对象
     */
    List<ClmsPaymentDuty> getPaymentDutyList(String idClmsPaymentPlan);

    /**
     * 统计总行数
     *
     * @param clmsPaymentDuty 查询条件
     * @return 总行数
     */
    long count(ClmsPaymentDuty clmsPaymentDuty);

    /**
     * 新增数据
     *
     * @param clmsPaymentDuty 实例对象
     * @return 影响行数
     */
    int insert(ClmsPaymentDuty clmsPaymentDuty);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<ClmsPaymentDuty> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ClmsPaymentDuty> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<ClmsPaymentDuty> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<ClmsPaymentDuty> entities);

    /**
     * 通过主键删除数据
     *q
     * @param idClmsPaymentDuty 主键
     * @return 影响行数
     */
    int deleteById(String idClmsPaymentDuty);

    /**
     * 获取责任不含税费用不包含追偿
     *
     * @param caseNo
     * @param caseTimes
     * @param planCode
     * @param dutyCode
     * @return
     */
    List<PaymentDutyDTO> getPaymentDutyFee(@Param("caseNo") String caseNo, @Param("caseTimes") Integer caseTimes,
                                           @Param("planCode") String planCode, @Param("dutyCode") String dutyCode);

    List<PaymentPlanDutyDTO> getPaymentDutyByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    /**
     * 获取追偿费用
     *
     * @param caseNo
     * @param planCode
     * @param dutyCode
     * @param subTimes
     * @return
     */
    List<PaymentDutyDTO> getReplevyPaymentDuty(@Param("caseNo") String caseNo,
                                               @Param("planCode") String planCode, @Param("dutyCode") String dutyCode, @Param("paymentType")String paymentType, Integer subTimes);


    /**
     * 查询险种责任剩余金额信息
     * @param reportNo 报案号
     * @return 险种责任剩余金额列表
     */
    List<PlanDutyRemainingDTO> getPlanDutyRemainingByReportNo(@Param("reportNo") String reportNo);
}

