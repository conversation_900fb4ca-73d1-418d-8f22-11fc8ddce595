package com.paic.ncbs.claim.dao.mapper.fileupload;

import com.paic.ncbs.claim.model.dto.fileupload.HospitalBatchImportDTO;
import com.paic.ncbs.claim.model.vo.fileupolad.HospitalInfoImportFailVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface HospitalBatchImportMapper {
    void insertImportInfo(HospitalBatchImportDTO importDTO);

    List<HospitalInfoImportFailVO> getImportFailInfo(@Param("batchNo")String batchNo);
}
