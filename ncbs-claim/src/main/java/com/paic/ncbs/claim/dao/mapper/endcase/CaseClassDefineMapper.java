package com.paic.ncbs.claim.dao.mapper.endcase;

import com.paic.ncbs.claim.model.dto.endcase.CaseClassDefineDTO;
import com.paic.ncbs.claim.model.dto.endcase.SubCaseClassDefineDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface CaseClassDefineMapper {

    List<CaseClassDefineDTO> getCaseClassDefines();

    List<SubCaseClassDefineDTO> getSubCaseClass(@Param("classCode") String classCode);

    List<SubCaseClassDefineDTO> getSubCaseClassName();

    String getIdCaseClassDefine(@Param("classCode") String classCode);

    List<String> getBigCaseClassBySub(@Param("subCaseClassList") List<String> subCaseClassList);

    List<String> getCaseClassParentCodeByReport(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("tacheCodeList") List<String> tacheCodeList);
}
