package com.paic.ncbs.claim.mq.producer;

import com.paic.ncbs.claim.dao.entity.report.*;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO;
import com.paic.ncbs.claim.dao.entity.report.*;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLRegistDto;

/**
 *      报案注销环节
 */
public interface MqProducerRegistCancelService {

    /**
     * 通过mq同步报案环节信息
     * @param reportNo
     */
    void syncProducerRegistCancelLink(String reportNo, Integer caseTimes);

    PrpLRegistDto initPrpLRegistDto(String reportNo, ReportInfoEntity reportInfo, ReportCustomerInfoEntity customerInfo, ReportInfoExEntity reportInfoExEntity, ReportAccidentEntity reportAccident, PolicyInfoDTO policyInfoDTO, WholeCaseBaseDTO wholeCaseBaseEntity, ReportAccidentExEntity reportAccidentEx, String flag);
}
