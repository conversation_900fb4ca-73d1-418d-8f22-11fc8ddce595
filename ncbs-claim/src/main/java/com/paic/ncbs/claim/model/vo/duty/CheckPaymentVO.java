package com.paic.ncbs.claim.model.vo.duty;

import com.paic.ncbs.claim.model.vo.settle.PolicyPayVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

@ApiModel("CheckPaymentVO-校验支付金额VO")
public class CheckPaymentVO {

    @ApiModelProperty("赔付金额")
    private BigDecimal payAmount;

    @ApiModelProperty("")
    private BigDecimal payedCost;

    @ApiModelProperty("")
    private BigDecimal totalPayCost;

    @ApiModelProperty("意健险保单赔付信息VO列表")
    private List<PolicyPayVO> policyPays;

    @ApiModelProperty("")
    private String endorsement;

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public BigDecimal getPayedCost() {
        return payedCost;
    }

    public void setPayedCost(BigDecimal payedCost) {
        this.payedCost = payedCost;
    }

    public BigDecimal getTotalPayCost() {
        return totalPayCost;
    }

    public void setTotalPayCost(BigDecimal totalPayCost) {
        this.totalPayCost = totalPayCost;
    }

    public List<PolicyPayVO> getPolicyPays() {
        return policyPays;
    }

    public void setPolicyPays(List<PolicyPayVO> policyPays) {
        this.policyPays = policyPays;
    }

    public String getEndorsement() {
        return endorsement;
    }

    public void setEndorsement(String endorsement) {
        this.endorsement = endorsement;
    }
}
