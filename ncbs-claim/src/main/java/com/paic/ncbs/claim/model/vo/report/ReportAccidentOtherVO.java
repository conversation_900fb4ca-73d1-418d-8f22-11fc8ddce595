package com.paic.ncbs.claim.model.vo.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@ApiModel(description = "其他非人伤信息VO")
public class ReportAccidentOtherVO {

    @ApiModelProperty(value = "报案号")
    private String reportNo;

    @ApiModelProperty(value = "其他非人伤类型")
    private String[] otherType;

    @ApiModelProperty(value = "费用预估")
    private BigDecimal costEstimate;

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String[] getOtherType() {
        return otherType;
    }

    public void setOtherType(String[] otherType) {
        this.otherType = otherType;
    }

    public BigDecimal getCostEstimate() {
        return costEstimate;
    }

    public void setCostEstimate(BigDecimal costEstimate) {
        this.costEstimate = costEstimate;
    }

}
