package com.paic.ncbs.claim.model.vo.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

public class HistoryCaseVO {

    private String reportType;
    private String reportNo;
    private String caseNo;
    private Short caseTimes;
    private String insuredName;
    private String clientType;
    private String clientTypeName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reportDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date accidentDate;
    private String caseStatusName;
    private String departmentCode;
    private String departmentAbbrName;
    private BigDecimal endCaseAmount;
    private BigDecimal estimateAmount;
    private String policyNo;
    private Date endCaseDate;
    private String certificateNo;
    private String accidentDetail;
    private String personnelAttribute = "";
    private String isRegister;

    /**
     * 赔款
     */
    private BigDecimal policySumPay;

    /**
     * 流程状态
     */
    private String processStatusName;

    /**
     * 未决金额
     */
    private String pendingAmount;

    private String subProcessFlag;

    private BigDecimal caseInputDuration;

    private String isAIModel;

    public BigDecimal getCaseInputDuration() {
        return caseInputDuration;
    }

    public void setCaseInputDuration(BigDecimal caseInputDuration) {
        this.caseInputDuration = caseInputDuration;
    }

    public String getIsAIModel() {
        return isAIModel;
    }

    public void setIsAIModel(String isAIModel) {
        this.isAIModel = isAIModel;
    }


    public BigDecimal getPolicySumPay() {
        return policySumPay;
    }

    public void setPolicySumPay(BigDecimal policySumPay) {
        this.policySumPay = policySumPay;
    }

    public String getProcessStatusName() {
        return processStatusName;
    }

    public void setSubProcessFlag(String subProcessFlag) {
        this.subProcessFlag = subProcessFlag;
    }

    public String getSubProcessFlag() {
        return subProcessFlag;
    }

    public void setProcessStatusName(String processStatusName) {
        this.processStatusName = processStatusName;
    }

    public String getPendingAmount() {
        return pendingAmount;
    }

    public void setPendingAmount(String pendingAmount) {
        this.pendingAmount = pendingAmount;
    }

    public String getAccidentDetail() {
        return accidentDetail;
    }

    public void setAccidentDetail(String accidentDetail) {
        this.accidentDetail = accidentDetail;
    }

    public BigDecimal getEstimateAmount() {
        return estimateAmount;
    }

    public void setEstimateAmount(BigDecimal estimateAmount) {
        this.estimateAmount = estimateAmount;
    }

    public BigDecimal getEndCaseAmount() {
        return endCaseAmount;
    }

    public void setEndCaseAmount(BigDecimal endCaseAmount) {
        this.endCaseAmount = endCaseAmount;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getReportType() {
        return reportType;
    }

    public void setReportType(String reportType) {
        this.reportType = reportType;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public Short getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Short caseTimes) {
        this.caseTimes = caseTimes;
    }

    public String getInsuredName() {
        return insuredName;
    }

    public void setInsuredName(String insuredName) {
        this.insuredName = insuredName;
    }

    public String getClientType() {
        return clientType;
    }

    public void setClientType(String clientType) {
        this.clientType = clientType;
    }

    public String getClientTypeName() {
        return clientTypeName;
    }

    public void setClientTypeName(String clientTypeName) {
        this.clientTypeName = clientTypeName;
    }

    public Date getReportDate() {
        return reportDate;
    }

    public void setReportDate(Date reportDate) {
        this.reportDate = reportDate;
    }

    public Date getAccidentDate() {
        return accidentDate;
    }

    public void setAccidentDate(Date accidentDate) {
        this.accidentDate = accidentDate;
    }

    public String getCaseStatusName() {
        return caseStatusName;
    }

    public void setCaseStatusName(String caseStatusName) {
        this.caseStatusName = caseStatusName;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public Date getEndCaseDate() {
        return endCaseDate;
    }

    public void setEndCaseDate(Date endCaseDate) {
        this.endCaseDate = endCaseDate;
    }

    public String getCertificateNo() {
        return certificateNo;
    }

    public void setCertificateNo(String certificateNo) {
        this.certificateNo = certificateNo;
    }

    public String getDepartmentAbbrName() {
        return departmentAbbrName;
    }

    public void setDepartmentAbbrName(String departmentAbbrName) {
        this.departmentAbbrName = departmentAbbrName;
    }

    public String getCaseNo() {
        return caseNo;
    }

    public void setCaseNo(String caseNo) {
        this.caseNo = caseNo;
    }

    public String getPersonnelAttribute() {
        return personnelAttribute;
    }

    public void setPersonnelAttribute(String personnelAttribute) {
        this.personnelAttribute = personnelAttribute;
    }

    public String getIsRegister() {
        return isRegister;
    }

    public void setIsRegister(String isRegister) {
        this.isRegister = isRegister;
    }
}
