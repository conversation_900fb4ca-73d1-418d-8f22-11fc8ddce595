package com.paic.ncbs.claim.model.vo.ahcs;

import com.paic.ncbs.claim.common.constant.SettleConst;

import java.math.BigDecimal;

/**
 * 拒付费用汇总VO，用于核赔拒付展示费用信息
 */
public class FeePaySumVO {

	/**
	 *保单本次理算金额总计（费用加赔款）
	 */
	private BigDecimal policyPayAmount;
	
	/**
	 *已预赔付总计（费用加赔款
	 */
	private BigDecimal prePayAmount;
	
	
	/**
	 *本次应支付合计（费用加赔款）
	 */
	private BigDecimal finalPayAmount;
	
	
	/**
	 * 赔付结论，默认等于正常赔付。
	 */
	private String indemnityMode = SettleConst.INDEMNITY_MODE_PAY ;
	
	/**
	 * 批单文案
	 */
	private String endorseTemplate;
	

	public String getEndorseTemplate() {
		return endorseTemplate;
	}

	public void setEndorseTemplate(String endorseTemplate) {
		this.endorseTemplate = endorseTemplate;
	}

	public String getIndemnityMode() {
		return indemnityMode;
	}

	public void setIndemnityMode(String indemnityMode) {
		this.indemnityMode = indemnityMode;
	}

	public BigDecimal getPolicyPayAmount() {
		return policyPayAmount;
	}

	public void setPolicyPayAmount(BigDecimal policyPayAmount) {
		this.policyPayAmount = policyPayAmount;
	}

	public BigDecimal getPrePayAmount() {
		return prePayAmount;
	}

	public void setPrePayAmount(BigDecimal prePayAmount) {
		this.prePayAmount = prePayAmount;
	}

	public BigDecimal getFinalPayAmount() {
		return finalPayAmount;
	}

	public void setFinalPayAmount(BigDecimal finalPayAmount) {
		this.finalPayAmount = finalPayAmount;
	}
	
}
