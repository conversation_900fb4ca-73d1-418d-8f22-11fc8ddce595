package com.paic.ncbs.claim.dao.mapper.rule;

import com.paic.ncbs.claim.dao.entity.rule.AutoRuleDetailInfoEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AutoRuleDetailInfoMapper {
    int deleteByPrimaryKey(String idAutoRuleInfo);

    int insert(AutoRuleDetailInfoEntity record);

    int insertSelective(AutoRuleDetailInfoEntity record);

    void insertList(List<AutoRuleDetailInfoEntity> detailInfoEntityList);

    AutoRuleDetailInfoEntity selectByPrimaryKey(String idAutoRuleInfo);

    List<AutoRuleDetailInfoEntity> selectByMainId(@Param("idAutoRuleMain") String idAutoRuleMain);

    Integer getCountByReportNo(@Param("reportNo") String reportNo,
                               @Param("caseTimes") Integer caseTimes,
                               @Param("ruleType") String ruleType,
                               @Param("code") String code);

    int updateByPrimaryKeySelective(AutoRuleDetailInfoEntity record);

    int updateByPrimaryKey(AutoRuleDetailInfoEntity record);

    Integer getSerialNo(@Param("idAutoRuleMain") String idAutoRuleMain);
}