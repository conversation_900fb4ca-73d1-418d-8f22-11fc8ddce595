package com.paic.ncbs.claim.dao.mapper.estimate;

import com.paic.ncbs.claim.dao.entity.estimate.PackageInfoEntity;
import org.apache.ibatis.annotations.Param;

public interface PackageInfoEntityMapper {
    int deleteByPrimaryKey(String idPackageInfo);

    int insert(PackageInfoEntity record);

    int insertSelective(PackageInfoEntity record);

    PackageInfoEntity selectByPrimaryKey(String idPackageInfo);

    int updateByPrimaryKeySelective(PackageInfoEntity record);

    int updateByPrimaryKey(PackageInfoEntity record);

    PackageInfoEntity getPackageInfoByCode(@Param("packageCode") String packageCode, @Param("productId") String productId);
}