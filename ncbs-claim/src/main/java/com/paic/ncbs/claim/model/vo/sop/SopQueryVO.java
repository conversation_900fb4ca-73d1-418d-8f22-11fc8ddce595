package com.paic.ncbs.claim.model.vo.sop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * SOP查询条件VO
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Data
@ApiModel("SOP查询条件")
public class SopQueryVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("SOP名称")
    private String sopName;

    @ApiModelProperty("发布人员")
    private String publisherCode;

    @ApiModelProperty("适用环节")
    private String taskBpmKey;

    @ApiModelProperty("适用产品")
    private String productCode;

    @ApiModelProperty("适用方案")
    private String groupCode;

    @ApiModelProperty("适用险种")
    private String planCode;

    @ApiModelProperty("文件类型（01-文本 02-文件 03-所有）")
    private String fileType;

    @ApiModelProperty("状态（01-暂存、02-有效、03-无效）")
    private String status;

    @ApiModelProperty("生效日期开始")
    private String effectiveDate;

    @ApiModelProperty("失效日期结束")
    private String invalidDate;

    @ApiModelProperty("当前页码")
    private Integer currentPage = 1;

    @ApiModelProperty("每页大小")
    private Integer pageSize = 20;

}
