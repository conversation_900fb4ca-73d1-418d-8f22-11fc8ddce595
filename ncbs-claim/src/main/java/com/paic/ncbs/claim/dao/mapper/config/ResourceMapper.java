package com.paic.ncbs.claim.dao.mapper.config;


import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.config.ResourceDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;


@MapperScan
public interface ResourceMapper extends BaseDao<ResourceDTO> {
	

	List<ResourceDTO> getResourceList(@Param("resourceType") String resourceType );
	
}