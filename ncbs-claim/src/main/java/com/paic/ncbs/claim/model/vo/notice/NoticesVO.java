package com.paic.ncbs.claim.model.vo.notice;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel("消息提醒")
public class NoticesVO {
    @ApiModelProperty("报案号")
    private String reportNo;
    @ApiModelProperty(value = "消息提醒ID")
    private String noticesId;
    @ApiModelProperty("赔付次数")
    private Integer caseTimes;
    /**
     * 提醒大类（1-立案超时，2-结案超时，3-TPA调查超时，4-审批退回，5-任务调度，6-支付失败）
     */
    @ApiModelProperty("提醒大类")
    private String noticeClass;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("消息提醒起期")
    private Date noticeStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("消息提醒止期")
    private Date noticeEndTime;

    @ApiModelProperty(value = "产品代码")
    private String productCode;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @ApiModelProperty(value = "创建时间")
    private Date createdDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @ApiModelProperty(value = "更新时间")
    private Date updatedDate;
    @ApiModelProperty(value = "产品名称")
    private String productName;
    @ApiModelProperty(value = "机构代码")
    private String companyCode;
    @ApiModelProperty(value = "机构名称")
    private String companyName;
    @ApiModelProperty(value = "方案名称")
    private String riskGroupName;
    @ApiModelProperty(value = "消息提醒内容")
    private String noticeContent;
    @ApiModelProperty(value = "消息提醒接受者ID")
    private String noticePersonId;
    /**
     * 消息接收者工号
     */
    @ApiModelProperty(value = "接收者工号")
    private String recipient;

    /**
     * 阅读状态（0-未读,1-已读,2-已发送外部系统（如企业微信））
     */
    @ApiModelProperty(value = "阅读状态")
    private String readStatus;
}
