package com.paic.ncbs.claim.dao.mapper.config;


import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.config.ResourceDetailValueDTO;
import com.paic.ncbs.claim.model.dto.user.UserPrivGroupRelDTO;
import com.paic.ncbs.claim.model.vo.config.PrivilegeGroupAndResourceVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.List;


@MapperScan
public interface ResourceDetailValueMapper extends BaseDao<ResourceDetailValueDTO> {
	

	//List<PrivilegeGroupAndResourceVO> getByResCodeAndDepCode(@Param("departmentCode")String departmentCode, @Param("resourceCode")String resourceCode);
	

	List<PrivilegeGroupAndResourceVO> getPrivAndResVOList(@Param("departmentCode")String departmentCode,
                                                          @Param("resourceCode")String resourceCode, @Param("resourceDetailCode")String resourceDetailCode);


	void modifyResourceDetailValueList(@Param("paramList")List<ResourceDetailValueDTO> paramList);


	void addResourceDetailValueList(@Param("paramList")List<ResourceDetailValueDTO> resourceDetailValueList);


    ResourceDetailValueDTO queryAmountPrivRange(@Param("auditorUm") String auditorUm, @Param("insuredApplyStatus")String insuredApplyStatus,
                                                @Param("departmentCode")String departmentCode, @Param("caseType")String caseType);


	void deleteResourceDetailValueList(@Param("paramList") List<String> paramList);




    public BigDecimal getPrivMaxValueByUid(@Param("userPrivRel") UserPrivGroupRelDTO userPrivRel, @Param("deptCode") String deptCode, @Param("resourceDetailCode") String resourceDetailCode);
}