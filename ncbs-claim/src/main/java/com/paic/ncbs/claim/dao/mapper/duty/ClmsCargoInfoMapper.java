package com.paic.ncbs.claim.dao.mapper.duty;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.model.dto.duty.ClmsCargoInfo;

/**
 * <p>
 * 货运险货物详情表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
public interface ClmsCargoInfoMapper extends BaseMapper<ClmsCargoInfo> {

    ClmsCargoInfo getClmsCargoInfo(String reportNo, String caseTimes);

    void saveClmsCargoInfo(ClmsCargoInfo clmsCargoInfo);

    void deleteByReportNoAndCaseTimes(String reportNo, String caseTimes);

}
