package com.paic.ncbs.claim.model.vo.ahcs;

import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.prepayinfo.PrePayInfoDTO;

import java.util.List;

public class PreApproveVO {
    private PrePayInfoDTO prePayInfoDTO;
    private List<DutyPayDTO> dutyPayList;
    private List<PlanPayDTO> planPayList;
    private List<PolicyPayDTO> updatePolicyPayList;
    private List<PolicyPayDTO> addPolicyPayList;
    private Integer subTimes;

    public PrePayInfoDTO getPrePayInfoDTO() {
        return prePayInfoDTO;
    }

    public void setPrePayInfoDTO(PrePayInfoDTO prePayInfoDTO) {
        this.prePayInfoDTO = prePayInfoDTO;
    }

    public List<DutyPayDTO> getDutyPayList() {
        return dutyPayList;
    }

    public void setDutyPayList(List<DutyPayDTO> dutyPayList) {
        this.dutyPayList = dutyPayList;
    }

    public List<PlanPayDTO> getPlanPayList() {
        return planPayList;
    }

    public void setPlanPayList(List<PlanPayDTO> planPayList) {
        this.planPayList = planPayList;
    }

    public List<PolicyPayDTO> getUpdatePolicyPayList() {
        return updatePolicyPayList;
    }

    public void setUpdatePolicyPayList(List<PolicyPayDTO> updatePolicyPayList) {
        this.updatePolicyPayList = updatePolicyPayList;
    }

    public List<PolicyPayDTO> getAddPolicyPayList() {
        return addPolicyPayList;
    }

    public void setAddPolicyPayList(List<PolicyPayDTO> addPolicyPayList) {
        this.addPolicyPayList = addPolicyPayList;
    }

    public Integer getSubTimes() {
        return subTimes;
    }

    public void setSubTimes(Integer subTimes) {
        this.subTimes = subTimes;
    }
}
