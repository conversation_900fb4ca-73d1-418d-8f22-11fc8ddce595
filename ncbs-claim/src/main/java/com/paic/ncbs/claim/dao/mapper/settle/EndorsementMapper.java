package com.paic.ncbs.claim.dao.mapper.settle;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.settle.EndorsementDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface EndorsementMapper extends BaseDao<PolicyPayDTO> {

    public void insertEndorsementInfo(EndorsementDTO endorsementInfo);

    public EndorsementDTO selectByReportNoAndCaseTime(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    public void updateEndorsementInfo(@Param("record") EndorsementDTO endorsementInfo);

    public void deleteByPolicyPayId(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    public String getEndorseTemplate(@Param("indemnityMode") String indemnityModel);
}
