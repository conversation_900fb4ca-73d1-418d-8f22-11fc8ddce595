package com.paic.ncbs.claim.model.vo.ahcs;

import com.paic.ncbs.claim.model.dto.ahcs.AhcsPolicyPlanDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoExDTO;

import java.math.BigDecimal;
import java.util.List;

public class AhcsPolicyDomainVO {

    private String validPolicy;
    private String validType;
    private BigDecimal applyNum;
    private String policySystem;
    private String productClass;
    private String technicProductCode;
    private List<AhcsPolicyPlanDTO> ahcsPolicyPlanDTOs;
    private PolicyInfoDTO ahcsPolicyInfo;
    private PolicyInfoExDTO policyInfoExDTO;

    public String getValidPolicy() {
        return validPolicy;
    }

    public void setValidPolicy(String validPolicy) {
        this.validPolicy = validPolicy;
    }

    public String getValidType() {
        return validType;
    }

    public void setValidType(String validType) {
        this.validType = validType;
    }

    public BigDecimal getApplyNum() {
        return applyNum;
    }

    public void setApplyNum(BigDecimal applyNum) {
        this.applyNum = applyNum;
    }

    public String getPolicySystem() {
        return policySystem;
    }

    public void setPolicySystem(String policySystem) {
        this.policySystem = policySystem;
    }

    public String getProductClass() {
        return productClass;
    }

    public void setProductClass(String productClass) {
        this.productClass = productClass;
    }

    public String getTechnicProductCode() {
        return technicProductCode;
    }

    public void setTechnicProductCode(String technicProductCode) {
        this.technicProductCode = technicProductCode;
    }

    public PolicyInfoDTO getAhcsPolicyInfo() {
        return ahcsPolicyInfo;
    }

    public void setAhcsPolicyInfo(PolicyInfoDTO ahcsPolicyInfo) {
        this.ahcsPolicyInfo = ahcsPolicyInfo;
    }

    public List<AhcsPolicyPlanDTO> getAhcsPolicyPlanDTOs() {
        return ahcsPolicyPlanDTOs;
    }

    public void setAhcsPolicyPlanDTOs(List<AhcsPolicyPlanDTO> ahcsPolicyPlanDTOs) {
        this.ahcsPolicyPlanDTOs = ahcsPolicyPlanDTOs;
    }

    public PolicyInfoExDTO getPolicyInfoExDTO() {
        return policyInfoExDTO;
    }

    public void setPolicyInfoExDTO(PolicyInfoExDTO policyInfoExDTO) {
        this.policyInfoExDTO = policyInfoExDTO;
    }

}
