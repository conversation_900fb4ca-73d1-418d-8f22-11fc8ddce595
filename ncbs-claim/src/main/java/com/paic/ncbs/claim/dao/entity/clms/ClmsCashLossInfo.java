package com.paic.ncbs.claim.dao.entity.clms;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 现金损失信息表(ClmsCashLossInfo)实体类
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:46
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ClmsCashLossInfo implements Serializable {
    private static final long serialVersionUID = 437816863566775989L;
    /**
     * 主键
     */
    private String id;
    /**
     * 报案号
     */
    private String reportNo;
    /**
     * 赔付次数
     */
    private Integer caseTimes;
    /**
     * 损失金额
     */
    private BigDecimal lossAmount;
    /**
     * 损失原因
     */
    private String lossReason;
    /**
     * 创建人员
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDate;
    /**
     * 修改人员
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDate;
    /*
    * 现金损失金额
    */
    private BigDecimal cashLossAmount;
    /*
     * 备注
     */
    private String remarks;


}

