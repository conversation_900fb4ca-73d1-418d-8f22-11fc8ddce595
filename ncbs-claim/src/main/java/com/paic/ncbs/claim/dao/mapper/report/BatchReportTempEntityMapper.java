package com.paic.ncbs.claim.dao.mapper.report;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.report.BatchReportTempEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface BatchReportTempEntityMapper extends BaseDao<BatchReportTempEntity> {

    int insertBatch(List<BatchReportTempEntity> record);

    int insertSelective(BatchReportTempEntity record);

    BatchReportTempEntity selectByPrimaryKey(String selectByPrimaryKey);

    int updateByPrimaryKeySelective(BatchReportTempEntity record);

    List<String> queryRepeatOrderNo(Map<String, String> param);

    List<Map<String, String>> queryReportInfoByPolicyNoAndDate(Map<String, String> param);

    String queryUserNameByUserId(String userId);

    Integer queryMd5Count(String md5);

    List<String> getIdListByReportBatchNo(String reportBatchNo);

    List<BatchReportTempEntity> getInfoByBatchNoAndType(@Param("reportBatchNo") String reportBatchNo, @Param("batchType") String batchType);

    BatchReportTempEntity selectByReportNo(String reportNo);

}