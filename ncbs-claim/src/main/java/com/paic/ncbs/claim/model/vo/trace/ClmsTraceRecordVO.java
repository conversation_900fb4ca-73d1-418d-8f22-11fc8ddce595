package com.paic.ncbs.claim.model.vo.trace;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * 通过ins-framework-mybatis工具自动生成，请勿手工修改。表clms_trace_record的PO对象<br/>
 * 对应表名：clms_trace_record,备注：人伤伤者干系人表
 *
 */
@ApiModel("人伤跟踪历史轨迹信息")
@Data
public class ClmsTraceRecordVO implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：id,备注：主键 */
	@TableField(value="id")
	private Integer id;
	/** 对应字段：report_no,备注：报案号 */
	@TableField(value="report_no")
	private String reportNo;
	/** 对应字段：injured_id,备注：伤亡人员表id */
	@TableField(value="injured_id")
	private Integer injuredId;
	/** 对应字段：case_times,备注：赔付次数 */
	@TableField(value="case_times")
	private Integer caseTimes;
	/** 对应字段：tracepers_context,备注：跟踪内容 */
	@TableField(value="tracepers_context")
	private String tracepersContext;
	/** 对应字段：tracepers_object,备注：跟踪对象 */
	@TableField(value="tracepers_object")
	private String tracepersObject;
	/** 对应字段：phone_number,备注：联系人电话 */
	@TableField(value="phone_number")
	private String phoneNumber;
	/** 对应字段：trace_date,备注：跟踪日期 */
	@TableField(value="trace_date")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date traceDate;
	/** 对应字段：tracepers_code,备注：跟踪人员代码 */
	@TableField(value="tracepers_code")
	private String tracepersCode;
	/** 对应字段：tracepers_name,备注：跟踪人员名称 */
	@TableField(value="tracepers_name")
	private String tracepersName;
	/** 对应字段：valid_flag,备注：有效标志 */
	@TableField(value="valid_flag")
	private String validFlag;
	/** 对应字段：remark,备注：备注 */
	@TableField(value="remark")
	private String remark;
	/** 对应字段：flag,备注：标志字段 */
	@TableField(value="flag")
	private String flag;
	/** 对应字段：created_by,备注：创建人 */
	@TableField(value="created_by")
	private String createdBy;
	/** 对应字段：sys_ctime,备注：创建时间 */
	@TableField(value="sys_ctime")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date sysCtime;
	/** 对应字段：updated_by,备注：最新修改人员 */
	@TableField(value="updated_by")
	private String updatedBy;
	/** 对应字段：sys_utime,备注：最新修改时间 */
	@TableField(value="sys_utime")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date sysUtime;
	/** 对应字段：task_info_id,备注：任务主表信息id主键 */
	@TableField(value="task_info_id")
	private String taskInfoId;
	//开始日期
	private Date createdDate;
	//结束日期
	private Date completeTime;

}
