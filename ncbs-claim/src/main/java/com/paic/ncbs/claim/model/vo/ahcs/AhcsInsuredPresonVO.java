package com.paic.ncbs.claim.model.vo.ahcs;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 被保人
 */
@Data
public class AhcsInsuredPresonVO {

    private String idAhcsInsuredPerson;

    private String idAhcsPolicyInfo;

    private String name;

    private String sexCode;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date birthday;

    private String certificateNo;

    private String certificateType;

    private String certificateName;

    private String professionCode;

    private String telephone;

    private String mobileTelephone;

    private String address;

    private String email;

    private String clientNo;

    private String schemeNo;

    private String schemeName;

    private String acceptNo;

    private String subPolicyNo;

    private String personnelCode;

    private String personnelName;

    private int age;

    private String riskPersonNo;

    private String plyCertificateType;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date effectiveDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date invalidateDate;

    private String isSociaSecurity;

    private String personnelAttribute;

    private String birthdayStr;

    private String clientType;

    private Integer personnelNature;

    private String relationshipWithApplicant;

    //创建人
    private String createdBy;
    //业务类型--个人单P，团体单G
    private String businessType;

}