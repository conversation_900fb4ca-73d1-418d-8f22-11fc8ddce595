package com.paic.ncbs.claim.dao.entity.ahcs;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.math.BigDecimal;

/**
 *险种
 */
public class AhcsPolicyPlanEntity extends EntityDTO {

    private static final long serialVersionUID = 5758999389949541917L;


    private String idAhcsPolicyPlan;

    private String idAhcsPolicyInfo;

    private String planName;

    private String planCode;

    private String rescueCompany;

    private String isMain;

    private String groupCode;

    private String orgPlanCode;

    private String orgPlanName;

    private BigDecimal applyNum;

    private BigDecimal taxRate;

    private String idPlyRiskGroup;

    private String riskGroupNo;

    private String riskGroupName;

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getIdAhcsPolicyPlan() {
        return idAhcsPolicyPlan;
    }

    public void setIdAhcsPolicyPlan(String idAhcsPolicyPlan) {
        this.idAhcsPolicyPlan = idAhcsPolicyPlan == null ? null : idAhcsPolicyPlan.trim();
    }

    public String getIdAhcsPolicyInfo() {
        return idAhcsPolicyInfo;
    }

    public void setIdAhcsPolicyInfo(String idAhcsPolicyInfo) {
        this.idAhcsPolicyInfo = idAhcsPolicyInfo == null ? null : idAhcsPolicyInfo.trim();
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName == null ? null : planName.trim();
    }

    public String getPlanCode() {
        return planCode;
    }

    public void setPlanCode(String planCode) {
        this.planCode = planCode == null ? null : planCode.trim();
    }

    public String getRescueCompany() {
        return rescueCompany;
    }

    public void setRescueCompany(String rescueCompany) {
        this.rescueCompany = rescueCompany == null ? null : rescueCompany.trim();
    }

    public String getIsMain() {
        return isMain;
    }

    public void setIsMain(String isMain) {
        this.isMain = isMain == null ? null : isMain.trim();
    }

    public String getOrgPlanCode() {
        return orgPlanCode;
    }

    public void setOrgPlanCode(String orgPlanCode) {
        this.orgPlanCode = orgPlanCode;
    }

    public String getOrgPlanName() {
        return orgPlanName;
    }

    public void setOrgPlanName(String orgPlanName) {
        this.orgPlanName = orgPlanName;
    }

    public BigDecimal getApplyNum() {
        return applyNum;
    }

    public void setApplyNum(BigDecimal applyNum) {
        this.applyNum = applyNum;
    }

    public String getIdPlyRiskGroup() {
        return idPlyRiskGroup;
    }

    public void setIdPlyRiskGroup(String idPlyRiskGroup) {
        this.idPlyRiskGroup = idPlyRiskGroup;
    }

    public String getRiskGroupNo() {
        return riskGroupNo;
    }

    public void setRiskGroupNo(String riskGroupNo) {
        this.riskGroupNo = riskGroupNo;
    }

    public String getRiskGroupName() {
        return riskGroupName;
    }

    public void setRiskGroupName(String riskGroupName) {
        this.riskGroupName = riskGroupName;
    }
}