package com.paic.ncbs.claim.dao.mapper.estimate;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.estimate.MarketProductInfoEntity;
import com.paic.ncbs.claim.model.vo.taskdeal.GroupInfoVO;
import com.paic.ncbs.claim.model.vo.taskdeal.MarketProductInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface MarketProductInfoEntityMapper extends BaseDao<MarketProductInfoEntity> {

    int deleteByPrimaryKey(String idMarketproductInfo);

    int insert(MarketProductInfoEntity record);

    int insertSelective(MarketProductInfoEntity record);

    MarketProductInfoEntity selectByPrimaryKey(String idMarketproductInfo);

    int updateByPrimaryKeySelective(MarketProductInfoEntity record);

    int updateByPrimaryKey(MarketProductInfoEntity record);

    MarketProductInfoEntity getMarketProductInfoByProductCode(@Param("productCode") String productCode, @Param("productVersion") String productVersion);

    List<MarketProductInfoVO> getAllMarketProductInfo(Map<String, String> map);

    List<GroupInfoVO> getAllRiskGroupInfo(Map<String, String> map);
}