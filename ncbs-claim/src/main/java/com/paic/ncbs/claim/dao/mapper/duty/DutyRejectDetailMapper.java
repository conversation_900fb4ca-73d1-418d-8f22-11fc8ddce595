package com.paic.ncbs.claim.dao.mapper.duty;

import com.paic.ncbs.claim.model.dto.duty.DutyRejectDetailDTO;
import com.paic.ncbs.claim.model.vo.duty.DutyRejectDetailVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.List;

@MapperScan
public interface DutyRejectDetailMapper {

    public List<DutyRejectDetailVO> getDutyRejectDetailList(@Param("reportNo") String reportNo,
                                                            @Param("caseTimes") Integer caseTimes, @Param("status") String status, @Param("lossObjectNo") String lossObjectNo);

    public List<DutyRejectDetailVO> getEstimatePolicyByReportNo(@Param("reportNo") String reportNo,
                                                                @Param("caseTimes") Integer caseTimes);

    public void removeDutyRejectDetailList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    public void addDutyRejectDetailList(List<DutyRejectDetailDTO> dutyRejectDetailList);

    public BigDecimal getRejectAmountByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);



}
