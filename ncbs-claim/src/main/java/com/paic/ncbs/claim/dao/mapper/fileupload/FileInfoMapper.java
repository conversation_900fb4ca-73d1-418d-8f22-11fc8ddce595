package com.paic.ncbs.claim.dao.mapper.fileupload;

import com.paic.ncbs.claim.model.dto.fileupload.FileDocumentDTO;
import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import com.paic.ncbs.claim.model.vo.doc.FileDocumentVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface FileInfoMapper {

    List<FileInfoDTO> getFileList(@Param("reportNo") String reportNo, @Param("fileType") String fileType);

    List<FileInfoDTO> getFileGroupId(FileInfoDTO fileInfoDTO);

    void addFileInfo(FileInfoDTO fileInfoDTO);

    List<String> getGroupIdList(@Param("reportNoList") List<String> reportNoList);

    List<String> getDocumentGroupIdByReportNoAndCaseTimes(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    String getDocumentItemId();

    String getSeq();

    void createDocument(FileDocumentVO vo);

    /**
     * 相比createDocument，多插入document_source、network_flag两个字段
     * @param vo
     */
    void apiCreateDocument(FileDocumentVO vo);

    List<FileDocumentDTO> queryDocumentByGroupId(@Param("documentGroupId") String documentGroupId);

    /**
     * 根据案件号和fileid查询发票影像的文件名
     * @return
     */
    List<FileDocumentDTO> getDocumentName(FileInfoDTO fileInfoDTO);

    /**
     * 根据fileid查询发影像
     * @return
     */
    List<FileDocumentDTO> getDocumentByFileId(FileInfoDTO fileInfoDTO);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);
}
