package com.paic.ncbs.claim.dao.mapper.ahcs;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsDutyAttributeEntity;
import com.paic.ncbs.claim.model.dto.settle.DutyAttributeDTO;
import com.paic.ncbs.claim.model.dto.settle.DutyAttributeValueDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.PolicyDutyAttributeDTO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface DutyAttributeMapper extends BaseDao<AhcsDutyAttributeEntity> {

    @MapKey("ATTRIBUTE_CODE")
    Map<String,Map<String,String>> getAttributeByDutyId(String idAhcsPolicyDuty);

    @MapKey("ATTRIBUTE_CODE")
    Map<String,Map<String,String>> getAttributeByAttrCode(String idAhcsPolicyDuty,List<String> attributeCode);

    List<AhcsDutyAttributeEntity> getInfoByDutyId(String idAhcsPolicyDuty);

    List<DutyAttributeDTO> getAllAttributes(String idAhcsPolicyDuty);

    AhcsDutyAttributeEntity selectByPrimaryKey(String idAhcsDutyAttribute);

    int insert(AhcsDutyAttributeEntity record);

    int insertSelective(AhcsDutyAttributeEntity record);

    void insertList(@Param("list") List<AhcsDutyAttributeEntity> dutyAttributeEntities);

    int updateByPrimaryKeySelective(AhcsDutyAttributeEntity record);

    int updateByPrimaryKey(AhcsDutyAttributeEntity record);

    int deleteByPrimaryKey(String idAhcsDutyAttribute);

    void deleteByReportNoAndPolicyNo(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo);

    List<DutyAttributeValueDTO> getAttributeValue(@Param("reportNo") String reportNo);

    /**
     * 根据报案号查询 保单责任属性是否配置了限额类型
     * @param reportNo
     * @return
     */
    List<DutyAttributeValueDTO> getDutyAttribute(String reportNo);
    @MapKey("ATTRIBUTE_DETAIL_CODE")
    Map<String,Map<String,String>> getAttributeDetailByidPolicyDuty(String idAhcsPolicyDuty);

    String getAttributeByAttrCodeInfo(String idAhcsPolicyDuty,String attributeCode);

    /**
     * 每月赔付次数属性
     * @param reportNo
     * @return
     */
    List<DutyAttributeValueDTO> getDutyAttributePayDays(String reportNo);

    List<DutyAttributeValueDTO>  getDutyAttributeYearlyPayDay(String reportNo);

    /**
     * 查询保单所有责任属性
     *
     */
    List<PolicyDutyAttributeDTO> getPolicyDutyAttributes(String reportNo, String policyNo);

    List<DutyAttributeValueDTO>  getDutyAttributeRemiamount(String reportNo);

    BigDecimal getDeductibleToDuty(@Param("reportNo") String reportNo,@Param("dutyCode") String dutyCode);
}