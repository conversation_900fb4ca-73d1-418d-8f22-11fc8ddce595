package com.paic.ncbs.claim.model.vo.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(description = "线上报案前检查信息")
public class OnlineReportCheckVO {

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @ApiModelProperty(value = "出险时间")
    private Date accidentDate;

    @ApiModelProperty(value = "保单号")
    private String policyNo;

    @ApiModelProperty(value = "客户名")
    private String clientName;

    @ApiModelProperty(value = "证件类型")
    private String certificateType;

    @ApiModelProperty(value = "身份证号")
    private String certificateNo;

}
