package com.paic.ncbs.claim.dao.mapper.other;

import com.paic.ncbs.claim.dao.entity.other.ExchangeRateEntity;
import com.paic.ncbs.claim.model.dto.settle.PlanCauseDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanCodeDTO;
import com.paic.ncbs.claim.model.dto.accident.AccidentCauseDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseCommonDTO;
import com.paic.ncbs.claim.model.dto.other.CityDefineDTO;
import com.paic.ncbs.claim.model.dto.other.DamageDTO;
import com.paic.ncbs.claim.model.dto.other.DomesticAddressDTO;
import com.paic.ncbs.claim.model.dto.other.GlobalAreaDefinedDTO;
import com.paic.ncbs.claim.model.dto.other.KeyValDTO;
import com.paic.ncbs.claim.model.dto.other.ProductLineDefineDTO;
import com.paic.ncbs.claim.model.dto.other.ProvinceDTO;
import com.paic.ncbs.claim.model.dto.other.ReasonCodeDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface BaseDataMapper {

    public List<ProvinceDTO> getProvinceList();

    public List<CityDefineDTO> getCityList(String provinceCode);

    public List<KeyValDTO> getBaseDataListByCollectionCode(String collectionCode);

    public String queryProvinceNameByCodeForReport(String provinceCode);

    public String queryCityNameByCodeForReport(String cityCode);

    public String getAccidentNameByCode(String cityCode);

    public String getContinentChineseNameByCode(String overseaContinentCode);

    public String getNationChineseNameByCode(String overseaNationCode);

    public String getValueChineseName(String valueCode);

    public List<DamageDTO> queryDamageThreeList(@Param("planCode") String planCode, @Param("typeCode") String typeCode);

    String getProvinceChineseNameByCode(String provinceCode);

    String getCityOrRegionChineseNameByCode(String code);

    public ExchangeRateEntity queryLatestExchangeRate(@Param("currency1Code") String currency1Code, @Param("currency2Code") String currency2Code, @Param("date") String date);

    public String queryPlanClassName(String planClassCode);

    public String queryPlanClassCode(String planCode);

    public List<AccidentCauseDTO> queryCauseList();

    List<AccidentCauseDTO> queryAccidentCause(@Param("productCode") String productCode, @Param("accidentCauseL2Name") String accidentCauseL2Name);

    public String queryParamValue(@Param("paramKey1") String paramKey1, @Param("paramKey2") String paramKey2, @Param("tradeCode") String tradeCode);


    public String queryDeptByAreaCode(String accidentCityCode);

    String queryDeptByDepartmentCode(String departmentCode);


    public String getProductClassCode(String productCode);

    public String getClaimProductClass(String productCode);

    public String getClaimMadeProductAmount(String productCode);

    public String getBdspName(String typeCode);

    public List<DamageDTO> queryDamageOneList(@Param("planCode") String planCode, @Param("threeIndustry") String threeIndustry);

    public List<DamageDTO> queryDamageTwoList(Map<String, String> param);

    public String queryDepartmentShortName(String departmentCode);

    public String queryPlanName(String planCode);

    List<PlanCodeDTO> getPlanClassCodeList();

    public Integer queryAccidentCauseIsExistByProductCode(String productCode);

    CaseCommonDTO getProductLineAndClassCode(String productCode);

    List<AccidentCauseDTO> queryCauseOneList(String planCode);

    List<AccidentCauseDTO> queryCauseTwoList(PlanCauseDTO planCauseDTO);

    List<AccidentCauseDTO> queryCauseThreeList(PlanCauseDTO planCauseDTO);

    List<DamageDTO> queryPeopleInjuryList();

    List<GlobalAreaDefinedDTO> getContinentList();

    List<GlobalAreaDefinedDTO> getAllNationList();

    List<GlobalAreaDefinedDTO> getNationList(String continentCode);

    List<DomesticAddressDTO> queryAllDomesticAddressList();

    String getParamConfig(String item);

    public List<PlanCodeDTO> getPlanCodeList(String planCode);

    public List<ProductLineDefineDTO> getProductLineCodeList();

    public List<PlanCodeDTO> getPlanClassCodeListBypld(String productLineCode);

    public List<ReasonCodeDTO> queryAllCertificateType();

    public String getProductLineCodeByProductCode(String productCode);

    public PlanCodeDTO getPlanClassCode(@Param("policyNo") String policyNo, @Param("reportNo") String reportNo);

    List<KeyValDTO> getNationalList();

    KeyValDTO getNationalNameByCode(String code);


    String queryOutMapByOldCode(@Param("valueCode")String valueCode);

}
