package com.paic.ncbs.claim.dao.mapper.settle;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayBaseInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayHisInfo;
import com.paic.ncbs.claim.model.dto.settle.SpecialPromiseDTO;
import com.paic.ncbs.claim.model.vo.settle.EpcisRequestVO;
import com.paic.ncbs.claim.model.vo.settle.MaxPayParam;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@MapperScan
public interface PolicyPayMapper extends BaseDao<PolicyPayDTO> {

    BigDecimal getPolicyPayAmount(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("policyNo") String policyNo);

    List<PolicyPayHisInfo> queryCaseInfo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    BigDecimal getPrePayAmount(@Param("reportNo") String policyNo, @Param("caseTimes") Integer caseTimes);

    List<PolicyPayBaseInfoDTO> getPolicyPayBaseInfo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    void deletePolicyPays(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    int checkExists(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("claimType") String claimType);

    List<PolicyPayDTO> selectFromPolicyCopy(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    List<PolicyPayDTO> selectByReportNo(@Param("reportNo") String reportNo,
                                        @Param("caseTimes") Integer caseTimes);

    List<PolicyPayDTO> getPrePolicyPays(@Param("reportNo") String reportNo,
                                        @Param("caseTimes") Integer caseTimes);

    EpcisRequestVO getEpicsRequest(@Param("clientNo")String clientNo, @Param("policyNo")String policyNo, @Param("reportNo")String reportNo);

    void updatePolicyPayInfoList(PolicyPayDTO policyPayArr);

    int insertPolicyPayInfoList(@Param("list") List<PolicyPayDTO> list);

    BigDecimal getPolicyPayTotal(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    BigDecimal getSumPayFee(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    List<PolicyPayDTO>  getPolicyPayListByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    List<PolicyPayDTO> getSimplePolicyDutyList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    void updatePolicyPayListForPrePay(@Param("list") List<PolicyPayDTO> policyPayArr);

    BigDecimal getPolicyPay(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    int policyHasPrePay(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,@Param("policyNo") String policyNo);

    /**
     * 查询赔款
     * @param reportNo
     * @param caseTimes
     * @param policyNo
     * @return
     */
    PolicyPayBaseInfoDTO getPolicyPayByPolicyNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("policyNo") String policyNo);
    List<SpecialPromiseDTO>  getSpecialPromise(String idAhcsPolicyPay);

    BigDecimal getPolicyHistoryPayAmount(MaxPayParam param);

    BigDecimal getPolicyHistoryPrePayAmount(MaxPayParam param);

    //查询产品编码
    String getPolicyProductCode(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    /**
     * 未决根据产品追溯12个月已结案的金额和报案号
     * @param productCode
     * @return
     */
    List<PolicyPayDTO> getBackSumPayByProductCode(String startDate,String endDate, String productCode);

    Integer getAllContByProductCode(String startDate, String endDate, String productCode);

    String getCaseNo(String reportNo,Integer caseTimes);

    BigDecimal getDutyFeeByCaseNo(@Param("caseNo") String caseNo, @Param("caseTimes") Integer caseTimes);

    BigDecimal getSettleAmountByCaseNo(@Param("caseNo") String caseNo, @Param("caseTimes") Integer caseTimes);

    BigDecimal selectSumFee(@Param("reportNo") String reportNo);
}
