package com.paic.ncbs.claim.dao.mapper.settle;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.settle.CoinsureRecordDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface CoinsureRecordMapper extends BaseDao<CoinsureRecordDTO> {

    List<CoinsureRecordDTO > selectByParam(CoinsureRecordDTO coinsureInfoDTO);

    void batchInsert(@Param("list")List<CoinsureRecordDTO> addList);

    void updateById(CoinsureRecordDTO coinsureRecordDTO);
}
