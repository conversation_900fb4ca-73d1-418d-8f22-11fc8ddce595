package com.paic.ncbs.claim.dao.mapper.checkloss;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.other.DetailFormulaDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface DetailFormulaMapper extends BaseDao<DetailFormulaDTO> {

    public int addDetailFormula(DetailFormulaDTO detailFormula);

    public List<String> getDetailFormulaGroupCodeByDuty(DetailFormulaDTO detailFormula);

    public List<DetailFormulaDTO> getDetailFormulaGroupCodeByDetail(DetailFormulaDTO detailFormula);

    public void removeDetailFormulaByGroupCode(String formulaGroupCode);

    public void removeDetailFormulaList(List<DetailFormulaDTO> formulas);

    public void addDetailFormulaList(List<DetailFormulaDTO> formulas);

    public void removeDetailFormulaListForPlan(List<DetailFormulaDTO> formulas);

    public List<String> getFormulaGroupByCode(@Param("list") List<String> detailCodes);

    public List<String> getHasSpecialTypeByReportNo(@Param("reportNo") String reportNo);

}