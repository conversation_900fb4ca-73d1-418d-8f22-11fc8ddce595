package com.paic.ncbs.claim.model.vo.policy;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PolicyPdfDTO {

    @ApiModelProperty("保单号码")
    private String policyNo;

    @ApiModelProperty("批单号")
    private String endorseNo;

    @ApiModelProperty("申请查询类型：1 电子保单 2 电子发票 3 批改信息")
    private Integer applyType;

    @ApiModelProperty("电子保单或电子发票PDF链接地址")
    private String pdfUrl;
}
