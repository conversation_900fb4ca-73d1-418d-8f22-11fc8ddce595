package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.util.Date;

/**
 * 意健险异常报案表
 */
public class ReportExcEntity extends EntityDTO {

	private static final long serialVersionUID = -4579380212316483798L;


    private String idReportExc;


    private String reportNo;


    private String policyNo;


    private String departmentCode;


    private String isVirtual;


    private Date birthday;


    private String certificateType;


    private String certificateNo;


    private String sex;


    private String accidentName;


    private String transName;


    private Date transDate;
    

    private String reasonDesc;

    private String reasonCode;

    private String elecSubPolicyNo;


    private String abnormalType;

    public String getAbnormalType() {
        return abnormalType;
    }

    public void setAbnormalType(String abnormalType) {
        this.abnormalType = abnormalType;
    }

    public String getReasonCode() {
		return reasonCode;
	}

	public void setReasonCode(String reasonCode) {
		this.reasonCode = reasonCode;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getElecSubPolicyNo() {
		return elecSubPolicyNo;
	}

	public void setElecSubPolicyNo(String elecSubPolicyNo) {
		this.elecSubPolicyNo = elecSubPolicyNo;
	}

	public String getReasonDesc() {
		return reasonDesc;
	}

	public void setReasonDesc(String reasonDesc) {
		this.reasonDesc = reasonDesc;
	}


    public String getIdReportExc() {
        return idReportExc;
    }


    public void setIdReportExc(String idReportExc) {
        this.idReportExc = idReportExc == null ? null : idReportExc.trim();
    }


    public String getReportNo() {
        return reportNo;
    }


    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }


    public String getPolicyNo() {
        return policyNo;
    }


    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo == null ? null : policyNo.trim();
    }


    public String getDepartmentCode() {
        return departmentCode;
    }


    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode == null ? null : departmentCode.trim();
    }


    public String getIsVirtual() {
        return isVirtual;
    }


    public void setIsVirtual(String isVirtual) {
        this.isVirtual = isVirtual == null ? null : isVirtual.trim();
    }


    public Date getBirthday() {
        return birthday;
    }


    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }


    public String getCertificateType() {
        return certificateType;
    }


    public void setCertificateType(String certificateType) {
        this.certificateType = certificateType == null ? null : certificateType.trim();
    }


    public String getCertificateNo() {
        return certificateNo;
    }


    public void setCertificateNo(String certificateNo) {
        this.certificateNo = certificateNo == null ? null : certificateNo.trim();
    }


    public String getSex() {
        return sex;
    }


    public void setSex(String sex) {
        this.sex = sex == null ? null : sex.trim();
    }


    public String getAccidentName() {
        return accidentName;
    }


    public void setAccidentName(String accidentName) {
        this.accidentName = accidentName == null ? null : accidentName.trim();
    }


    public String getTransName() {
        return transName;
    }


    public void setTransName(String transName) {
        this.transName = transName == null ? null : transName.trim();
    }


    public Date getTransDate() {
        return transDate;
    }


    public void setTransDate(Date transDate) {
        this.transDate = transDate;
    }
    
}