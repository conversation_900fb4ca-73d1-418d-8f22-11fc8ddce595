package com.paic.ncbs.claim.dao.mapper.estimate;

import com.paic.ncbs.claim.model.dto.openapi.ClmsTpaRequestRegisterDTO;
import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface ClmsTpaRequestRegisterMapper {
    /**
     * 保存
     * @param dto
     */
    public void save(ClmsTpaRequestRegisterDTO dto);

    /**
     * 查询
     * @param reportNo
     * @param caseTimes
     * @return
     */
    public ClmsTpaRequestRegisterDTO getClmsTpaRequestRegisterDTO(String reportNo,Integer caseTimes);

    void updateData(ClmsTpaRequestRegisterDTO updateDto);
}
