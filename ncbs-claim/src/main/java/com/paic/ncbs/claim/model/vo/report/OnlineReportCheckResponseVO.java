package com.paic.ncbs.claim.model.vo.report;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "线上报案前检查响应信息")
public class OnlineReportCheckResponseVO {

    // 是否可以报案（Y:可以，N：不可）
    private String isCanReport;

    // 不可报案的原因
    private String notReportReason;

}
