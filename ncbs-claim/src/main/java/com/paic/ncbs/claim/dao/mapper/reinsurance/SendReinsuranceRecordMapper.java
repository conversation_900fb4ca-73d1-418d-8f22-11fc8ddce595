package com.paic.ncbs.claim.dao.mapper.reinsurance;

import com.paic.ncbs.claim.dao.entity.reinsurance.SendReinsuranceRecord;
import com.paic.ncbs.claim.model.dto.reinsurance.RepayCalDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 理赔发送再保记录
 */
public interface SendReinsuranceRecordMapper {

    /**
     * 新增数据
     *
     * @param sendReinsuranceRecord 实例对象
     * @return 影响行数
     */
    int insert(SendReinsuranceRecord sendReinsuranceRecord);

    /**
     * 查询一条送再保记录
     *
     * @param reportNo
     * @param caseTimes
     * @param claimType
     * @return
     */
    SendReinsuranceRecord queryOneByCondition(@Param("reportNo") String reportNo,
                                              @Param("caseTimes") Integer caseTimes,
                                              @Param("claimType") String claimType);

    /**
     * 获取需要补推再保的数据
     * @return List<RepayCalDTO>
     */
    List<RepayCalDTO> getCompensateList();

    Integer selectCount(@Param("reportNo")String reportNo, @Param("caseTimes")Integer caseTimes, @Param("claimType")String claimType);
}

