package com.paic.ncbs.claim.feign;

import com.paic.ncbs.claim.config.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * @author: justinwu
 * @create 2025/5/15 10:42
 */
@Component
@FeignClient(name = "customapp", url = "${wof.customapp.url:http://wof-customapp.sssit.com:40001}", configuration = FeignConfiguration.class)
public interface WOFFeign {

    /**
     * 发起办事中心审批流
     * @param applicant
     * @param appkey
     * @param json
     * @return
     */
    @PostMapping(path = "/manage/processWorkFlow/submit?appKey={appkey}", consumes = {MediaType.APPLICATION_JSON_VALUE})
    String submitProcessWorkFlow(@RequestHeader("x-wesure-ename") String applicant, @PathVariable("appkey") String appkey, @RequestBody String json);
}
