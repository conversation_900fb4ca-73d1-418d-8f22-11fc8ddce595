package com.paic.ncbs.claim.model.vo.risk;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("风险详细信息")
public class RiskDetailVO{
	
	@ApiModelProperty("风险信息来源环节列表")
	private List<String> riskFromList;

	@ApiModelProperty("风险对象名称（人员姓名或单位名称）")
	private String objectName;

	@ApiModelProperty("风险对象类别（人员类别或单位类别，CLM_COMMON_PARAMETER.COLLECTION_CODE=AHCS_RISK_MAN_TYPE或AHCS_RISK_UNIT_TYPE）")
	private String objectType;

	@ApiModelProperty("人员性质（1-个人;2-团体）")
	private String personProperty;

	@ApiModelProperty("证件类型(CLM_COMMON_PARAMETER.COLLECTION_CODE=AHCS_RISK_ID_TYPE)")
	private List<String> certificateTypeList;

	@ApiModelProperty("证件编号")
	private List<String> certificateNoList;

	@ApiModelProperty("移动电话")
	private List<String> mobileNoList;

	@ApiModelProperty("固定电话")
	private List<String> fixedTelephoneList;

	@ApiModelProperty("风险类型(个人或团体：CLM_COMMON_PARAMETER.COLLECTION_CODE=AHCS_BLK_PERSON_TYPE或AHCS_BLK_ORG_TYPE)")
	private String type;

	@ApiModelProperty("原因详情")
	private String reasonDetails;

	@ApiModelProperty("风险类别（1-个人;2-团体）")
	private String riskClass;

	@ApiModelProperty("单位地址-省/直辖市CODE")
	private String provinceCode;

	@ApiModelProperty("单位地址-市CODE")
	private String cityCode;

	@ApiModelProperty("单位地址-区/县CODE")
	private String countyCode;

	@ApiModelProperty("单位详细地址")
	private String unitAddress;

	@ApiModelProperty("统一社会信用代码")
	private String socialCreditCode;

	public List<String> getRiskFromList() {
		return riskFromList;
	}

	public void setRiskFromList(List<String> riskFromList) {
		this.riskFromList = riskFromList;
	}

	public String getObjectName() {
		return objectName;
	}

	public void setObjectName(String objectName) {
		this.objectName = objectName;
	}

	public String getObjectType() {
		return objectType;
	}

	public void setObjectType(String objectType) {
		this.objectType = objectType;
	}

	public String getPersonProperty() {
		return personProperty;
	}

	public void setPersonProperty(String personProperty) {
		this.personProperty = personProperty;
	}

	public List<String> getCertificateTypeList() {
		return certificateTypeList;
	}

	public void setCertificateTypeList(List<String> certificateTypeList) {
		this.certificateTypeList = certificateTypeList;
	}

	public List<String> getCertificateNoList() {
		return certificateNoList;
	}

	public void setCertificateNoList(List<String> certificateNoList) {
		this.certificateNoList = certificateNoList;
	}

	public List<String> getMobileNoList() {
		return mobileNoList;
	}

	public void setMobileNoList(List<String> mobileNoList) {
		this.mobileNoList = mobileNoList;
	}

	public List<String> getFixedTelephoneList() {
		return fixedTelephoneList;
	}

	public void setFixedTelephoneList(List<String> fixedTelephoneList) {
		this.fixedTelephoneList = fixedTelephoneList;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getReasonDetails() {
		return reasonDetails;
	}

	public void setReasonDetails(String reasonDetails) {
		this.reasonDetails = reasonDetails;
	}

	public String getRiskClass() {
		return riskClass;
	}

	public void setRiskClass(String riskClass) {
		this.riskClass = riskClass;
	}

	public String getProvinceCode() {
		return provinceCode;
	}

	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}

	public String getCityCode() {
		return cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}

	public String getCountyCode() {
		return countyCode;
	}

	public void setCountyCode(String countyCode) {
		this.countyCode = countyCode;
	}

	public String getUnitAddress() {
		return unitAddress;
	}

	public void setUnitAddress(String unitAddress) {
		this.unitAddress = unitAddress;
	}

	public String getSocialCreditCode() {
		return socialCreditCode;
	}

	public void setSocialCreditCode(String socialCreditCode) {
		this.socialCreditCode = socialCreditCode;
	}

}
