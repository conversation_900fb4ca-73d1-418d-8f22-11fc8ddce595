package com.paic.ncbs.claim.dao.entity.ahcs;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.util.Date;

public class AhcsInsuredPersonExtEntity extends EntityDTO {

	private static final long serialVersionUID = 4490393265313881700L;

    private String idAhcsInsuredPersonExt;

    private String idAhcsInsuredPerson;

    private String bankAccount;

    private String bankCode;

    private String bankName;

    private String bankHeadquartersCode;

    private String vehicleLicenceCode;

    private String vehicleFrameNo;

    private String engineNo;

    private String flightNo;

    private Date flightDate;

    private String original;

    private String destination;

    private String transactionNo;

    public String getIdAhcsInsuredPersonExt() {
        return idAhcsInsuredPersonExt;
    }

    public void setIdAhcsInsuredPersonExt(String idAhcsInsuredPersonExt) {
        this.idAhcsInsuredPersonExt = idAhcsInsuredPersonExt == null ? null : idAhcsInsuredPersonExt.trim();
    }

    public String getIdAhcsInsuredPerson() {
        return idAhcsInsuredPerson;
    }

    public void setIdAhcsInsuredPerson(String idAhcsInsuredPerson) {
        this.idAhcsInsuredPerson = idAhcsInsuredPerson == null ? null : idAhcsInsuredPerson.trim();
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount == null ? null : bankAccount.trim();
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode == null ? null : bankCode.trim();
    }

    public String getBankHeadquartersCode() {
        return bankHeadquartersCode;
    }

    public void setBankHeadquartersCode(String bankHeadquartersCode) {
        this.bankHeadquartersCode = bankHeadquartersCode == null ? null : bankHeadquartersCode.trim();
    }

    public String getVehicleLicenceCode() {
        return vehicleLicenceCode;
    }

    public void setVehicleLicenceCode(String vehicleLicenceCode) {
        this.vehicleLicenceCode = vehicleLicenceCode == null ? null : vehicleLicenceCode.trim();
    }

    public String getVehicleFrameNo() {
        return vehicleFrameNo;
    }

    public void setVehicleFrameNo(String vehicleFrameNo) {
        this.vehicleFrameNo = vehicleFrameNo == null ? null : vehicleFrameNo.trim();
    }

    public String getEngineNo() {
        return engineNo;
    }

    public void setEngineNo(String engineNo) {
        this.engineNo = engineNo == null ? null : engineNo.trim();
    }

    public String getFlightNo() {
        return flightNo;
    }

    public void setFlightNo(String flightNo) {
        this.flightNo = flightNo == null ? null : flightNo.trim();
    }

    public Date getFlightDate() {
        return flightDate;
    }

    public void setFlightDate(Date flightDate) {
        this.flightDate = flightDate;
    }

    public String getOriginal() {
        return original;
    }

    public void setOriginal(String original) {
        this.original = original == null ? null : original.trim();
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination == null ? null : destination.trim();
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo == null ? null : transactionNo.trim();
    }

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}
}