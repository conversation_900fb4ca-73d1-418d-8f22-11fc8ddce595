/*
package com.paic.ncbs.claim.mq;

import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.pay.ThirdCompanyPaymentBackResultVO;
import com.paic.ncbs.claim.mq.producer.MqProducerClaimPaymentInformationService;
import com.paic.ncbs.claim.mq.producer.MqProducerPreClaimPaymentInformationService;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.other.MailSendService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
@RabbitListener(bindings = @QueueBinding(value = @Queue(value = "${mq.payNotice.queue}"),
        exchange = @Exchange(value = "${mq.payNotice.exchange}", type = ExchangeTypes.FANOUT)))
public class PayResultCustomer {

    @Autowired
    PaymentItemMapper paymentItemMapper;
    @Autowired
    AhcsPolicyInfoMapper ahcsPolicyInfoMapper;

    @Autowired
    BpmService bpmService;

    @Autowired
    TaskInfoService taskInfoService;
    @Autowired
    private MailSendService mailSendService;
    @Autowired
    private MqProducerPreClaimPaymentInformationService mqProducerPreClaimPaymentInformationService;
    @Autowired
    private MqProducerClaimPaymentInformationService mqProducerClaimPaymentInformationService;

    @RabbitHandler
    public void onMessage(Object message) {
        try{
 //            {"certitype":"E",
//            "certino":"xxxxxxxx",
//             "serialno":"1",
//             "unitname":"张三",
//             "payStatus":"2",
//              "payInfo":"支付成功",
//              "paymentDate":"2022-05-25",
//               "payValue":"100.00",
//               "sourcenotecode":"BC631012320090190020006-1-01"}
            Message message1 = JSONObject.parseObject(JSONObject.toJSONString(message), Message.class);
            log.info("接收到的实收实付通知消息：{}", new String(message1.getBody()));
            ThirdCompanyPaymentBackResultVO paymentBackResult = JSONObject.parseObject(new String(message1.getBody()), ThirdCompanyPaymentBackResultVO.class);
            log.info("PayResultCustomer-收到的消息:"+paymentBackResult);
            handlePayBack(paymentBackResult);
        }catch ( Exception e){
        }

    }

    public void handlePayBack(ThirdCompanyPaymentBackResultVO paymentBackResult) {
        // 计算书号 对应 clm_payment_item 的 compensate_no
        String certino = paymentBackResult.getCertino();
        // 领款人 对应clm_payment_item 的 CLIENT_NAME
        String unitname = paymentBackResult.getUnitname();
        // 第三方系统id 对应clm_payment_item 的 financial_id
        String sourcenotecode = paymentBackResult.getSourcenotecode();
        String serialno = paymentBackResult.getSerialno();
        // 支付日期
        String paymentDate = paymentBackResult.getPaymentDate();
        // 2成功，3失败，6退票
        String payStatus = paymentBackResult.getPayStatus();
        PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
        paymentItemDTO.setSerialNo(Integer.valueOf(serialno));
        paymentItemDTO.setClientName(unitname);
        paymentItemDTO.setCompensateNo(certino);
        // 按照这个维度应该只会查出来一条
        List<PaymentItemDTO> paymentItem = paymentItemMapper.getPaymentItem(paymentItemDTO);
        if (CollectionUtils.isEmpty(paymentItem) || paymentItem.size() > 1){
            return;
        }
        PaymentItemDTO dto = paymentItem.get(0);
        String reportNo = dto.getReportNo();
        Integer caseTimes = dto.getCaseTimes();
        if ("2".equals(payStatus)){
            dto.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_80);
            try {
                dto.setPayDate(DateUtils.parse2Date(paymentDate));
            } catch (ParseException e) {
            }
        }
        if ("3".equals(payStatus)){
            dto.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_70);
            try {
                dto.setPayBackDate(DateUtils.parse2Date(paymentDate));
            } catch (ParseException e) {
            }
        }
        if ("6".equals(payStatus)){
            dto.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_71);
            try {
                dto.setPayBackDate(DateUtils.parse2Date(paymentDate));
            } catch (ParseException e) {
            }
        }

        if ( !"2".equals(payStatus)){
            // 待支付修改
            TaskInfoDTO taskInfoDTO = taskInfoService.findLatestByReportNoAndBpmKey(reportNo, caseTimes, BpmConstants.OC_MANUAL_SETTLE);
            if (taskInfoDTO==null){
                // 预赔 的扔到案件池去
                String acceptDepartmentCode = ahcsPolicyInfoMapper.selectDepartmentCodeByReportNo(dto.getReportNo());
                bpmService.startProcessOc(reportNo,caseTimes, BpmConstants.OC_PAY_BACK_MODIFY,dto.getIdClmPaymentItem(),"",acceptDepartmentCode);
            } else {
                bpmService.startProcessOc(reportNo,caseTimes, BpmConstants.OC_PAY_BACK_MODIFY,dto.getIdClmPaymentItem(),taskInfoDTO.getAssigner(),taskInfoDTO.getDepartmentCode());
                dto.setUpdatedBy(taskInfoDTO.getAssigner());
                //发送支付修改邮件,收件人是理算人
//                mailSendService.sendCaseMail(dto.getReportNo(),dto.getUpdatedBy());
            }
        }
        dto.setPaymentAmount(new BigDecimal(paymentBackResult.getPayValue()));
        dto.setExtendInfo(paymentBackResult.getPayInfo());
        dto.setFinancialId(sourcenotecode);
        dto.setUpdatedDate(new Date());
        paymentItemMapper.updatePaymentItem(dto);
        // 根据计算书号 判断计算书下的 是否都已经支付成功 如果是 通知 老核心
        int noPay = paymentItemMapper.getNoPayItemByCompensateNo(certino);
        if (noPay == 0){
            if ("1".equals(dto.getClaimType())){
                // 核赔
                mqProducerClaimPaymentInformationService.syncClaimPaymentInformationLink(certino,dto.getPayDate());
            }
            if ("2".equals(dto.getClaimType())){
                // 预赔
                mqProducerPreClaimPaymentInformationService.syncPreClaimPaymentInformationLink(certino,dto.getPayDate());

            }
        }
    }
}
*/
