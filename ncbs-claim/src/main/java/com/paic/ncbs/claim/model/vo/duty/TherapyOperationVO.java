package com.paic.ncbs.claim.model.vo.duty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("手术名称-TherapyOperationVO")
public class TherapyOperationVO {

	@ApiModelProperty("手术编码")
	private String operationCode;

	@ApiModelProperty("手术名称")
	private String operationName;

	@ApiModelProperty("报案号")
	private String reportNo;

	@ApiModelProperty("手术编码类型")
	private String orgType;

	public String getOrgType() {
		return orgType;
	}

	public void setOrgType(String orgType) {
		this.orgType = orgType;
	}
	public String getReportNo() {
		return reportNo;
	}

	public void setReportNo(String reportNo) {
		this.reportNo = reportNo;
	}

	public String getOperationCode() {
		return operationCode;
	}

	public void setOperationCode(String operationCode) {
		this.operationCode = operationCode;
	}

	public String getOperationName() {
		return operationName;
	}

	public void setOperationName(String operationName) {
		this.operationName = operationName;
	}

}
