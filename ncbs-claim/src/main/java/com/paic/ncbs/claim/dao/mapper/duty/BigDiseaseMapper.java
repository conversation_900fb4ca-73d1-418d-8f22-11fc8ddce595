package com.paic.ncbs.claim.dao.mapper.duty;

import com.paic.ncbs.claim.model.dto.checkloss.BigDiseaseDTO;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import com.paic.ncbs.claim.model.vo.settle.SettleBigDiseaseVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface BigDiseaseMapper {

    public void saveBigDisease(BigDiseaseDTO bigDiseaseDTO);

    public void removeBigDisease(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId);

    void updateEffective(BigDiseaseDTO bigDiseaseDTO);

    public BigDiseaseDTO getBigDisease(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId, @Param("status") String status);

    public SettleBigDiseaseVO getSettleBigDisease(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId, @Param("status") String status);

    public BigDiseaseDTO getBigDiseaseDTO(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskId") String taskId);

    public BigDiseaseDTO getBigDiseaseByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") String caseTimes, @Param("taskId") String taskId, @Param("status") String status);

    public String getCriticalIllnessNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("taskId") String taskId);

    public String getBigDisaseDateByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    public BigDiseaseDTO getBigDiseaseDTOByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,
                                                    @Param("status") String status, @Param("taskId") String taskId);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);
}
