package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.convert.BeanConvert;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import com.paic.ncbs.claim.model.vo.report.LinkManVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

@ApiModel("联系人")
@Data
public class LinkManEntity extends EntityDTO {

    private static final long serialVersionUID = -2441534626221639908L;

    @ApiModelProperty("主键")
    private String idAhcsLinkMan;
    @ApiModelProperty("报案号")
    private String reportNo;
    @ApiModelProperty("赔付次数")
    private Short caseTimes;
    @ApiModelProperty("序号")
    private Short linkManNo;
    @ApiModelProperty("联系人姓名")
    private String linkManName;
    @ApiModelProperty("与被保险人关系")
    private String linkManRelation;
    @ApiModelProperty("联系人电话")
    private String linkManTelephone;
    @ApiModelProperty("短信发送 Y：发送 N：不发送")
    private String sendMessage;
    @ApiModelProperty("是否报案录入的联系人")
    private String isReport;
    @ApiModelProperty("申请人名字")
    private String applicantPerson;
    @ApiModelProperty("理赔申请人类型")
    private String applicantType;
    @ApiModelProperty("理赔申请人证件类型")
    private String certificateType;
    @ApiModelProperty("理赔申请人证件号")
    private String certificateNo;

    public void setCertificateType(String certificateType) {
        this.certificateType = certificateType == null ? null : certificateType.trim();
    }

    public void setCertificateNo(String certificateNo) {
        this.certificateNo = certificateNo == null ? null : certificateNo.trim();
    }

    public void setIdAhcsLinkMan(String idAhcsLinkMan) {
        this.idAhcsLinkMan = idAhcsLinkMan == null ? null : idAhcsLinkMan.trim();
    }

    public void setLinkManName(String linkManName) {
        this.linkManName = linkManName == null ? null : linkManName.trim();
    }

    public void setLinkManRelation(String linkManRelation) {
        this.linkManRelation = linkManRelation == null ? null : linkManRelation.trim();
    }

    public void setLinkManTelephone(String linkManTelephone) {
        this.linkManTelephone = linkManTelephone == null ? null : linkManTelephone.trim();
    }

    public void setSendMessage(String sendMessage) {
        this.sendMessage = sendMessage == null ? null : sendMessage.trim();
    }

    public LinkManVO convertToVo(){
        LinkManEntityConvert convert = new LinkManEntityConvert();
        return convert.convert(this);
    }

    private static class LinkManEntityConvert implements BeanConvert<LinkManEntity, LinkManVO> {

        @Override
        public LinkManVO convert(LinkManEntity linkManEntity) {
            LinkManVO linkManVO = new LinkManVO();
            BeanUtils.copyProperties(linkManEntity,linkManVO);
            return linkManVO;
        }
    }
}