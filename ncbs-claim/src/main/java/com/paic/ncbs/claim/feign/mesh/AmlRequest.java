package com.paic.ncbs.claim.feign.mesh;

import com.paic.ncbs.base.util.MeshSendUtils;
import com.paic.ncbs.claim.common.util.HttpClientUtil;
import com.paic.ncbs.claim.feign.AmlFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023-11-02
 * @description global反洗钱
 */
@Slf4j
@RefreshScope
@Component
public class AmlRequest {

	/**
	 * 调用mesh和feign开关, true:调用mesh
	 */
	@Value("${switch.mesh}")
	private Boolean switchMesh;

	@Value("${ncbs.aml.url}")
	private String domainUrl;

	@Autowired
	private AmlFeign amlFeign;

	/**
	 * 上报反洗钱可疑数据
	 *
	 * @param paraJson
	 * @return
	 */
	public String reportGlobalAmlTarget(String paraJson){
		String result;
		if (switchMesh) {
			log.info("");
            log.info("可疑数据推送反洗钱入参-mesh：{}", paraJson.toString());
			result = MeshSendUtils.post(domainUrl, paraJson);
			log.info("可疑数据推送反洗钱成功-mesh，出参：{}", result);
		} else {

			log.info("可疑数据推送反洗钱入参：{}", paraJson.toString());
			result = HttpClientUtil.doPost(domainUrl, paraJson);
			log.info("可疑数据推送反洗钱成功，出参：{}", result);
		}
		return result;
	}

}
