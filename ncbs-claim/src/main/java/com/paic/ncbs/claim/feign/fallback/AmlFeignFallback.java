package com.paic.ncbs.claim.feign.fallback;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.feign.AmlFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 调用反洗钱接口降级类
 * <AUTHOR>
 */
@Component
@Slf4j
public class AmlFeignFallback implements FallbackFactory<AmlFeign> {

     @Override
    public AmlFeign create(Throwable cause) {
        return param -> {
            log.error("AmlFeign.createTask error, param={}", JSON.toJSONString(param), cause);
            return null;
        };
    }
}
