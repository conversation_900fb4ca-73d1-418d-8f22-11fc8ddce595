package com.paic.ncbs.claim.model.vo.policy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("PolicyHistoryRequestVO-保单历史请求VO")
public class PolicyHistoryRequestVO {

	@ApiModelProperty(value = "报案号")
	private String reportNo;

	@ApiModelProperty(value = "赔付次数")
	private Integer caseTimes;

	@ApiModelProperty(value = "保单号")
	private String policyNo;

	@ApiModelProperty(value = "保单证书编号")
	private String policyCerNo;

	@ApiModelProperty(value = "客户编号")
	private String clientNo;

	public String getReportNo() {
		return reportNo;
	}

	public void setReportNo(String reportNo) {
		this.reportNo = reportNo;
	}

	public Integer getCaseTimes() {
		return caseTimes;
	}

	public void setCaseTimes(Integer caseTimes) {
		this.caseTimes = caseTimes;
	}

	public String getPolicyNo() {
		return policyNo;
	}

	public void setPolicyNo(String policyNo) {
		this.policyNo = policyNo;
	}

	public String getClientNo() {
		return clientNo;
	}

	public void setClientNo(String clientNo) {
		this.clientNo = clientNo;
	}

	public String getPolicyCerNo() {
		return policyCerNo;
	}

	public void setPolicyCerNo(String policyCerNo) {
		this.policyCerNo = policyCerNo;
	}

	@Override
	public String toString() {
		return "PolicyHistoryRequestVO [reportNo=" + reportNo + ", caseTimes=" + caseTimes + ", policyNo=" + policyNo + ", policyCerNo=" + policyCerNo
				+ ", clientNo=" + clientNo + "]";
	}
	
}
