package com.paic.ncbs.claim.feign.mesh;


import com.alibaba.fastjson.JSON;
import com.paic.ncbs.base.util.MeshSendUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.feign.NbsFeign;
import com.paic.ncbs.claim.model.dto.report.PolicyNoNbsQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Slf4j
@RefreshScope
@Component
public class NbsRequest {

    private static final String HTTP_REQUEST_PREFIX = "http://ncbs-nbs-http";

    /**
     * 调用mesh和feign开关, true:调用mesh
     */
    @Value("${switch.mesh}")
    private Boolean switchMesh;

    @Autowired
    private NbsFeign nbsFeign;

    /**
     * 查询订单信息
     * @param miniOrderNo
     * @return
     */
    public String getMiniOrderInfo(PolicyNoNbsQueryDTO miniOrderNo) {

        if (switchMesh) {
            String url = HTTP_REQUEST_PREFIX + "/nbs/mini-order/selectMiniOrderByMiniOrderNo";
            LogUtil.audit("NBS查询订单信息，url={}，入参={}",url, JSON.toJSONString(miniOrderNo));
            return MeshSendUtils.post(url, JSON.toJSONString(miniOrderNo));
        } else {
            LogUtil.audit("NBS查询订单信息，入参={}", JSON.toJSONString(miniOrderNo));
            return nbsFeign.getMiniOrderInfo(miniOrderNo);
        }
    }

    /**
     * 个人凭证下载
     * @param queryDTO
     * @return
     */
    public String queryEPolicy(PolicyNoNbsQueryDTO queryDTO) {

        queryDTO.setApplyType("6");
        if(switchMesh) {
            String url = HTTP_REQUEST_PREFIX + "/nbs/app/online/queryEPolicy";
            LogUtil.audit("NBS查询个人凭证下载，url={}，入参={}",url, JSON.toJSONString(queryDTO));
            return MeshSendUtils.post(url, JSON.toJSONString(queryDTO));
        } else {
            LogUtil.audit("NBS查询个人凭证下载，入参={}", JSON.toJSONString(queryDTO));
            return nbsFeign.queryEPolicy(queryDTO);
        }
    }

    /**
     * 分页获取保单下的订单信息
     * @param queryDTO
     * @return
     */
    public String getPoliyMinOrderList(PolicyNoNbsQueryDTO queryDTO) {
        Integer current = queryDTO.getPageNum();
        Integer size = queryDTO.getPageSize();
        String policyNo = queryDTO.getPolicyNo();
        String miniOrderNo = queryDTO.getMiniOrderNo();
        String serviceName = queryDTO.getServiceName();

        if (switchMesh) {
            String url = HTTP_REQUEST_PREFIX + "/nbs/mini-order/page";
            url =url+"?current ="+current+"&size="+size;
            if(StringUtils.isNotEmpty(policyNo)) {
                url = url +"&policyNo="+policyNo;
            }
            if(StringUtils.isNotEmpty(miniOrderNo)) {
                url = url+"&miniOrderNo="+miniOrderNo;
            }
            if(StringUtils.isNotEmpty(serviceName)) {
                url = url+ "&serviceName="+serviceName;
            }
            LogUtil.audit("NBS分页获取保单下的订单信息，url={}，入参={}",url, JSON.toJSONString(queryDTO));
            return MeshSendUtils.get(url);
        } else {
            LogUtil.audit("NBS分页获取保单下的订单信息，入参={}", JSON.toJSONString(queryDTO));
            return nbsFeign.getPoliyMinOrderList(current, size, policyNo, miniOrderNo,serviceName);
        }
    }


}
