package com.paic.ncbs.claim.dao.mapper.checkloss;

import com.paic.ncbs.claim.model.vo.settle.SettleVehiclDelayOtherVO;
import com.paic.ncbs.claim.model.dto.checkloss.VehiclDelayOtherDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface VehiclDelayOtherMapper {

    public void addVehiclDelayOther(VehiclDelayOtherDTO vehiclDelayOtherDTO);

    public void removeVehiclDelayOther(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("taskCode") String taskCode, @Param("channelProcessId") String channelProcessId);

    void updateEffective(VehiclDelayOtherDTO vehiclDelayOtherDTO);

    public VehiclDelayOtherDTO getVehiclDelayOther(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("status") String status, @Param("taskCode") String taskCode, @Param("channelProcessId") String channelProcessId);

    public SettleVehiclDelayOtherVO getSettleVehiclDelayOther(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("status") String status, @Param("taskCode") String taskCode, @Param("idAhcsChannelProcess") String idAhcsChannelProcess);

    public List<VehiclDelayOtherDTO> getVehiclDelayOtherList(@Param("idAhcsChannelProcess") String idAhcsChannelProcess, @Param("taskCode") String taskCode);

    public void addVehiclDelayOtherList(@Param("vehiclDelayOtherList") List<VehiclDelayOtherDTO> vehiclDelayOtherList,
                                        @Param("caseTimes") Integer caseTimes, @Param("userId") String userId, @Param("channelProcessId") String channelProcessId);

    public void updateVehiclDelayOther(VehiclDelayOtherDTO vehiclDelayOtherDTO);
}
