package com.paic.ncbs.claim.model.vo.pay;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

public class PaymentBackReasonVO {


	private String reportNo;
	private String caseNo;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date backDate;
	private BigDecimal paymentAmount;
	private String backType;
	private String Reason;
	/**
	 * 理赔支出类型
	 */
	private String clmPaymentTypeName;

	public String getReportNo() {
		return reportNo;
	}

	public void setReportNo(String reportNo) {
		this.reportNo = reportNo;
	}

	public String getCaseNo() {
		return caseNo;
	}

	public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}

	public Date getBackDate() {
		return backDate;
	}

	public void setBackDate(Date backDate) {
		this.backDate = backDate;
	}

	public BigDecimal getPaymentAmount() {
		return paymentAmount;
	}

	public void setPaymentAmount(BigDecimal paymentAmount) {
		this.paymentAmount = paymentAmount;
	}

	public String getBackType() {
		return backType;
	}

	public void setBackType(String backType) {
		this.backType = backType;
	}

	public String getReason() {
		return Reason;
	}

	public void setReason(String reason) {
		Reason = reason;
	}


    public String getClmPaymentTypeName() {
        return clmPaymentTypeName;
    }

    public void setClmPaymentTypeName(String clmPaymentTypeName) {
        this.clmPaymentTypeName = clmPaymentTypeName;
    }
}
