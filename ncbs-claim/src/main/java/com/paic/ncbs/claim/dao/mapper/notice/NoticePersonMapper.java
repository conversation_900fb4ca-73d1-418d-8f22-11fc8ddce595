package com.paic.ncbs.claim.dao.mapper.notice;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.model.dto.notice.NoticePersonDTO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;

/**
 * <p>
 * 消息提醒对象表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
public interface NoticePersonMapper extends BaseMapper<NoticePersonDTO> {

    void updatePersonStatus(@Param("id") String id, @Param("readStatus") String readStatus,@Param("userId") String userId);

    void deletePersonByDate(@Param("dateTimeD") LocalDate dateTimeD);
}
