package com.paic.ncbs.claim.feign;

import com.paic.ncbs.claim.model.dto.report.PolicyNoNbsQueryDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Component
@FeignClient(name = "ncbs-nbs")
public interface NbsFeign {
    /**
     * 根据小订单号查询保单
     * @param miniOrderNo
     * @return
     */
    @PostMapping(value="/nbs/mini-order/selectMiniOrderByMiniOrderNo")
    String getMiniOrderInfo(PolicyNoNbsQueryDTO miniOrderNo);

    /**
     * 查询电子凭证
     * @param queryDTO
     * @return
     */
    @PostMapping(value="/nbs/app/online/queryEPolicy")
    String queryEPolicy(PolicyNoNbsQueryDTO queryDTO);

    /**
     * 分页查询小订单信息
     * @return
     */
    @GetMapping(value="/nbs/mini-order/page")
    String getPoliyMinOrderList(@RequestParam(value = "current") Integer current,
                                @RequestParam(value = "size") Integer size,
                                @RequestParam(value = "policyNo", required = false) String policyNo,
                                @RequestParam(value = "miniOrderNo", required = false) String miniOrderNo,
                                @RequestParam(value = "serviceName", required = false) String serviceName);
}
