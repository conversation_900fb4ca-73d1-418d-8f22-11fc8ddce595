package com.paic.ncbs.claim.dao.mapper.report;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.report.AcceptRecordDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface AcceptRecordMapper extends BaseDao<AcceptRecordDTO> {

    void insertAcceptRecord(AcceptRecordDTO acceptRecordDTO);

    AcceptRecordDTO getAcceptRecord(@Param("reportNo") String reportNo,
                                    @Param("caseTimes") Integer caseTimes);

    List<AcceptRecordDTO> getAcceptRecordByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    void modifyAcceptRecordSuffice(AcceptRecordDTO acceptRecordDTO);

    AcceptRecordDTO getAcceptRecordByAsc(@Param("reportNo") String reportNo,
                                         @Param("caseTimes") Integer caseTimes);

}
