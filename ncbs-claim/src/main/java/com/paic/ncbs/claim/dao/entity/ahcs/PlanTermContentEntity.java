package com.paic.ncbs.claim.dao.entity.ahcs;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

public class PlanTermContentEntity extends EntityDTO {
    private String id;
    /**
     * 报案号
     */
    private String reportNo;

    /**
     * 赔付次数
     */
    private Integer caseTimes;
    /**
     * 险种分组id
     */
    private String riskGroupId;
    /**
     * 序号
     */
    private Integer sortNo;
    /**
     * 小条款编码
     */
    private String termCode;
    /**
     * 小条款名称
     */
    private String termName;
    /**
     * 小条款内容
     */
    private String termContent;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public Integer getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Integer caseTimes) {
        this.caseTimes = caseTimes;
    }

    public String getRiskGroupId() {
        return riskGroupId;
    }

    public void setRiskGroupId(String riskGroupId) {
        this.riskGroupId = riskGroupId;
    }

    public Integer getSortNo() {
        return sortNo;
    }

    public void setSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }

    public String getTermCode() {
        return termCode;
    }

    public void setTermCode(String termCode) {
        this.termCode = termCode;
    }

    public String getTermName() {
        return termName;
    }

    public void setTermName(String termName) {
        this.termName = termName;
    }

    public String getTermContent() {
        return termContent;
    }

    public void setTermContent(String termContent) {
        this.termContent = termContent;
    }
}
