/*
package com.paic.ncbs.claim.mq;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.MessagePostProcessor;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

*/
/**
 * <AUTHOR>
 * @since 2022/4/13
 *//*

@Slf4j
@Component
public class RabbitProducer {

    // @Resource
    // private RabbitTemplate rabbitTemplate;
    @Resource
    private ConnectionFactory connectionFactory;

    public RabbitTemplate getRabbitTemplate() {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        rabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
        rabbitTemplate.setMandatory(true);
        return rabbitTemplate;
    }

    final RabbitTemplate.ConfirmCallback confirmCallback = (correlationData, ack, cause) -> {
        if (ack) {
            log.info("发送成功：{}", correlationData.getId());
        } else {
        }
    };

    public void sendMessage(Object message, String exchange, String routingKey, Map<String, Object> properties,
                            RabbitTemplate.ConfirmCallback confirmCallback) {
        MessageHeaders mhs = new MessageHeaders(properties);
        Message<Object> msg = MessageBuilder.createMessage(message, mhs);

        RabbitTemplate rabbitTemplate = getRabbitTemplate();

        rabbitTemplate.setConfirmCallback(confirmCallback);

        CorrelationData correlationData = new CorrelationData(UUID.randomUUID().toString());

        MessagePostProcessor mpp = new MessagePostProcessor() {
            @Override
            public org.springframework.amqp.core.Message postProcessMessage(org.springframework.amqp.core.Message message)
                    throws AmqpException {
                log.info("--> post to do:" + message);
                return message;
            }
        };

        rabbitTemplate.convertAndSend(exchange, routingKey, msg, mpp, correlationData);
    }

    public void sendMessage(Object message, Map<String, Object> properties, String exchange, String routingKey) {
        sendMessage(message, exchange, routingKey, properties, confirmCallback);
    }

    */
/**
     * 发送mq消息
     *
     * @param message 消息
     * @param exchange exchange
     * @param routingKey routingKey
     * @param confirmCallback 确认消息的回调，用于确认消息是否被 broker 所收到
     *//*

    public void sendMessage(Object message, String exchange, String routingKey, RabbitTemplate.ConfirmCallback confirmCallback) {
        sendMessage(message, exchange, routingKey, new HashMap<>(), confirmCallback);
    }

}
*/
