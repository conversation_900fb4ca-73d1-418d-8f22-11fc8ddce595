package com.paic.ncbs.claim.model.vo.duty;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@ApiModel("重大疾病VO")
public class BigDiseaseVO {

	@ApiModelProperty("重大疾病主键")
	private String bigDiseaseId;

	@ApiModelProperty("报案号")
	private String reportNo;

	@ApiModelProperty("赔付次数")
	private Integer caseTimes;

	@ApiModelProperty("通道任务表主键")
	private String idAhcsChannelProcess;

	@ApiModelProperty("重大疾病编码")
	private String bigDiseaseCode;

	@ApiModelProperty("重大疾病其他描述")
	private String bigDiseaseOther;

	@ApiModelProperty("确诊时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date disaseDiagnosisDate;

	@ApiModelProperty("是否免健康告知(Y是，N否)")
	private String isInformHealth;

	@ApiModelProperty("是否与投保时身体状况一致(Y是，N否)")
	private String isSituationConsistent;

	@ApiModelProperty("既往病史")
	private String historyDisease;

	@ApiModelProperty("原发部位(clm_common_parameter  COLLECTION_CODE=AHCS_BODY_PARTS)")
	private String primarySite;

	@ApiModelProperty("原发部位(乳腺、宫颈、甲状腺、肺、前列腺、淋巴、血液、咽喉、其他)")
	private String primarySiteDesc;

	@ApiModelProperty("原发部位不明")
	private String primarySiteUnknown;

	@ApiModelProperty("原位癌")
	private String placeCancers;

	@ApiModelProperty("")
	private String moduleCode;
	
	public String getBigDiseaseId() {
		return bigDiseaseId;
	}
	
	public void setBigDiseaseId(String bigDiseaseId) {
		this.bigDiseaseId = bigDiseaseId;
	}
	
	public String getReportNo() {
		return reportNo;
	}
	
	public void setReportNo(String reportNo) {
		this.reportNo = reportNo;
	}
	
	public Integer getCaseTimes() {
		return caseTimes;
	}
	
	public void setCaseTimes(Integer caseTimes) {
		this.caseTimes = caseTimes;
	}
	
	public String getIdAhcsChannelProcess() {
		return idAhcsChannelProcess;
	}

	public void setIdAhcsChannelProcess(String idAhcsChannelProcess) {
		this.idAhcsChannelProcess = idAhcsChannelProcess;
	}

	public String getBigDiseaseCode() {
		return bigDiseaseCode;
	}
	
	public void setBigDiseaseCode(String bigDiseaseCode) {
		this.bigDiseaseCode = bigDiseaseCode;
	}
	
	public String getBigDiseaseOther() {
		return bigDiseaseOther;
	}

	public void setBigDiseaseOther(String bigDiseaseOther) {
		this.bigDiseaseOther = bigDiseaseOther;
	}

	public Date getDisaseDiagnosisDate() {
		return disaseDiagnosisDate;
	}

	public void setDisaseDiagnosisDate(Date disaseDiagnosisDate) {
		this.disaseDiagnosisDate = disaseDiagnosisDate;
	}

	public String getIsInformHealth() {
		return isInformHealth;
	}
	
	public void setIsInformHealth(String isInformHealth) {
		this.isInformHealth = isInformHealth;
	}
	
	public String getIsSituationConsistent() {
		return isSituationConsistent;
	}
	
	public void setIsSituationConsistent(String isSituationConsistent) {
		this.isSituationConsistent = isSituationConsistent;
	}
	
	public String getHistoryDisease() {
		return historyDisease;
	}
	
	public void setHistoryDisease(String historyDisease) {
		this.historyDisease = historyDisease;
	}
	
	public String getPrimarySite() {
		return primarySite;
	}
	
	public void setPrimarySite(String primarySite) {
		this.primarySite = primarySite;
	}
	
	public String getPrimarySiteDesc() {
		return primarySiteDesc;
	}
	
	public void setPrimarySiteDesc(String primarySiteDesc) {
		this.primarySiteDesc = primarySiteDesc;
	}
	
	public String getPrimarySiteUnknown() {
		return primarySiteUnknown;
	}
	
	public void setPrimarySiteUnknown(String primarySiteUnknown) {
		this.primarySiteUnknown = primarySiteUnknown;
	}
	
	public String getPlaceCancers() {
		return placeCancers;
	}
	
	public void setPlaceCancers(String placeCancers) {
		this.placeCancers = placeCancers;
	}

	public String getModuleCode() {
		return moduleCode;
	}

	public void setModuleCode(String moduleCode) {
		this.moduleCode = moduleCode;
	}
	
}
