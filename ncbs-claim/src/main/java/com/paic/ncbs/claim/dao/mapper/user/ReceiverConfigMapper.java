package com.paic.ncbs.claim.dao.mapper.user;

import com.paic.ncbs.claim.model.dto.user.ReceiverConfigDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface ReceiverConfigMapper {

    List<ReceiverConfigDTO> getReceiverList(ReceiverConfigDTO receiverConfigDTO);

    void updateReceiverList(@Param("list")List<ReceiverConfigDTO> receiverConfigList);

    void updateEmail(ReceiverConfigDTO receiverConfigDTO);

}
