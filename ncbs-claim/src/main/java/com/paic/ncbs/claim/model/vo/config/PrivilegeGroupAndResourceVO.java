package com.paic.ncbs.claim.model.vo.config;

import java.math.BigDecimal;

public class PrivilegeGroupAndResourceVO {


	private String idAhcsPrivilegeGroup;


	private String privilegeGroupName;
	

	private String privilegeType;
	

	private String resourceDetailName;


	private String idAhcsResourceDetailValue;


	private String resourceDetailType;


	private String resourceDetailCode;


	private String resourceDetailValue;


	private String resourceDetailMinValue;


	private String resourceDetailMaxValue;


	private BigDecimal sortNo;
	
	private Integer privilegeLevel;

	public String getIdAhcsPrivilegeGroup() {
		return idAhcsPrivilegeGroup;
	}

	public void setIdAhcsPrivilegeGroup(String idAhcsPrivilegeGroup) {
		this.idAhcsPrivilegeGroup = idAhcsPrivilegeGroup;
	}

	public String getPrivilegeGroupName() {
		return privilegeGroupName;
	}

	public String getPrivilegeType() {
		return privilegeType;
	}

	public void setPrivilegeType(String privilegeType) {
		this.privilegeType = privilegeType;
	}

	public void setPrivilegeGroupName(String privilegeGroupName) {
		this.privilegeGroupName = privilegeGroupName;
	}

	public String getResourceDetailName() {
		return resourceDetailName;
	}

	public void setResourceDetailName(String resourceDetailName) {
		this.resourceDetailName = resourceDetailName;
	}

	public String getIdAhcsResourceDetailValue() {
		return idAhcsResourceDetailValue;
	}

	public void setIdAhcsResourceDetailValue(String idAhcsResourceDetailValue) {
		this.idAhcsResourceDetailValue = idAhcsResourceDetailValue;
	}

	public String getResourceDetailType() {
		return resourceDetailType;
	}

	public void setResourceDetailType(String resourceDetailType) {
		this.resourceDetailType = resourceDetailType;
	}

	public String getResourceDetailMinValue() {
		return resourceDetailMinValue;
	}

	public void setResourceDetailMinValue(String resourceDetailMinValue) {
		this.resourceDetailMinValue = resourceDetailMinValue;
	}

	public String getResourceDetailMaxValue() {
		return resourceDetailMaxValue;
	}

	public void setResourceDetailMaxValue(String resourceDetailMaxValue) {
		this.resourceDetailMaxValue = resourceDetailMaxValue;
	}

	public String getResourceDetailValue() {
		return resourceDetailValue;
	}

	public void setResourceDetailValue(String resourceDetailValue) {
		this.resourceDetailValue = resourceDetailValue;
	}

	public String getResourceDetailCode() {
		return resourceDetailCode;
	}

	public void setResourceDetailCode(String resourceDetailCode) {
		this.resourceDetailCode = resourceDetailCode;
	}

	public BigDecimal getSortNo() {
		return sortNo;
	}

	public void setSortNo(BigDecimal sortNo) {
		this.sortNo = sortNo;
	}

	public Integer getPrivilegeLevel() {
		return privilegeLevel;
	}

	public void setPrivilegeLevel(Integer privilegeLevel) {
		this.privilegeLevel = privilegeLevel;
	}
}
