package com.paic.ncbs.claim.dao.mapper.pet;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.model.dto.pet.ClmsPetLossDTO;

import java.util.List;

/**
 * <p>
 * 宠物损失信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public interface ClmsPetLossMapper extends BaseMapper<ClmsPetLossDTO> {

     List<ClmsPetLossDTO> getClmsPetLoss(String reportNo, Integer caseTimes);

     void saveClmsPetLoss(ClmsPetLossDTO clmsPetLossDTO);

     void delClmsPetLoss(String reportNo, Integer caseTimes);

}
