package com.paic.ncbs.claim.dao.mapper.settle;

import com.paic.ncbs.claim.model.dto.restartcase.BillCopyDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillSpecialDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface MedicalBillSpecialMapper {

     void addBillSpecial(MedicalBillSpecialDTO billSpecial);

     void modifyBillSpecial(MedicalBillSpecialDTO billSpecial);

     MedicalBillSpecialDTO getBillSpecial(String idAhcsBillInfo);

     void removeBillSpecialList(@Param("idAhcsBillInfoList") List<String> idAhcsBillInfoList, @Param("userUM") String userUM);

     void addBillSpecialList(List<MedicalBillSpecialDTO> billSpecials);

     /**
      * 案件重开，数据拷贝
      * @param paramList
      */
     void copyForCaseReopen(List<BillCopyDTO> paramList);
}
