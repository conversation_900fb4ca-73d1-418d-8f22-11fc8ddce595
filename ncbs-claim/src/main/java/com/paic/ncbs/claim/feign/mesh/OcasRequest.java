package com.paic.ncbs.claim.feign.mesh;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.base.util.MeshSendUtils;
import com.paic.ncbs.claim.feign.OcasFeign;
import com.paic.ncbs.claim.model.dto.report.CopyPolicyQueryVO;
import com.paic.ncbs.claim.model.dto.report.RatingQueryVO;
import com.paic.ncbs.claim.model.dto.secondunderwriting.UwSendPosConclusionDTO;
import com.paic.ncbs.claim.model.vo.senconduw.SurrendApplyReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-11-02
 * @description 批单
 */
@Slf4j
@RefreshScope
@Component
public class OcasRequest {

	private static final String HTTP_REQUEST_PREFIX = "http://ncbs-pos-http";

	/**
	 * 调用mesh和feign开关, true:调用mesh
	 */
	@Value("${switch.mesh}")
	private Boolean switchMesh;
	/**
	 * 本地联调抄单时 ,@FeignClient 注解中增加 url = "http://*************:8080" 调试本地
	 */
	@Autowired
	private OcasFeign ocasFeign;

	public String getPolicyInfoByPolicyNoContract(CopyPolicyQueryVO vo){
		String url = HTTP_REQUEST_PREFIX + "/ocas/do/app/getPolicyInfoByPolicyNo/contract";
		if (switchMesh){
			return MeshSendUtils.post(url, JSON.toJSONString(vo));
		}else {
			return ocasFeign.getPolicyInfoByPolicyNoContract(vo);
		}
	}

	public String getPolicyInfoByPolicyNo(Map<String,String> param){
		String url = HTTP_REQUEST_PREFIX + "/ocas/do/app/queryPolicyInfoAction/getPolicyInfoByPolicyNo";
		if (switchMesh){
			return MeshSendUtils.post(url, JSON.toJSONString(param));
		}else {
			return ocasFeign.getPolicyInfoByPolicyNo(param);
		}
	}

	public String getDocumentByPolicyNo(Map<String,String> param){
		String url = HTTP_REQUEST_PREFIX + "/ocas/do/getDocumentByPolicyNo";
		if (switchMesh){
			return MeshSendUtils.post(url, JSON.toJSONString(param));
		}else {
			return ocasFeign.getDocumentByPolicyNo(param);
		}
	}

	public String getEdrApplyList(Map<String,Object> param){
		String url = HTTP_REQUEST_PREFIX + "/ocas/do/app/edrApplyInfo/getEdrApplyList";
		if (switchMesh){
			return MeshSendUtils.post(url, JSON.toJSONString(param));
		}else {
			return ocasFeign.getEdrApplyList(param);
		}
	}

	public String getPremiumByPolicyNo(Map<String,String> param){
		String url = HTTP_REQUEST_PREFIX + "/ocas/do/policyList/queryPolicyPremiumInfo";
		if (switchMesh){
			return MeshSendUtils.post(url, JSON.toJSONString(param));
		}else {
			return ocasFeign.getPremiumByPolicyNo(param);
		}
	}
	public String queryEdrApplyHistoryList(Map<String,Object> param){
		String url = HTTP_REQUEST_PREFIX + "/ocas/do/app/commonquery/queryEdrApplyHistoryList";
		if (switchMesh){
			return MeshSendUtils.post(url, JSON.toJSONString(param));
		}else {
			return ocasFeign.queryEdrApplyHistoryList(param);
		}
	}

	public String queryEPolicy(Map<String,Object> param){
		String url = HTTP_REQUEST_PREFIX + "/ocas/do/channel/queryEPolicy";
		if (switchMesh){
			return MeshSendUtils.post(url, JSON.toJSONString(param));
		}else {
			return ocasFeign.queryEPolicy(param);
		}
	}

	/**
	 * 查询客户类型(CRM)
	 */
	public String searchCustomerRating(RatingQueryVO queryVO){
		String url = HTTP_REQUEST_PREFIX + "/ocas/do/clientManage/searchCustomerRating";
		if (switchMesh){
			return MeshSendUtils.post(url, JSON.toJSONString(queryVO));
		}else {
			return ocasFeign.searchCustomerRating(queryVO);
		}
	}

	public String isGarcePerid(CopyPolicyQueryVO vo){
		String url = HTTP_REQUEST_PREFIX + "/ocas/do/app/contract/isGarcePerid";
		if (switchMesh){
			return MeshSendUtils.post(url, JSON.toJSONString(vo));
		}else {
			return ocasFeign.isGarcePerid(vo);
		}
	}

	public String queryByClientNo(Map<String,String> param){
		String url = HTTP_REQUEST_PREFIX + "/ocas/do/app/policyInfo/queryByClientNo";
		if (switchMesh){
			return MeshSendUtils.post(url, JSON.toJSONString(param));
		}else {
			return ocasFeign.queryByClientNo(param);
		}
	}

	public String calculateAgree(Map<String,String> param){
		String url = HTTP_REQUEST_PREFIX + "/ocas/do/app/edrApplySurrender/calculateAgree";
		if (switchMesh){
			return MeshSendUtils.post(url, JSON.toJSONString(param));
		}else {
			return ocasFeign.calculateAgree(param);
		}
	}

	public String applyEndorse(SurrendApplyReq surrendApplyReq){
		String url = HTTP_REQUEST_PREFIX + "/ocas/do/app/edrApplySurrender/applyEndorse";
		if (switchMesh){
			return MeshSendUtils.post(url, JSON.toJSONString(surrendApplyReq));
		}else {
			return ocasFeign.applyEndorse(surrendApplyReq);
		}
	}

	public String updateUnderwritingConclusion(UwSendPosConclusionDTO param){
		String url = HTTP_REQUEST_PREFIX + "/ocas/do/app/updateUnderwritingConclusion";
		if (switchMesh){
			return MeshSendUtils.post(url, JSON.toJSONString(param));
		}else {
			return ocasFeign.updateUnderwritingConclusion(param);
		}
	}

	public String queryEPolicyClaim(Map<String, Object> param){
		String url = HTTP_REQUEST_PREFIX + "/ocas/do/channel/queryEPolicyClaim";
		if (switchMesh){
			return MeshSendUtils.post(url, JSON.toJSONString(param));
		}else {
			log.info("ocasRequest.queryEPolicyClaim, param={}", JSON.toJSONString(param));
			String result = ocasFeign.queryEPolicyClaim(param);
			log.info("ocasRequest.queryEPolicyClaim, result={}", result);
			return result;
		}
	}
}
