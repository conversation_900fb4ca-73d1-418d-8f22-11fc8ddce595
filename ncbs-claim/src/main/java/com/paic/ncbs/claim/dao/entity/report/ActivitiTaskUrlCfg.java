package com.paic.ncbs.claim.dao.entity.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "任务配置项")
public class ActivitiTaskUrlCfg {

    private String taskDefinitionBpmCode;

    @ApiModelProperty(value = "任务节点代码")
    private String taskDefinitionCode;

    private String taskTabId;

    private String taskUrl;

    private String taskListColumnName;

    private String taskListColumnKey;

    private String taskListColumnSize;

    private String taskGroupLabel;

    @ApiModelProperty(value = "任务名")
    private String taskDefinitionName;

    private String taskParam;

    private String isWorkTask;

    @ApiModelProperty(value = "数据源")
    private String dataSource;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "页码")
    private int currentPage = 1;

    @ApiModelProperty(value = "每页条数")
    private int pageSize = 10;

    private String isNoTempSave;
    @ApiModelProperty(value = "是否单证齐全（Y/N）")
    private String isSuffice;

    @ApiModelProperty(value = "是否自动注册")
    private String isAutoRegister;

    private String isNoRegister;

    private String orderName;

    private String order;

    public String getTaskDefinitionBpmCode() {
        return taskDefinitionBpmCode;
    }

    public void setTaskDefinitionBpmCode(String taskDefinitionBpmCode) {
        this.taskDefinitionBpmCode = taskDefinitionBpmCode;
    }

    public String getTaskDefinitionCode() {
        return taskDefinitionCode;
    }

    public void setTaskDefinitionCode(String taskDefinitionCode) {
        this.taskDefinitionCode = taskDefinitionCode;
    }

    public String getTaskTabId() {
        return taskTabId;
    }

    public void setTaskTabId(String taskTabId) {
        this.taskTabId = taskTabId;
    }

    public String getTaskUrl() {
        return taskUrl;
    }

    public void setTaskUrl(String taskUrl) {
        this.taskUrl = taskUrl;
    }

    public String getTaskListColumnName() {
        return taskListColumnName;
    }

    public void setTaskListColumnName(String taskListColumnName) {
        this.taskListColumnName = taskListColumnName;
    }

    public String getTaskListColumnKey() {
        return taskListColumnKey;
    }

    public void setTaskListColumnKey(String taskListColumnKey) {
        this.taskListColumnKey = taskListColumnKey;
    }

    public String getTaskListColumnSize() {
        return taskListColumnSize;
    }

    public void setTaskListColumnSize(String taskListColumnSize) {
        this.taskListColumnSize = taskListColumnSize;
    }

    public String getTaskGroupLabel() {
        return taskGroupLabel;
    }

    public void setTaskGroupLabel(String taskGroupLabel) {
        this.taskGroupLabel = taskGroupLabel;
    }

    public String getTaskDefinitionName() {
        return taskDefinitionName;
    }

    public void setTaskDefinitionName(String taskDefinitionName) {
        this.taskDefinitionName = taskDefinitionName;
    }

    public String getTaskParam() {
        return taskParam;
    }

    public void setTaskParam(String taskParam) {
        this.taskParam = taskParam;
    }

    public String getIsWorkTask() {
        return isWorkTask;
    }

    public void setIsWorkTask(String isWorkTask) {
        this.isWorkTask = isWorkTask;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public int getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(int currentPage) {
        this.currentPage = currentPage;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getIsNoTempSave() {
        return isNoTempSave;
    }

    public void setIsNoTempSave(String isNoTempSave) {
        this.isNoTempSave = isNoTempSave;
    }

    public String getIsSuffice() {
        return isSuffice;
    }

    public void setIsSuffice(String isSuffice) {
        this.isSuffice = isSuffice;
    }

    public String getIsAutoRegister() {
        return isAutoRegister;
    }

    public void setIsAutoRegister(String isAutoRegister) {
        this.isAutoRegister = isAutoRegister;
    }

    public String getIsNoRegister() {
        return isNoRegister;
    }

    public void setIsNoRegister(String isNoRegister) {
        this.isNoRegister = isNoRegister;
    }

    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    @Override
    public String toString() {
        return "ActivitiTaskUrlCfgDTO [taskDefinitionBpmCode=" + taskDefinitionBpmCode + ", taskDefinitionCode="
                + taskDefinitionCode + ", taskTabId=" + taskTabId + ", taskUrl=" + taskUrl + ", taskListColumnName="
                + taskListColumnName + ", taskListColumnKey=" + taskListColumnKey + ", taskListColumnSize="
                + taskListColumnSize + ", taskGroupLabel=" + taskGroupLabel + ", taskDefinitionName="
                + taskDefinitionName + ", taskParam=" + taskParam + ", isWorkTask=" + isWorkTask + ", remarks="
                + remarks + ", currentPage=" + currentPage + ", pageSize=" + pageSize + "]";
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

}
