package com.paic.ncbs.claim.model.vo.settle;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.dto.settle.CoinsureInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.EndorsementDTO;
import com.paic.ncbs.claim.model.vo.checkloss.LossReduceVO;
import com.paic.ncbs.claim.model.dto.verify.VerifyDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("核赔")
@Data
public class VerifyInfoVO extends EntityDTO {

    private static final long serialVersionUID = -1447676314012723972L;
    @ApiModelProperty("报案号")
    private String reportNo;
    @ApiModelProperty("赔付批次号")
    private String idAhcsBatch;
    @ApiModelProperty("赔付次数")
    private Integer caseTimes;
    @ApiModelProperty("批单信息")
    private EndorsementDTO endorsement;
    @ApiModelProperty("核赔信息")
    private VerifyDTO verify;
    @ApiModelProperty("减损信息")
    private LossReduceVO lossReduce;
    @ApiModelProperty("支付项目集合")
    private List<PaymentItemComData> paymentItem;
    @ApiModelProperty("差错代码ahcs_mistake_define")
    private List<String> mistakeCodeList;
    @ApiModelProperty("共保信息")
    List<CoinsureInfoDTO> coinsureInfos;

    private String recordTaskId;

    private String verifyUm;
    private String taskId;

    private String indemnityModel;
    /**
     * 上级审批人
     */
    private String selectedUserId;

}
