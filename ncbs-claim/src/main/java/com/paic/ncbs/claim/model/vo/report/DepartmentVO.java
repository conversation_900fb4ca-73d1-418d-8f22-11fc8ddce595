package com.paic.ncbs.claim.model.vo.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ApiModel("机构信息")
public class DepartmentVO {
    @ApiModelProperty("机构编码")
    private String departmentCode;
    @ApiModelProperty("机构名称")
    private String departmentChineseName;
    @ApiModelProperty("机构简称")
    private String departmentAbbrName;
    @ApiModelProperty("承保机构等级")
    private int departmentLevel;
    @ApiModelProperty("上级机构编码")
    private String upperDepartmentCode;
    @ApiModelProperty("上级机构")
    private DepartmentVO parentDepartment;
    @ApiModelProperty("子机构")
    private List<DepartmentVO> childDepartmentVO;

}
