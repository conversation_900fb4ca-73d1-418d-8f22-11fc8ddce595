package com.paic.ncbs.claim.dao.mapper.chase;


import com.paic.ncbs.claim.model.dto.chase.ChaseApplyDTO;
import com.paic.ncbs.claim.model.vo.chase.ChaseTaskInfoVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface ChaseMapper {

	void mergeChaseApply(@Param("chaseApply") ChaseApplyDTO chaseApply);


	ChaseApplyDTO getChaseApply(@Param("reportNo") String reportNo,
                                @Param("caseTimes") Integer caseTimes, @Param("applyTimes") Integer applyTimes);


	Integer getApplyTimes(@Param("reportNo") String reportNo,
                          @Param("caseTimes") Integer caseTimes);


	String getChaseApplyId(@Param("reportNo") String reportNo,
                           @Param("caseTimes") Integer caseTimes, @Param("applyTimes") Integer applyTimes);

	List<ChaseApplyDTO> listChaseApply(@Param("reportNo") String reportNo,
                                       @Param("caseTimes") Integer caseTimes);
	

	Integer getApplyTaskCounts(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);
	

	Integer getChaseReportCount(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);
	

	List<ChaseTaskInfoVO> getChaseTaskInfoVO(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);
	

	ChaseApplyDTO getPassChaseApply(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);
	

	String getChaseApplyReasonByReport(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

}
