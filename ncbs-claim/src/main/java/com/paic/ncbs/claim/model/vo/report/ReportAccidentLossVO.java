package com.paic.ncbs.claim.model.vo.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@ApiModel(description = "财产损失信息表VO")
public class ReportAccidentLossVO {

    @ApiModelProperty(value = "报案号")
    private String reportNo;

    @ApiModelProperty(value = "财产损失类型")
    private String[] lossType;

    @ApiModelProperty(value = "费用预估")
    private BigDecimal costEstimate;

    @ApiModelProperty(value = "事故原因")
    private String accidentReason;

    @ApiModelProperty(value = "事故原因描述")
    private String accidentReasonDesc;

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String[] getLossType() {
        return lossType;
    }

    public void setLossType(String[] lossType) {
        this.lossType = lossType;
    }

    public BigDecimal getCostEstimate() {
        return costEstimate;
    }

    public void setCostEstimate(BigDecimal costEstimate) {
        this.costEstimate = costEstimate;
    }

    public String getAccidentReason() {
        return accidentReason;
    }

    public void setAccidentReason(String accidentReason) {
        this.accidentReason = accidentReason;
    }

    public String getAccidentReasonDesc() {
        return accidentReasonDesc;
    }

    public void setAccidentReasonDesc(String accidentReasonDesc) {
        this.accidentReasonDesc = accidentReasonDesc;
    }
}
