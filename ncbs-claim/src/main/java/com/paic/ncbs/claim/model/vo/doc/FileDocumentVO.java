package com.paic.ncbs.claim.model.vo.doc;

import java.io.Serializable;
import java.util.Date;

public class FileDocumentVO implements Serializable {
    private static final long serialVersionUID = 7264251791213163636L;
    private String createdBy;
    private String updatedBy;
    private String documentGroupItemsId;
    private String documentGroupId;
    private String documentId;
    private String documentOrder;
    private String documentStatus;
    private String remark;
    private String documentType;
    private String documentName;
    private String originName;
    private String documentFormat;
    private String documentDesc;
    private String uploadPersonnel;
    private Date uploadDate;
    private String uploadPath;
    private String documentClass;
    private String documentSize;
    private String documentNo;
    private Date uploadIsDate;
    private String status;
    private String uploadFlag;
    private String queueId;
    private String pageCount;
    private Date generatedDate;
    private String longitude;
    private String latitude;
    private String documentSource;
    private String documentProperty;
    private Date certificateBeginDate;
    private Date certificateEndDate;
    private String networkFlag;
    private String storageType;
    private String bucketName;

    public FileDocumentVO() {
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getDocumentGroupItemsId() {
        return this.documentGroupItemsId;
    }

    public void setDocumentGroupItemsId(String documentGroupItemsId) {
        this.documentGroupItemsId = documentGroupItemsId;
    }

    public String getDocumentGroupId() {
        return this.documentGroupId;
    }

    public void setDocumentGroupId(String documentGroupId) {
        this.documentGroupId = documentGroupId;
    }

    public String getDocumentId() {
        return this.documentId;
    }

    public void setDocumentId(String documentId) {
        this.documentId = documentId;
    }

    public String getDocumentOrder() {
        return this.documentOrder;
    }

    public void setDocumentOrder(String documentOrder) {
        this.documentOrder = documentOrder;
    }

    public String getDocumentStatus() {
        return this.documentStatus;
    }

    public void setDocumentStatus(String documentStatus) {
        this.documentStatus = documentStatus;
    }

    public String getRemark() {
        return this.remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getDocumentType() {
        return this.documentType;
    }

    public void setDocumentType(String documentType) {
        this.documentType = documentType;
    }

    public String getDocumentName() {
        return this.documentName;
    }

    public void setDocumentName(String documentName) {
        this.documentName = documentName;
    }

    public String getOriginName() {
        return this.originName;
    }

    public void setOriginName(String originName) {
        this.originName = originName;
    }

    public String getDocumentFormat() {
        return this.documentFormat;
    }

    public void setDocumentFormat(String documentFormat) {
        this.documentFormat = documentFormat;
    }

    public String getDocumentDesc() {
        return this.documentDesc;
    }

    public void setDocumentDesc(String documentDesc) {
        this.documentDesc = documentDesc;
    }

    public String getUploadPersonnel() {
        return this.uploadPersonnel;
    }

    public void setUploadPersonnel(String uploadPersonnel) {
        this.uploadPersonnel = uploadPersonnel;
    }

    public Date getUploadDate() {
        return this.uploadDate;
    }

    public void setUploadDate(Date uploadDate) {
        this.uploadDate = uploadDate;
    }

    public String getUploadPath() {
        return this.uploadPath;
    }

    public void setUploadPath(String uploadPath) {
        this.uploadPath = uploadPath;
    }

    public String getDocumentClass() {
        return this.documentClass;
    }

    public void setDocumentClass(String documentClass) {
        this.documentClass = documentClass;
    }

    public String getDocumentSize() {
        return this.documentSize;
    }

    public void setDocumentSize(String documentSize) {
        this.documentSize = documentSize;
    }

    public String getDocumentNo() {
        return this.documentNo;
    }

    public void setDocumentNo(String documentNo) {
        this.documentNo = documentNo;
    }

    public Date getUploadIsDate() {
        return this.uploadIsDate;
    }

    public void setUploadIsDate(Date uploadIsDate) {
        this.uploadIsDate = uploadIsDate;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUploadFlag() {
        return this.uploadFlag;
    }

    public void setUploadFlag(String uploadFlag) {
        this.uploadFlag = uploadFlag;
    }

    public String getQueueId() {
        return this.queueId;
    }

    public void setQueueId(String queueId) {
        this.queueId = queueId;
    }

    public String getPageCount() {
        return this.pageCount;
    }

    public void setPageCount(String pageCount) {
        this.pageCount = pageCount;
    }

    public Date getGeneratedDate() {
        return this.generatedDate;
    }

    public void setGeneratedDate(Date generatedDate) {
        this.generatedDate = generatedDate;
    }

    public String getLongitude() {
        return this.longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return this.latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getDocumentSource() {
        return this.documentSource;
    }

    public void setDocumentSource(String documentSource) {
        this.documentSource = documentSource;
    }

    public String getDocumentProperty() {
        return this.documentProperty;
    }

    public void setDocumentProperty(String documentProperty) {
        this.documentProperty = documentProperty;
    }

    public Date getCertificateBeginDate() {
        return this.certificateBeginDate;
    }

    public void setCertificateBeginDate(Date certificateBeginDate) {
        this.certificateBeginDate = certificateBeginDate;
    }

    public Date getCertificateEndDate() {
        return this.certificateEndDate;
    }

    public void setCertificateEndDate(Date certificateEndDate) {
        this.certificateEndDate = certificateEndDate;
    }

    public String getNetworkFlag() {
        return this.networkFlag;
    }

    public void setNetworkFlag(String networkFlag) {
        this.networkFlag = networkFlag;
    }

    public String getStorageType() {
        return this.storageType;
    }

    public void setStorageType(String storageType) {
        this.storageType = storageType;
    }

    public String getBucketName() {
        return this.bucketName;
    }

    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }
}

