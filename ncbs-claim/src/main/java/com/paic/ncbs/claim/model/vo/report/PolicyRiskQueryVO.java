package com.paic.ncbs.claim.model.vo.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.common.page.Pager;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.Map;

@Data
public class PolicyRiskQueryVO {

    // 保单号
    private String policyNo;

    // 标的姓名
    private String riskSubPropName;
    // 证件类型
    private String certificateType;
    // 证件号码
    private String certificateNo;
    // 地址
    private String address;
    // 方案号
    private String riskGroupNo;
    // 分页
    private Pager pager;
    //出险时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @ApiModelProperty(value = "出险时间")
    private Date accidentDate;
    // 标的组类型
    private String riskGroupType;
    // 参数
    private Map<String, Object> paramMap;

        
}
