package com.paic.ncbs.claim.dao.mapper.secondunderwriting;

import com.paic.ncbs.claim.dao.entity.clms.ClmsSeconduwPolicyConclusionEntity;
import com.paic.ncbs.claim.model.vo.senconduw.ClmsSeconduwPolicyConclusionVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 理赔二核保单层核保结论表(ClmsSeconduwPolicyConclusionEntity)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-14 14:07:14
 */
public interface ClmsSeconduwPolicyConclusionMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ClmsSeconduwPolicyConclusionEntity queryById(String id);
    
    /**
     * 通过报案号查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ClmsSeconduwPolicyConclusionEntity queryByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes);

    /**
     * 新增数据
     *
     * @param clmsSeconduwPolicyConclusionEntity 实例对象
     * @return 影响行数
     */
    int insert(ClmsSeconduwPolicyConclusionEntity clmsSeconduwPolicyConclusionEntity);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<ClmsSeconduwPolicyConclusionEntity> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ClmsSeconduwPolicyConclusionEntity> entities);

    /**
     * 修改数据
     *
     * @param clmsSeconduwPolicyConclusionEntity 实例对象
     * @return 影响行数
     */
    int update(ClmsSeconduwPolicyConclusionEntity clmsSeconduwPolicyConclusionEntity);
    
    /**
     * 根据报案号修改数据
     *
     * @param reportNo 报案号， caseTimes 赔付次数
     * @return 影响行数
     */
    int updateByReportNo( @Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

    /**
     * 根据理赔二核申请表主键查询
     * @param idClmsSecondUnderwriting
     * @return
     */
    List<ClmsSeconduwPolicyConclusionVO> getPolicyConclusionVOList(String idClmsSecondUnderwriting);
}

