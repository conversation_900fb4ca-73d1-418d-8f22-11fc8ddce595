package com.paic.ncbs.claim.dao.entity.ahcs;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.math.BigDecimal;

/**
 * 责任明细
 */
public class AhcsPolicyDutyDetailEntity extends EntityDTO {

	private static final long serialVersionUID = 1896485504817907526L;

    private String idAhcsPolicyDutyDetail;

    private String idAhcsPolicyDuty;

    private String dutyDetailName;

    private String dutyDetailCode;

    private String dutyDetailtype;

    private BigDecimal dutyAmount;

    private String noclaimProperty;

    private BigDecimal noclaimAmount;

    private Integer noclaimDays;

    private Integer noclaimTimes;

    private String amountType;

    private Integer observedDays;

    private BigDecimal claimProportion;

    private BigDecimal allowanceEveryday;

    private BigDecimal detailLimitAmount;

    private Integer allowanceDaysLimit;

    private String secondType;

    private String orgDutyDetailCode;

    private String orgDutyDetailName;

    /**
     * 固定限额
     */
    private BigDecimal fixedLimit;



    public String getDutyDetailtype() {
        return dutyDetailtype;
    }

    public void setDutyDetailtype(String dutyDetailtype) {
        this.dutyDetailtype = dutyDetailtype;
    }

    public String getIdAhcsPolicyDutyDetail() {
        return idAhcsPolicyDutyDetail;
    }

    public void setIdAhcsPolicyDutyDetail(String idAhcsPolicyDutyDetail) {
        this.idAhcsPolicyDutyDetail = idAhcsPolicyDutyDetail == null ? null : idAhcsPolicyDutyDetail.trim();
    }

    public String getIdAhcsPolicyDuty() {
        return idAhcsPolicyDuty;
    }

    public void setIdAhcsPolicyDuty(String idAhcsPolicyDuty) {
        this.idAhcsPolicyDuty = idAhcsPolicyDuty == null ? null : idAhcsPolicyDuty.trim();
    }

    public String getDutyDetailName() {
        return dutyDetailName;
    }

    public void setDutyDetailName(String dutyDetailName) {
        this.dutyDetailName = dutyDetailName == null ? null : dutyDetailName.trim();
    }

    public String getDutyDetailCode() {
        return dutyDetailCode;
    }

    public void setDutyDetailCode(String dutyDetailCode) {
        this.dutyDetailCode = dutyDetailCode == null ? null : dutyDetailCode.trim();
    }

    public BigDecimal getDutyAmount() {
        return dutyAmount;
    }

    public void setDutyAmount(BigDecimal dutyAmount) {
        this.dutyAmount = dutyAmount;
    }

    public String getNoclaimProperty() {
        return noclaimProperty;
    }

    public void setNoclaimProperty(String noclaimProperty) {
        this.noclaimProperty = noclaimProperty == null ? null : noclaimProperty.trim();
    }

    public BigDecimal getNoclaimAmount() {
        return noclaimAmount;
    }

    public void setNoclaimAmount(BigDecimal noclaimAmount) {
        this.noclaimAmount = noclaimAmount;
    }

    public Integer getNoclaimDays() {
        return noclaimDays;
    }

    public void setNoclaimDays(Integer noclaimDays) {
        this.noclaimDays = noclaimDays;
    }

    public Integer getNoclaimTimes() {
        return noclaimTimes;
    }

    public void setNoclaimTimes(Integer noclaimTimes) {
        this.noclaimTimes = noclaimTimes;
    }

    public String getAmountType() {
        return amountType;
    }

    public void setAmountType(String amountType) {
        this.amountType = amountType == null ? null : amountType.trim();
    }

    public Integer getObservedDays() {
        return observedDays;
    }

    public void setObservedDays(Integer observedDays) {
        this.observedDays = observedDays;
    }

    public BigDecimal getClaimProportion() {
        return claimProportion;
    }

    public void setClaimProportion(BigDecimal claimProportion) {
        this.claimProportion = claimProportion;
    }

    public BigDecimal getAllowanceEveryday() {
        return allowanceEveryday;
    }

    public void setAllowanceEveryday(BigDecimal allowanceEveryday) {
        this.allowanceEveryday = allowanceEveryday;
    }

    public Integer getAllowanceDaysLimit() {
        return allowanceDaysLimit;
    }

    public void setAllowanceDaysLimit(Integer allowanceDaysLimit) {
        this.allowanceDaysLimit = allowanceDaysLimit;
    }

    public String getSecondType() {
        return secondType;
    }

    public void setSecondType(String secondType) {
        this.secondType = secondType == null ? null : secondType.trim();
    }

	public String getOrgDutyDetailCode() {
		return orgDutyDetailCode;
	}

	public void setOrgDutyDetailCode(String orgDutyDetailCode) {
		this.orgDutyDetailCode = orgDutyDetailCode;
	}

	public String getOrgDutyDetailName() {
		return orgDutyDetailName;
	}

	public void setOrgDutyDetailName(String orgDutyDetailName) {
		this.orgDutyDetailName = orgDutyDetailName;
	}

    public BigDecimal getDetailLimitAmount() {
        return detailLimitAmount;
    }

    public void setDetailLimitAmount(BigDecimal detailLimitAmount) {
        this.detailLimitAmount = detailLimitAmount;
    }

    public BigDecimal getFixedLimit() {
        return fixedLimit;
    }

    public void setFixedLimit(BigDecimal fixedLimit) {
        this.fixedLimit = fixedLimit;
    }
}