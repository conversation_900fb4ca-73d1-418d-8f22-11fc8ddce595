package com.paic.ncbs.claim.model.vo.trace;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 通过ins-framework-mybatis工具自动生成，请勿手工修改。表clms_pers_injured的PO对象<br/>
 * 对应表名：clms_pers_injured,备注：人伤跟踪详情
 *
 */
@ApiModel("人伤跟踪详情")
@Data
public class ClmsPersInjuredVO  implements Serializable {
	private static final long serialVersionUID = 1L;
	/** 对应字段：id,备注：主键 */
	@TableField(value="id")
	private Integer id;
	/** 对应字段：report_no,备注：报案号 */
	@TableField(value="report_no")
	private String reportNo;
	/** 对应字段：case_times,备注：赔付次数 */
	@TableField(value="case_times")
	private Integer caseTimes;
	/** 对应字段：pers_trace_main_id,备注：跟踪任务主表id */
	@TableField(value="pers_trace_main_id")
	private Integer persTraceMainId;
	/** 对应字段：class_code,备注：险类代码 */
	@TableField(value="class_code")
	private String classCode;
	/** 对应字段：risk_code,备注：险种代码 */
	@TableField(value="risk_code")
	private String riskCode;
	/** 对应字段：person_name,备注：人员名称 */
	@TableField(value="person_name")
	private String personName;
	/** 对应字段：certi_type,备注：伤亡人员证件类型 */
	@TableField(value="certi_type")
	private String certiType;
	/** 对应字段：certi_code,备注：伤亡人员证件号码 */
	@TableField(value="certi_code")
	private String certiCode;
	/** 对应字段：phone_number,备注：电话 */
	@TableField(value="phone_number")
	private String phoneNumber;
	/** 对应字段：person_age,备注：年龄 */
	@TableField(value="person_age")
	private Integer personAge;
	/** 对应字段：birthday,备注：出生日期 */
	@TableField(value="birthday")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8" )
	private Date birthday;
	/** 对应字段：person_sex,备注：性别 */
	@TableField(value="person_sex")
	private String personSex;
	/** 对应字段：domicile,备注：户口性质(责任) */
	@TableField(value="domicile")
	private String domicile;
	/** 对应字段：domicile_place_code,备注：户口所在地代码 */
	@TableField(value="domicile_place_code")
	private String domicilePlaceCode;
	/** 对应字段：domicile_place,备注：户口所在地(责) */
	@TableField(value="domicile_place")
	private String domicilePlace;
	/** 对应字段：domicile_area,备注：户口所在 */
	@TableField(value="domicile_area")
	private String domicileArea;
	/** 对应字段：pamanent_address_code,备注：常住地址代码 */
	@TableField(value="pamanent_address_code")
	private String pamanentAddressCode;
	/** 对应字段：pamanent_address,备注：常住地址(责) */
	@TableField(value="pamanent_address")
	private String pamanentAddress;
	/** 对应字段：live_area,备注：常住地点 */
	@TableField(value="live_area")
	private String liveArea;
	/** 对应字段：work_unit,备注：工作单位(意健险) */
	@TableField(value="work_unit")
	private String workUnit;
	/** 对应字段：work_comment,备注：岗位描述(意) */
	@TableField(value="work_comment")
	private String workComment;
	/** 对应字段：issign,备注：是否签定劳动合同 */
	@TableField(value="issign")
	private String issign;
	/** 对应字段：hire_date,备注：入职时间 */
	@TableField(value="hire_date")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date hireDate;
	/** 对应字段：work_address,备注：工作地址 */
	@TableField(value="work_address")
	private String workAddress;
	/** 对应字段：tic_code,备注：从事行业代码 */
	@TableField(value="tic_code")
	private String ticCode;
	/** 对应字段：tic_name,备注：从事行业名称 */
	@TableField(value="tic_name")
	private String ticName;
	/** 对应字段：in_come,备注：月收入状况 */
	@TableField(value="in_come")
	private String inCome;
	/** 对应字段：accreditation_name,备注：鉴定机构名称 */
	@TableField(value="accreditation_name")
	private String accreditationName;
	/** 对应字段：disabled_degree,备注：估计伤残等级 */
	@TableField(value="disabled_degree")
	private String disabledDegree;
	/** 对应字段：reference_standard,备注：参照标准 */
	@TableField(value="reference_standard")
	private String referenceStandard;
	/** 对应字段：demage_code,备注：出险原因代码 */
	@TableField(value="demage_code")
	private String demageCode;
	/** 对应字段：unexpected_name,备注：意外名称 */
	@TableField(value="unexpected_name")
	private String unexpectedName;
	/** 对应字段：conscious,备注：是否昏迷 */
	@TableField(value="conscious")
	private String conscious;
	/** 对应字段：isno_disability,备注：是否伤残 */
	@TableField(value="isno_disability")
	private String isnoDisability;
	/** 对应字段：isno_die,备注：是否死亡 */
	@TableField(value="isno_die")
	private String isnoDie;
	/** 对应字段：disabled_situation,备注：伤残情况 */
	@TableField(value="disabled_situation")
	private String disabledSituation;
	/** 对应字段：tracer_equired,备注：跟踪要求 */
	@TableField(value="tracer_equired")
	private String tracerEquired;
	/** 对应字段：visit_type,备注：访谈方式 */
	@TableField(value="visit_type")
	private String visitType;
	/** 对应字段：trace_feedback,备注：跟踪要求反馈 */
	@TableField(value="trace_feedback")
	private String traceFeedback;
	/** 对应字段：report_flag,备注：储存从云客服报案带入的医院信息 */
	@TableField(value="report_flag")
	private String reportFlag;
	/** 对应字段：case_type,备注：案件类型 */
	@TableField(value="case_type")
	private String caseType;
	/** 对应字段：injury_part,备注：受伤部位 */
	@TableField(value="injury_part")
	private String injuryPart;
	/** 对应字段：identify_criteria,备注：鉴定标准 */
	@TableField(value="identify_criteria")
	private String identifyCriteria;
	/** 对应字段：disability_name,备注：伤残名称 */
	@TableField(value="disability_name")
	private String disabilityName;
	/** 对应字段：identify_time,备注：伤残等级 */
	@TableField(value="identify_time")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date identifyTime;
	/** 对应字段：identify_agencies,备注：鉴定机构 */
	@TableField(value="identify_agencies")
	private String identifyAgencies;
	/** 对应字段：miamitem,备注：伤残名称 */
	@TableField(value="miamitem")
	private String miamitem;
	/** 对应字段：indentify_way,备注：鉴定方法 */
	@TableField(value="indentify_way")
	private String indentifyWay;
	/** 对应字段：sum_claim_deloss,备注：立案估损总金额 */
	@TableField(value="sum_claim_deloss")
	private BigDecimal sumClaimDeloss;
	/** 对应字段：sum_report_fee,备注：总估损金额 */
	@TableField(value="sum_report_fee")
	private BigDecimal sumReportFee;
	/** 对应字段：sum_real_fee,备注：总索赔金额 */
	@TableField(value="sum_real_fee")
	private BigDecimal sumRealFee;
	/** 对应字段：sum_detraction_fee,备注：总减损金额金额 */
	@TableField(value="sum_detraction_fee")
	private BigDecimal sumDetractionFee;
	/** 对应字段：sum_def_loss,备注：总核定损金额 */
	@TableField(value="sum_def_loss")
	private BigDecimal sumDefLoss;
	/** 对应字段：sum_veri_report_fee,备注：总估损金额(审核) */
	@TableField(value="sum_veri_report_fee")
	private BigDecimal sumVeriReportFee;
	/** 对应字段：sum_veri_real_fee,备注：总索赔金额(审核) */
	@TableField(value="sum_veri_real_fee")
	private BigDecimal sumVeriRealFee;
	/** 对应字段：sum_veri_detraction_fee,备注：总减损金额金额(审核) */
	@TableField(value="sum_veri_detraction_fee")
	private BigDecimal sumVeriDetractionFee;
	/** 对应字段：sum_veri_def_loss,备注：总核定损金额(审核) */
	@TableField(value="sum_veri_def_loss")
	private BigDecimal sumVeriDefLoss;
	/** 对应字段：currency,备注：币别 */
	@TableField(value="currency")
	private String currency;
	/** 对应字段：undwrt_valid_flag,备注：核损伤亡人员标志位 */
	@TableField(value="undwrt_valid_flag")
	private String undwrtValidFlag;
	/** 对应字段：social_security,备注：有无社保 */
	@TableField(value="social_security")
	private String socialSecurity;
	/** 对应字段：job_type,备注：职业类别大类*/
	@TableField(value="profession_code")
	private String professionCode;
	/** 对应字段：job_type,备注：职业类别小类 */
	@TableField(value="sub_profession_code")
	private String subProfessionCode;
	/** 对应字段：job_grade,备注：职业等级 */
	@TableField(value="profession_grade")
	private String professionGrade;
	/** 对应字段：job_type,备注：投保职业类别 */
	@TableField(value="occupation_type")
	private String occupationType;
	/** 对应字段：job_type,备注：投保职业类别小类 */
	@TableField(value="occupation_code")
	private String occupationCode;
	/** 对应字段：job_grade,备注：投保职业等级 */
	@TableField(value="occupation_grade")
	private String occupationGrade;
	/** 对应字段：medical_type,备注：就诊类型 */
	@TableField(value="medical_type")
	private String medicalType;
	/** 对应字段：medical_type,备注：是否涉高 */
	@TableField(value="is_high")
	private String isHigh;
	/** 对应字段：valid_flag,备注：有效标志 */
	@TableField(value="valid_flag")
	private String validFlag;
	/** 对应字段：remarks,备注：备注 */
	@TableField(value="remarks")
	private String remarks;
	/** 对应字段：created_by,备注：创建人 */
	@TableField(value="created_by")
	private String createdBy;
	/** 对应字段：sys_ctime,备注：创建时间 */
	@TableField(value="sys_ctime")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date sysCtime;
	/** 对应字段：updated_by,备注：最新修改人员 */
	@TableField(value="updated_by")
	private String updatedBy;
	/** 对应字段：sys_utime,备注：最新修改时间 */
	@TableField(value="sys_utime")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date sysUtime;
	//职业名称
	private String profession;
	/** 对应字段：accident_type,备注：事故类型 */
	@TableField(value="accident_type")
	private String accidentType;

}
