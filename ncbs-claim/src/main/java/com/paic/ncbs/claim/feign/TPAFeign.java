package com.paic.ncbs.claim.feign;


import com.paic.ncbs.claim.config.FeignConfiguration;
import com.paic.ncbs.claim.model.dto.problem.ProblemCaseRequestDTO;
import com.paic.ncbs.claim.model.dto.problem.ProblemCaseResponseDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * TPA
 */
@Component
@FeignClient(name ="tpa-transplat", url = "${tpa.url}",configuration = {FeignConfiguration.class})
public interface TPAFeign {
    /**
     * TPA中台问题件答复接口
     * @param requestDTO
     * @return
     */
    //DEV:http://tpa-transplat.lb.ssdev.com:48003/tpa-transplat/itfp/commonEntrance.do
    //PRD: http://tpa-transplat.lb.ssprd.com:48003/tpa-transplat/itfp/commonEntrance.do
    @PostMapping({"/tpa-transplat/itfp/commonEntrance.do"})
    @Async
    ProblemCaseResponseDTO response(@RequestBody ProblemCaseRequestDTO requestDTO);
}
