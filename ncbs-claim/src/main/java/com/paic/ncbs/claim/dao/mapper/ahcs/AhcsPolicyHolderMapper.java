package com.paic.ncbs.claim.dao.mapper.ahcs;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyHolderEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AhcsPolicyHolderMapper extends BaseDao<AhcsPolicyHolderEntity> {

    int deleteByPrimaryKey(String idAhcsPolicyHolder);

    int insert(AhcsPolicyHolderEntity record);

    int insertSelective(AhcsPolicyHolderEntity record);

    AhcsPolicyHolderEntity selectByPrimaryKey(String idAhcsPolicyHolder);

    int updateByPrimaryKeySelective(AhcsPolicyHolderEntity record);

    int updateByPrimaryKey(AhcsPolicyHolderEntity record);

    int deleteByIdAhcsPolicyInfo(String idAhcsPolicyInfo);

    int insertList(@Param("holderEntities") List<AhcsPolicyHolderEntity> holderEntities);

    List<AhcsPolicyHolderEntity> getInfoByPolicyId(String idAhcsPolicyInfo);

    void deleteByReportNoAndPolicyNo(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo);

    List<AhcsPolicyHolderEntity> getInfoByReportNo(String reportNo);

    void insertBatch(@Param("list") List<AhcsPolicyHolderEntity> list);
}