package com.paic.ncbs.claim.feign.mesh;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.base.util.MeshSendUtils;
import com.paic.ncbs.claim.feign.UwFeign;
import com.paic.ncbs.claim.model.vo.senconduw.UwTaskVO;
import com.paic.ncbs.claim.model.vo.senconduw.UwsLetterSellBackDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023-11-02
 * @description 核保
 */
@Slf4j
@RefreshScope
@Component
public class UWRequest {
	private static final String HTTP_REQUEST_PREFIX = "http://ncbs-uws-http";

	/**
	 * 调用mesh和feign开关, true:调用mesh
	 */
	@Value("${switch.mesh}")
	private Boolean switchMesh;

	@Autowired
	private UwFeign uwFeign;

	/**
	 * 查询产品信息
	 * @param param
	 * @return
	 */
	public String createTask(UwTaskVO param){
		String url = HTTP_REQUEST_PREFIX + "/uws/app/manual/udr/createTask";
		if (switchMesh){
			return MeshSendUtils.post(url, JSON.toJSONString(param));
		}else {
			return uwFeign.createTask(param);
		}
	}


	/**
	 * 函件回销
	 * @param uwsLetterSellBackDTO
	 * @return
	 */
	public String letterSellBack(UwsLetterSellBackDTO uwsLetterSellBackDTO) {
		String url = HTTP_REQUEST_PREFIX + "/uws/app/letterInfo/letterSellBack";
		if (switchMesh) {
			return MeshSendUtils.post(url, JSON.toJSONString(uwsLetterSellBackDTO));
		} else {
			return uwFeign.letterSellBack(uwsLetterSellBackDTO);
		}
	}
}
