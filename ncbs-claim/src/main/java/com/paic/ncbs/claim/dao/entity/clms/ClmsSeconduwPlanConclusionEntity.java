package com.paic.ncbs.claim.dao.entity.clms;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 理赔二核险种层核保结论表(ClmsSeconduwPlanConclusionEntity)实体类
 *
 * <AUTHOR>
 * @since 2023-09-14 14:07:13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("ClmsSeconduwPlanConclusion实体类")
public class ClmsSeconduwPlanConclusionEntity implements Serializable {
    private static final long serialVersionUID = -32723759113080533L;
    /**
     *  主键
     */
    private String id;
    /**
     * 理赔二核保单层核保结论表主键
     */
    private String idClmsSeconduwPolicyConclusion;
    /**
     *  报案号
     */
    private String reportNo;
    /**
     *  赔付次数
     */
    private Integer caseTimes;
    /**
     * 保单号
     */
    private String policyNo;
    /**
     * 险种编码
     */
    private String planCode;
    /**
     * 险种名称
     */
    private String planName;
    /**
     * 险种起期
     */
    private Date insuranceBeginDate;
    /**
     * 险种止期
     */
    private Date insuranceEndDate;
    /**
     * 险种状态：有效、中止、终止
     */
    private String planStatus;
    /**
     * 核保决定：除外、标体，拒保
     */
    private String uwDecisions;
    /**
     * 除外事项：核保说明 核保结论为“除外”或者“拒保”时才有核保说明
     */
    private String uwExceptions;
    /**
     *  创建人员
     */
    private String createdBy;
    /**
     *  创建时间
     */
    private Date createdDate;
    /**
     *  修改人员
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDate;


}

