package com.paic.ncbs.claim.dao.entity.indicators;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* clms_case_indicator_log实体类
* <AUTHOR>
* @since 2025-04-14
*/
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("clms_case_indicator_log")
 public class ClmsCaseIndicatorLog implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 创建人	
    */
    @TableField(value = "created_by")
    private String createdBy;

    /**
    * 创建时间
    */
    @TableField(value = "sys_ctime")
    private LocalDateTime sysCtime;

    /**
    * 最新修改人员
    */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
    * 最新修改时间
    */
    @TableField(value = "sys_utime")
    private LocalDateTime sysUtime;

    /**
    * 唯一标识
    */
    @TableId(value = "id", type = IdType.NONE)
    private Integer id;

    /**
    * 指标代码
    */
    @TableField(value = "indicator_code")
    private String indicatorCode;

    /**
    * 指标名称
    */
    @TableField(value = "indicator_name")
    private String indicatorName;

    /**
    * 时效统计起期
    */
    @TableField(value = "start_time")
    private LocalDateTime startTime;

    /**
    * 时效统计止起期
    */
    @TableField(value = "end_time")
    private LocalDateTime endTime;

    /**
    * 运行结果
    */
    @TableField(value = "run_result")
    private String runResult;

    /**
    * 错误信息
    */
    @TableField(value = "error_info")
    private String errorInfo;

}