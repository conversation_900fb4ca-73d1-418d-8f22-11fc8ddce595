package com.paic.ncbs.claim.model.vo.duty;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
@ApiModel("核责拒赔详情")
public class DutyRejectDetailVO {

	@ApiModelProperty("保单号")
	private String policyNo;

	@ApiModelProperty("电子保单号")
	private String policyCerNo;

	@ApiModelProperty("保单展示名称")
	private String showName;

	@ApiModelProperty("拒赔金额")
	private BigDecimal rejectAmount;
	@ApiModelProperty("保单编号")
	private List<String> policyNoList;
	@ApiModelProperty("损失对象代码")
	private String lossObjectNo;
	public String getShowName() {
		StringBuffer showNameStr=new StringBuffer();
		showNameStr.append(policyNo);
		if (StringUtils.isNotEmpty(policyCerNo)) {
			showNameStr.append("(").append(policyCerNo).append(")");
		}
		showName=showNameStr.toString();
		return showName;
	}

	public String getPolicyCerNo() {
		return policyCerNo;
	}

	public void setPolicyCerNo(String policyCerNo) {
		this.policyCerNo = policyCerNo;
	}

	public String getPolicyNo() {
		return policyNo;
	}

	public void setPolicyNo(String policyNo) {
		this.policyNo = policyNo;
	}

	public BigDecimal getRejectAmount() {
		return rejectAmount;
	}

	public void setRejectAmount(BigDecimal rejectAmount) {
		this.rejectAmount = rejectAmount;
	}

	public List<String> getPolicyNoList() {
		return policyNoList;
	}

	public void setPolicyNoList(List<String> policyNoList) {
		this.policyNoList = policyNoList;
	}

	public String getLossObjectNo() {
		return lossObjectNo;
	}

	public void setLossObjectNo(String lossObjectNo) {
		this.lossObjectNo = lossObjectNo;
	}
}
