package com.paic.ncbs.claim.model.vo.pay;

import lombok.Data;

/**
 * 第三方系统二次支付接口入参对象
 *
 * <AUTHOR>
 */
@Data
public class PaymentForThirdCompanyCoreDTO {

	// 业务类型 C实赔 Y 预赔 E 退保
	public static final String CERTI_TYPE_ACTUAL = "C";
	public static final String CERTI_TYPE_PRE = "Y";

	// 收付方式，1转账，3支票，99为其他
	public static final String PAY_TYPE_TRANSFER = "1";
	public static final String PAY_TYPE_CHEQUE = "3";
	public static final String PAY_TYPE_OT = "99";

	// 付款对象类型 0公对公、1公对私
	public static final String BANK_PAY_TYPE_BUSINESS = "0";
	public static final String BANK_PAY_TYPE_SELF = "1";

	// 支付通道，0 为系统默认 1 为招行通用
	public static final String PAYMENT_DEFAULT = "0";
	public static final String PAYMENT_MERCHANTS_BANK = "1";

	/**
	 *  业务类型 C实赔 Y 预赔 E 退保
	 */
	private String certiType;

	/**
	 *  业务单号
	 */
	private String certiNo;

	/**
	 *  期次
	 */
	private String serialNo;

	/**
	 *  收款人户名
	 */
	private String unitname;

	/**
	 *  收款人账号
	 */
	private String accountcode;

	/**
	 *  银行大类代码
	 */
	private String bankcode;

	/**
	 *  银行网点名称
	 */
	private String banklocation;

	/**
	 *  省地区
	 */
	private String provinceName;

	/**
	 *  市地区
	 */
	private String cityName;

	/**
	 *  资金ID
	 *  支付通知和支付状态查询会提供
	 */
	private String sourcenotecode;

	/**
	 *  收付方式，1转账，3支票，99为其他
	 */
	private String payType;

	/**
	 *  付款对象类型 0公对公、1公对私
	 */
	private String bankPayType;

	/**
	 *  银行名称
	 */
	private String bankname;

	/**
	 *  支付通道，0 为系统默认 1 为招行通用
	 */
	private String payment;


}
