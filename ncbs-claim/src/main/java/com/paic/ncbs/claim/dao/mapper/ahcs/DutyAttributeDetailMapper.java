package com.paic.ncbs.claim.dao.mapper.ahcs;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsDutyAttributeDetailEntity;
import com.paic.ncbs.claim.model.dto.settle.DutyAttributeDTO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface DutyAttributeDetailMapper extends BaseDao<AhcsDutyAttributeDetailEntity> {

    @MapKey("ATTRIBUTE_DETAIL_CODE")
    Map<String,String> getAttributeByDutyId(String idAhcsDutyAttribute);

    List<AhcsDutyAttributeDetailEntity> getInfoByDutyAttributeId(String idAhcsDutyAttribute);

    List<DutyAttributeDTO> getAllAttributeDetails(String idAhcsDutyAttribute);

    AhcsDutyAttributeDetailEntity selectByPrimaryKey(String idAhcsDutyAttributeDetail);

    int insert(AhcsDutyAttributeDetailEntity record);

    int insertSelective(AhcsDutyAttributeDetailEntity record);

    int updateByPrimaryKeySelective(AhcsDutyAttributeDetailEntity record);

    int updateByPrimaryKey(AhcsDutyAttributeDetailEntity record);

    int insertList(@Param("dutyAttributeDetailEntities") List<AhcsDutyAttributeDetailEntity> dutyAttributeDetailEntities);

    void deleteByReportNoAndPolicyNo(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo);

    int deleteByPrimaryKey(String idAhcsDutyAttributeDetail);

    void insertBatch(@Param("list") List<AhcsDutyAttributeDetailEntity> dutyAttributeDetailEntities);

    List<DutyAttributeDTO> getAttributeByRepotNo(String reportNo,String policyNo);

    List<AhcsDutyAttributeDetailEntity> getInfoByDutyAttributeDutyId(String idPolicyDuty);
}