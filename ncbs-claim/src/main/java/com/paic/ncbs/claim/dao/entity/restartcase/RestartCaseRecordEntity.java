package com.paic.ncbs.claim.dao.entity.restartcase;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @date 2023-05-22 15:00
 */
@Data
public class RestartCaseRecordEntity implements Serializable {


    private static final long serialVersionUID = -932671507408943226L;
    /**
     * 表主键
     */
    private String idClmRestartCaseRecord;
    /**
     *  重开原因
     */
    private String restartReason;
    /**
     *  案件类别，一级类别：1 人伤，2 非人伤
     */
    private Integer caseType;
    /**
     *  案件类别，二级类别：意外医疗、疾病住院医疗、疾病门急诊医疗、重大疾病、津贴、意外残疾、疾病残疾、意外身故、疾病身故、其他
     */
    private String caseKind;
    /**
     *  重开说明
     */
    private String restartDescription;
    /**
     *  报案号
     */
    private String reportNo;
    /**
     *  赔付次数
     */
    private Integer caseTimes;
    /**
     *  审批意见
     */
    private Integer approvalOpinions;
    /**
     * 审批说明
     */
    private String approvalDescription;

    /**
     *  重开金额
     */
    private BigDecimal restartAmount;

    /**
     * 创建人员
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDate;
    /**
     * 修改人员
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDate;
    /**
     *  差错类型
     */
    private String errorType;
}
