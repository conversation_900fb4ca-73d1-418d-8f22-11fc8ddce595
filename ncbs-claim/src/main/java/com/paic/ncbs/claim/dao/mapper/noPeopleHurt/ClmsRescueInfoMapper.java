package com.paic.ncbs.claim.dao.mapper.noPeopleHurt;

import com.paic.ncbs.claim.dao.entity.clms.ClmsRescueInfo;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 救援信息表(ClmsRescueInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:50
 */
public interface ClmsRescueInfoMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ClmsRescueInfo queryById(String id);

    /**
     * 通过报案号查询单条数据
     *
     * @return 实例对象
     */
    List<ClmsRescueInfo> queryByReportNo(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes);

    /**
     * 新增数据
     *
     * @param clmsRescueInfo 实例对象
     * @return 影响行数
     */
    int insert(ClmsRescueInfo clmsRescueInfo);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<ClmsRescueInfo> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ClmsRescueInfo> entities);

    /**
     * 修改数据
     *
     * @param clmsRescueInfo 实例对象
     * @return 影响行数
     */
    int update(ClmsRescueInfo clmsRescueInfo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);

    void deleteByCondition(String reportNo, int caseTimes);

    /**
     * 获取理算金额
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return 理算金额
     */
    BigDecimal getSettleAmout(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes);
}

