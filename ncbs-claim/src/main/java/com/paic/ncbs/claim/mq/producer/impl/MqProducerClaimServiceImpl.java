package com.paic.ncbs.claim.mq.producer.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.NcbsConstant;
import com.paic.ncbs.claim.common.enums.AccidentTypeEnum;
import com.paic.ncbs.claim.common.enums.ChargeCodeEnum;
import com.paic.ncbs.claim.common.enums.InsuredApplyStatusEnum;
import com.paic.ncbs.claim.common.enums.InsuredApplyTypeEnum;
import com.paic.ncbs.claim.common.enums.ReportModeEnum;
import com.paic.ncbs.claim.common.util.CodeUtil;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.EstimateUtil;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.SettleHelper;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.entity.ClaimRejectionApprovalRecordEntity;
import com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentExEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoExEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.casezero.CaseZeroCancelMapper;
import com.paic.ncbs.claim.dao.mapper.duty.OperationDefineMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseClassMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.ClaimRejectionApprovalRecordEntityMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimatePolicyMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoExMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyPayMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
import com.paic.ncbs.claim.model.dto.duty.PersonDiagnoseDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseZeroCancelDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyFormDTO;
import com.paic.ncbs.claim.model.dto.mq.ClaimDto;
import com.paic.ncbs.claim.model.dto.mq.claim.ClaimCoinsDto;
import com.paic.ncbs.claim.model.dto.mq.claim.ClaimDetailInfDto;
import com.paic.ncbs.claim.model.dto.mq.claim.ClaimItemDto;
import com.paic.ncbs.claim.model.dto.mq.claim.ClaimRepayCalReqDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLAcciPersonDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLClaimDangerItemDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLClaimDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLClaimLossDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLClaimLossTraceDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLLTextDto;
import com.paic.ncbs.claim.model.dto.mq.prpL.PrpLRegistRPolicyDto;
import com.paic.ncbs.claim.model.dto.mq.swf.SwfLogStoreDto;
import com.paic.ncbs.claim.model.dto.report.ReportBaseInfoResData;
import com.paic.ncbs.claim.model.dto.settle.CoinsureDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.duty.PeopleHurtVO;
import com.paic.ncbs.claim.model.vo.duty.SurveyVO;
import com.paic.ncbs.claim.mq.producer.MqProducerClaimService;
import com.paic.ncbs.claim.mq.producer.MqProducerRegistService;
import com.paic.ncbs.claim.service.duty.DutySurveyService;
import com.paic.ncbs.claim.service.endcase.CaseBaseService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.endcase.WholeCaseService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.report.ReportAccidentExService;
import com.paic.ncbs.claim.service.report.ReportAccidentService;
import com.paic.ncbs.claim.service.report.ReportCustomerInfoService;
import com.paic.ncbs.claim.service.report.ReportInfoService;
import com.paic.ncbs.claim.service.settle.CoinsureService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;
import static com.paic.ncbs.claim.common.util.BigDecimalUtils.sum;

@Service
@Slf4j
public class MqProducerClaimServiceImpl implements MqProducerClaimService {
    @Value("${mq.rabbit.exchange.ncbs.claim.root:ncbsClaimExchange}")
    private String ncbsClaimExchange;
    //立案环节topic
    @Value("${mq.claim.topic}")
    private String claimTopic;
    /*@Autowired
    private RabbitTemplate rabbitTemplate;*/
    @Autowired
    private ReportCustomerInfoService reportCustomerInfoService;
    @Autowired
    private ReportAccidentService reportAccidentService;
    @Autowired
    private ReportAccidentExService reportAccidentExService;
    @Autowired
    private CaseZeroCancelMapper caseZeroCancelMapper;
    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;
    @Autowired
    private ReportInfoService reportInfoService;
    @Autowired
    private ReportInfoExMapper reportInfoExMapper;

    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;

    @Autowired
    private PolicyInfoMapper policyInfoMapper;
    @Autowired
    private OcasMapper ocasMapper;
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private DutySurveyService dutySurveyService;
    @Autowired
    private PolicyPayService policyPayService;
    @Autowired
    private DepartmentDefineMapper departmentDefineMapper;

    @Autowired
    private CaseClassMapper caseClassDao;
    @Autowired
    private MqProducerRegistService mqProducerRegistService;
    @Autowired
    private WholeCaseService wholeCaseService;
    @Autowired
    private CaseBaseService caseBaseService;
    @Autowired
    private ClaimRejectionApprovalRecordEntityMapper claimRejectionApprovalRecordEntityMapper;
    @Autowired
    private PolicyPayMapper policyPayDao;

    @Autowired
    private EstimatePolicyMapper estimatePolicyMapper;

    @Autowired
    private EstimateService estimateService;
    @Autowired
    private CoinsureService coinsureService;

    @Autowired
    private OperationDefineMapper operationDefineMapper;

    @Override
    @Async("asyncPool")
    public void syncClaimLink(String reportNo, Integer caseTimes) {
        // 数据组装
        ClaimDto claimDto = initClaimDto(reportNo, caseTimes);
        //发送MQ消息
        JSONObject jsonObj = (JSONObject) JSON.toJSON(claimDto);
        log.info("syncClaimLink===sendMessage: {}",jsonObj);
       // rabbitTemplate.convertAndSend(ncbsClaimExchange, claimTopic, claimDto);
    }

    private ClaimDto initClaimDto(String reportNo, Integer caseTimes) {
        ClaimDto claimDto = new ClaimDto();
        ReportInfoEntity reportInfo = reportInfoService.getReportInfo(reportNo);
        //报案方式
        reportInfo.setReportMode(ReportModeEnum.getName(reportInfo.getReportMode() == null ? "2" : reportInfo.getReportMode()));
        //查询 报案客户 被保险人 信息表
        ReportCustomerInfoEntity customerInfo = reportCustomerInfoService.getReportCustomerInfoByReportNo(reportNo);
        //查询 事故信息表
        ReportAccidentEntity reportAccident = reportAccidentService.getReportAccident(reportNo);
        EstimatePolicyFormDTO result  = estimateService.getEstimateDataByTache(reportNo, caseTimes, EstimateUtil.ESTIMATE_TYPE_REGISTRATION, null);
        List<EstimatePolicyDTO> policyList = result.getEstimatePolicyList();
        LogUtil.info("initClaimDto-policyList：{}",JSON.toJSONString(policyList));
        WholeCaseBaseDTO wholeCaseBaseEntity = wholeCaseBaseService.getWholeCaseBase(reportNo,caseTimes);
        List<ReportInfoExEntity> reportInfoExEntityList = reportInfoExMapper.getReportInfoEx(reportNo);
        ReportBaseInfoResData reportBaseInfo = wholeCaseService.getReportBaseInfo(reportNo, caseTimes);
        TaskInfoDTO taskInfoDTO = taskInfoMapper.findLatestByReportNoAndBpmKey(reportNo, caseTimes, BpmConstants.OC_REGISTER_REVIEW);
        //查询 意键险报案信息扩展表
        ReportAccidentExEntity reportAccidentEx = reportAccidentExService.getReportAccidentEx(reportNo);
        //查询保单信息表
        List<PolicyInfoDTO> policyInfoDTOList = policyInfoMapper.getPolicyInfoListByReportNo(reportNo);
        EstimatePolicyDTO estimatePolicyDTO = policyList.get(0);
        PolicyInfoDTO policyInfoDTO = policyInfoDTOList.get(0);

        ReportInfoExEntity reportInfoExEntity = reportInfoExEntityList.get(0);
        PeopleHurtVO peopleHurtVO = dutySurveyService.getPeopleHurtVO(reportNo, caseTimes);

        //责任明细
        List<PolicyPayDTO> policys = policyPayDao.selectFromPolicyCopy(reportNo, caseTimes);
        SettleHelper.setPolicyPays(policys,null);
        LogUtil.info("initClaimDto-policys：{}",JSON.toJSONString(policys));
        //补充立案基本信息表数据
        List<PrpLClaimDto> prpLClaimDtos = initPrpLClaimDto("claim", reportInfo, customerInfo, reportAccident, policyInfoDTOList, wholeCaseBaseEntity, reportInfoExEntity,taskInfoDTO,reportAccidentEx,reportBaseInfo);
        claimDto.setPrpLClaimDtoList(prpLClaimDtos);
        //补充立案险别估损金额表数据
        List<PrpLClaimLossDto> prpLClaimLossDtoList = initPrpLClaimLossDto(reportInfo,wholeCaseBaseEntity,policyInfoDTOList,customerInfo,policys,policyList);
        claimDto.setPrpLClaimLossDtoList(prpLClaimLossDtoList);
        //补充立案险别估损金额轨迹信息表
        List<PrpLClaimLossTraceDto> prpLClaimLossTraceDtoList = initPrpLClaimLossTraceDto(policyInfoDTOList,reportInfo,wholeCaseBaseEntity,customerInfo,policys,policyList);
        claimDto.setPrpLClaimLossTraceDtoList(prpLClaimLossTraceDtoList);
        //补充立案文字表
        List<PrpLLTextDto> prpLLTextDtoList = initPrpLLTextDto( reportInfo,wholeCaseBaseEntity);
        claimDto.setPrpLLTextDtoList(prpLLTextDtoList);
        //危险单位信息表
//        List<PrpLDangerItemDto> prpLDangerItemList = initPrpLDangerItemDto(policyList,wholeCaseBaseEntity);
//        claimDto.setPrpLDangerItemDtoList(prpLDangerItemList);
        // 理赔的危险单位信息表
        List<PrpLClaimDangerItemDto> prpLClaimDangerItemList = initPrpLClaimDangerItemDto(wholeCaseBaseEntity,policyList,prpLClaimLossDtoList);
        claimDto.setPrpLClaimDangerItemDtoList(prpLClaimDangerItemList);
        // 意健险索赔申请人信息表
        List<CaseBaseEntity> caseBaseInfoList = caseBaseService.getCaseBaseInfoByReportNoAndCasetimes(reportNo, String.valueOf(caseTimes));
        List<PrpLAcciPersonDto> prpLAcciPersonDtoList = mqProducerRegistService.getPrpLAcciPersonDto(reportNo, customerInfo, reportAccidentEx, reportAccident,policyInfoDTO,reportBaseInfo,peopleHurtVO,caseBaseInfoList,"03",null);

//        PrpLAcciPersonDto prpLAcciPersonDto = initPrpLAcciPersonDto(customerInfo, wholeCaseBaseEntity, policyInfoDTO,peopleHurtVO,reportAccidentEx);
        claimDto.setPrpLAcciPersonDtoList(prpLAcciPersonDtoList);
        //理赔工作流转储表
        List<SwfLogStoreDto> swfLogStoreDtoList = getSwfLogStoreDto(reportNo, customerInfo, taskInfoDTO, estimatePolicyDTO,reportInfo,caseBaseInfoList);
        claimDto.setSwfLogStoreDtoList(swfLogStoreDtoList);
//        List<PrpLDangerUnitDto> prpLDangerUnitList =  initPrpLDangerUnitDto(wholeCaseBaseEntity,reportInfo);
//        claimDto.setPrpLDangerUnitDtoList(prpLDangerUnitList);
        ClaimRepayCalReqDto claimRepayCalReqDto = initClaimRepayCalReqDto(reportAccident, taskInfoDTO, caseBaseInfoList, prpLClaimLossDtoList,policyList,reportInfo);
        claimDto.setClaimRepayCalReqDto(claimRepayCalReqDto);

        List<PrpLRegistRPolicyDto> prpLRegistRPolicyDtoList = new ArrayList<>();
        List<EstimatePolicyDTO> estimatePolicyDTOS = estimatePolicyMapper.getByReportNoAndCaseTimes(reportNo, caseTimes);
        if (CollectionUtils.isNotEmpty(estimatePolicyDTOS)){
            for (int i = 0; i < estimatePolicyDTOS.size(); i++) {
                PrpLRegistRPolicyDto prpLRegistRPolicyDto = new PrpLRegistRPolicyDto();
                prpLRegistRPolicyDto.setRegistNo(reportNo);
                String policyNo = estimatePolicyDTOS.get(i).getPolicyNo();
                CaseBaseEntity caseBaseEntity = caseBaseInfoList.stream().filter(it -> it.getPolicyNo().equals(policyNo)).findFirst().orElse(null);
                prpLRegistRPolicyDto.setPolicyNo(estimatePolicyDTOS.get(i).getPolicyNo());
                prpLRegistRPolicyDto.setClaimNo(caseBaseEntity.getRegistNo());
                Map<String, String> plyBaseInfo = ocasMapper.getPlyBaseInfo(policyNo);
                String productCode = MapUtils.getString(plyBaseInfo,"productCode");
                String businessType = MapUtils.getString(plyBaseInfo,"businessType");
                prpLRegistRPolicyDto.setPolicyType(businessType);
                prpLRegistRPolicyDto.setFlowID("");
                prpLRegistRPolicyDto.setRemark(reportInfo.getRemark());
                prpLRegistRPolicyDto.setValidStatus("1");
                prpLRegistRPolicyDto.setRiskCode(CodeUtil.subStringRiskCode(productCode));
                prpLRegistRPolicyDto.setRegistFlag("1");
                prpLRegistRPolicyDto.setRegistCancelDate(null);
                prpLRegistRPolicyDto.setCompensateNo(null);
                prpLRegistRPolicyDto.setSerialNo(i+1);
                prpLRegistRPolicyDto.setUpdateTime(new Date());
                prpLRegistRPolicyDtoList.add(prpLRegistRPolicyDto);
            }
        }
        claimDto.setPrpLRegistRPolicyDtoList(prpLRegistRPolicyDtoList);
        return claimDto;
    }

    private ClaimRepayCalReqDto initClaimRepayCalReqDto(ReportAccidentEntity reportAccident, TaskInfoDTO taskInfoDTO,
                                                       List<CaseBaseEntity> caseBaseInfoList, List<PrpLClaimLossDto> prpLClaimLossDtoList,
                                                        List<EstimatePolicyDTO> policyList,ReportInfoEntity reportInfo) {
        ClaimRepayCalReqDto claimRepayCalReqDto=new ClaimRepayCalReqDto();
        List<EstimatePlanDTO> result  = new ArrayList<>() ;
        policyList.forEach(estimateEx -> {
            List<EstimatePlanDTO> estimatePlanList = estimateEx.getEstimatePlanList();
            if (null != estimatePlanList &&  !CollectionUtils.isEmpty(estimatePlanList)){
                result.addAll(estimatePlanList) ;
            }
        });
        List<ClaimDetailInfDto> claimDetailInfDtoList=new ArrayList<>();
        for (CaseBaseEntity caseBaseEntity:caseBaseInfoList){
            List<CoinsureDTO> coinsureDTOList= coinsureService.getCoinsureByPolicyNo(caseBaseEntity.getPolicyNo());
            ClaimDetailInfDto claimDetailInfDto=new ClaimDetailInfDto();
//            claimNo	立案号
            String registNo = caseBaseEntity.getRegistNo();
            claimDetailInfDto.setClaimNo(registNo);
//            policyNo	保单号
            claimDetailInfDto.setPolicyNo(caseBaseEntity.getPolicyNo());
//            dangerNo	危险单位序号
            claimDetailInfDto.setDangerNo(1);
//            certiType	立案/预赔/实赔，1:立案/2：预赔/3：实赔
            claimDetailInfDto.setCertiType("1");
//            certiNo	业务号
            claimDetailInfDto.setCertiNo(registNo);
//            currency	币种
            claimDetailInfDto.setCurrency("CNY");
            List<PrpLClaimLossDto> prpLClaimLossDtos = prpLClaimLossDtoList.stream().filter(e -> e.getClaimNo().equals(registNo)).collect(Collectors.toList());
            List<BigDecimal> sumClaim = prpLClaimLossDtos.stream().map(PrpLClaimLossDto::getSumClaim).collect(Collectors.toList());
            BigDecimal sum = sum(sumClaim);
            //            sumClaim	总估损
            claimDetailInfDto.setSumClaim(sum.doubleValue());
//            damageDate	出险日期
            Date accidentDate = reportAccident.getAccidentDate();
            SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy-MM-dd");
            claimDetailInfDto.setDamageDate(formatter1.format(accidentDate));
//            itemName	保险项目 ，(财产险、水险) /船名
            claimDetailInfDto.setItemName("");
//            damageCode	出险原因编码
            claimDetailInfDto.setDamageCode("1805");
//            damageReason	出险原因描述
            claimDetailInfDto.setDamageReason("其他");
//            postCode	postCode
            claimDetailInfDto.setPostCode("");
//            addressName	危险单位地址信息
            claimDetailInfDto.setAddressName("");
//            coinsFlag	联共保标识，0：非共保，1：主共保，2：从共保
            if (CollectionUtils.isNotEmpty(coinsureDTOList)){
                claimDetailInfDto.setCoinsFlag(coinsureDTOList.get(0).getCoinsuranceType());
            } else {
                claimDetailInfDto.setCoinsFlag("0");
            }
//            endCaseFlag	结案标识
            claimDetailInfDto.setEndCaseFlag("0");
//            makeComCode	操作人员所属机构
            if (taskInfoDTO == null){
                claimDetailInfDto.setMakeComCode(reportInfo.getAcceptDepartmentCode());
//            createrCode	操作人员代码
                claimDetailInfDto.setCreaterCode(reportInfo.getReportRegisterUm());
            } else {
                claimDetailInfDto.setMakeComCode(taskInfoDTO.getDepartmentCode());
//            createrCode	操作人员代码
                claimDetailInfDto.setCreaterCode(taskInfoDTO.getAssigner());
            }

//            createDate	操作日期
            claimDetailInfDto.setCreateDate(formatter1.format(new Date()));
            //理赔金额信息送再保接口-共保信息
            List<ClaimCoinsDto> claimCoinsDtoList = new ArrayList<>();
            claimDetailInfDto.setClaimCoinsDtoList(claimCoinsDtoList);
            //理赔金额信息送再保接口-险别信息
            List<ClaimItemDto> claimItemDtoList = getClaimItemDtos(prpLClaimLossDtos,result);
            claimDetailInfDto.setClaimItemDtoList(claimItemDtoList);
            claimDetailInfDtoList.add(claimDetailInfDto);
        }
        claimRepayCalReqDto.setClaimDetailInfDtoList(claimDetailInfDtoList);
        return claimRepayCalReqDto;
    }

    private List<ClaimItemDto> getClaimItemDtos(List<PrpLClaimLossDto> prpLClaimLossDtos,List<EstimatePlanDTO> result) {
        List<ClaimItemDto> claimItemDtoList = new ArrayList<>();
        for (PrpLClaimLossDto p : prpLClaimLossDtos) {
            String feeCategory = p.getFeeCategory();
            // 查勘费不送再保
            if (ChargeCodeEnum.FEE_07.getCode().equals(feeCategory)){
                continue;
            }
            String kindCode = p.getKindCode();
            if ("0798".equals(kindCode)){
                // 不送
                continue;
            }
            EstimatePlanDTO plan=result.stream().filter(resultEx->CodeUtil.subStringCode(resultEx.getPlanCode()).equals(kindCode)).findFirst().orElse(null);
            ClaimItemDto itemDto = new ClaimItemDto();
            itemDto.setKindCode(kindCode);
            itemDto.setKindName(Objects.isNull(plan) ? "":plan.getPlanName());
            String lossFeeType = p.getLossFeeType();
            if ("P".equals(lossFeeType)){
                itemDto.setPayType("1");
                // 赔款 98
                itemDto.setChargeCode("98");
            } else {
                itemDto.setPayType("2");
                itemDto.setChargeCode(feeCategory);
            }
            BigDecimal sumClaim = p.getSumClaim();
//            if (ChargeCodeEnum.FEE_03.getCode().equals(feeCategory)){
//                sumClaim = sumClaim.divide(BigDecimal.valueOf(1.06),2,BigDecimal.ROUND_HALF_UP);
//            }
            itemDto.setCurrency("CNY");
            itemDto.setSumPaid(sumClaim.doubleValue());
            claimItemDtoList.add(itemDto);
        }
        return claimItemDtoList;
    }

    @SneakyThrows
    private List<SwfLogStoreDto> getSwfLogStoreDto(String reportNo, ReportCustomerInfoEntity customerInfo, TaskInfoDTO taskInfoDTO,
                                             EstimatePolicyDTO estimatePolicyDTO, ReportInfoEntity reportInfo,List<CaseBaseEntity> caseBaseInfoList) {
        List<SwfLogStoreDto> swfLogStoreDtoList=new ArrayList<>();
        for(CaseBaseEntity caseBaseEntity:caseBaseInfoList){
            SwfLogStoreDto swfLogStoreDto=new SwfLogStoreDto();

            swfLogStoreDto.setLogNo(2);
            swfLogStoreDto.setModelNo(12);
            swfLogStoreDto.setNodeName("立案");
            swfLogStoreDto.setBusinessNo(caseBaseEntity.getRegistNo());
            if (taskInfoDTO == null){
                // 批量结案的 没有任务信息
                swfLogStoreDto.setDeptName(departmentDefineMapper.queryDepartmentNameByDeptCode(reportInfo.getAcceptDepartmentCode()));
                swfLogStoreDto.setComCode(reportInfo.getAcceptDepartmentCode());
                swfLogStoreDto.setHandleDept(reportInfo.getAcceptDepartmentCode());
                swfLogStoreDto.setHandlerCode(NcbsConstant.ZK_ADMIN_UM);
                swfLogStoreDto.setHandlerName(NcbsConstant.ZK_ADMIN_NAME);
            } else {
                swfLogStoreDto.setDeptName(departmentDefineMapper.queryDepartmentNameByDeptCode(reportInfo.getAcceptDepartmentCode()));
                swfLogStoreDto.setComCode(reportInfo.getAcceptDepartmentCode());
                swfLogStoreDto.setHandleDept(taskInfoDTO.getDepartmentCode());
                swfLogStoreDto.setHandlerCode(taskInfoDTO.getAssigner());
                swfLogStoreDto.setHandlerName(taskInfoDTO.getAssigneeName());
            }
            String dateStr = DateUtils.parseToFormatString(new Date(), DateUtils.FULL_DATE_STR);
            swfLogStoreDto.setFlowInTime(dateStr);
            swfLogStoreDto.setTimeLimit(0);
            swfLogStoreDto.setHandleTime(dateStr);
            swfLogStoreDto.setSubmitTime(dateStr);
            swfLogStoreDto.setPackageID("0");
            swfLogStoreDto.setTaskNo(0);
            swfLogStoreDto.setNodeType("claim");
            swfLogStoreDto.setTitleStr("");
            swfLogStoreDto.setBusinessType("1");
            Map<String, String> productMap = ocasMapper.getPlyBaseInfo(caseBaseEntity.getPolicyNo());
            String productCode = MapUtils.getString(productMap,"productCode");
            swfLogStoreDto.setRiskCode(CodeUtil.subStringRiskCode(productCode));
            swfLogStoreDto.setKeyIn(reportNo);
            swfLogStoreDto.setKeyOut(reportNo);
            swfLogStoreDto.setMainFlowID("0");
            swfLogStoreDto.setSubFlowID("0");
            swfLogStoreDto.setPosX(0);
            swfLogStoreDto.setPosY(0);
            swfLogStoreDto.setEndFlag("");
            swfLogStoreDto.setBeforeHandlerCode("");
            swfLogStoreDto.setBeforeHandlerName("");
            swfLogStoreDto.setPolicyNo(caseBaseEntity.getPolicyNo());
            swfLogStoreDto.setRegistNo(reportNo);
            swfLogStoreDto.setInsuredName(customerInfo.getName());
            swfLogStoreDto.setEntrustFlag("1");
            swfLogStoreDto.setEntrustNodeStatus("1");
            swfLogStoreDto.setDamageEndDate(new Date());
            swfLogStoreDto.setDamageStartDate(new Date());
            swfLogStoreDto.setUpdateTime(new Date());

            swfLogStoreDto.setFlowID("");
            swfLogStoreDto.setNodeStatus("4");
            swfLogStoreDto.setNodeNo(3);
            swfLogStoreDto.setFlowStatus("0");
            swfLogStoreDtoList.add(swfLogStoreDto);
        }
        return swfLogStoreDtoList;
    }

    private List<PrpLClaimDangerItemDto> initPrpLClaimDangerItemDto(WholeCaseBaseDTO wholeCaseBaseEntity, List<EstimatePolicyDTO> policyList, List<PrpLClaimLossDto> prpLClaimLossDtoList) {
        int i = 1;
        List<PrpLClaimDangerItemDto> prpLClaimDangerItemList = new ArrayList<>();
        List<EstimatePlanDTO> result  = new ArrayList<>() ;
        policyList.forEach(estimateEx -> {
            List<EstimatePlanDTO> estimatePlanList = estimateEx.getEstimatePlanList();
            if (null != estimatePlanList &&  !CollectionUtils.isEmpty(estimatePlanList)){
                    result.addAll(estimatePlanList) ;
            }
        });
        LogUtil.info("initPrpLClaimDangerItemDto-result:{}",result);
        for (PrpLClaimLossDto lossDto : prpLClaimLossDtoList) {
            PrpLClaimDangerItemDto prpLClaimDangerItemDto = new PrpLClaimDangerItemDto();
            //        业务号	CERTINO	Y
            prpLClaimDangerItemDto.setCertiNo(lossDto.getClaimNo());
            //        险种代码	RISKCODE	Y
            prpLClaimDangerItemDto.setRiskCode(lossDto.getRiskCode());
            //        危险单位号码	DANGERNO	Y	默认为“1”
            prpLClaimDangerItemDto.setDangerNo(1);
            //        序号	SERIALNO	Y	递加
            prpLClaimDangerItemDto.setSerialNo(i++);
            EstimatePlanDTO plan=result.stream().filter(resultEx->CodeUtil.subStringCode(resultEx.getPlanCode()).equals(lossDto.getKindCode())).findFirst().orElse(null);
            //        险别归类标志	KINDFLAG	CY
            prpLClaimDangerItemDto.setKindFlag("0");
            //        险别代码	KINDCODE	Y
            prpLClaimDangerItemDto.setKindCode(lossDto.getKindCode());
            //        险别名称	KINDNAME	Y
            prpLClaimDangerItemDto.setKindName(Objects.isNull(plan) ? "":plan.getPlanName());
            //        币别	CURRENCY	CY
            prpLClaimDangerItemDto.setCurrency("CNY");
            //        险别赔款金额	SUMPAID	Y
            if ("Z".equals(lossDto.getLossFeeType())) {
            prpLClaimDangerItemDto.setSumFee(lossDto.getSumClaim());
            prpLClaimDangerItemDto.setSumPaid(new BigDecimal("0.000"));
            }
            if ("P".equals(lossDto.getLossFeeType())) {
            prpLClaimDangerItemDto.setSumPaid(lossDto.getSumClaim());
            prpLClaimDangerItemDto.setSumFee(new BigDecimal("0.000"));
            }
            prpLClaimDangerItemDto.setUpdateTime(new Date());
            prpLClaimDangerItemList.add(prpLClaimDangerItemDto);
        }
        return prpLClaimDangerItemList;

    }


    private List<PrpLLTextDto> initPrpLLTextDto(ReportInfoEntity reportInfo,WholeCaseBaseDTO wholeCaseBaseEntity) {
        List<CaseBaseEntity> caseBaseInfoList = caseBaseService.getCaseBaseInfoByReportNoAndCasetimes(wholeCaseBaseEntity.getReportNo(), "1");
        List<PrpLLTextDto> prpLLTextDtoList=new ArrayList<>();
        SurveyVO surveyVOByCaseType = dutySurveyService.getSurveyVOByCaseType(wholeCaseBaseEntity.getReportNo(), wholeCaseBaseEntity.getCaseTimes(), 1);
        for (int i = 0 ; i< caseBaseInfoList.size();i++){
            PrpLLTextDto prpLLTextDto=new PrpLLTextDto();
//        立案号码	CLAIMNO	Y		无明确字段
            prpLLTextDto.setClaimNo(caseBaseInfoList.get(i).getRegistNo());
//        文字说明类型 	TEXTTYPE	Y	出险摘要/查勘报告/结案报告"	无明确字段
            prpLLTextDto.setTextType("09");
//        行号	LINENO	Y	按顺序递加	无明确字段
            prpLLTextDto.setLineNo(i+1);
//        文字说明	CONTEXT	Y		能否传立案审批说明？可以
            if (surveyVOByCaseType!=null&& StringUtils.isNotEmpty(surveyVOByCaseType.getPhoneSurveyDetail())){
                prpLLTextDto.setContext(surveyVOByCaseType.getPhoneSurveyDetail());
            } else {
                prpLLTextDto.setContext("立案");
            }
//        更新时间	UPDATETIME           	CY	只要对表操作就修改updatetime
            prpLLTextDto.setUpdateTime(new Date());
            prpLLTextDtoList.add(prpLLTextDto) ;
        }
        return prpLLTextDtoList;
    }

    @Override
    public List<PrpLClaimLossTraceDto> initPrpLClaimLossTraceDto(List<PolicyInfoDTO> policyInfoDTOList, ReportInfoEntity reportInfo, WholeCaseBaseDTO wholeCaseBaseEntity, ReportCustomerInfoEntity customerInfo, List<PolicyPayDTO> policys,List<EstimatePolicyDTO> policyList) {
        List<PrpLClaimLossTraceDto> PrpLClaimLossTraceDtoList  = new ArrayList<>();
        List<CaseBaseEntity> caseBaseInfoList = caseBaseService.getCaseBaseInfoByReportNoAndCasetimes(wholeCaseBaseEntity.getReportNo(), "1");
        List<EstimateDutyRecordDTO> result  = new ArrayList<>() ;
        policyList.forEach(estimateEx -> {
            List<EstimatePlanDTO> estimatePlanList = estimateEx.getEstimatePlanList();
            if (null != estimatePlanList &&  !CollectionUtils.isEmpty(estimatePlanList)){
                for (EstimatePlanDTO estimatePlanListEx:estimatePlanList){
                    List<EstimateDutyRecordDTO> estimateDutyList = estimatePlanListEx.getEstimateDutyRecordList();
                    result.addAll(estimateDutyList) ;
                }
            }
        });
        LogUtil.info("initPrpLClaimLossTraceDto-result：{}",JSON.toJSONString(result));
        int i = 1;
        for (EstimateDutyRecordDTO resultEx : result) {
            BigDecimal sumPaid = new BigDecimal("0");
            BigDecimal sumumFee = new BigDecimal("0");
            sumPaid = sum(sumPaid, resultEx.getEstimateAmount());
            sumumFee = sum(sumumFee, resultEx.getArbitrageFee(), resultEx.getInquireFee(),
                    resultEx.getOtherFee(), resultEx.getSpecialSurveyFee(),
                    resultEx.getCommonEstimateFee(), resultEx.getExecuteFee(),
                    resultEx.getLawsuitFee(), resultEx.getLawyerFee(),
                    resultEx.getVerifyAppraiseFee());
            PrpLClaimLossTraceDto prpLClaimLossTraceDto = new PrpLClaimLossTraceDto();
            String policyNo = resultEx.getPolicyNo();
            CaseBaseEntity caseBaseEntity = caseBaseInfoList.stream().filter(e -> e.getPolicyNo().equals(policyNo)).findFirst().orElse(null);
            PolicyInfoDTO policyInfoDTO = policyInfoDTOList.stream().filter(e1 -> e1.getPolicyNo().equals(policyNo)).findFirst().orElse(null);
            //        立案号	CLAIMNO	Y		无明确字段(调用核心生成)
            prpLClaimLossTraceDto.setClaimNo(caseBaseEntity.getRegistNo());
            //                险种	RISKCODE	Y
            prpLClaimLossTraceDto.setRiskCode(CodeUtil.subStringRiskCode(policyInfoDTO.getProductCode()));
            //        标的子险序号	ITEMKINDNO	Y
            prpLClaimLossTraceDto.setItemKindNo(1);
            //        险别代码	KINDCODE	Y
            prpLClaimLossTraceDto.setKindCode(CodeUtil.subStringCode(resultEx.getPlanCode()));
            //        保单标的项目代码	ITEMCODE	Y		无明确字段(prpCitemKind表中ItemCode字段)
            if (StringUtils.isNotEmpty(resultEx.getDutyCode())) {
                prpLClaimLossTraceDto.setItemCode(CodeUtil.subStringCode(resultEx.getDutyCode()));
            } else {
                prpLClaimLossTraceDto.setItemCode("0000");
            }
            //                币别	CURRENCY	Y
            prpLClaimLossTraceDto.setCurrency("CNY");
            //        估损金额	SUMCLAIM	Y
            //        输入日期	INPUTDATE	Y	系统自动获取当前操作时间
            prpLClaimLossTraceDto.setInputDate(new Date());
            //        备注	REMARKFLAG	CY
            prpLClaimLossTraceDto.setRemarkFlag(null);
            //        事故责任免赔率	ACCIDEDUCTIBLERATE	CY	0.000
            prpLClaimLossTraceDto.setAcciDeductibleRate(new BigDecimal("0.000"));
            //        绝对免赔率	DEDUCTIBLERATE	CY	0.000
            prpLClaimLossTraceDto.setDeductible(new BigDecimal("0.000"));
            //        绝对免赔额	DEDUCTIBLE	CY	0.000
            prpLClaimLossTraceDto.setDeductible(new BigDecimal("0.000"));
            //        分户序号	FAMILYNO	Y	从PrpLclaimLossDto取	无明确字段(prpCitemKind表中FamilyNo字段)
            String riskPersonNo = ocasMapper.getRiskPersonNo(policyNo, customerInfo.getCertificateNo(), customerInfo.getName());
            prpLClaimLossTraceDto.setFamilyNo(Integer.valueOf(riskPersonNo));
            //        调整次数	ADJUSTTIMES	Y
            prpLClaimLossTraceDto.setAdjustTimes(0);
            //        调整估损人员代码	OPERATORCODE	Y
            prpLClaimLossTraceDto.setOperatorCode(wholeCaseBaseEntity.getRegisterUm());
            //        状态	STATUS	Y
            prpLClaimLossTraceDto.setStatus("1");
            //        操作人姓名	OPERATENAME	Y
            prpLClaimLossTraceDto.setOperateName(reportInfo.getReportRegisterUm());
            //        剩余保额	REMNANTAMOUNT	Y
            prpLClaimLossTraceDto.setRemnantAmount(nvl(policyInfoDTO.getTotalInsuredAmount(),0));
            //        责任名细分类名称	LIABDETAILNAME	CY	立案环节在险别估损金额信息添加	无明确字段
            prpLClaimLossTraceDto.setLiabDetailName(null);
            //        调整时间	ADJUSTDATE	Y
            prpLClaimLossTraceDto.setAdjustDate(new Date());
            //        对立案主表币别兑换率	EXCHRATE	Y		无明确字段(国际正常汇率)
            prpLClaimLossTraceDto.setExCHRate(new BigDecimal("0.00"));
            //                案件标识（现存：核损修正未决）	CLAIMFLAG	Y		无明确字段(默认1)
            prpLClaimLossTraceDto.setClaimFlag("1");
            //        本案是否适用超赔合约	ISBEYONDPAIDFLAG	CY	是，否	无明确字段(默认为0)
            prpLClaimLossTraceDto.setIsBeyondPaidFlag("0");
            //                超赔合约预摊回金额	BEYONDPAIDSUM	CY	本案是否适用超赔合约为是，需要录入金额；为否，不需要录入金额	无明确字段(默认为0)
            prpLClaimLossTraceDto.setBeyondPaidSum(null);
            //                更新时间	UPDATETIME	CY	只要对表操作就修改updatetime
            prpLClaimLossTraceDto.setUpdateTime(new Date());
            //  prpLClaimLossTraceDto.setLiabDetailCode(dutyDetailPayDTO.getDutyDetailCode());
            //  prpLClaimLossTraceDto.setLiabDetailName(dutyDetailPayDTO.getDutyDetailName());
            if (sumPaid.compareTo(new BigDecimal("0")) > 0) {
                //        序号	SERIALNO	Y
                prpLClaimLossTraceDto.setSerialNo(i++);
                //        金额类型	LOSSFEETYPE	Y		无明确字段(P:赔款；Z：费用)
                prpLClaimLossTraceDto.setLossFeeType("P");
                //        估损金额	SUMCLAIM	Y
                prpLClaimLossTraceDto.setSumClaim(sumPaid);
                //        险别损失金额	KINDLOSS	Y		无明确字段(？？？)
                prpLClaimLossTraceDto.setKindLoss(sumPaid);
                //        费用类型	FEECATEGORY	CY
                prpLClaimLossTraceDto.setFeeCategory(null);
                PrpLClaimLossTraceDtoList.add(prpLClaimLossTraceDto);
            }
            if (sumumFee.compareTo(new BigDecimal("0")) > 0) {
                PrpLClaimLossTraceDto prpLClaimLossTraceDto2 = new PrpLClaimLossTraceDto();
                BeanUtils.copyProperties(prpLClaimLossTraceDto,prpLClaimLossTraceDto2);
                //        序号	SERIALNO	Y
                prpLClaimLossTraceDto2.setSerialNo(i++);
                //        金额类型	LOSSFEETYPE	Y		无明确字段(P:赔款；Z：费用)
                prpLClaimLossTraceDto2.setLossFeeType("Z");
                //        估损金额	SUMCLAIM	Y
                prpLClaimLossTraceDto2.setSumClaim(sumumFee);
                //        险别损失金额	KINDLOSS	Y		无明确字段(？？？)
                prpLClaimLossTraceDto2.setKindLoss(sumumFee);
                //        费用类型	FEECATEGORY	CY
                String feeCategory =getFeeType(resultEx.getArbitrageFee(), resultEx.getInquireFee(),
                        resultEx.getOtherFee(), resultEx.getSpecialSurveyFee(),
                        resultEx.getCommonEstimateFee(), resultEx.getExecuteFee(),
                        resultEx.getLawsuitFee(), resultEx.getLawyerFee(),
                        resultEx.getVerifyAppraiseFee());
                prpLClaimLossTraceDto2.setFeeCategory(feeCategory);
                PrpLClaimLossTraceDtoList.add(prpLClaimLossTraceDto2);
            }
        }
        return PrpLClaimLossTraceDtoList;
    }

    @Override
    public List<PrpLClaimLossDto>  initPrpLClaimLossDto(ReportInfoEntity reportInfo, WholeCaseBaseDTO wholeCaseBaseEntity, List<PolicyInfoDTO> policyInfoDTOList, ReportCustomerInfoEntity customerInfo, List<PolicyPayDTO> policys, List<EstimatePolicyDTO> policyList) {
        List<PrpLClaimLossDto> PrpLClaimLossDtoList  = new ArrayList<>();
        List<CaseBaseEntity> caseBaseInfoList = caseBaseService.getCaseBaseInfoByReportNoAndCasetimes(wholeCaseBaseEntity.getReportNo(), "1");
        List<EstimateDutyRecordDTO> result  = new ArrayList<>() ;
        policyList.forEach(estimateEx -> {
            List<EstimatePlanDTO> estimatePlanList = estimateEx.getEstimatePlanList();
            if (null != estimatePlanList &&  !CollectionUtils.isEmpty(estimatePlanList)){
                for (EstimatePlanDTO estimatePlanListEx:estimatePlanList){
                    List<EstimateDutyRecordDTO> estimateDutyList = estimatePlanListEx.getEstimateDutyRecordList();
                    result.addAll(estimateDutyList) ;
                }
            }
        });
        LogUtil.info("initPrpLClaimLossDto-result：{}",JSON.toJSONString(result));
        int i = 1 ;
        for (EstimateDutyRecordDTO resultEx : result) {
            BigDecimal sumPaid = new BigDecimal("0");
            BigDecimal sumumFee = new BigDecimal("0");
            sumPaid = sum(sumPaid, resultEx.getEstimateAmount());
            sumumFee = sum(sumumFee, resultEx.getArbitrageFee(), resultEx.getInquireFee(),
                    resultEx.getOtherFee(), resultEx.getSpecialSurveyFee(),
                    resultEx.getCommonEstimateFee(), resultEx.getExecuteFee(),
                    resultEx.getLawsuitFee(), resultEx.getLawyerFee(),
                    resultEx.getVerifyAppraiseFee());

            PrpLClaimLossDto prpLClaimLossDto = new PrpLClaimLossDto();
            String policyNo = resultEx.getPolicyNo();
            CaseBaseEntity caseBaseEntity = caseBaseInfoList.stream().filter(e -> e.getPolicyNo().equals(policyNo)).findFirst().orElse(null);
            PolicyInfoDTO policyInfoDTO = policyInfoDTOList.stream().filter(e1 -> e1.getPolicyNo().equals(policyNo)).findFirst().orElse(null);
            //        立案号	CLAIMNO	Y		无明确字段(调用核心生成)
            prpLClaimLossDto.setClaimNo(caseBaseEntity.getRegistNo());
            //                险种	RISKCODE	Y
            prpLClaimLossDto.setRiskCode(CodeUtil.subStringRiskCode(policyInfoDTO.getProductCode()));

            //        标的子险序号	ITEMKINDNO	Y		无明确字段(prpCitemKind表中ItemKindNo字段)
            prpLClaimLossDto.setItemKindNo(1);
            //                险别代码	KINDCODE	Y
            String planCode = resultEx.getPlanCode();
            prpLClaimLossDto.setKindCode(CodeUtil.subStringCode(planCode));
            //        保单标的项目代码	ITEMCODE	Y		无明确字段(prpCitemKind表中ItemCode字段)
            prpLClaimLossDto.setItemCode(CodeUtil.subStringCode(resultEx.getDutyCode()));
            //                币别	CURRENCY	Y
            prpLClaimLossDto.setCurrency("CNY");

            //        输入日期	INPUTDATE	Y	系统自动获取当前操作时间
            prpLClaimLossDto.setInputDate(new Date());
            //        备注	REMARKFLAG	CY
            prpLClaimLossDto.setRemarkFlag(null);
            //        事故责任免赔率	ACCIDEDUCTIBLERATE	CY	0.000
            prpLClaimLossDto.setAcciDeductibleRate(new BigDecimal("0.000"));
            //        绝对免赔率	DEDUCTIBLERATE	CY	0.000
            prpLClaimLossDto.setDeductibleRate(new BigDecimal("0.000"));
            //        绝对免赔额	DEDUCTIBLE	CY	0.000
            prpLClaimLossDto.setDeductible(new BigDecimal("0.000"));
            //        分户序号	FAMILYNO	Y	从承保取	无明确字段(prpCitemKind表中FamilyNo字段)
            String riskPersonNo = ocasMapper.getRiskPersonNo(policyNo, customerInfo.getCertificateNo(), customerInfo.getName());
            prpLClaimLossDto.setFamilyNo(Integer.valueOf(riskPersonNo));
            //        操作人姓名	OPERATENAME	Y
            prpLClaimLossDto.setOperateName(reportInfo.getReportRegisterUm());
            //        剩余保额	REMNANTAMOUNT	Y
            prpLClaimLossDto.setRemnantAmount(nvl(policyInfoDTO.getTotalInsuredAmount(),0));
            //        对立案主表币别兑换率	EXCHRATE	Y		无明确字段(国际正常汇率)
            prpLClaimLossDto.setExCHRate(new BigDecimal("0.00"));
            //                案件标识（现存：核损修正未决）	CLAIMFLAG	Y		无明确字段(默认1)
            prpLClaimLossDto.setClaimFlag("1");
            //        本案是否适用超赔合约	ISBEYONDPAIDFLAG	CY	是，否	无明确字段(默认为0)
            prpLClaimLossDto.setIsBeyondPaidFlag("0");
            //                超赔合约预摊回金额	BEYONDPAIDSUM	CY	本案是否适用超赔合约为是，需要录入金额；为否，不需要录入金额	无明确字段(默认为0)
            prpLClaimLossDto.setBeyondPaidSum(new BigDecimal("0.00"));
            //                更新时间	UPDATETIME	CY	只要对表操作就修改updatetime
            prpLClaimLossDto.setUpdateTime(new Date());
            // prpLClaimLossDto.setLiabDetailCode(dutyDetailPayDTO.getDutyDetailCode());
            // prpLClaimLossDto.setLiabDetailName(dutyDetailPayDTO.getDutyDetailName());
            // 计算金额 赔款和费用
            if (sumPaid.compareTo(new BigDecimal("0")) > 0) {
                //        序号	SERIALNO	Y
                prpLClaimLossDto.setSerialNo(i++);
                //        金额类型	LOSSFEETYPE	Y		无明确字段(P:赔款；Z：费用)
                prpLClaimLossDto.setLossFeeType("P");
                //        估损金额	SUMCLAIM	Y
                prpLClaimLossDto.setSumClaim(sumPaid);
                //        险别损失金额	KINDLOSS	Y		无明确字段(？？？)
                prpLClaimLossDto.setKindLoss(sumPaid);
                //        费用类型	FEECATEGORY	CY
                prpLClaimLossDto.setFeeCategory(null);
                PrpLClaimLossDtoList.add(prpLClaimLossDto);
            }
            if (sumumFee.compareTo(new BigDecimal("0")) > 0) {
                PrpLClaimLossDto prpLClaimLossDto2 = new PrpLClaimLossDto();
                BeanUtils.copyProperties(prpLClaimLossDto,prpLClaimLossDto2);
                //        序号	SERIALNO	Y
                prpLClaimLossDto2.setSerialNo(i++);
                //        金额类型	LOSSFEETYPE	Y		无明确字段(P:赔款；Z：费用)
                prpLClaimLossDto2.setLossFeeType("Z");
                //        估损金额	SUMCLAIM	Y
                prpLClaimLossDto2.setSumClaim(sumumFee);
                //        险别损失金额	KINDLOSS	Y		无明确字段(？？？)
                prpLClaimLossDto2.setKindLoss(sumumFee);
                //        费用类型	FEECATEGORY	CY
               String feeCategory =getFeeType(resultEx.getArbitrageFee(), resultEx.getInquireFee(),
                        resultEx.getOtherFee(), resultEx.getSpecialSurveyFee(),
                        resultEx.getCommonEstimateFee(), resultEx.getExecuteFee(),
                        resultEx.getLawsuitFee(), resultEx.getLawyerFee(),
                        resultEx.getVerifyAppraiseFee());
                prpLClaimLossDto2.setFeeCategory(feeCategory);
                PrpLClaimLossDtoList.add(prpLClaimLossDto2);
            }
        }
        return PrpLClaimLossDtoList;
    }

    private String getFeeType(BigDecimal arbitrageFee, BigDecimal inquireFee, BigDecimal otherFee, BigDecimal specialSurveyFee, BigDecimal commonEstimateFee, BigDecimal executeFee, BigDecimal lawsuitFee, BigDecimal lawyerFee, BigDecimal verifyAppraiseFee) {
         List<BigDecimal>  fee = Arrays.asList(arbitrageFee,inquireFee,otherFee,specialSurveyFee,commonEstimateFee,executeFee,lawsuitFee,lawyerFee,verifyAppraiseFee);
         List<String>  feeType=Arrays.asList("FEE_01","FEE_08","FEE_09","FEE_07","FEE_03","FEE_05","FEE_02","FEE_04","FEE_06");
         for (int i = 0 ; i < fee.size();i++){
             if (!Objects.isNull(fee.get(i)) && fee.get(i).compareTo(new BigDecimal("0")) > 0 ){
                 return ChargeCodeEnum.getCode(feeType.get(i));
             }
         }
       return "";
    }


    @SneakyThrows
    @Override
    public List<PrpLClaimDto> initPrpLClaimDto(String flag, ReportInfoEntity reportInfo,
                                         ReportCustomerInfoEntity customerInfo, ReportAccidentEntity reportAccident, List<PolicyInfoDTO> policyInfoDTOList,
                                         WholeCaseBaseDTO wholeCaseBaseEntity, ReportInfoExEntity reportInfoExEntity,
                                         TaskInfoDTO taskInfoDTO, ReportAccidentExEntity reportAccidentEx,ReportBaseInfoResData reportBaseInfo) {
        List<PrpLClaimDto> prpLClaimDtos = new ArrayList<>();
        String reportNo = reportInfo.getReportNo();
        List<CaseBaseEntity> caseBaseInfoList = caseBaseService.getCaseBaseInfoByReportNoAndCasetimes(reportNo, "1");
        for (CaseBaseEntity caseBaseEntity : caseBaseInfoList) {

            String policyNo = caseBaseEntity.getPolicyNo();

            PolicyInfoDTO policyInfoDTO = policyInfoDTOList.stream().filter(policy -> policy.getPolicyNo().equals(policyNo)).findFirst().orElse(null);
            if (policyInfoDTO == null){
                log.info("同步数据异常：" + reportNo + "-" + policyNo);
                continue;
            }
            PrpLClaimDto prpLClaimDto = new PrpLClaimDto();
//        立案号码	CLAIMNO              	Y
            prpLClaimDto.setClaimNo(caseBaseEntity.getRegistNo());
//        理赔类型	LFLAG                	Y	默认值“L”
            prpLClaimDto.setlFlag("L");

//        险类代码	CLASSCODE	Y
            Map<String, String> productMap = ocasMapper.getPlyBaseInfo(policyNo);
//            String productClass = MapUtils.getString(productMap,"productClass");
            String productCode = MapUtils.getString(productMap,"productCode");
            String riskCode = CodeUtil.subStringRiskCode(productCode);
            prpLClaimDto.setClassCode(riskCode.substring(0,2));
            //        险种代码	RISKCODE	Y
            prpLClaimDto.setRiskCode(riskCode);
//        报案号码	REGISTNO             	Y
            prpLClaimDto.setRegistNo(reportNo);
//        保单号码	POLICYNO             	Y
            prpLClaimDto.setPolicyNo(policyNo);
//        业务性质	BUSINESSNATURE	Y		无明确字段（默认0）
            prpLClaimDto.setBusinessNature("0");
//        语种	LANGUAGE	Y		无明确字段(默认中文) C:中文 E:英文
            prpLClaimDto.setLanguage("C");
//        保单类型	POLICYTYPE	CY	保单信息	目前只有个团(01:个单；02：团单)
            prpLClaimDto.setPolicyType("1".equals(policyInfoDTO.getBusinessType())?"01":"02");
//        被保险人代码	INSUREDCODE          	Y	保单信息	客户号？从承保prpCmain表中取InsuredCode
            String personCode = ocasMapper.getRiskPersonCode(policyNo, customerInfo.getCertificateNo(), customerInfo.getName());
            prpLClaimDto.setInsuredCode(personCode);
//        被保险人名称	INSUREDNAME          	Y
            prpLClaimDto.setInsuredName(customerInfo.getName());
            Date insuranceBeginTime = policyInfoDTO.getInsuranceBeginTime();
//            SimpleDateFormat formatter = new SimpleDateFormat("HH:mm:ss");
            SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy/MM/dd");

//        起保日期	STARTDATE            	Y	保单信息
            prpLClaimDto.setStartDate(formatter1.parse(formatter1.format(insuranceBeginTime)));
//        起保小时	STARTHOUR            	Y	保单信息
            prpLClaimDto.setStartHour(insuranceBeginTime.getHours());
            Date insuranceEndTime = policyInfoDTO.getInsuranceEndTime();
//        终保日期	ENDDATE              	Y	保单信息
            prpLClaimDto.setEndDate(formatter1.parse(formatter1.format(insuranceEndTime)));
//        终保小时	ENDHOUR              	Y	保单信息
            prpLClaimDto.setEndHour(insuranceEndTime.getHours());
//        币别代码	CURRENCY	Y
            prpLClaimDto.setCurrency("CNY");
//        总保额	SUMAMOUNT            	Y
            prpLClaimDto.setSumAmount(policyInfoDTO.getTotalInsuredAmount());
//        总保费	SUMPREMIUM           	Y
            prpLClaimDto.setSumPremium(policyInfoDTO.getTotalActualPremium());
//        总数量	SUMQUANTITY          	Y	默认值“0”
            prpLClaimDto.setSumQuantity(0);
//        出险日期起	DAMAGESTARTDATE      	Y
            Date accidentDate = reportAccident.getAccidentDate();
            prpLClaimDto.setDamageStartDate(formatter1.parse(formatter1.format(accidentDate)));
//        出险开始小时	DAMAGESTARTHOUR      	Y
            SimpleDateFormat formatter = new SimpleDateFormat("HH:mm:ss");
            prpLClaimDto.setDamageStartHour(formatter.format(accidentDate));
//        出险日期止	DAMAGEENDDATE        	Y
            prpLClaimDto.setDamageEndDate(formatter1.parse(formatter1.format(accidentDate)));
//        出险终止小时	DAMAGEENDHOUR        	Y

            prpLClaimDto.setDamageEndHour(formatter.format(accidentDate));

//        出险原因代码	DAMAGECODE	Y		无明确字段默认其他
            prpLClaimDto.setDamageCode("1805");
//        出险原因说明	DAMAGENAME	Y		无明确字段默认其他
            prpLClaimDto.setDamageName("其他");
            List<String> caseSubClassList = caseClassDao.getCaseClassList(reportNo, wholeCaseBaseEntity.getCaseTimes(), "report1");
            if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(caseSubClassList)) {
                //        出险类型	DAMAGETYPECODE	Y
                prpLClaimDto.setDamageTypeCode(InsuredApplyTypeEnum.getCode(caseSubClassList.get(0)));
                //        出险类型名称	DAMAGETYPENAME	Y
                prpLClaimDto.setDamageTypeName(InsuredApplyTypeEnum.getName(caseSubClassList.get(0)));
            }else {
                //        事故类型代码	DAMAGETYPECODE	Y
                prpLClaimDto.setDamageTypeCode(reportAccident.getAccidentType());
                //        事故类型说明	DAMAGETYPENAME	Y
                prpLClaimDto.setDamageTypeName(reportAccident.getAccidentTypeDetail());
            }

            // addresscode出现地省代码，damageareacode出险地市代码，damageaddresstype出险地区代码
//        出险区域代码	DAMAGEAREACODE	Y不必填		出险地址省市区如何对应？
            prpLClaimDto.setDamageAreaCode(reportAccident.getAccidentCityCode());
//        出险地点分类	DAMAGEADDRESSTYPE	Y不必填
            prpLClaimDto.setDamageAddressType(reportAccident.getAccidentCountyCode());
//        地址编码	ADDRESSCODE	Y不必填
            prpLClaimDto.setAddressCode(reportAccident.getProvinceCode());
//        出险地点	DAMAGEADDRESS	Y	出现地点正常录入汉字
            prpLClaimDto.setDamageAddress(reportAccident.getAccidentPlace());

//        立案日期	CLAIMDATE            	Y
            prpLClaimDto.setClaimDate(wholeCaseBaseEntity.getRegisterDate());
//        责任比例	INDEMNITYDUTYRATE    	Y	默认值“0.0000”
            prpLClaimDto.setIndemnityDutyRate(new BigDecimal("0.0000"));
//        免赔率	DEDUCTIBLERATE       	Y	默认值“0.0000”
            prpLClaimDto.setDeductibleRate(new BigDecimal("0.0000"));
//            BigDecimal recordAmount = clmsEstimateRecordMapper.getLastEstimateRecordAmount(wholeCaseBaseEntity.getReportNo(), wholeCaseBaseEntity.getCaseTimes());
            BigDecimal registerAmount = estimatePolicyMapper.getRegisterAmountByPolicyNo(reportNo,wholeCaseBaseEntity.getCaseTimes(),policyNo);
//        保险损失金额(同保单币别)	SUMCLAIM             	Y		无明确字段(立案估损金额)
            prpLClaimDto.setSumClaim(registerAmount == null ? new BigDecimal("0.00") : registerAmount);
//        总核定损金额(同保单币别)	SUMDEFLOSS           	Y		无明确字段（默认为0）
            prpLClaimDto.setSumDefLoss(new BigDecimal("0"));

//        总追偿金额(同保单币别)	SUMREPLEVY           	Y	默认值“0.00”
            prpLClaimDto.setSumReplevy(new BigDecimal("0.00"));
//        备注	REMARK               	CY
            prpLClaimDto.setRemark(reportInfo.getRemark());
//        案件性质 	CASETYPE	CY	0 注销 1 拒赔 2 结案 正常赔付且未结案为空，其他都需要有值
            if ("end".equals(flag)){
                prpLClaimDto.setCaseType("2");
                // 查理赔理算保单赔付金额
                List<PolicyPayDTO> policyPayList = policyPayService.getByReportNo(reportNo,wholeCaseBaseEntity.getCaseTimes());
                PolicyPayDTO policyPayDTO = policyPayList.stream().filter(it -> it.getPolicyNo().equals(caseBaseEntity.getPolicyNo())).findFirst().orElse(null);

                BigDecimal policyPrePay = nvl(policyPayDTO.getPolicyPrePay(), 0);
                BigDecimal policyPreFee = nvl(policyPayDTO.getPolicyPreFee(), 0);
                BigDecimal policySumFee = nvl(policyPayDTO.getPolicySumFee(), 0);
                BigDecimal policyPay = nvl(policyPayDTO.getPolicyPay(), 0);
                BigDecimal sumPaid = sum(policyPrePay, policyPreFee, policySumFee, policyPay);
                //        总赔付金额(同保单币别)--** (实赔+预赔)	SUMPAID              	Y		历史已赔金额？(赔款后回写)
                prpLClaimDto.setSumPaid(sumPaid);
            } else {
                prpLClaimDto.setCaseType(null);
                //        总赔付金额(同保单币别)--** (实赔+预赔)	SUMPAID              	Y		历史已赔金额？(赔款后回写)
                prpLClaimDto.setSumPaid(new BigDecimal("0"));
            }
//        理赔登记机构代码	MAKECOM              	Y		无明确字段(报案操作人归属机构)
            prpLClaimDto.setMakeCom(reportInfo.getAcceptDepartmentCode());
//        业务归属机构代码	COMCODE              	Y
            prpLClaimDto.setComCode(reportInfo.getAcceptDepartmentCode());
//        代理人代码	AGENTCODE            	CY		无明确字段（不填）
            prpLClaimDto.setAgentCode(null);

            if (taskInfoDTO == null){
                // 批量结案的 没有任务信息
                //        经办人代码	HANDLERCODE          	Y		无明确字段(立案处理人代码)
                prpLClaimDto.setHandlerCode(NcbsConstant.ZK_ADMIN_UM);
//        归属业务员代码	HANDLER1CODE         	Y		无明确字段(立案处理人归属机构代码)
                prpLClaimDto.setHandler1Code(reportInfo.getAcceptDepartmentCode());
//        操作员代码	OPERATORCODE         	Y		申请人还是审批人？（立案处理人代码）
                prpLClaimDto.setOperatorCode(NcbsConstant.ZK_ADMIN_UM);
            } else {
                //        经办人代码	HANDLERCODE          	Y		无明确字段(立案处理人代码)
                prpLClaimDto.setHandlerCode(taskInfoDTO.getAssigner());
//        归属业务员代码	HANDLER1CODE         	Y		无明确字段(立案处理人归属机构代码)
                prpLClaimDto.setHandler1Code(taskInfoDTO.getDepartmentCode());
//        操作员代码	OPERATORCODE         	Y		申请人还是审批人？（立案处理人代码）
                prpLClaimDto.setOperatorCode(taskInfoDTO.getAssigner());
            }

//        计算机输单日期	INPUTDATE            	Y	系统自动获取当前操作时间
            prpLClaimDto.setInputDate(new Date());
            if ("end".equals(flag) || "claimCancel".equals(flag)){
                //        结案日期	ENDCASEDATE          	CY	当理赔状态为结案时，必传
                prpLClaimDto.setEndCaseDate(new Date());
                //        赔案号	CASENO               	CY	核赔通过生成赔案号，回写此字段；非拒赔时填写；拒赔时，非必填
                prpLClaimDto.setCaseNo(caseBaseEntity.getCaseNo());
                //        结案员代码	ENDCASERCODE         	CY	案件结案回写此字段
                prpLClaimDto.setEndCaserCode(taskInfoDTO == null ?reportInfo.getReportRegisterUm() :taskInfoDTO.getAssigner());
                //        是否自动结案	ENDCASEFLAG	CY	没有结案为空；案件结案之后，自动结案传1，不是自动结案传0
                prpLClaimDto.setEndCaseFlag("0");
            } else {
                //        结案日期	ENDCASEDATE          	CY	当理赔状态为结案时，必传
                prpLClaimDto.setEndCaseDate(null);
                //        赔案号	CASENO               	CY	核赔通过生成赔案号，回写此字段；非拒赔时填写；拒赔时，非必填
                prpLClaimDto.setCaseNo("");
//        结案员代码	ENDCASERCODE         	CY	案件结案回写此字段
                prpLClaimDto.setEndCaserCode(null);
                //        是否自动结案	ENDCASEFLAG	CY	没有结案为空；案件结案之后，自动结案传1，不是自动结案传0
                prpLClaimDto.setEndCaseFlag(null);
            }

            if ("claimCancel".equals(flag)){
//        注销/拒赔日期	CANCELDATE           	CY	案件注销/拒赔回写此字段
                prpLClaimDto.setCancelDate(new Date());
//        注销/拒赔原因	CANCELREASON         	CY	案件注销/拒赔回写此字段
                ClaimRejectionApprovalRecordEntity claimRejectionApprovalRecordEntity = claimRejectionApprovalRecordEntityMapper.selectAgreedRecord(reportNo, wholeCaseBaseEntity.getCaseTimes());
                if (claimRejectionApprovalRecordEntity !=null){
                    prpLClaimDto.setCaseType("1");
                    prpLClaimDto.setCancelReason(claimRejectionApprovalRecordEntity.getConclusionCauseDesc());
                } else {
                    // 不管是零结还是注销都是按照注销传 0
                    prpLClaimDto.setCaseType("0");
                    List<CaseZeroCancelDTO> caseZeroCancelApplyList = caseZeroCancelMapper.getCaseZeroCancelApplyList(reportNo, wholeCaseBaseEntity.getCaseTimes(), "2");
                    if (CollectionUtils.isNotEmpty(caseZeroCancelApplyList)){
                        CaseZeroCancelDTO caseZeroCancelDTO = caseZeroCancelApplyList.get(caseZeroCancelApplyList.size() - 1);
                        prpLClaimDto.setCancelReason(caseZeroCancelDTO.getApplyReasonDetails());
                    }else {
                        prpLClaimDto.setCancelReason("立案取消");
                    }
                }

//        注销/拒赔人代码	DEALERCODE           	CY	案件注销/拒赔回写此字段
                prpLClaimDto.setDealerCode(taskInfoDTO.getAssigner());
            } else {
//        注销/拒赔日期	CANCELDATE           	CY	案件注销/拒赔回写此字段
                prpLClaimDto.setCancelDate(null);
//        注销/拒赔原因	CANCELREASON         	CY	案件注销/拒赔回写此字段
                prpLClaimDto.setCancelReason(null);
//        注销/拒赔人代码	DEALERCODE           	CY	案件注销/拒赔回写此字段
                prpLClaimDto.setDealerCode(null);
            }

            //        是否有人伤标记	INJURYFLAG	Y		人伤传是，非人伤传否？(是)
            prpLClaimDto.setInjuryFlag("1");
//        标志字段	FLAG                 	CY
            prpLClaimDto.setFlag(null);
//        是否有其他理赔中介机构	THIRDCOMFLAG	Y		无明确字段(没有就选择否)
            prpLClaimDto.setThirdComFlag("0");
//        计算书责任比例	CINDEMNITYDUTYRATE   	Y	默认值“0.00”
            prpLClaimDto.setcIndemnityDutyRate(new BigDecimal("0.00"));

//        赔案类别	CLAIMTYPE	Y		无明确字段(按照案件信息定位，实在没有就默认一般案件)
            prpLClaimDto.setClaimType("0");
//        委托赔案标志	ENTRUSTFLAG          	Y	默认值:"01"
            prpLClaimDto.setEntrustFlag("01");
//        赔付数量	LOSSESNUMBER	Y	默认值“0.00”
            prpLClaimDto.setLossesNumber(String.valueOf(wholeCaseBaseEntity.getCaseTimes()));
//        出险户次	DAMAGEINSURED        	Y	默认值“0.00”
            prpLClaimDto.setDamageInsured(new BigDecimal("0.00"));
//        非定点医疗机构	NOTFIXEDHOSPITAL     	CY
            prpLClaimDto.setNotFixedHospital(null);
//        疾病代码	DISEASECODE	Y	详情见意健险疾病代码表
            PeopleHurtVO peopleHurtVO = dutySurveyService.getPeopleHurtVO(reportNo, wholeCaseBaseEntity.getCaseTimes());
            if (peopleHurtVO.getDiagnoseVO()!= null && peopleHurtVO.getDiagnoseVO().getDiagnoseDTOs() != null) {
                List<PersonDiagnoseDTO> diagnoseDTOs = peopleHurtVO.getDiagnoseVO().getDiagnoseDTOs();
                PersonDiagnoseDTO personDiagnoseDTO = diagnoseDTOs.get(0);
                //        疾病名称	DISEASENAME          	Y	详情见意健险疾病代码表
                prpLClaimDto.setDiseaseCode(personDiagnoseDTO.getDiagnoseCode());
                prpLClaimDto.setDiseaseName(personDiagnoseDTO.getDiagnoseName());
            } else {
                prpLClaimDto.setDiseaseCode("");
                prpLClaimDto.setDiseaseName("");
            }
            if (null != peopleHurtVO.getHospitalVO() && !CollectionUtils.isEmpty(peopleHurtVO.getHospitalVO().getPersonHospitalList())){
                prpLClaimDto.setFixedHospital(peopleHurtVO.getHospitalVO().getPersonHospitalList().get(0).getHospitalName());
                prpLClaimDto.setFixedHospitalCode(peopleHurtVO.getHospitalVO().getPersonHospitalList().get(0).getHospitalCode());
            }

//        入院日期	INHOSPDATE           	CY	有住院记录需要填写
            prpLClaimDto.setInHospDate(null);
//        出院日期	OUTHOSPDATE          	CY
            prpLClaimDto.setOutHospDate(null);
//        事故者现状	PERSONSITUATION	Y
            prpLClaimDto.setPersonSituation(InsuredApplyStatusEnum.getCode(reportAccidentEx.getInsuredApplyStatus()));

            if (peopleHurtVO.getDiagnoseVO()!= null && peopleHurtVO.getDiagnoseVO().getDiagnoseDTOs() != null) {
                List<PersonDiagnoseDTO> diagnoseDTOs = peopleHurtVO.getDiagnoseVO().getDiagnoseDTOs();
                PersonDiagnoseDTO personDiagnoseDTO = diagnoseDTOs.get(0);
                //        疾病名称	DISEASENAME          	Y	详情见意健险疾病代码表
                prpLClaimDto.setDiseaseCode(personDiagnoseDTO.getDiagnoseCode());
                prpLClaimDto.setDiseaseName(personDiagnoseDTO.getDiagnoseName());
                String surgicalCode = personDiagnoseDTO.getSurgicalCode();
                if (StringUtils.isNotEmpty(surgicalCode)){
                    //根据报案号查询机构编码
                    String acceptDepartmentCode = ahcsPolicyInfoMapper.selectDepartmentCodeByReportNo(reportNo);
                    //根据机构编码确认机构是全国、北京(211)还是上海(231)
                    String orgType = NcbsConstant.ORG_TYPE_ONE;
//                    if(StringUtils.isNotEmpty(acceptDepartmentCode) &&acceptDepartmentCode.startsWith(NcbsConstant.BEIJING)){
//                        orgType = NcbsConstant.ORG_TYPE_TWO;
//                    }
                    String operationName = operationDefineMapper.getTherapyOperationByCode(surgicalCode,orgType);
                    prpLClaimDto.setOperationCode(surgicalCode);
                    prpLClaimDto.setOperationName(operationName);
                }
            } else {
                prpLClaimDto.setDiseaseCode("");
                prpLClaimDto.setDiseaseName("");
            }


//        注销/拒赔金额	CANCELAMOUNT	CY	案件注销/拒赔回写此字段
            prpLClaimDto.setCancelAmount(null);
//        是否追偿案件	ISREPLEVYCASE	Y
            prpLClaimDto.setIsReplevyCase(wholeCaseBaseEntity.getAgentType());
//        对CNY兑换率	EXCHRATE             	Y	正常兑换率	无明确字段(百度)
            prpLClaimDto.setExCHRate(new BigDecimal("0.00"));
//        损伤外部原因		CY	出险原因为意外时，必录	枚举？是的
            prpLClaimDto.setInJuryReasonCode(wholeCaseBaseEntity.getInjuryReasonCode());
            //        事故类型代码（意健险）	ACCIDENTTYPECODE	Y
            prpLClaimDto.setAccidentTypeCode(AccidentTypeEnum.getCode(reportAccidentEx.getAccidentType()));
//        事故类型说明	ACCIDENTTYPENAME	Y
            prpLClaimDto.setAccidentTypeName(AccidentTypeEnum.getName(reportAccidentEx.getAccidentType()));

//        本案是否适用超赔合约	ISBEYONDPAIDFLAG	CY	是，否
            prpLClaimDto.setIsBeyondPaidFlag(null);
//        超赔合约预摊回金额	BEYONDPAIDSUM        	CY	本案是否适用超赔合约为是，需要录入金额；为否，不需要录入金额
            prpLClaimDto.setBeyondPaidSum(null);
//        线上化理赔:是否面见客户查勘	ISMEETCUSTOMERCHECK	Y		无明确字段(没有默认为否)
            prpLClaimDto.setIsMeetCustomerCheck("0");
//        更新时间	UPDATETIME           	CY	只要对表操作就修改updatetime
            prpLClaimDto.setUpdateTime(new Date());
            prpLClaimDtos.add(prpLClaimDto);
        }

        return prpLClaimDtos;
    }
}

