package com.paic.ncbs.claim.model.vo.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@ApiModel(description = "旅行变更信息VO")
public class ReportAccidentTravelVO {

    @ApiModelProperty(value = "报案号")
    private String reportNo;

    @ApiModelProperty(value = "变更原因")
    private String changeReason;

    @ApiModelProperty(value = "旅行变更类型")
    private String[] changeType;

    @ApiModelProperty(value = "机票票价")
    private BigDecimal ticketPrice;

    @ApiModelProperty(value = "扩展信息", example = "订单号，发车时间，票价，车次号，打款流水号，打款账户，赔付记录状态")
    private String travelExtend;

    @ApiModelProperty(value = "费用预估")
    private BigDecimal costEstimate;

    public String getTravelExtend() {
        return travelExtend;
    }

    public void setTravelExtend(String travelExtend) {
        this.travelExtend = travelExtend;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String getChangeReason() {
        return changeReason;
    }

    public void setChangeReason(String changeReason) {
        this.changeReason = changeReason == null ? null : changeReason.trim();
    }

    public String[] getChangeType() {
        return changeType;
    }

    public void setChangeType(String[] changeType) {
        this.changeType = changeType;
    }

    public BigDecimal getCostEstimate() {
        return costEstimate;
    }

    public void setCostEstimate(BigDecimal costEstimate) {
        this.costEstimate = costEstimate;
    }

    public BigDecimal getTicketPrice() {
        return ticketPrice;
    }

    public void setTicketPrice(BigDecimal ticketPrice) {
        this.ticketPrice = ticketPrice;
    }
}
