package com.paic.ncbs.claim.dao.entity.ahcs;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.math.BigDecimal;

/**
 * 共保信息
 */
public class AhcsCoinsureEntity extends EntityDTO {

	private static final long serialVersionUID = -1225425320524358250L;

    private String idAhcsCoinsure;

    private String idAhcsPolicyInfo;

    private String coinsuranceType;

    private String acceptInsuranceFlag;

    private String reinsureCompanyCode;

    private String reinsureCompanyName;

    private BigDecimal reinsureScale;

    private BigDecimal insuredAmount;

    private BigDecimal premium;

    public String getIdAhcsCoinsure() {
        return idAhcsCoinsure;
    }

    public void setIdAhcsCoinsure(String idAhcsCoinsure) {
        this.idAhcsCoinsure = idAhcsCoinsure == null ? null : idAhcsCoinsure.trim();
    }

    public String getIdAhcsPolicyInfo() {
        return idAhcsPolicyInfo;
    }

    public void setIdAhcsPolicyInfo(String idAhcsPolicyInfo) {
        this.idAhcsPolicyInfo = idAhcsPolicyInfo == null ? null : idAhcsPolicyInfo.trim();
    }

    public String getCoinsuranceType() {
        return coinsuranceType;
    }

    public void setCoinsuranceType(String coinsuranceType) {
        this.coinsuranceType = coinsuranceType == null ? null : coinsuranceType.trim();
    }

    public String getAcceptInsuranceFlag() {
        return acceptInsuranceFlag;
    }

    public void setAcceptInsuranceFlag(String acceptInsuranceFlag) {
        this.acceptInsuranceFlag = acceptInsuranceFlag == null ? null : acceptInsuranceFlag.trim();
    }

    public String getReinsureCompanyCode() {
        return reinsureCompanyCode;
    }

    public void setReinsureCompanyCode(String reinsureCompanyCode) {
        this.reinsureCompanyCode = reinsureCompanyCode == null ? null : reinsureCompanyCode.trim();
    }

    public String getReinsureCompanyName() {
        return reinsureCompanyName;
    }

    public void setReinsureCompanyName(String reinsureCompanyName) {
        this.reinsureCompanyName = reinsureCompanyName == null ? null : reinsureCompanyName.trim();
    }

    public BigDecimal getReinsureScale() {
        return reinsureScale;
    }

    public void setReinsureScale(BigDecimal reinsureScale) {
        this.reinsureScale = reinsureScale;
    }

    public BigDecimal getInsuredAmount() {
        return insuredAmount;
    }

    public void setInsuredAmount(BigDecimal insuredAmount) {
        this.insuredAmount = insuredAmount;
    }

    public BigDecimal getPremium() {
        return premium;
    }

    public void setPremium(BigDecimal premium) {
        this.premium = premium;
    }
}