package com.paic.ncbs.claim.model.vo.duty;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Accessors(chain = true)
@ApiModel("ClmsCargoVO-货运险VO")
public class ClmsCargoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    // ==================== 货物详情字段 ====================

    /**
     * 报案号
     */
    private String reportNo;

    /**
     * 赔付次数
     */
    private Integer caseTimes;

    /**
     * 发票号
     */
    private String invoiceNo;

    /**
     * 发票金额
     */
    private BigDecimal invoiceAmount;

    /**
     * 运单号
     */
    private String billNo;

    /**
     * 加成率（%）
     */
    private BigDecimal loadingRate;

    /**
     * 运输方式
     */
    private String transCode;

    /**
     * 运输方式名称
     */
    private String transName;

    /**
     * 运输工具
     */
    private String vehicle;

    /**
     * 运输工具单号
     */
    private String vehicleNo;

    /**
     * 贸易术语
     */
    private String tradeCode;

    /**
     * 贸易术语名称
     */
    private String tradeName;

    /**
     * 货物名称
     */
    private String cargoName;

    /**
     * 包装方式
     */
    private String packingCode;

    /**
     * 包装方式名称
     */
    private String packingName;

    /**
     * 货物类别一级
     */
    private String category1;

    /**
     * 货物类别二级
     */
    private String category2;

    /**
     * 货物类别三级
     */
    private String category3;

    /**
     * 损失类型
     */
    private String lossCode;

    /**
     * 损失类型名称
     */
    private String lossName;

    /**
     * 损失区段
     */
    private String lossSecCode;

    /**
     * 损失区段名称
     */
    private String lossSecName;

    /**
     * 损失区段详情
     */
    private String lossSecDetail;

    /**
     * 损失描述
     */
    private String lossDesc;

    /**
     * 备注
     */
    private String remark1;

    // ==================== 运输路径字段 ====================

    /**
     * 运输类型
     */
    private String transTypeCode;

    /**
     * 运输类型名称
     */
    private String transTypeName;

    /**
     * 起运地
     */
    private String startAddr;

    /**
     * 起运地日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    /**
     * 装货地
     */
    private String loadAddr;

    /**
     * 装货地日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate loadDate;

    /**
     * 卸货地
     */
    private String unloadAddr;

    /**
     * 卸货地日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate unloadDate;

    /**
     * 中转地
     */
    private String transAddr;

    /**
     * 中转地日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate transDate;

    /**
     * 赔款偿付地
     */
    private String payAddr;

    /**
     * 赔款偿付地日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate payDate;

    /**
     * 目的地
     */
    private String endAddr;

    /**
     * 目的地日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    /**
     * 备注
     */
    private String remark2;

}
