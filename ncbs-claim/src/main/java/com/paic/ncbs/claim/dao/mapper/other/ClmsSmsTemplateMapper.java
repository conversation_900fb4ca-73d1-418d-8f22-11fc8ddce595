package com.paic.ncbs.claim.dao.mapper.other;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.model.dto.message.ClmsSmsTemplateDTO;
import org.mybatis.spring.annotation.MapperScan;

/**
* <AUTHOR>
* @description 针对表【clms_sms_template(短信模板表)】的数据库操作Mapper
* @createDate 2025-05-29 15:17:55
* @Entity com.paic.ncbs.claim.model.dto.message.ClmsSmsTemplate
*/
@MapperScan
public interface ClmsSmsTemplateMapper extends BaseMapper<ClmsSmsTemplateDTO> {

    ClmsSmsTemplateDTO getSmsTemplate(String template);

    void modifySmsTemplate(String templateDesc, String templateClass);
}




