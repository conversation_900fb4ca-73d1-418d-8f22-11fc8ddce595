package com.paic.ncbs.claim.dao.entity.clms;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 财产损失信息表(ClmsPropertyLossInfo)实体类
 *
 * <AUTHOR>
 * @since 2023-08-11 10:09:48
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ClmsPropertyLossInfo implements Serializable {
    private static final long serialVersionUID = 956423305740338630L;
    /**
     * 主键
     */
    private String id;
    /**
     * 报案号
     */
    private String reportNo;
    /**
     * 赔付次数
     */
    private Integer caseTimes;
    /**
     * 第三者信息-个人 1、公司 0
     */
    private String thirdPartyLossType;
    /**
     * 第三者信息-姓名
     */
    private String thirdPartyName;
    /**
     * 第三者信息-证件类型
     */
    private String thirdPartyCertificateType;
    /**
     * 第三者信息-证件号
     */
    private String thirdPartyCertificateNo;
    /**
     * 财产损失原因
     */
    private String propertyLossCause;
    /**
     * 损失项目名称
     */
    private String propertyLossProject;
    /**
     * 赔偿金额
     */
    private BigDecimal payAmount;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人员
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDate;
    /**
     * 修改人员
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDate;
    /**
     * 报损金额
     */
    private BigDecimal reportAmount;
    /**
     * 定损金额
     */
    private BigDecimal estimateAmount;
    /**
     * 残值
     */
    private BigDecimal salvageValue;


}

