package com.paic.ncbs.claim.dao.entity.antimoneylaundering;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 公司反洗钱信息实体对象
 */
@Data
public class ClmsAmlCompanyInfoEntity extends EntityDTO {

    /**
     * 主键
     */
    private String id;

    /**
     * 报案号
      */
    private String reportNo;
    /**
     * 赔付次数
     */
    private int caseTimes;

    /**
     * 公司名称：领款人姓名/收款人姓名
     */
    private String companyName;

    /**
     * 机构类型
     */
    private String agencyType;
    /**
     * 机构编码：机构类型为一般机构时存 统一社会信用代码，机构类型为其他
     */
    private String agencyCode;

    /**
     * 客户号
     */
    private String customerNo;
    /**
     * 经营范围和业务范围
     */
    private String bussScope;
    /**
     * 营业执照有效起期
     */
    private Date bussLicenseStart;
    /**
     * 营业执照有效止期
     */
    private Date bussLicenseEnd;

    /**
     * 税务登记号
     */
    private String taxNo;
    /**
     * 行业
     */
    private String industry;
    /**
     * 注册资金类型
     */
    private String regisCapitalType;

    /**
     * 注册资金
     */
    private Integer regisCapital;
    /**
     * 经营证件有效起期
     */
    private Date bussCertificateStart;
    /**
     * 经营证件有效止期
     */
    private Date bussCertificateEnd;
    /**
     * 法定代表人/负责人姓名
     */
    private String legalRepresentName;
    /**
     * 法定代表人/负责人证件类型
     */
    private String legalRepresentCardType;
    /**
     * 法定代表人/负责人证件号
     */
    private String legalRepresentCardNo;
    /**
     * 法定代表人/负责人证件有效起期
     */
    private Date legalRepresentCardStart;
    /**
     * 法定代表人/负责人证件有效止期
     */
    private Date legalRepresentCardEnd;
    /**
     * 法定代表人/负责人证件是否长期有效 0-否 1-是
     */
    private String legalLongTermFlag;
    /**
     * 法定代表人/负责人证件附件id
     */
    private String legalFileId;
    /**
     * 业务人员姓名
     */
    private String bussPeopleName;
    /**
     * 业务人员证件类型
     */
    private String bussPeopleCardType;
    /**
     * 业务人员证件号码
     */
    private String bussPeopleCardNo;
    /**
     * 业务人员证件有效起期
     */
    private Date bussPeopleCardStart;
    /**
     * 业务人员证件有效止期
     */
    private Date bussPeopleCardEnd;
    /**
     * 业务人员证件附件id
     */
    private String bussPeopleFileId;
    /**
     * 省份编码
     */
    private String provinceCode;
    /**
     * 城市编码
     */
    private String cityCode;
    /**
     *    区/县编码
     */
    private String countyCode;
    /**
     * 详细地址
     */
    private String address;

    /**
     * 境内境外标志：0-国内;1-国外
     */
    private String overseasOccur;

    /**
     * 有效状态 0-有效，1作废
     */
    private String effectiveStatus;
    /**
     * 公司证件类型/企业证件类型
     *取值：CertificateTypeEnum枚举类
     *  统一社会信用代码-610099
     * 组织机构代码-610001
     * 税务登记证-610007
     * 营业执照-610005
     * 其他证件-619999
     */
    private String companyCardType;
}
