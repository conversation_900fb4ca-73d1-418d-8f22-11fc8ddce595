package com.paic.ncbs.claim.model.vo.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 批改自动实名化
 */
@Data
public class OcasRealNameVo {

    @ApiModelProperty("报案号")
    @NotBlank
    private String reportNo;

    @ApiModelProperty("姓名")
    @NotBlank
    private String insuredName;

    @ApiModelProperty("证件类型")
    @NotBlank
    private String certificateType;

    @ApiModelProperty("证件号码")
    @NotBlank
    private String certificateNo;

    @ApiModelProperty("人员标的表ID")
    @NotBlank
    private String idPlyRiskPerson;

}
