package com.paic.ncbs.claim.dao.mapper.taskdeal;

import com.paic.ncbs.claim.dao.entity.clms.ClmsMngDashboardHis;
import com.paic.ncbs.claim.dao.entity.clms.ClmsMngDashboardIntf;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.model.vo.dashboard.ClmsMngDashboardHisVo;
import com.paic.ncbs.claim.model.vo.dashboard.ManageDashBoardReqVo;

import java.util.List;

/**
 * <p>
 * 理赔管理看板接口表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
public interface ClmsMngDashboardIntfMapper extends BaseMapper<ClmsMngDashboardIntf> {

    ClmsMngDashboardIntf selectByTimeAndUser(ManageDashBoardReqVo manageDashBoardReqVo);

    void deleteAll();

    List<ClmsMngDashboardHis> selectByData(ManageDashBoardReqVo manageDashBoardReqVo);

    List<ClmsMngDashboardHisVo> selectByTimeToMounth(ManageDashBoardReqVo manageDashBoardReqVo);
}
