package com.paic.ncbs.claim.dao.mapper.estimate;

import com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface EstimatePlanMapper {

    List<EstimatePlanDTO> getByPolicyID(@Param("idAhcsEstimatePolicy") String idAhcsEstimatePolicy);

    List<EstimatePlanDTO> getPlanFormCopy(@Param("idAhcsEstimatePolicy") String idAhcsEstimatePolicy);

    void addBatchEstimatePlan(@Param("paramList") List<EstimatePlanDTO> estimatePlanListUpdate);

    void modifyBatchEstimatePlan(@Param("paramList") List<EstimatePlanDTO> estimatePlanList);

    List<EstimatePlanDTO> getListByPolicyId(@Param("paramList") List<String> paramList);

    void modifyPlanAmount(@Param("planList") List<EstimatePlanDTO> estimatePlanList);

    void delEstimatePlanDataByCaseNo(@Param("caseNo") String caseNo, @Param("caseTimes") Integer caseTimes);

    List<EstimatePlanDTO> getByPolicyIDForRestart(@Param("idAhcsEstimatePolicy") String idAhcsEstimatePolicy);

    /**
     * 案件重开，数据拷贝
     * @param paramList
     */
    void copyForCaseReopen(List<EstimatePlanDTO> paramList);

    void delEstimatePlanByCaseNo(@Param("caseNoList") List<String> caseNoList,@Param("caseTimes") Integer caseTimes);
}
