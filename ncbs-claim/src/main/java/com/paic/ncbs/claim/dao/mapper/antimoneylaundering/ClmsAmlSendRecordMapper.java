package com.paic.ncbs.claim.dao.mapper.antimoneylaundering;

import com.paic.ncbs.claim.dao.entity.antimoneylaundering.ClmsSendAmlRecordEntity;
import com.paic.ncbs.claim.model.vo.antimoneylaundering.AmlSendRecordVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * 理赔送反洗钱记录mapper
 */
@MapperScan
public interface ClmsAmlSendRecordMapper {

    /**
     * 批量新增数据
     *
     * @param entityList 实例对象集合
     * @return 影响行数
     */
    int batchInsert(List<ClmsSendAmlRecordEntity> entityList);

    /**
     * 批量更新数据
     *
     * @param entityList 实例对象集合
     * @return 影响行数
     */
    int batchUpdate(List<ClmsSendAmlRecordEntity> entityList);

    /**
     * 根据条件查询单条记录
     *
     * @param query 查询条件
     * @return 单条记录
     */
    ClmsSendAmlRecordEntity selectOne(ClmsSendAmlRecordEntity query);

    /**
     * 获取可疑交易反洗钱审批记录
     *
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return 可疑交易反洗钱审批记录
     */
    List<AmlSendRecordVO> selectList(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);
}
