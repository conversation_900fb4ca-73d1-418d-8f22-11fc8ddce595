package com.paic.ncbs.claim.model.vo.duty;


import com.paic.ncbs.claim.model.dto.duty.PersonOtherLossDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("其他人伤信息VO")
public class PersonOtherLossVO {

    @ApiModelProperty("")
    private String moduleCode;
    @ApiModelProperty("其他人伤信息DTO")
    private List<PersonOtherLossDTO> personOtherLossList;
    @ApiModelProperty("状态")
    private String status;

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public List<PersonOtherLossDTO> getPersonOtherLossList() {
        return personOtherLossList;
    }

    public void setPersonOtherLossList(List<PersonOtherLossDTO> personOtherLossList) {
        this.personOtherLossList = personOtherLossList;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
