package com.paic.ncbs.claim.dao.entity.checkloss;

import java.util.Date;

public class BaseProdPifTermEntity {

    private String idProdPifTerm;

    private String salesRegion;

    private String insuredType;

    private String termCode;

    private String termName;

    private String insuredSpecies;

    private String insuredSpeciesName;

    private String termUrl;

    private Date createdDate;

    private String createdBy;

    private Date updatedDate;

    private String updatedBy;

    private String documentGroupId;

    private String flag;

    private String termEnName;

    public String getIdProdPifTerm() {
        return idProdPifTerm;
    }

    public void setIdProdPifTerm(String idProdPifTerm) {
        this.idProdPifTerm = idProdPifTerm == null ? null : idProdPifTerm.trim();
    }

    public String getSalesRegion() {
        return salesRegion;
    }

    public void setSalesRegion(String salesRegion) {
        this.salesRegion = salesRegion == null ? null : salesRegion.trim();
    }

    public String getInsuredType() {
        return insuredType;
    }

    public void setInsuredType(String insuredType) {
        this.insuredType = insuredType == null ? null : insuredType.trim();
    }

    public String getTermCode() {
        return termCode;
    }

    public void setTermCode(String termCode) {
        this.termCode = termCode == null ? null : termCode.trim();
    }

    public String getTermName() {
        return termName;
    }

    public void setTermName(String termName) {
        this.termName = termName == null ? null : termName.trim();
    }

    public String getInsuredSpecies() {
        return insuredSpecies;
    }

    public void setInsuredSpecies(String insuredSpecies) {
        this.insuredSpecies = insuredSpecies == null ? null : insuredSpecies.trim();
    }

    public String getInsuredSpeciesName() {
        return insuredSpeciesName;
    }

    public void setInsuredSpeciesName(String insuredSpeciesName) {
        this.insuredSpeciesName = insuredSpeciesName == null ? null : insuredSpeciesName.trim();
    }

    public String getTermUrl() {
        return termUrl;
    }

    public void setTermUrl(String termUrl) {
        this.termUrl = termUrl == null ? null : termUrl.trim();
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(Date updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public String getDocumentGroupId() {
        return documentGroupId;
    }

    public void setDocumentGroupId(String documentGroupId) {
        this.documentGroupId = documentGroupId == null ? null : documentGroupId.trim();
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag == null ? null : flag.trim();
    }

    public String getTermEnName() {
        return termEnName;
    }

    public void setTermEnName(String termEnName) {
        this.termEnName = termEnName == null ? null : termEnName.trim();
    }
}