package com.paic.ncbs.claim.dao.mapper.settle;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyClaimCaseDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface PolicyClaimCaseMapper extends BaseDao<PolicyClaimCaseDTO> {

    int insertDutyPayInfo(@Param("list") List<DutyPayDTO> list);

    PolicyClaimCaseDTO selectByReportNo(String reportNo);

    String selectPartyNoByCaseNo(@Param("caseNo") String caseNo);

    PolicyClaimCaseDTO getByReportNoAndPolicyNo(@Param("reportNo") String reportNo, @Param("policyNo") String policyNo);

    void addBatchPolicyClaimCase(@Param("paramList") List<PolicyClaimCaseDTO> policyClaimCaseDTOList);

    List<PolicyClaimCaseDTO> getPolicyClaimCaseListByReportNo(@Param("reportNo") String reportNo);

    List<PolicyClaimCaseDTO> getByCaseNo(@Param("caseNo") String caseNo);

    public int getReportNoByPolicyNo(@Param("policyNoList") List<String> policyNoList);

    List<PolicyClaimCaseDTO> getInsuredCodeByReportNo(@Param("reportNo") String reportNo);

    /**
     * 根据报案号，被保险人客户号查询保单号
     * @param reportNo
     * @param insuredNo
     * @return
     */
    List<String> getInsuredPolicyNo(String reportNo, String insuredNo);
}