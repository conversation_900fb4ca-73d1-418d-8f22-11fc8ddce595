package com.paic.ncbs.claim.dao.mapper.mistake;


import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.model.dto.mistake.MistakeRecordDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface MistakeRecordMapper extends BaseDao<MistakeRecordDTO> {


    void addMistakeRecords(@Param("mistakeRecord") MistakeRecordDTO record);

    List<MistakeRecordDTO> getSimpleMistakeRecord(@Param("reportNo")String reportNo, @Param("caseTimes") Integer caseTimes, @Param("recordTaskId") String recordTaskId);

    void deleteByReportNoAndBpmKey(@Param("reportNo")String reportNo,@Param("caseTimes") Integer caseTimes,@Param("recordTaskId") String recordTaskId);
}
