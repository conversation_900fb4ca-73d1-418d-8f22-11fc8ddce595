# NCBS-CLAIM工程说明

## spring boot
1. 版本：2.3.12.RELEASE

## spring cloud
1. 版本：Hoxton.SR12
2. 添加spring cloud的中的组件，统一加Hoxton.SR12版本对应的组件

## spring cloud alibaba
1. 版本：2.2.7.RELEASE

## Sentinel
1. 版本：1.8.1

## nacos
1. 版本：1.4.2

## MySQL
1. 版本：8.0.23

## redis
1. 版本：5.0.7

## RabbitMQ
1. 版本： 3.8.16
2. 消息队列，暂时没有用上

## seata
1. 版本： 1.4.2
2. 分布式事务组件，暂时没有用上

## swagger api 注释
1. Controller层代码，出入参，按swagger风格添加注释

## 代码目录结构
```shell
ncbs-claim
    └─src
        ├─main
        │  ├─docker
        │  ├─java
        │  │  └─com
        │  │      └─paic
        │  │          └─ncbs
        │  │              └─claim
        │  │                  ├─aspect
        │  │                  ├─cache
        │  │                  │  └─impl
        │  │                  ├─common
        │  │                  │  ├─annotation
        │  │                  │  ├─constant
        │  │                  │  ├─context
        │  │                  │  ├─enums
        │  │                  │  ├─page
        │  │                  │  ├─response
        │  │                  │  ├─service
        │  │                  │  └─util
        │  │                  ├─config
        │  │                  ├─controller
        │  │                  │  ├─ahcs
        │  │                  │  ├─doc
        │  │                  │  ├─duty
        │  │                  │  ├─mng
        │  │                  │  ├─report
        │  │                  │  └─who
        │  │                  │      ├─caseclass
        │  │                  │      ├─channelprocess
        │  │                  │      ├─estimate
        │  │                  │      ├─settle
        │  │                  │      └─taskdeal
        │  │                  ├─dao
        │  │                  │  ├─base
        │  │                  │  ├─entity
        │  │                  │  │  ├─ahcs
        │  │                  │  │  ├─checkloss
        │  │                  │  │  ├─endcase
        │  │                  │  │  ├─estimate
        │  │                  │  │  ├─other
        │  │                  │  │  ├─report
        │  │                  │  │  ├─taskdeal
        │  │                  │  │  └─user
        │  │                  │  └─mapper
        │  │                  │      ├─accident
        │  │                  │      ├─ahcs
        │  │                  │      ├─bpm
        │  │                  │      ├─checkloss
        │  │                  │      ├─doc
        │  │                  │      ├─duty
        │  │                  │      ├─endcase
        │  │                  │      ├─estimate
        │  │                  │      ├─fileupload
        │  │                  │      ├─other
        │  │                  │      ├─report
        │  │                  │      ├─settle
        │  │                  │      ├─taskdeal
        │  │                  │      ├─user
        │  │                  │      └─verify
        │  │                  ├─exception
        │  │                  ├─feign
        │  │                  │  ├─doc
        │  │                  │  └─who
        │  │                  ├─filter
        │  │                  ├─handler
        │  │                  ├─model
        │  │                  │  ├─dto
        │  │                  │  │  ├─accident
        │  │                  │  │  ├─ahcs
        │  │                  │  │  ├─bpm
        │  │                  │  │  ├─checkloss
        │  │                  │  │  ├─communicate
        │  │                  │  │  ├─doc
        │  │                  │  │  ├─duty
        │  │                  │  │  ├─endcase
        │  │                  │  │  ├─estimate
        │  │                  │  │  ├─fee
        │  │                  │  │  ├─fileupload
        │  │                  │  │  ├─other
        │  │                  │  │  ├─pay
        │  │                  │  │  ├─protocol
        │  │                  │  │  ├─report
        │  │                  │  │  ├─settle
        │  │                  │  │  ├─taskdeal
        │  │                  │  │  ├─user
        │  │                  │  │  └─verify
        │  │                  │  └─vo
        │  │                  │      ├─accident
        │  │                  │      ├─ahcs
        │  │                  │      ├─bpm
        │  │                  │      ├─checkloss
        │  │                  │      ├─doc
        │  │                  │      ├─duty
        │  │                  │      ├─endcase
        │  │                  │      ├─fileupolad
        │  │                  │      ├─other
        │  │                  │      ├─report
        │  │                  │      ├─settle
        │  │                  │      ├─taskdeal
        │  │                  │      └─user
        │  │                  ├─sao
        │  │                  │  └─impl
        │  │                  ├─service
        │  │                  │  ├─accident
        │  │                  │  │  └─impl
        │  │                  │  ├─ahcs
        │  │                  │  │  └─impl
        │  │                  │  ├─base
        │  │                  │  │  └─impl
        │  │                  │  ├─bpm
        │  │                  │  │  └─impl
        │  │                  │  ├─checkloss
        │  │                  │  │  └─impl
        │  │                  │  ├─doc
        │  │                  │  │  └─impl
        │  │                  │  ├─duty
        │  │                  │  │  └─impl
        │  │                  │  ├─endcase
        │  │                  │  │  └─impl
        │  │                  │  ├─estimate
        │  │                  │  │  └─impl
        │  │                  │  ├─fileupload
        │  │                  │  │  └─impl
        │  │                  │  ├─mock
        │  │                  │  │  └─impl
        │  │                  │  ├─other
        │  │                  │  │  └─impl
        │  │                  │  ├─report
        │  │                  │  │  └─impl
        │  │                  │  ├─self
        │  │                  │  ├─settle
        │  │                  │  │  └─impl
        │  │                  │  ├─taskdeal
        │  │                  │  │  └─impl
        │  │                  │  ├─user
        │  │                  │  │  └─impl
        │  │                  │  └─verify
        │  │                  │      └─impl
        │  │                  └─strategy
        │  │                      └─calculate
        │  │                          └─impl
        │  └─resources
        │      └─mapper
        │          ├─accident
        │          ├─ahcs
        │          ├─bpm
        │          ├─checkloss
        │          ├─doc
        │          ├─duty
        │          ├─endcase
        │          ├─estimate
        │          ├─fileupload
        │          ├─other
        │          ├─report
        │          ├─settle
        │          ├─taskdeal
        │          ├─user
        │          └─verify
        └─test
            └─java
```
